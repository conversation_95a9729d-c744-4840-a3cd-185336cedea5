<?php
/**
 * This File provides the input fields which needs to create add & update meals
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: MealForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\CommonConfig as QSConfig;
use Lib\Utility;

use QuickServe\Model\ProductTable as Product;
use QuickServe\Model\LocationTable;
use QuickServe\Model\Meal;

class MealForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * It adds an input fields which are needed to create product form
	 *
	 * @param Adapter $adapter
	 */
	
	protected $kitchen_code = null;
	
    public function __construct($sm)
    {        
        // we want to ignore the name passed
        parent::__construct('product');
        $this->service_locator = $sm;
        /* $adapt was passed intially. changed for  */
        //$this->adapter = $sm->get('Write_Adapter');
        
        $utility = Utility::getInstance ();
        $setting = new Container('setting');
        $setting = $setting->setting;        
        $currencySymbol = $utility->getCurrencySymbol($setting['GLOBAL_CURRENCY'],$setting['GLOBAL_LOCALE']);

        $this->setAttribute('method', 'post');
        
        $this->add(array(
            'name' => 'pk_meal_code',
            'attributes' => array(
                'type'  => 'Hidden',
            ),
        ));

        $this->add(array(
            'name' => 'pk_product_code',
        	'type' => 'Zend\Form\Element\Hidden',
            'attributes' => array(
                'type'  => 'Hidden',
            ),
        ));
        
		 $this->add(array(
            'name' => 'name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Name...',
            	'maxlength' => 75,
                //'required' => 'required',
            ),
            'options' => array(
                'label' => 'Name<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
		 
		$this->add(array(
		 	'name' => 'description',
		 	'type' => 'Zend\Form\Element\Textarea',
		 	'attributes' => array(
		 		'class' => 'smallinput',
		 		'placeholder' => 'Enter Description...',
		 		//'required' => 'required',
		 	),
		 	'options' => array(
		 		'label' => 'Description<span class="red">*</span>',
		 		'label_options' => array('disable_html_escape' => true),
		 	),
		));
		 
		$this->add(array(
            'name' => 'unit_price',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Unit Price...',
               // 'required' => 'required',
            ),
            'options' => array(
                'label' => 'Price('.$currencySymbol.')<span	class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true)
            ),
            
        ));

		$this->add(array(
				'name' => 'food_type',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'food_type',
						//	'required' => 'required',
						'value' => '0',
				),
				'options' => array(
					'label' => 'Type of Food<span class="red">*</span>',
					'label_options' => array('disable_html_escape' => true),
					'value_options' => $this->getFoodTypes(),
						'label_options' => array('disable_html_escape' => true),
				),
		));

		$this->add(array(
				'name' => 'product_category',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'product_category',
						//	'required' => 'required',
						'value' => '',
				),
				'options' => array(
					'label' => 'Product Category',
					'label_options' => array('disable_html_escape' => true),
					'value_options' => $this->getProductCategories()
				),
		));
		
		$this->add(array(
        		'name' => 'meal_plans',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        			'class'=>'chosen-select',
        			'multiple' => 'multiple',
        			'id' => 'meal_plans',
        	  		//'required' => 'required',
        		),
        		'options' => array(
        				'label' => 'Meal Plan',
        				'value_options' => $this->getMealAllPlans(),
        				'label_options' => array('disable_html_escape' => true),
        				'disable_inarray_validator' => true
        		),
        ));
		
		$this->add(array(
			'name' => 'category',
			'type' => 'Zend\Form\Element\Select',
			'attributes' => array(
				'style'=>"height:75px",
				'multiple' => 'multiple',
				'id' => 'menu',
				//'required' => 'required',
				'value' => '0',
			),
			'options' => array(
				'label' => 'Menu <span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true),
				'value_options' => $this->getMenuTypes(),
			),
		));
		

		
		$objProduct = new Product($this->service_locator);

		$objProduct = $this->service_locator->get('QuickServe\Model\ProductTable');

		
		$select = new QSelect();
		$select->where("product_type IN ('Main','Extra') AND status='1' AND product_subtype='generic'");
		$select->order("name asc");
        
        if (! $this->authservice){
     		$this->authservice = $sm->get('AuthService');
     	}
     	       
     	$iden = $this->authservice->getIdentity();
     	
        $products = $objProduct->fetchAll($select, null, null, null, null, null, $_SESSION['adminkitchen'], null, 'yes', array_column($iden->kitchens, 'fk_kitchen_code') );
	
		$arrProds = array();
		$arrProds['']= "Select Products";
		foreach($products as $prod){
			$arrProds[$prod->pk_product_code] = $prod->name;
		}
		
		$this->add(array(
			'name' => 'product[]',
			'type' => 'Zend\Form\Element\Select',
			'attributes' => array(
				'class' => 'smallinput itemscls',
				'id' => 'items',
				'required' => 'required',
			),
			'options' => array(
            	'label' => 'Screen<span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true),
				'label_options' => array('disable_html_escape' => true),
				'value_options' => $arrProds
			 ),
		));
		
		$this->add(array(
			'name' => 'product_quantity[]',
			'type' => 'Zend\Form\Element\Text',
			'attributes' => array(
				'class' => 'smallinput',
				'required' => 'required',
			),
			'options' => array(
				'label' => 'Quantity<span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true),
			),
		));

       $this->add(array(
            'name' => 'threshold',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Kitchen Capacity...',
                //'required' => 'required',
            ),
            'options' => array(
            	'label' => 'Kitchen Capacity<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
       
       $this->add(array(
       		'name' => 'image_path',
       		'type'=>'file',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'accept'=>"image/*",
       		),
       		'options' => array(
       			'label' => 'Meal/Combo Image<span class="red">*</span>',
       			'label_options' => array('disable_html_escape' => true)
       		),
       ));
        

       $kitchenScreens = array();
       
       /*for($screen=1;$screen <= $subscription_keys_session->keys['KITCHEN_SCREEN_COUNT'];$screen++){
        	
       $kitchenScreens[$screen] = "Screen ".$screen;
       	
       }*/
       $kitchenScreens = $this->getKitchenScreens();
       
       
       $this->add(array(
       		'name' => 'screen',
       		'type' => 'Zend\Form\Element\Select',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'id' => 'screen',
       				//	'required' => 'required',
       				'value' => '',
       		),
       		'options' => array(
       				'label' => 'Kitchen<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       				'value_options' => $kitchenScreens
       		),
       ));
       
      $this->add(array(
				'name' => 'status',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
					'id' => 'status',
				),
				'options' => array(
					'label' => 'Status',
					'label_options' => array('disable_html_escape' => true),
					'value_options' => array(
						'1' => 'Active',
						'0' => 'Inactive',
					),
				),
		));
      
      
      // Create form elements for meal swapping
      
       $this->add(array(
       		'name'=>'is_swappable',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       			'class'=>'swapradio small',
       			'id' => 'is_swappable',
       		),
       		'options'=>array(
       			'label'=>'Swappable <span class="red">*</span>',
       			'label_attributes' => array(
       				'class' => 'inline mr5',
       			),
       			'value_options' => array(
       			        
   			        array(
						'value' => '1',
						'label' => 'Yes',
						'label_attributes' => array(
							'class'=>"swappable left mr5"
						),
   					),
   					array(
						'value' => '0',
						'label' => 'No',
						'label_attributes' => array(
							'class'=>"swappable left mr5"
						),
   					)
       			),
       		       
       			'label_options' => array('disable_html_escape' => true),
       			'disable_inarray_validator' => true,
       		),
           'attributes'=>array(
   		        'value'=>'1'       
   		    ), 
       ));
      
       $this->add(array(
       		'name'=>'swap_with',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       			'class'=>'small',
       			'id' => 'swap_with',
       		),
       		'options'=>array(
       			'label'=>'Swap With <span class="red">*</span>',
       			'label_attributes' => array(
       				'class' => 'mr5',
       			),
       			'value_options' => array(
       				'nocharge' => 'No Charge',
       				'askdifference' => 'Ask Difference',
       			    'swapcharge'=>'Swap Charges'
       			),
       			'label_options' => array('disable_html_escape' => true),
       			'disable_inarray_validator' => true,
       		),
            'attributes'=>array(
   		        'value'=>'nocharge'       
   		    ),
       ));
       
       $this->add(array(
            'name' => 'swap_charges',
            'type' => 'Zend\Form\Element\Text',
            'required'=>false,   
            'attributes' => array(
                'class' => 'smallinput',
                'style' =>'width:150px;',    
                'id'=>'swap_charges', 
                'placeholder' => 'Swapping Charges',
                 'onkeypress' => 'javascript:return isNumber(event)' 
            ),
            'options' => array(
            	'label' => 'Swapping Charges',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
       
       
		 $this->add(array(
        		'name' => 'csrf',
        		'type' => 'Zend\Form\Element\Csrf',
        ));

        $this->add(array(
            'name' => 'submit',
            'attributes' => array(
                'type'  => 'submit',
                'value' => 'Go',
                'id' => 'submitbutton',
            ),
        ));

        $this->add(array(
        	'name' => 'cancel',
        	'attributes' => array(
        		'type'  => 'button',
        		'value' => 'Cancel',
        		'id' => 'cancelbutton',
        	),
        ));

        $this->add(array(
        	'name'=>'backurl',
        	'type' => 'Zend\Form\Element\Hidden',
        	'attributes'=>array(
        		'id'=>'backurl',
        		'value'=>'/meal',
        ),
        ));
                
    }

    /**
     *
     * The function `getProductCategories` fetches product categories from the product_category table.
     * 
     * @method getProductCategories
     * @access private
     * @return array
     */
    private function getProductCategories() {   
    	$product_category_array = array();
       	$sql = new QSql($this->service_locator);
    
    	$select = $sql->select();
    	$select->from('product_category');
		$select->where('type="meal"');
    	$select->where('status',1);
        $results = $sql->execQuery($select);
    	// Iterate through all records.
    	foreach ($results as $res) {
    		// value is the product category name
    		$product_category_array[$res['product_category_name']] = $res['product_category_name'];
    	}// end of foreach
    
    	#print_r($product_category_array);exit();
    	return $product_category_array;
    }
    
    private function getMenuTypes(){
    	        
        $session_setting = new Container("setting");
        $setting = $session_setting->setting;
        
    	$menu_type = $setting['MENU_TYPE'];
        ;
    	$arr=array();
    
    	foreach( $menu_type as $k => $v ) {
    		$arr[$v] = strtoupper($v);
    	}
        
    	return $arr;
    }    
    
    private function getFoodTypes() {
        
    	$session_setting = new Container("setting");
        $setting = $session_setting->setting;
        
    	$food_type = $setting['FOOD_TYPE'];
    	
    	$arr=array();
    	
    	foreach( $food_type as $k => $v ) {
    		$arr[$v] = strtoupper($v);
    	}
        
    	return $arr;    	
    }

    public function getLocationArray() {
    	$location_array = array(0 => 'All Locations');
		$tblLocations = $this->service_locator->get('QuickServe\Model\LocationTable');
    	$temp_location_array = $tblLocations->getActiveLocations();
    	if(is_array($temp_location_array) && !empty($temp_location_array)) {
    		foreach( $temp_location_array as $location_id => $location_name) {
    			$location_array[$location_id] = $location_name;
    		}//end of foreach
    	}
    	return $location_array;
    }
    
    private function getKitchenScreens() {
    	$kitchen_array = array('' => 'Select the kitchen', '0'=>'All');
    	$sql = new QSql($this->service_locator);
    
    	$select = $sql->select();
    	$select->from('kitchen_master');
		$results = $sql->execQuery($select);
    
    	// Iterate through all records.
    	foreach ($results as $res) {
    		// value is the product category name
    		$kitchen_array[$res['pk_kitchen_code']] = $res['kitchen_name'].( !empty($res['kitchen_name']) ? ' ('.$res['location'].')' : '' );
    	}// end of foreach
    	return $kitchen_array;
    }
    
    /**
     * To get the list of active products
     * @return array
     */
    public function getMealAllPlans()
    {
    	//$selectData = array('' => 'Select Plans', '0'=>'All');
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('plan_master');
    	$select->where(array(
     		'plan_status' => 1,
    		'show_to_customer' => 'yes',
    	));

		$results = $sql->execQuery($select);
    	foreach ($results as $res) {
    		$selectData[$res['pk_plan_code']."@".$res['plan_name']] = $res['plan_name'];      //concatinating Prod_code and Prod_name
    	}
    	
    	return $selectData;
    
    }    
}

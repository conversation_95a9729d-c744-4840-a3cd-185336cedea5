<?php

/**
 * This File provides the input fields which needs to create add & update location
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: KitchenForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */

namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

class PlanManagerForm extends Form{
	
	private $adapter;
    private $service_locator;
	
	public function __construct($sm){
		
		$this->service_locator = $sm;
		
		parent::__construct('plan_master');
		
		$this->setAttribute('method', 'post');
		
		
		$this->add(array(
				'name' => 'pk_plan_code',
				'attributes' => array(
						'type'  => 'Hidden',
				),
		));
		
		
		$this->add(array(
			'name'=>'plan_name',
			'type' => 'Zend\Form\Element\Text',
			'attributes'=>array(
					'class' => "smallinput",
					'placeholder' => 'Enter plan name',
			),
			'options' => array(
					'label' => 'Plan Name <span class="red">*</span>',
					'label_options' => array('disable_html_escape' => true),
			),
				
		));
		
		$this->add(
					array(
							'name'=>'plan_quantity',
							'type' => 'Zend\Form\Element\Text',
							'attributes'=>array(
									'class' => "smallinput",
									'placeholder' => 'Enter Meal Quantity...',
							),
							'options' => array(
									'label' => 'Meal Quantity <span class="red">*</span>',
									'label_options' => array('disable_html_escape' => true),
							),
							
					)
				);
		
		$this->add(array(
			'name'=>'plan_period',
			'type'=>'Zend\Form\Element\Text',	
			'attributes'=>array(
					'class'=>'smallinput',
					'Placeholder'=>'Enter Plan Period...',
			),
			'options' => array(
					'label' => 'Plan Period (Days)<span class="red">*</span>',
					'label_options' => array('disable_html_escape' => true),
			),
					
		));
		
		$this->add(array(
				'name'=>'plan_type',
				'type'=>'Zend\Form\Element\Select',
				'attributes'=>array(
						'class'=>'plantype',
				),
				'options' => array(
        				'label' => 'Plan Type <span class="red">*</span>',
        				'value_options' => array(
        										''=>'Choose Type',
        										'periodbased'=>"Auto Assign Dates",
        										'datebased'=>'Allow Customer to Choose dates',
        									),
        				'label_options' => array('disable_html_escape' => true),
        		),
					
		));

		$this->add(array(
				'name'=>'promo_code',
				'type'=>'Zend\Form\Element\Select',
				'attributes'=>array(
						'class'=>'promo_code',
                        'Placeholder'=>'Select Promo Code',
                        
				),
          
				'options' => array(
//                       'disable_inarray_validator' => true,
        				'label' => 'Promo Code',
        				'value_options' =>$this->getPromocode(),
        				'label_options' => array('disable_html_escape' => true),
        		),
					
		));
		$this->add(array(
				'name'=>'plan_start_date',
				'type'=>'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'left&#x20;filterSelect calender',
						'id'=>'maxDate',
						'size'=>"10",
				),
				'options' => array(
						'label' => 'Start Date <span class="red">*</span>',
						'label_options' => array('disable_html_escape' => true),
				),
					
		));
		
		$this->add(array(
				'name'=>'plan_end_date',
				'type'=>'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'left&#x20;filterSelect calender',
						'id'=>'minDate',
						'size'=>"10",
				),
				'options' => array(
						'label' => 'End Date <span class="red">*</span>',
						'label_options' => array('disable_html_escape' => true),
				),
					
		));
        
		$this->add(array(
       		'name' => 'fk_kitchen_code',
       		'type' => 'Zend\Form\Element\Select',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'id' => 'screen',
       				//	'required' => 'required',
       				'value' => '',
       		),
       		'options' => array(
       				'label' => 'Kitchen<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       				'value_options' => $this->getKitchenScreens()
       		),
       ));
		
        $this->add(array(
				'name'=>'show_to_customer',
				'type'=>'Zend\Form\Element\Radio',
				'attributes'=>array(
						'class' => 'inline right',
						'value' => 'yes' ,
						
				),
				'options'=>array(
        				'label'=>'Show to Customer<span class="red">*</span>',

        				'value_options' => array(
        						'yes' => 'Yes',
        						'no' => 'No',
        				),
        				'label_options' => array('disable_html_escape' => true, 'style' => 'display:inline-block'),
        		),
//                'decorators' => array('ViewHelper', 'Errors', array(array('data' => 'HtmlTag'),array('tag' => 'span', 'class' => 'no-padding'), ))
					
		));
		
		$this->add(array(
				'name'=>'plan_status',
				'type' => 'Zend\Form\Element\Radio',
				'attributes'=>array(
						'id'=>'uniform-status_1',
						'class' => 'inline right',
						'value' => '1',
				),
				'options'=>array(
						'label'=>'Plan Status<span class="red">*</span>',
						'value_options' => array(
								'1' => 'Active',
								'0' => 'Inactive',
						),
						'label_options' => array('disable_html_escape' => true),
				)
		));
		
		$this->add(array(
				'name' => 'submit',
				'attributes' => array(
						'type'  => 'submit',
						'value' => 'Go',
						'id' => 'submitbutton',
				),
		));
		
         $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
         
		$this->add(array(
				'name' => 'cancel',
				'attributes' => array(
						'type'  => 'button',
						'value' => 'Cancel',
						'id' => 'cancelbutton',
				),
		));
	}
    
    private function getKitchenScreens() {
    	$kitchen_array = array('' => 'Select the kitchen', '0'=>'All');
        //$sm = $this->getServiceLocator();
    	$sql = new QSql($this->service_locator);
    
    	$select = $sql->select();
    	$select->from('kitchen_master');
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        $results = $sql->execQuery($select);
    	// Iterate through all records.
    	foreach ($results as $res) {
    		// value is the product category name
    		$kitchen_array[$res['pk_kitchen_code']] = $res['kitchen_name'].( !empty($res['kitchen_name']) ? ' ('.$res['location'].')' : '' );
    	}// end of foreach
    	return $kitchen_array;
    }
    
    /**
     * @todo date between start_date and end_date
     * @return array
     */
    public function getPromocode(){
        
        //$sm = $this->getServiceLocator();
        $sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('promo_codes');

        $select->where("applied_on = 'plan'");
        $select->where("promo_type = 'discount'");
        $select->where("status = 1");
        $select->where->lessThanOrEqualTo("start_date",date('Y-m-d'));
        $select->where->greaterThanOrEqualTo("end_date",date('Y-m-d'));

        //$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
         $results = $sql->execQuery($select);
        $selectData = array('' => 'Select Promo Code');
        
    	foreach ($results as $res) {
    		$selectData[$res['pk_promo_code']] = $res['promo_code'];      
    	}
        
    	return $selectData;
    
        
    }
}
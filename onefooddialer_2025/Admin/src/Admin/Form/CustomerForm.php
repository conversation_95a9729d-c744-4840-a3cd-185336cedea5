<?php
/**
 * This File provides the input fields which needs to create  add & update customer
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustomerForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\Sql\QSql;

class CustomerForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
     private $service_locator;
	/**
	 * It adds an input fields which are needed to create customer login form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {
        parent::__construct('admin');
        $this->service_locator = $sm;
        $this->setAttribute('method', 'post');
        
        $this->setAttribute('enctype', 'multipart/form-data');

        $this->add(array(
            'name' => 'pk_customer_code',
            'type' => 'Zend\Form\Element\Hidden',
            'attributes' => array(
            'id' => 'pk_customer_code',
            'class'=> 'smallinput',
            ),
        ));

        $this->add(array(
            'name' => 'customer_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'id' => 'customer_name',
                'placeholder' => 'Please enter customer name ...',
                //'required' => 'required',
            	
            ),
            'options' => array(
                'label' => 'Customer Name<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
        
        
       $this->add(array(
        		'name' => 'company_name',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'company_name',
        				'placeholder' => 'Please enter company name ...',
        				'class'=> 'smallinput',
        		),
        		'options' => array(
        				'label' => 'Company Name',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));

        $this->add(array(
        		'name' => 'phone',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'phone',
        				'placeholder' => 'Please enter phone no...',
        				//'required' => 'required',
        				'class'=> 'smallinput',
        		),
        		'options' => array(
        				'label' => 'Phone No',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));

        $this->add(array(
            'name' => 'email_address',
            'type' => 'Zend\Form\Element\Email',
            'attributes' => array(
                'id' => 'email_address',
                'placeholder' => 'Email Address...',
              //  'required' => 'required',
            	
            ),
            'options' => array(
                'label' => 'Email',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

      $this->add(array(
				'name' => 'location_code[]',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'location_code',
						//  'required' => 'required',
						'class' => 'form-control locationcodecopycls locationcodes',
				),
				'options' => array(
						'label' => 'Default Location<span class="red">*</span>',
						'value_options' => $this->getLocation(),
						'label_options' => array('disable_html_escape' => true),
				),
		));
      
    
      
        $this->add(array(
        		'name' => 'delivery_person[]',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        				'id' => 'delivery_person',
        				//  'required' => 'required',
        				'class'=> 'delivery_person_cls locationdeliverycopycls locationdeliverycls',
        		),
        		'options' => array(
        				'label' => 'Lunch Delivery Person',
        				'value_options' => $this->getDeliveryPerson(),
        		),
        ));
        
        $this->add(array(
        		'name' => 'location_address[]',
        		'type' => 'Zend\Form\Element\Textarea',
        		'attributes' => array(
        				'class' => 'form-control clstextareaaddress locationaddrcopycls',
        		),
        		'options' => array(
                    'label' => 'Address<span class="red">*</span>',
                    'label_options' => array('disable_html_escape' => true),
        		),
        ));
        	
        
        $this->add(array(
        		'name'=>'city',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes'=>array(
        				'id'=>'city',
        				'class'=>'selectpicker small',
        		),
        		'options'=>array(
        				'label'=>'City<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        				'value_options' => $this->getCities()
        		)
        ));
        
       
        
       
        
       $this->add(array(
        		'name' => 'dabbawala_code[]',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'dabbawala_code',
        				'placeholder' => 'Please enter dibbawala code ...',
        				'class'=> 'smallinput',
        		),
        		'options' => array(
        				'label' => 'Dibbawala Code',
        		),
        )); 

       $this->add(array(
            'name' => 'group_code',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'group_code',
            	'class'=> 'smallinput',

            ),
            'options' => array(
                'label' => 'Group Name',
                'value_options' => $this->getGroup(),
            ),
        ));

        $this->add(array(
            'name' => 'registered_on',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'id' => 'datepicker',
                'placeholder' => 'Type something...',
              //  'required' => 'required',
                //'min' => '1970-01-01',
               // 'max' => 2020-02-01,
               // 'step' => '1',
            	'class'=> 'calender smallinput',
            	'disabled'=>true
            ),
            'options' => array(
                'label' => 'Registered On',
            ),
        ));

        $this->add(array(
        		'name' => 'food_preference',
        		'type' => 'Zend\Form\Element\Textarea',
        		'attributes' => array(
        				'id' => 'food_preference',
        				'placeholder' => 'Please enter food preference ...',
        				//	'required' => 'required',
        				'class'=> 'smallinput',
        		),
        		'options' => array(
        				'label' => 'Food Preference',
        		),
        ));
        
        $this->add(array(
            'name' => 'status',
            'type' => 'Zend\Form\Element\Radio',
            'attributes' => array(
                'id' => 'status',
             //   'required' => 'required',
            ),
            'options' => array(
                'label' => 'Status',
            		'value_options' => array(
            				array(
            						'value' => '1',
            						'label' => 'Active',
            						'selected' => true,
            				),
            				array(
            						'value' => '0',
            						'label' => 'Inactive',
            				),
            		),
            ),
        ));

        $this->add(array(
            'name' => 'isguest',
            'type' => 'Zend\Form\Element\Radio',
            'attributes' => array(
                'id' => 'isguest',
             //   'required' => 'required',
            ),
            'options' => array(
                'label' => 'Guest User',
                    'value_options' => array(
                            array(
                                    'value' => 'Y',
                                    'label' => 'Yes',
                                    'selected' => true,
                            ),
                            array(
                                    'value' => 'N',
                                    'label' => 'No',
                            ),
                    ),
            ),
        ));        

        $this->add(array(
        		'name' => 'submit',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Save',
        				'id' => 'submitbutton',
        				'class'=>'button left tiny left5 dark-greenBg'
        		),
        		
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',
        				'class'=>'button left tiny left5 redBg'

        		),
        ));

        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/customer',
        		),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
        
        $this->add(array(
        		'name' => 'cust_name',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'cust_name',
        				'placeholder' => 'Please enter customer name ...',
        				//'required' => 'required',
        				 
        		),
        		'options' => array(
        				'label' => 'Customer Name<span class="red">*</span>',
        		),
        ));
        
        
        $this->add(array(
        		'name' => 'cash_amt',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'cash_amt',
        				'placeholder' => 'Enter Amount',
        				//'required' => 'required',
        				'class'=> 'amtcls',
        				 
        		),
        
        ));
        
        $this->add(array(
        		'name' => 'idcpyaddress',
        		'type' => 'Zend\Form\Element\Checkbox',
        		'attributes' => array(
        				'id' => 'idcpyaddress',
        				'class' => 'icheck'
        
        		),
        		'options' => array(
        				'checked_value' => 'copy',
        				'unchecked_value' => 'donotcopy'
        		),
        ));
        
        $this->add(array(
        		'name' => 'debit_amt',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'debit_amt',
        				'placeholder' => 'Enter Amount',
        				'class'=> 'amtcls',
        				 
        		),
        
        ));
        
        $this->add(array(
        		'name' => 'lock_amt',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'lock_amt',
        				'placeholder' => 'Enter Amount',
        				'class'=> 'amtcls',
        				 
        		),
        
        ));
        
        $this->add(array(
        		'name' => 'cheque_no',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'cheque_no',
        				'placeholder' => 'Enter Cheque No',
        				 
        		),
        
        ));
        
        $this->add(array(
        		'name' => 'cheque_amt',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'cheque_amt',
        				'placeholder' => 'Enter Cheque Amount',
        				'class'=> 'amtcls',
        				 
        		),
        
        ));
        
        
        $this->add(array(
        		'name' => 'bank_name',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'bank_name',
        				'placeholder' => 'Enter Bank Name',
        				//'required' => 'required',
        				 
        		),
        
        ));
        
        
        $this->add(array(
        		'name' => 'trans_id',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'trans_id',
        				'placeholder' => 'Enter Transaction id',
        				//'required' => 'required',
        				 
        		),
        
        ));
        
        $this->add(array(
        		'name' => 'neft_amt',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'neft_amt',
        				'placeholder' => 'Enter Amount',
        				//'required' => 'required',
        				'class'=> 'amtcls',
        				 
        		),
        
        ));
        
        
        $this->add(array(
        		'name' => 'neft_date',
        		'type' => 'Zend\Form\Element\Date',
        		'attributes' => array(
        				'id' => 'datepicker',
        				'placeholder' => 'YYYY-MM-DD',
        				//  'required' => 'required',
        				//'min' => '1970-01-01',
        				//            'max' => 2014-2-1,
        				'readonly' =>true,
        				'step' => '1',
        				'class'=> 'smallinput calender',
        		),
        ));
        
        $this->add(array(
        		'name'=>'thirdparty',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        				//'required' => 'required',
        				//	'value' => $customer['city'],
        				'id' => 'thirdparty',
        		),
        		'options'=>array(
        				'label'=>'Third Party',
        				'value_options' => $this->getThirdParty(),
        				'label_attributes' => array(
        						'class' => 'inline right'
        				),
        		)
        ));
        $this->add(array(
        		'name' => 'delivery_person',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        				'id' => 'delivery_person',
        				//  'required' => 'required',
        				'class'=> 'smallinput',
        		),
        		'options' => array(
        				'label' => 'Delivery Person',
        				'value_options' => $this->getDeliveryPerson(),
        		),
        ));
       
        
    }
    /**
     * To get list of Delivery Persons
     * @method getDeliveryPerson($param)
     * @return array $selectData
     */
    public function getDeliveryPerson($param=false) {
        
        $sql = new QSql($this->service_locator);
    	$select = $sql->select();
        $select->columns(array('pk_user_code','first_name','last_name'));
        $select->join('roles',"users.role_id=roles.pk_role_id",array('role_name'),$select::JOIN_LEFT);
        $select->join('third_party',"third_party.third_party_id=users.third_party_id",array('thirdparty_system'), $select::JOIN_LEFT );
        
        $select->from('users');
        
        $select->where->in('roles.role_name' , array('Delivery Person', 'Third-Party Delivery'),$select::JOIN_LEFT);
        
        $select->where("IF(roles.role_name  = 'Third-Party Delivery', third_party.thirdparty_system != 'other', 1=1 )" );
        
//        echo $select->getSqlString();die;
        //$statement = $sql->prepareStatementForSqlObject($select);            
    	//$resultSet = $statement->execute();
        
        $resultSet = $sql->execQuery($select);
        
        $selectData[""]="Select Delivery Person";
        foreach ($resultSet as $key => $res) {
            $selectData[$res['pk_user_code']] = $res['first_name']." ".$res['last_name'];
        }
        return $selectData;
    }
    
	/**
	 * To get the list of delivery locations
	 * @method getLocation()
	 * @return array $selectData
	 */
    public function getLocation()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('delivery_locations');
    	//$select->where('isDefault',1);
    	//$select->where('status',1);
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
    	$selectData[""]="SELECT LOCATION";
    	foreach ($results as $res) {
    		$selectData[$res['pk_location_code'].'#'.$res['location']] = $res['location'];
    	}
    	return $selectData;

    }
	/**
	 * To get the list of customer groups
	 *
	 * @return Array
	 */
    public function getGroup()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('groups');
    	$select->where(array('STATUS'=>"1"));
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        $results = $sql->execQuery($select);
    	$selectData["0"]="Select Group";
    	foreach ($results as $res) {
    		$selectData[$res['group_code'].'#'.$res['group_name']] = $res['group_name'];
    	}
    	return $selectData;

    }
    
    /**
     * To get list of cities
     * @method getCities()
     * @return array $selectData
     */
    public function getCities()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('city');
    	$select->where('status',1);
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
    	$selectData[''] = "select your city";
    	foreach ($results as $res) {
    		$selectData[$res['pk_city_id'].'#'.$res['city']] = $res['city'];
    	}
    
    	return $selectData;
    
    }
    
    /**
     * To get third party information
     * @method getThirdParty()
     * @return array $selectData
     */
    public function getThirdParty()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('third_party');
    	$select->where('status',1);
    
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
    	//		echo'<pre>';print_r($select->getSqlString());die;
        
        $results = $sql->execQuery($select);
    	$selectData[""] = "select";
    
    	foreach ($results as $res) {
    		$selectData[$res['third_party_id']] = $res['name'];
    	}
    	//echo '<pre>';print_r($selectData);exit();
    	return $selectData;
    }
}
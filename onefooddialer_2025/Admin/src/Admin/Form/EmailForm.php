<?php
/**
 * This File provides the input fields which needs to create  add & update customer
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustomerForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Lib\QuickServe\Db\Sql\QSql;

class EmailForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
     private $service_locator;
	/**
	 * It adds an input fields which are needed to create customer login form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {
        parent::__construct('admin');
        $this->service_locator = $sm;
        $this->setAttribute('method', 'post');

        $this->add(array(
            'name' => 'pk_set_id',
            'type' => 'Zend\Form\Element\Hidden',
            'attributes' => array(
            'id' => 'pk_set_id',
            'class'=> 'smallinput',
            ),
        ));
        $this->add(array(
        		'name' => 'pk_template_id',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes' => array(
        				'id' => 'pk_template_id',
        				'class'=> 'smallinput',
        		),
        		'options' => array(
        				'label' => 'Template Name<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        

        $this->add(array(
            'name' => 'name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'id' => 'name',
                'placeholder' => 'Please enter Name ...',
                //'required' => 'required',
            	
            ),
            'options' => array(
                'label' => 'Template Name<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
        
        $this->add(array(
        		'name' => 'template_key',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'template_key',
        				'readonly' => TRUE,
        			//'placeholder' => 'Please enter Name ...',
        				//'required' => 'required',
        				 
        		),
        		'options' => array(
        				'label' => 'Title<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'name' => 'purpose',
        		'type' => 'Zend\Form\Element\Textarea',
        		'attributes' => array(
        				'id' => 'purpose',
        				'placeholder' => 'Please enter Purpose ...',
        			//	'required' => 'required',
        				'class'=> 'smallinput',
        		),
        		'options' => array(
        				'label' => 'Purpose<span class="red">*',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'name' => 'subject',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'subject',
        				'placeholder' => 'Please enter Subject ...',
        				//	'required' => 'required',
        				'class'=> 'smallinput',
        		),
        		'options' => array(
        				'label' => 'Subject<span class="red">*',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'name' => 'body',
        		'type' => 'Zend\Form\Element\Textarea',
        		'attributes' => array(
        				'id' => 'body',
        				'placeholder' => 'Please enter Body ...',
        				//	'required' => 'required',
        				'class'=> 'jqte-test',
        				//'class'=> 'jqte-test',
        				
        		),
        		'options' => array(
        				'label' => 'Message Body<span class="red">*',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        
        $this->add(array(
        		'name' => 'pokemonRed',
        		'type' => 'Zend\Form\Element\Checkbox',
        		'attributes' => array(
        				'id' => 'pokemonRed',
        				
        		),
        		'options' => array(
        				'label' => 'Make it Default',
        				'checked_value' => '1',
        				'unchecked_value' => '0'
        		),
        ));
        
        $this->add(array(
            'name' => 'status',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'status',
             //   'required' => 'required',
            ),
            'options' => array(
                'label' => 'Status',
                'value_options' => array(
                    '1' => 'Active',
                    '0' => 'Deactive',
                ),
            ),
        ));
        
        $this->add(array(
        		'name' => 'copy_template',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        				'id' => 'copy_template',
        				'class'=> 'selectpicker small',
        
        		),
        		'options' => array(
        				'label' => 'Copy Template',
        				'value_options' => $this->getTemplate(),
        		),
        ));
        
       /*  $this->add(array(
        		'name' => 'type',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'type',
        				//'readonly' => TRUE,
        				//'placeholder' => 'Please enter Name ...',
        				//'required' => 'required',
        				 
        		),
        		'options' => array(
        				'label' => 'Type<span class="red">*</span>',
        		),
        ));
         */
        $this->add(array(
        		'name' => 'type',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        				'id' => 'type',
        				//   'required' => 'required',
        		),
        		'options' => array(
        				'label' => 'Type',
        				'value_options' => array(
        						'html' => 'HTML',
        						'text' => 'Text',
        				),
        		),
        ));
        
        $this->add(array(
        		'name' => 'csrf',
        		'type' => 'Zend\Form\Element\Csrf',
        ));
        
        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/emailtemplate',
        		),
        ));
        
        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',
        				'class'=>'button left tiny left5 redBg'
        
        		),
        ));
        
    }
    /**
     * To get the list of customer groups
     *
     * @return Array
     */
    public function getTemplate()
    {
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('email_set');
    	$select->where(array('status'=>"1"));
    	//$statement = $sql->prepareStatementForSqlObject($select);
    	//$results = $statement->execute();
   //	echo'<pre>';print_r($select->getSqlString());die;
        $results = $sql->execQuery($select);
    	$selectData = array();
    	$selectData['default']="Select Set";
    	foreach ($results as $res) {
    		
    		$selectData[$res['pk_set_id']] = $res['set_name'];
    	}
    	//echo '<pre>';print_r($selectData);exit();
    	return $selectData;
    
    }


}
<?php
/**
 * This File provides the input fields which needs to create add & update City
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CityForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Captcha;
use Zend\Session\Container;
use Zend\Form\Form;
use Zend\Form\Element;
use Zend\Db\Adapter\Adapter;
use Lib\Utility;
use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\CommonConfig as QSConfig;

class TimeslotForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    //private $service_locator;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {
        parent::__construct('admin');
        $this->service_locator = $sm;
        
        $utility = Utility::getInstance ($sm);
        $setting = new Container('setting');
        $setting = $setting->setting;

        $this->setAttribute('method', 'post');

        $this->add(array(
                'name' => 'fk_kitchen_code',
                'type' => 'Zend\Form\Element\Select',
                'attributes' => array(
                    'class' => 'smallinput',
                    'id' => 'fk_kitchen_code',
                    'required' => '',
                    'value' => '',
                ),
                'options' => array(
                'label' => 'Kitchen',
                'label_options' => array('disable_html_escape' => true),
                'value_options' => $this->getKitchenScreens()
            ),
        )); 
        
        $this->add(array(
            'name' => 'menu_type',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'menu_type',
                'required' => '',
                'value' => '',
            ),
            'options' => array(
                'label' => 'Menu Type',
                'value_options' => array(
                    '' => 'Select Menu'
                ),
                'label_options' => array('disable_html_escape' => true),
                'disable_inarray_validator' => true
            ),
        ));


        $this->add(array(
            'name' => 'starttime',
            'type' => 'text',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'starttime',
                'required' => 'required',
                //'data-time' => '',
                'format' => '%H:%M:%S', //%H:%M:%S
                'min' => '00:00:00',
                'max' => '23:59:59',
                'step' => '60',  
                'placeholder' => 'Enter Time in 24hrs fromat',              
            ),
            'options' => array(
                'label' => 'Start Time<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
                //'format' => 'Y-m-d\TH:i'
            ),
        ));

        $this->add(array(
            'name' => 'endtime',
            'type' => 'text',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'endtime',
                'required' => 'required',
                //'data-time' => '',
                //'data-value-format' => '%H:%M',
                'readonly' => true,
                //'min' => '00:00',
                //'max' => '23:59',
                //'step' => '60',                
            ),
            'options' => array(
                'label' => 'End Time<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
                //'format' => 'Y-m-d\TH:i'
            ),
        )); 

        $this->add(array(
            'name' => 'interval',
            'type' => 'Zend\Form\Element\Number',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'interval',
                'required' => 'required',
                'min' => '0',
                'max' => '60',
                'step' => '1',                
            ),
            'options' => array(
                'label' => 'Slot Interval(min)<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
                //'format' => 'Y-m-d\TH:i'
            ),
        ));

        $this->add(array(
            'name' => 'span',
            'type' => 'Zend\Form\Element\Number',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'interval',
                'required' => 'required',
                'min' => '0',
                'max' => '60',
                'step' => '1',                
            ),
            'options' => array(
                'label' => 'Slot Span(min)<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
                //'format' => 'Y-m-d\TH:i'
            ),
        ));          





        $this->add(array(
                'name' => 'days',
                'type' => 'Zend\Form\Element\Select',
                'attributes' => array(
                    'class' => 'smallinput',
                    'id' => 'days',
                    'required' => '',
                    'value' => '',
                ),
                'options' => array(
                'label' => 'Days',
                'label_options' => array('disable_html_escape' => true),
                'value_options' => $this->getWorkingDays()
            ),
        ));                   

/*        $this->add(array(
            'name' => 'status',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                    'id' => 'status',
                    'value' => '0',
            ),
            'options' => array(
                'label' => 'Status',
                'value_options' => array(
                    '1' => 'Active',
                    '0' => 'Inactive',
                ),
            ),
		));*/

        $this->add(array(
            'name' => 'submit',
            'attributes' => array(
                'type'  => 'submit',
                'value' => 'Go',
                'id' => 'submitbutton',
            ),
        ));

        $this->add(array(
            'name' => 'cancel',
            'attributes' => array(
                'type'  => 'button',
                'value' => 'Cancel',
                'id' => 'cancelbutton',
            ),
        ));

        $this->add(array(
            'name'=>'backurl',
            'type' => 'Zend\Form\Element\Hidden',
            'attributes'=>array(
                'id'=>'backurl',
                'value'=>'/timeslot',
            ),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));                
    }
    
    public function getCities()
    {
		$sql = new QSql($this->service_locator);
    	$select = $sql->select();
		$select->from('city');
		$select->columns(array('pk_city_id','city'));
		$select->where(array('status'=>'1'));

        $cities = $sql->execQuery($select);
    	$returnData = array();
    	
    	foreach($cities as $city){
    		$returnData[$city['pk_city_id']] = $city['city'];
    	}
    	return $returnData;
    }

    /*
    private function getMenuTypes(){
        $libConfig = QSConfig::getInstance($this->service_locator);
        $setting = $libConfig->getSettings();
        $menu_type = $setting['MENU_TYPE'];
        
        $arr = array('' => 'Select menu type', '0' => 'All');
        
        foreach( $menu_type as $k => $v ) {
            $arr[$v] = ucfirst($v);
        }
                
        return $arr;
    }
    */ 

    private function getKitchenScreens() {
        //$kitchen_array = array('' => 'Select Kitchen', '0' => 'All');
        $kitchen_array = array('' => 'Select Kitchen');
        $sql = new QSql($this->service_locator);

        $select = $sql->select();
        $select->from('kitchen_master');
        $results = $sql->execQuery($select);
        foreach ($results as $res) {
            $kitchen_array[$res['pk_kitchen_code']] = $res['kitchen_name'].( !empty($res['kitchen_name']) ? ' ('.$res['location'].')' : '' );
        }

        return $kitchen_array;
    }   

    private function getWorkingDays() {

        $libCommon = QSConfig::getInstance($this->service_locator);

        $weekOff = $libCommon->fetchHolidaysList('weekoff');
        $result_array = $libCommon->getWorkingDays($weekOff); 

        $workingdays =array('' => "Select Days", '7' => 'All');

        foreach($result_array['unique'] as $key => $value) {
            $workingdays[$key] = $value;
        }

        return $workingdays;
    }                
}
<?php
/**
 * This File provides the input fields which needs to create add & update location
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: KitchenForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Zend\Session\Container;

use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\Utility;

class KitchenForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	
	private $service_locator;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {
        parent::__construct('kitchen');
        
        $adapt = $sm->get('Write_Adapter');
        $this->adapter = $adapt;
        $this->service_locator = $sm;
        
        $utility = Utility::getInstance ();
        $setting = new Container('setting');
        $setting = $setting->setting;
        $currencySymbol = $utility->getCurrencySymbol($setting['GLOBAL_CURRENCY'],$setting['GLOBAL_LOCALE']);

        $this->setAttribute('method', 'post');

        $this->add(array(
        		'name' => 'pk_kitchen_code',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes' => array(
        			'id' => 'pk_kitchen_code',
        		),
        		'options' => array(
      		),
        ));

        $this->add(array(
            'name' => 'kitchen_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            	'class'=> 'smallinput',
                'id' => 'kitchen_name',
                'placeholder' => 'Enter kitchen name',
                'required' => 'required',
                'autofocus' => true
            ),
            'options' => array(
                'label' => 'Kitchen Name <span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
        	'name' => 'kitchen_alias',
        	'type' => 'Zend\Form\Element\Text',
        	'attributes' => array(
        		'class'=> 'smallinput',
        		'id' => 'kitchen_alias',
        		'placeholder' => 'Enter kitchen alias',
        		'required' => 'required',
        		'autofocus' => true
        	),
        	'options' => array(
        		'label' => 'Kitchen Alias <span class="red">*</span>',
        		'label_options' => array('disable_html_escape' => true),
        	),
        ));
        
        $cities = $this->getCities();
        
        $this->add(array(
        	'name' => 'city_id',
        	'type' => 'Zend\Form\Element\Select',
        	'attributes' => array(
        		'class'=> 'smallinput chosen-select',
        		'id' => 'city_id',
        		'required' => 'required',
        	),
        	'options' => array(
        		'label' => 'Select City <span class="red">*</span>',
        		'value_options' => $cities,
        		'label_options' => array('disable_html_escape' => true),
        	),
        ));
        
        //$locations = $this->getLocations();
        
        $this->add(array(
        	'name' => 'location_id',
        	'type' => 'Zend\Form\Element\Select',
        	'attributes' => array(
        		'class'=> 'smallinput chosen-select',
        		'id' => 'location_id',
        		'value' => 'product',
        	),
        	'options' => array(
        		'label' => 'Select Location',
        		'value_options' => array(),
        		'label_options' => array('disable_html_escape' => true),
        		'disable_inarray_validator' => true
        	),
        ));

        $this->add(array(
        		'name' => 'kitchen_address',
        		'type' => 'Zend\Form\Element\Textarea',
        		'attributes' => array(
        				'class' => 'form-control clstextareaaddress locationaddrcopycls',
        				'placeholder' => 'Address Here...',
                                        'required' => 'required',
                                        'autofocus' => true
        		),
        		'options' => array(
        				'label' => 'Address<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'type' => 'Zend\Form\Element\Radio',
        		'name' => 'base_kitchen',
        		'attributes' => array(
        			'class'=> 'smallinput',
        			'id' => 'type',
        			'required' => 'required',
        			'value' => 'product',
        		),
        		'options' => array(
        			'label' => 'Base kitchen ?',
        			'label_attributes' => array(
        				'class' => 'inline right',
        			),
        			'value_options' => array(
        				'0' => 'No',
        				'1' => 'Yes',
        			),
        		),
        ));

        
        $this->add(array(
        		'name' => 'CUSTOMER_PAYMENT_MODE',
        		'type' => 'Zend\Form\Element\MultiCheckbox',
        		'options' => array(
        				'label' => 'Payment Modes',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'attributes' => array(
        						'id' => 'CUSTOMER_PAYMENT_MODE',
        				),
        				'label_options' => array('disable_html_escape' => true),
        				'disable_inarray_validator' => true,
        		),
        ));
        $options=array(
        						array(
        								'value' => 'neft',
        								'label' => 'NEFT',
        								'attributes' => array(
        										'class'=>"neftcheck" ,
        										'data-close'=>".onlinerow"
        								),
        								'label_attributes' => array(
        										'class'=>"left mr25 mtm4"
        								),
        						),
        						array(
        								'value' => 'cheque',
        								'label' => 'Cheque',
        								'attributes' => array(
        										'class'=>"chequecheck" ,
        										'data-close'=>".onlinerow"
        								),
        								'label_attributes' => array(
        										'class'=>"left mr25 mtm4"
        								),
        						),

        				);
        
        if($utility->checksubscription('customer_wallet','allowed')){
            $wallet = array(
                'value' => 'wallet',
                'label' => 'Wallet',
                'attributes' => array(
                        'class'=>"walletcheck" ,
                        'data-open'=>".onlinerow"
                ),
                'label_attributes' => array(
                        'class'  => 'inline left mr5',
                        'style'=>'line-height: 0.9em;'
                ),
            );
            
            array_push($options,$wallet);
        }
        
        if($utility->checksubscription('payment_online','allowed')){
            $online =  array(
                        'value' => 'online',
                        'label' => 'Pay Online',
                        'attributes' => array(
                                'class'=>"onlinecheck" ,
                                'data-open'=>".onlinerow"
                        ),
                        'label_attributes' => array(
                                'class'  => 'inline left mr5',
                                'style'=>'line-height: 0.9em;'
                        ),
                    );
            
            array_push($options,$online);
        }
        if($utility->checksubscription('payment_cod','allowed')){
            $cash = array(
                        'value' => 'cash',
                        'label' => 'Cash',
                        'attributes' => array(
                                'class'=>"cashcheck" ,
                                'data-close'=>".onlinerow"
                        ),
                        'label_attributes' => array(
                                'class'  => 'inline left mr5',
                                'style'=>'line-height: 0.9em;'
                        ),
                    );
            
            array_push($options,$cash);
        }
        
        $this->get('CUSTOMER_PAYMENT_MODE')->setValueOptions($options);
        
        $this->add(array(
        		'name' => 'MIN_ORDER_PRICE',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'MIN_ORDER_PRICE',
        				'placeholder' => 'Enter amount',
        				//'required' => 'required',
        				'autofocus' => true
        		),
        		'options' => array(
        				'label' => 'Minimum order amount ('.$currencySymbol.')',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'name' => 'MAX_ORDER_PRICE',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'MAX_ORDER_PRICE',
        				'placeholder' => 'Enter amount',
        				//'required' => 'required',
        				'autofocus' => true
        		),
        		'options' => array(
        				'label' => 'Maximum amount for COD ('.$currencySymbol.')',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $menu_type = $this->getMenuType();
        
        $this->add(array(
        		'name' => 'MENU_TYPE',
        		'type' => 'Zend\Form\Element\MultiCheckbox',
        		'options' => array(
        				'label' => 'Food menu',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'attributes' => array(
        						'id' => 'MENU_TYPE',
        				),
        				'value_options' => 
                            	$menu_type
        				
        				/* 'label_options' => array('disable_html_escape' => true),
        				'disable_inarray_validator' => true, */
        		),
        ));
        
        $this->add(array(
        		'name' => 'ORDER_NOTIFICATION_EMAIL',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'ORDER_NOTIFICATION_EMAIL',
        				'placeholder' => 'Enter Order Notification Email',
        				//'required' => 'required',
        				'autofocus' => true
        		),
        		'options' => array(
        				'label' => 'Order notification email',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));

        $this->add(array(
        		'name' => 'submit',
        		'attributes' => array(
        			'type'  => 'submit',
        			'value' => 'Go',
        			'id' => 'submitbutton',
        		),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        			'type'  => 'button',
        			'value' => 'Cancel',
        			'id' => 'cancelbutton',

        		),
        ));

        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        			'id'=>'backurl',
        			'value'=>'/kitchen-master',
        		),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
    }

    /**
     * The function `getDeliveryLocations` is used to fetch the types of delivery locations.
     * 
     * @method getDeliveryLocations
     * @access private
     * @return array LocationTable::$locations
     */
	private function getLocations($city=null) {
		
		$libCommon = QSCommon::getInstance($this->service_locator);
		$locations = $libCommon->getLocations($city);
		
		$arrLocation = array();
		
		foreach($locations as $location){
			$lid = $location['pk_location_code']."#".$location['location']."#".$location['pk_city_id'];
			$arrLocation[$lid] = $location['location'];
		}
		return $arrLocation;
		
    }
    
    private function getCities() {

    	$libCommon = QSCommon::getInstance($this->service_locator);
    	
    	$cities = $libCommon->getCity();
    	
    	$arrCities = array(""=>"-- Select City --");
    	
    	foreach($cities as $city){
    		$arrCities[$city['pk_city_id']] = $city['city'];
    	}
    	return $arrCities;
    
    }

   
    private function getMenuType(){
        $session_setting = new Container("setting");
        $setting = $session_setting->setting;
        
        foreach($setting['MENU_TYPE'] as $menu_type) {
            
            $data[] = array(
                
                'value' => $menu_type,
                'label' => ucfirst($menu_type),
                'attributes' => array(
                        'class'=> $menu_type."check" ,
                        'data-close'=>".onlinerow"
                ),
                'label_attributes' => array(
                        'class'=>"left mr5"
                ),
            );   
        }
        
        return $data;
    }
}
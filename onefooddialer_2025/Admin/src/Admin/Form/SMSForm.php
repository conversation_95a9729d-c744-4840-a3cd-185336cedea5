<?php

/**
 * This File provides the input fields which needs to create  add & update customer
*
* PHP versions 5.4
*
* Project name FoodDialer
* @version 1.1: CustomerForm.php 2014-06-19 $
* @package Admin/Form
* @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
* @license Copyright (C) 2014 � Futurescape Technology
* @license http://www.futurescapetech.com/copyleft/gpl.html
* @link http://www.futurescapetech.com
* @category <Form Admin>
* <AUTHOR> <<EMAIL>>
* @since File available since Release 1.1.0
*/
namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

class SMSForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create customer login form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
	public function __construct($sm)
	{
		parent::__construct('admin');
		$this->adapter = $adapter;
		$this->setAttribute('method', 'post');

		$this->add(array(
				'name' => 'pk_set_id',
				'type' => 'Zend\Form\Element\Hidden',
				'attributes' => array(
						'id' => 'pk_set_id',
						'class'=> 'smallinput',
				),
		));
		
		$this->add(array(
				'name' => 'sms_template_id',
				'type' => 'Zend\Form\Element\Hidden',
				'attributes' => array(
						'id' => 'sms_template_id',
						'class'=> 'smallinput',
				),
		));
		
		

		$this->add(array(
				'name' => 'name',
				'type' => 'Zend\Form\Element\Text',
				'attributes' => array(
						'id' => 'name',
						'placeholder' => 'Please enter Name ...',
						//'required' => 'required',
						 
				),
				'options' => array(
						'label' => 'Set Name<span class="red">*</span>',
						'label_options' => array('disable_html_escape' => true),
				),
		));

		
		$this->add(array(
				'name' => 'default',
				'type' => 'Zend\Form\Element\Checkbox',
				'attributes' => array(
						'id' => 'default',
		
				),
				'options' => array(
						'label' => 'Make it Default',
						'checked_value' => '1',
						'unchecked_value' => '0'
				),
		));

		$this->add(array(
				'name' => 'character',
				'type' => 'Zend\Form\Element\Text',
				'attributes' => array(
						'id' => 'character',
						'placeholder' => 'Character Limit ...',
						//	'required' => 'required',
						'class'=> 'smallinput',
				),
				'options' => array(
						'label' => 'Character Limit<span class="red">*',
						'label_options' => array('disable_html_escape' => true),
				),
		));
		
		
		$this->add(array(
				'name' => 'is_approved',
				'type' => 'Zend\Form\Element\Text',
				'attributes' => array(
						'id' => 'is_approved',
						'placeholder' => 'Status ...',
						//	'required' => 'required',
						'class'=> 'smallinput',
				),
				'options' => array(
						'label' => 'Status',
				),
		));
		
		$this->add(array(
				'name' => 'purpose',
				'type' => 'Zend\Form\Element\Text',
				'attributes' => array(
						'id' => 'purpose',
						'placeholder' => 'Please enter Purpose ...',
						//	'required' => 'required',
						'class'=> 'smallinput',
				),
				'options' => array(
						'label' => 'Purpose<span class="red">*',
						'label_options' => array('disable_html_escape' => true),
				),
		));
		
		
		$this->add(array(
				'name' => 'description',
				'type' => 'Zend\Form\Element\Textarea',
				'attributes' => array(
						'id' => 'description',
						'placeholder' => 'Please enter your message here ...',
						//	'required' => 'required',
						'class'=> 'smallinput',
				),
				'options' => array(
						'label' => 'Description<span class="red">*',
						'label_options' => array('disable_html_escape' => true),
				),
		));
		
		$this->add(array(
				'name' => 'csrf',
				'type' => 'Zend\Form\Element\Csrf',
		));
		
		$this->add(array(
				'name'=>'backurl',
				'type' => 'Zend\Form\Element\Hidden',
				'attributes'=>array(
						'id'=>'backurl',
						'value'=>'/smstemplate',
				),
		));
		
		$this->add(array(
				'name' => 'cancel',
				'attributes' => array(
						'type'  => 'submit',
						'value' => 'Cancel',
						'id' => 'cancelbutton',
						'class'=>'button left tiny left5 redBg'
		
				),
		));
		
	}
}
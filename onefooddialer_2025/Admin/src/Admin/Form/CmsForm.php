<?php
/**
 * This File provides the input fields which needs to create add & update cms
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1:CmsForm.php 2017-04-17 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Db\Sql\Sql;
use Zend\Session\Container;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\Utility;

class CmsForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {
        parent::__construct('admin');
        $this->service_locator = $sm;
        $utility = Utility::getInstance ();
        $setting = new Container('setting');
        $setting = $setting->setting;

        $this->setAttribute('method', 'post');

        $this->add(array(
        		'name' => 'cms_id',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes' => array(
        				'id' => 'cms_id',
        		),
        		'options' => array(
      		),
        ));

        $this->add(array(
            'name' => 'title',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            		'class'=> 'smallinput',
                    'id' => 'title',
                    'placeholder' => 'Enter Title',
                // 'required' => 'required',
            ),
            'options' => array(
                'label' => 'Title<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'url_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            	'class'=> 'smallinput',
                'id' => 'url_name',
                'placeholder' => 'Enter URI Name',
            ),
            'options' => array(
                'label' => 'URI Name<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'meta_title',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            	'class'=> 'smallinput',
                'id' => 'meta_title',
            	'placeholder' => 'Enter Meta Title',
              //  'required' => 'required',
            ),
            'options' => array(
            		'label' => 'Meta Title',
            		'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'meta_keyword',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'meta_keyword',
                'placeholder' => 'Enter Meta Keyword',
              //  'required' => 'required',
            ),
            'options' => array(
                    'label' => 'Meta Keyword',
                    'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'meta_description',
            'type' => 'Zend\Form\Element\Textarea',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'meta_description',
                'placeholder' => 'Enter Meta Description',
              //  'required' => 'required',
            ),
            'options' => array(
                    'label' => 'Meta Description',
                    'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'content',
            'type' => 'Zend\Form\Element\Textarea',
            'attributes' => array(
                'class'=> 'jqte-test',
                'id' => 'content',
                'placeholder' => 'Enter Content',
              //  'required' => 'required',
            ),
            'options' => array(
                    'label' => 'Content',
                    'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'ga_code',
            'type' => 'Zend\Form\Element\Textarea',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'ga_code',
                'placeholder' => 'Enter Google Analytics Code',
              //  'required' => 'required',
            ),
            'options' => array(
                    'label' => 'Google Analytics Code',
                    'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'sequence',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class'=> 'smallinput',
                'id' => 'sequence',
                'placeholder' => 'Enter sequence',
              //  'required' => 'required',
            ),
            'options' => array(
                'label' => 'Sequence',
                'label_options' => array('disable_html_escape' => true),
            ),
        ));

        
        $this->add(array(
				'name' => 'status',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'status',
				),
				'options' => array(
						'label' => 'Status',
						'value_options' => array(
								'1' => 'Active',
								'0' => 'Inactive',
						),
				),
		));

        $this->add(array(
            'name' => 'image_path[]',
            'type'=>'file',
            'attributes' => array(
                'class' => 'smallinput',
                'accept'=>"image/*",
            ),
            'options' => array(
                'label' => 'Image<span class="red">*</span>',
                'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
        		'name' => 'submit',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Go',
        				'id' => 'submitbutton',
        		),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'button',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',

        		),
        ));

        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/cms',
        		),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));


    }    
            
}
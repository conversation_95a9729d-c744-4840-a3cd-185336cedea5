<?php
/**
 * This File is not used for the backorderprocess
 * As BackOrder access the form library from Front Module
 * This File is not recommended & it will be deleted in future
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: orderForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 * @deprecated No longer used by internal code and not recommended.
 */
namespace Admin\Form;

use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Zend\Session\Container;

use Lib\Utility;
use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\CommonConfig as QSConfig;

class orderForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */

	public function __construct($sm)
    {
        parent::__construct('admin');
        $this->service_locator = $sm;
        $utility = Utility::getInstance ();
        $setting = new Container('setting');
        $setting = $setting->setting;

	// public function __construct($sm,$customer)
	// {
	// 	parent::__construct('admin');
	// 	$this->adapter = $adapter;
 //        $this->service_locator = $sm;
	// 	$this->setAttribute('method', 'post');
		//$this->setAttribute('id', 'login');
		//$this->setAttribute('class', 'stdform');
		/*$this->add(array(
				'name'=>'customer_name',
				'attributes'=>array(
						'type'=>'text',
						'class'=>'small',
						'readonly' => 'true'
				),
				'options'=>array(
						'label'=>'Name',
						'label_attributes' => array(
								'class'  => 'control-label'
						),
				)
		));*/
		$this->add(array(
				'name'=>'phone',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'type' => 'number',
						'class'=>'small',
						'placeholder' => 'Enter your contact number',
						'required' => 'required',
						'value' => $customer['phone']
				),
				'options'=>array(
						'label'=>'Contact Number',
						'label_attributes' => array(
								'class'  => 'control-label'
						),
				)
		));
		$this->add(array(
				'name'=>'dates',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'id'=>'altField',
						'placeholder' => 'Select dates'
				),
				'options'=>array(
						'label'=>'Contact Number',
						'label_attributes' => array(
								'class'  => 'control-label'
						),
				)
		));
		/*$this->add(array(
				'name'=>'email_address',
				'attributes'=>array(
						'type'=>'text',
						'class'=>'small'
				),
				'options'=>array(
						'label'=>'Email Id',
						'label_attributes' => array(
								'class'  => 'control-label'
						),
				)
		));*/

		$this->add(array(
				'name'=>'location_code',
				'type' => 'Zend\Form\Element\Select',
				'attributes'=>array(
						'class'=>'selectpicker small',
						'required' => 'required',
						'value' => $customer['location_code'],
				),
				'options'=>array(
						'label'=>'<span class="red-ast">*</span> Delivery Location',
						'value_options' => $this->getCities()
				)
		));
		/*$this->add(array(
				'name'=>'company_name',
				'attributes'=>array(
						'type' => 'text',
						'class'=>'small'
				),
				'options'=>array(
						'label'=>'Company Name',
						'label_attributes' => array(
								'class'  => 'control-label'
						),
				)
		));*/
		$this->add(array(
				'name'=>'ship_address',
				'type' => 'Zend\Form\Element\Textarea',
				'attributes'=>array(
						'class'=>'selectpicker small',
						'placeholder' => 'Enter your shipping address',
						'required' => 'required',
						'style' => 'margin-bottom:10px',
						'value' => $customer['customer_Address']
				),
				'options'=>array(
						'label'=>'<span class="red-ast">*</span> Address'
				)
		));
		$this->add(array(
				'name'=>'promo_code',
				'attributes'=>array(
						'type' => 'text',
						'placeholder' => 'Enter promo code',
						'class'=>'small'
				),
				'options'=>array(
						'label'=>'Promo Code',
						'label_attributes' => array(
								'class'  => 'control-label'
						),
				)
		));

		/* Past order delivery*/

		$this->add(array(
        		'name' => 'past_date',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'past_date',
        				'placeholder' => 'Select Date',
        				'autofocus' => true
        		),
        		'options' => array(
        				'label' => 'Select Date <span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));

		$this->add(array(
       		'name' => 'menu',
       		'type' => 'Zend\Form\Element\Select',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'id' => 'menu',
       				//	'required' => 'required',
       				'value' => '',
       		),
       		'options' => array(
       				'label' => 'Menu<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       				'value_options' => $this->getMenuTypes()
       		),
       ));

		$this->add(array(
       		'name' => 'pk_kitchen_code',
       		'type' => 'Zend\Form\Element\Select',
       		'attributes' => array(
       				'class' => 'smallinput',
       				'id' => 'pk_kitchen_code',
       				//	'required' => 'required',
       				'value' => '',
       		),
       		'options' => array(
       				'label' => 'Kitchen<span class="red">*</span>',
       				'label_options' => array('disable_html_escape' => true),
       				'value_options' => $this->getKitchenScreens()
       		),
       ));


		/* Past order delivery*/

        $this->add(array(
				'name'=>'Remark',
				'type' => 'Zend\Form\Element\Textarea',
				'attributes'=>array(
						'class'=>'selectpicker small',
						'placeholder' => 'Enter Remark',
						'required' => 'required',
						'style' => 'margin-bottom:10px',
						'value' => $customer['remark']
				),
				'options'=>array(
						'label'=>'<span class="red-ast">*</span> Address'
				)
		));
		$this->add(array(
				'name' => 'csrf',
				'type' => 'Zend\Form\Element\Csrf',
		));
		$this->add(array(
				'name'=>'submit',
				'attributes'=>array(
						'type'=>'submit',
						'value'=>'SUBMIT',
						'class'=>'button',
				)
		));
	}
	/**
	 * To get the list of delivery locations
	 *
	 * @return array
	 */
	public function getCities()
	{
       // $sm = $this->getServiceLocator();
		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('delivery_locations');
		$select->where('status',1);
		//$select->order('price_name ASC');
		//$statement = $sql->prepareStatementForSqlObject($select);
		//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
		$selectData['default'] = "select any city";
		foreach ($results as $res) {
			$selectData[$res['pk_location_code']] = $res['location'];
		}
		//echo '<pre>';print_r($selectData);exit();
		return $selectData;

	}

	private function getKitchenScreens() {
    	$kitchen_array = array('0'=>'All');
    	$sql = new QSql($this->service_locator);
    	$select = $sql->select();
    	$select->from('kitchen_master');
        $results = $sql->execQuery($select);
    	foreach ($results as $res) {
    		$kitchen_array[$res['pk_kitchen_code']] = $res['kitchen_name'].( !empty($res['kitchen_name']) ? ' ('.$res['location'].')' : '' );
    	}
    	return $kitchen_array;
    }

    private function getMenuTypes(){
		$libConfig = QSConfig::getInstance($this->service_locator);
		$setting = $libConfig->getSettings();
		$menu_type = $setting['MENU_TYPE'];
		
		$arr=array();
		
		foreach( $menu_type as $k => $v ) {
			$arr[$v] = strtoupper($v);
		}
		return $arr;
	}
}
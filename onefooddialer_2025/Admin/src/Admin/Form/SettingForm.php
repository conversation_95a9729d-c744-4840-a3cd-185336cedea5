<?php
/**
 * This File provides the input fields which needs to create add & update user
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SettingForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\CommonConfig as QSConfig;

class SettingForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	/**
	 * It adds an input fields which are needed to create user form
	 *
	 * @param Adapter $adapter
	 */
    public function __construct($sm)
    {
        // we want to ignore the name passed
        parent::__construct('setting');
        $this->adapter = $adapter;
        $this->service_locator = $sm;

        $this->setAttribute('method', 'post');
      
        $this->add(array(
        		'name'=>'PAYMENT_METHODS',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        		),
        		'options'=>array(
        				'label'=>'Payment Method<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'value_options' => array(
        						'1' => 'Prepaid',
        						'0' => 'Postpaid',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
    
        
        $this->add(array(
        		'name'=>'PRINT_LOCATION',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        		),
        		'options'=>array(
        				'label'=>'Print Location code<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'value_options' => array(
        						'yes' => 'Yes',
        						'no' => 'No',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'DELIVERY_CHARGES',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        		),
        		'options'=>array(
        				'label'=>'Delivery Charges Applicable<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'value_options' => array(
        						1 => 'Yes',
        						0 => 'No',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name' => 'BREAKFAST_TIMINGS',
        		'type' => 'Zend\Form\Element\Checkbox',
        		'options' => array(
        				'label' => 'For Breakfast',
        				'checked_value' => 'breakfast',
        				'unchecked_value' => '0'
        		),
        ));
        
        $this->add(array(
        		'name'=>'MENU_TYPE',
        		'type' => 'Zend\Form\Element\MultiCheckbox',
        		'attributes'=>array(
        				'class'=>'small',
        		),

        		'options' => array(
        				'label' => 'Menu Type',
        				'label_options' => array('disable_html_escape' => true),
        				'value_options' => $this->getMenuTypes(),
        				'disable_inarray_validator' => true,
        				/*
        				 'value_options' => array(
        				 		'lunch' => 'Lunch',
        				 		'breakfast' => 'Breakfast',
        				 		'dinner' => 'Dinner'
        				 ),
        		*/
        				'label_options' => array('disable_html_escape' => true),
        		),        		
        ));

        $this->add(array(
        		'name'=>'TAX_METHOD',
        		'type' => 'Zend\Form\Element\MultiCheckbox',
        		'attributes'=>array(
        				'class'=>'small',
        		),
        
        		'options' => array(
        				'label' => 'Tax Method',
        				'label_options' => array('disable_html_escape' => true),
        				'value_options' => $this->getTaxMethod(),
        				'disable_inarray_validator' => true,
        				/*
        				 'value_options' => array(
        				 		'lunch' => 'Lunch',
        				 		'breakfast' => 'Breakfast',
        				 		'dinner' => 'Dinner'
        				 ),
        */
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
                
        $this->add(array(
        		'name'=>'BREAKFAST_ORDER_ACCEPTANCE_TIME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small timePicker',
        				'required' => 'required',
        				'placeholder' => 'Acceptance Time',
        				'data-time'=>true,
        				'data-value-format'=>"%H:%M:%S"
        		),
        		'options'=>array(
        				'label'=>'Breakfast Acceptance Time',
        				'label_attributes' => array(
        						'class'  => 'control-label'
        				),
        		)
        ));
        $this->add(array(
        		'name'=>'BREAKFAST_ORDER_CUT_OFF_TIME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        			   'class'=>'small timePicker',
        				'placeholder' => 'Cut-Off Time',
        				'data-time'=>true,
        				'data-value-format'=>"%H:%M:%S"
        				
        		),
        		'options'=>array(
        				'label'=>'Breakfast Order Cut-Off Time',
        				'label_attributes' => array(
        						'class'  => 'control-label'
        				),
        		)
        ));
        
        $this->add(array(
        		'name' => 'LUNCH_TIMINGS',
        		'type' => 'Zend\Form\Element\Checkbox',
        		'options' => array(
        				'label' => 'For Lunch',
        				'checked_value' => 'lunch',
        				'unchecked_value' => '0'
        		),
        ));
        $this->add(array(
        		'name'=>'LUNCH_ORDER_ACCEPTANCE_TIME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small timePicker',
        				'placeholder' => 'Acceptance Time',
        				'data-time'=>true,
        				'data-value-format'=>"%H:%M:%S"
        		),
        		'options'=>array(
        				'label'=>'Lunch Acceptance Time',
        				'label_attributes' => array(
        						'class'  => 'control-label'
        				),
        		)
        ));
        

        $this->add(array(
        		'name'=>'LUNCH_ORDER_CUT_OFF_TIME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => 'Cut-Off Time',
        				'data-time'=>true,
        				'data-value-format'=>"%H:%M:%S"
        		),
        		'options'=>array(
        				'label'=>'Lunch Order Cut-Off Time',
        				'label_attributes' => array(
        						'class'  => 'control-label'
        				),
        		)
        ));
        

        $this->add(array(
        		'name' => 'DINNER_TIMINGS',
        		'type' => 'Zend\Form\Element\Checkbox',
        		'options' => array(
        				'label' => 'For Dinner',
        				'checked_value' => 'dinner',
        				'unchecked_value' => '0'
        		),
        ));
        $this->add(array(
        		'name'=>'DINNER_ORDER_ACCEPTANCE_TIME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small timePicker',
        				'placeholder' => 'Acceptance Time',
        				'data-time'=>true,
        				'data-value-format'=>"%H:%M:%S"
        		),
        		'options'=>array(
        				'label'=>'Dinner Acceptance Time',
        				'label_attributes' => array(
        						'class'  => 'control-label'
        				),
        		)
        ));
        $this->add(array(
        		'name'=>'DINNER_ORDER_CUT_OFF_TIME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => 'Cut-Off Time',
        				'data-time'=>true,
        				'data-value-format'=>"%H:%M:%S"
        		),
        		'options'=>array(
        				'label'=>'Dinner Order Cut-Off Time',
        				'label_attributes' => array(
        						'class'  => 'control-label'
        				),
        		)
        ));
        
        $this->add(array(
        		'name'=>'ADMIN_WEB_URL',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => 'Web URL'
        		),
        		'options'=>array(
        				'label'=>'Web url<span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'DATE_FORMAT',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes'=>array(
        				'class'=>'small timePicker',
        				'placeholder' => 'dd/mm/yy, mm/dd/yy, yy/mm/dd',
        			//	  'multiple' => 'multiple',
        		),
        		'options'=>array(
        				'label'=>'Date Format<span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right'
        				),
        				'value_options' => array(
        						'd-m-Y' => 'dd-mm-yyyy',
        						'd/m/Y' => 'dd/mm/yyyy',
        						'm/d/Y' => 'mm/dd/yyyy',
        						'm-d-Y' => 'mm-dd-yyyy',
        						'F j, Y' => 'mmm dd,yyyy',
        						'm.d.y' => 'm.d.y',
        						'j F, Y' => 'dd mmm yyyy',
        				),
        				'label_options' => array('disable_html_escape' => true),
        				
        		)
        ));
        
         $this->add(array(
        		'name'=>'TIME_ZONE',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes'=>array(
        				'class'=>'small',
        		),
        		'options'=>array(
        				'label'=>'Time Zone<span class="red">*</span>',
        				'label_attributes' => array(
        						'class'  => 'inline right',
        				),
        				'value_options' => $this->getTimeZones(),
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
   
         $this->add(array(
         		'name' => 'GLOBAL_CUSTOMER_PAYMENT_MODE',
         		'type' => 'Zend\Form\Element\MultiCheckbox',
         		'options' => array(
          		'label' => 'Accept Payment by<span class="red">*</span>',
         		'value_options' => array(
                array(
                   'value' => 'cash',
                   'label' => 'Cash',
               	   'attributes' => array(
						'class'=>"cashcheck" ,
               			'data-close'=>".onlinerow"
                   ),
                   'label_attributes' => array(
                       'class'=>"left mr5"
                   ),
               ),
               array(
                   'value' => 'neft',
                   'label' => 'NEFT',
               	   'attributes' => array(
						'class'=>"neftcheck" ,
               			'data-close'=>".onlinerow"
                   ),
                   'label_attributes' => array(
                       'class'=>"left mr5"
                   ),
               ),
               array(
                   'value' => 'cheque',
                   'label' => 'Cheque',
               	   'attributes' => array(
						'class'=>"chequecheck" ,
               			'data-close'=>".onlinerow"
                   ),
                   'label_attributes' => array(
                       'class'=>" left mr5"
                   ),
               ),
          	 array(
          			'value' => 'online',
          			'label' => 'Pay Online',
          			 'attributes' => array(
          						'class'=>"onlinecheck" ,
          						'data-open'=>".onlinerow"
          			),
          			 'label_attributes' => array(
          						'class'=>"left mr5"
          			 ),
          		),
           ),
         	'label_options' => array('disable_html_escape' => true),
       	),
       ));
        
         $this->add(array(
         		'name' => 'ONLINE_PAYMENT_GATEWAY',
         		'type' => 'Zend\Form\Element\Radio',
         		'options' => array(
         	    'value_options' => array(
	                array(
	                   'value' => 'icici_first_data',
	                   'label' => 'ICICI First Data',
	               	   'attributes' => array(
							'class'=>"iciciradio" ,
	               			'data-close'=>".merchancy"
	                   ),
	                   'label_attributes' => array(
	                       'class'=>"pull-left mr5"
	                   ),
	               ),
	               array(
	                   'value' => 'payu',
	                   'label' => 'PayU',
	               	   'attributes' => array(
							'class'=>"payuradio",
	               	   		 'data-close'=>".merchancy"
	                   ),
	                   'label_attributes' => array(
	                       'class'=>"pull-left mr5"
	                   ),
	               ),
	               array(
	                   'value' => 'ebs',
	                   'label' => 'EBS',
	               	   'attributes' => array(
							'class'=>"ebsradio" ,
	               			'data-close'=>".merchancy"
	                   ),
	                   'label_attributes' => array(
	                       'class'=>"pull-left mr5"
	                   ),
	               ),
		          	array(
		          			'value' => 'cc_avenue',
		          			'label' => 'CC Avenue',
		          			 'attributes' => array(
		          						'class'=>"aveneyradio" ,
		          						'data-close'=>".merchancy"
		          			),
		          			 'label_attributes' => array(
		          						'class'=>"pull-left mr5"
		          			 ),
		          	),
         	    	array(
         	    			'value' => 'fd_icici_first_data',
         	    			'label' => 'Fooddialer ICICI First Data',
         	    			'attributes' => array(
         	    						'class'=>"fdradio" ,
         	    						'data-open'=>".merchancy"
         	    			),
         	    			'label_attributes' => array(
         	    						'class'=>"pull-left mr5"
         	    			),
         	    ),
           ),
         	'label_options' => array('disable_html_escape' => true),
         )
         ));
         
         $this->add(array(
         		'name'=>'MERCHANT_ID',
         		'type' => 'Zend\Form\Element\Text',
         		'attributes'=>array(
         				'type' => 'text',
         				'id' => 'merchant_id',
         				'placeholder' => 'Merchant Id',
         		),
         		'options'=>array(
         				'label'=>'Merchant Key<span class="red">*</span>',
         				'label_attributes' => array(
         						'class' => 'inline right',
         				),
         				'label_options' => array('disable_html_escape' => true),
         		),
         
         ));
         
         $this->add(array(
         		'name' => 'ICICI_KEY_FILE',
         		'type'=>'file',
         		'attributes'=>array(
         				"id"=>"ICICI_KEY_FILE",
         		),
         		'options'=>array(
         				'label'=>'Upload Key<span class="red">*</span>',
         				'label_attributes' => array(
         						'class' =>'inline left',
         				),
         		),
         		'label_options' => array('disable_html_escape' => true),
         ));
         $this->add(array(
         		'name'=>'SMS_QOUTA',
         		'type' => 'Zend\Form\Element\Text',
         		'attributes'=>array(
         				'class'=>'small timePicker',
         				'placeholder' => "Enter max 5000 characters"
         		),
         		'options'=>array(
         				'label'=>'SMS qouta<span class="red">*</span>',
         				'label_attributes' => array(
         						'class'  => 'inline right'
         				),
         				'label_options' => array('disable_html_escape' => true),
         		)
         ));

       	$this->add(array(
				'name' => 'GLOBAL_ALLOW_SMS_QUOTA_EXCEED',
				'type' => 'Zend\Form\Element\Checkbox',
				
				'options' => array(
						'label' => 'Allow Exceeding Limit',
						'checked_value' => 1,
						'unchecked_value' => '0'
				),
		));
          
        $this->add(array(
        		'name'=>'SMTP_FROM_NAME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small timePicker widthSmall',
        				'id' => 'from_name',
        				'placeholder' => 'From Name',
        		),
        		'options'=>array(
        				'label'=>'From Name<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'label_options' => array('disable_html_escape' => true),
        			
        		)
        
        ));
        $this->add(array(
        		'name'=>'SMTP_FROM_EMAIL',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small widthSmall',
        				'id' => 'from_email',
        				'placeholder' => 'From Email',
        		),
        		'options'=>array(
        			'label'=>'From Email<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        			'label_options' => array('disable_html_escape' => true),
        		),
        
        ));
        $this->add(array(
        		'name'=>'SMTP_HOST',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small widthSmall',
        				'id' => 'smtp_host',
        				'placeholder' => 'Smpt host',
        		),
        		'options'=>array(
        				'label'=>'SMTP Host<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		),
        	
        ));
        $this->add(array(
        		'name'=>'SMTP_PORT',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small widthSmall',
        				'id' => 'smtp_port',
        				'placeholder' => 'Smpt port',
        		),
        		'options'=>array(
        				'label'=>'SMTP Port<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'name'=>'SMTP_USERNAME',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small widthSmall',
        				'id' => 'smtp_username',
        				'placeholder' => 'Smpt username',
        		),
        
        		'options'=>array(
        				'label'=>'SMPT Username<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        $this->add(array(
        		'name'=>'SMTP_PASSWORD',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'password',
        				'class'=>'small widthSmall',
        				'id' => 'smtp_password',
        				'placeholder' => 'Smpt password',
        		),
        		'options'=>array(
        				'label'=>'SMTP Password<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		),
        
        ));
        $this->add(array(
        		'name'=>'GLOBAL_APPLY_TAX',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        		),
        		'options'=>array(
        				'label'=>'Tax Setting<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'value_options' => array(
        						'YES' => 'Tax Applicable',
        						'NO' => 'No Tax Applicable',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        $this->add(array(
        		'name'=>'GLOBAL_APPLY_TAX',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        		),
        		'options'=>array(
        				'label'=>'Tax Setting<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'value_options' => array(
        						'yes' => 'Tax Applicable',
        						'no' => 'No Tax Applicable',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));        
        
        $this->add(array(
        		'name' => 'ENABLE_AUTO_DELIVERY',
        		'type' => 'Zend\Form\Element\Checkbox',
        		'options' => array(
        				'label' => 'Allow Auto Delivery ',
        				'label_attributes' => array(
        						'class' => "left inline",
        				),
        				'checked_value' => 'yes',
        				'unchecked_value' => 'no'
        		),
        ));
        
        
        $this->add(array(
        		'name'=>'DELIVERY_TIME_FOR_BREAKFAST',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small timePicker',
        				'required' => 'required',
        				'placeholder' => 'Acceptance Time',
        				'data-time'=>true,
        				'data-value-format'=>"%H:%M:%S"
        		),
        		'options'=>array(
        				'label'=>'Breakfast Acceptance Time',
        				'label_attributes' => array(
        						'class'  => 'control-label'
        				),
        		)
        ));
        
        $this->add(array(
        		'name'=>'DELIVERY_TIME_FOR_LUNCH',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small timePicker',
        				'required' => 'required',
        				'placeholder' => 'Acceptance Time',
        				'data-time'=>true,
        				'data-value-format'=>"%H:%M:%S"
        		),
        		'options'=>array(
        				'label'=>'Lunch Acceptance Time',
        				'label_attributes' => array(
        						'class'  => 'control-label'
        				),
        		)
        ));
        
        $this->add(array(
        		'name'=>'DELIVERY_TIME_FOR_DINNER',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes'=>array(
        				'type' => 'text',
        				'class'=>'small timePicker',
        				'required' => 'required',
        				'placeholder' => 'Acceptance Time',
        				'data-time'=>true,
        				'data-value-format'=>"%H:%M:%S"
        		),
        		'options'=>array(
        				'label'=>'Dinner Acceptance Time',
        				'label_attributes' => array(
        						'class'  => 'control-label'
        				),
        		)
        ));
        
        $this->add(array(
        		'name'=>'PHONE_VERIFICATION_METHOD',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        		),
        		'options'=>array(
        				'label'=>'Phone validation Method<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'value_options' => array(
        						'dial2verify' => 'dial2verify verification',
        						'otp' => 'OTP verification',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
        
        
        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/dashboard',
        		),
        ));
        
        $this->add(array(
        		'name' => 'csrf',
        		'type' => 'Zend\Form\Element\Csrf',
        ));
        
        $this->add(array(
        		'name'=>'submit',
        		'attributes'=>array(
        				'type'=>'submit',
        				'value'=>'Save',
        				'class'=>'dark-greenBg',
        				'id' => 'submitbutton',
        		)
        ));
        $this->add(array(
        		'name'=>'SHOW_PRODUCT_AND_MEAL_CALENDAR',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        		),
        		'options'=>array(
        				'label'=>'Calendar Applicable<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'pull-left',
        				),
        				'value_options' => array(
        						1 => 'Calendar Applicable',
        						0 => 'No Calendar Applicable',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));
    }
    


    private function getTaxMethod() {
    	$libConfig = QSConfig::getInstance($sm);
    	$setting = $libConfig->getSettings();
    	$menu_type = $setting['GLOBAL_TAX_METHOD'];
    	
    	$arr=array();
    	
    	foreach( $menu_type as $k => $v ) {
    		$arr[$v] = strtoupper($v);
    	}
    	
    	//echo "<pre>";print_r($menu_type);die;
    	return $menu_type;    	
    }

    private function getMenuTypes(){
    	$libConfig = QSConfig::getInstance($sm);
    	$setting = $libConfig->getSettings();
    	$menu_type = $setting['MENU_TYPE'];
    
    	$arr=array();
    
    	foreach( $menu_type as $k => $v ) {
    		$arr[$v] = strtoupper($v);
    	}
    
    	//echo "<pre>";print_r($arr);die;
    	return $arr;
    }
    
    public function getTimeZones(){
    
    	$returndata =array();
    	$zones= timezone_identifiers_list();
    	foreach ($zones as $zone){
    		$returndata[$zone] = $zone;
    	}
    	
    	return  $returndata;
    }
    
   
}

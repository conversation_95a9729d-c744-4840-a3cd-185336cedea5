<?php
/**
 * This File provides the input fields which needs to create add & update location
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: KitchenForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

class PlanAddSettingForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
    public function __construct($sm)
    {

    	parent::__construct('setting');
        $this->adapter = $adapter;
        $this->service_locator = $sm;
        $this->setAttribute('method', 'post');

        $this->add(array(
            'name' => 'plan_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
            	'class'=> 'smallinput',
                'id' => 'plan_name',
                'placeholder' => 'Enter Plan name',
                'autofocus' => true
            ),
            'options' => array(
                'label' => 'Plan Name <span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
        
        $this->add(array(
        		'name' => 'plan_quantity',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'plan_name',
        				'placeholder' => 'Enter Plan quantity',
        				'autofocus' => true
        		),
        		'options' => array(
        				'label' => 'Plan Name <span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'name' => 'plan_period',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'plan_name',
        				'placeholder' => 'Enter Plan period',
        				'autofocus' => true
        		),
        		'options' => array(
        				'label' => 'Plan Name <span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
            'name' => 'plan_type',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'plan_type',
            ),
            'options' => array(
            	'disable_inarray_validator' => true,
                'label' => 'Plan Type',
                'value_options' => array(
                    'datebased' => 'Datebased',
                    'periodbased' => 'Periodbased',
                ),
            ),
        ));
        
      
        
        $this->add(array(
        		'name' => 'plan_start_date',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'plan_start_date',
        				'placeholder' => 'Enter Start Date',
        				'autofocus' => true
        		),
        		'options' => array(
        				'label' => 'Plan Start Date <span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'name' => 'plan_end_date',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'class'=> 'smallinput',
        				'id' => 'plan_end_date',
        				'placeholder' => 'Enter End Date',
        				'autofocus' => true
        		),
        		'options' => array(
        				'label' => 'Plan End Date <span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));
        
        $this->add(array(
        		'name'=>'plan_status',
        		'type' => 'Zend\Form\Element\Radio',
        		'attributes'=>array(
        				'class'=>'selectpicker small',
        		),
        		'options'=>array(
        				'label'=>'Plan Status<span class="red">*</span>',
        				'label_attributes' => array(
        						'class' => 'inline right',
        				),
        				'value_options' => array(
        						'1' => 'Active',
        						'0' => 'Inactive',
        				),
        				'label_options' => array('disable_html_escape' => true),
        		)
        ));

        $this->add(array(
        		'name' => 'submit',
        		'attributes' => array(
        			'type'  => 'submit',
        			'value' => 'add',
        			'id' => 'submit',
        		),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        			'type'  => 'button',
        			'value' => 'Cancel',
        			'id' => 'cancelbutton',

        		),
        ));

         $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        )); 
    }
}
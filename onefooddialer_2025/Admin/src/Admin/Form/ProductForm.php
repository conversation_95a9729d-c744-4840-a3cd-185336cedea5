<?php
/**
 * This File provides the input fields which needs to create add & update product
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Session\Container;
use QuickServe\Model\LocationTable;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use Lib\Utility;
use Lib\QuickServe\CommonConfig as QSConfig;

class ProductForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * It adds an input fields which are needed to create product form
	 *
	 * @param Adapter $adapter
	 */
    public function __construct($sm)
    {
        // we want to ignore the name passed
        parent::__construct('product');
        $this->service_locator = $sm;
        
        $subscription_keys_session = new Container('subscription_keys');
        
        $utility = Utility::getInstance ();
		$setting_session = new Container( "setting" );
		$setting = $setting_session->setting;
		$currencySymbol = $utility->getCurrencySymbol($setting['GLOBAL_CURRENCY'],$setting['GLOBAL_LOCALE']);
        
		$this->setAttribute('method', 'post');

        $this->add(array(
            'name' => 'pk_product_code',
            'attributes' => array(
                'type'  => 'Hidden',
            ),
        ));
		
		$this->add(array(
            'name' => 'name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Name...',
            	'maxlength' => 45,
                //'required' => 'required',
            ),
            'options' => array(
                'label' => 'Name<span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true)
            ),
        ));
		
		$this->add(array(
				'name' => 'kitchen_code',
				'type' => 'Zend\Form\Element\Text',
				'attributes' => array(
						'class' => 'smallinput',
						'placeholder' => 'Enter Kitchen Code...',
					//	'required' => 'required',
						'maxlength'=>'9',
				),
				'options' => array(
					'label' => 'Kitchen Code<span class="red">*</span>',
					'label_options' => array('disable_html_escape' => true)
				),
		));
		
		$this->add(array(
				'name'=>'product_subtype',
				'type' => 'Zend\Form\Element\Radio',
				'attributes'=>array(
						'class'=>'selectpicker small',
						'id' => 'product_subtype'
				),
				'options'=>array(
						'label'=>'Product Subtype<span class="red">*</span>',
		       			'label_attributes' => array(
		       				'class' => 'inline mr5',
		       			),						
						'value_options' => array(
								array(
										'value' => 'generic',
										'label' => 'Generic',
										'label_attributes' => array(
											'class'=>"left mr5"
										),
									
								),
								array(
										'value' => 'specific',
										'label' => 'Specific',
										'label_attributes' => array(
											'class'=>"left mr5"
										),										
								),
						),
						'label_options' => array('disable_html_escape' => true),
						'disable_inarray_validator' => true,
				),
		));
		
		$this->get('product_subtype')->setValue('generic');
		
		$this->add(array(
	 		'name' => 'description',
	 		'type' => 'Zend\Form\Element\Textarea',
	 		'attributes' => array(
	 			'class' => 'smallinput',
	 			'placeholder' => 'Enter Description...',
	 		//	'required' => 'required',
	 		),
	 		'options' => array(
	 			'label' => 'Description<span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true)
	 		),
		 ));
		 
		$this->add(array(
		 		'name' => 'recipe',
		 		'type' => 'Zend\Form\Element\Textarea',
		 		'attributes' => array(
		 				'class' => 'smallinput',
		 				'placeholder' => 'Enter Recipe...',
		 				//	'required' => 'required',
		 		),
		 		'options' => array(
		 			'label' => 'Recipe',
					'label_options' => array('disable_html_escape' => true)
		 		),
		 ));
		 
		$this->add(array(
            'name' => 'unit_price',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Unit Price...',
               // 'required' => 'required',
            ),
            'options' => array(
                'label' => 'Unit Price('.$currencySymbol.')<span	class="red">*</span>',

				'label_options' => array('disable_html_escape' => true)
            ),
        ));
		
		

		$this->add(array(
				'name' => 'food_type',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'food_type',
						//	'required' => 'required',
						'value' => '0',
				),
				'options' => array(
					'label' => 'Type of Food<span class="red">*</span>',
					'label_options' => array('disable_html_escape' => true),
					'value_options' => $this->getFoodTypes(),
					'label_options' => array('disable_html_escape' => true),
				),
		));

		$this->add(array(
				'name'=>'product_type',
				'type' => 'Zend\Form\Element\Radio',
				'attributes'=>array(
						'class'=>'selectpicker small',
						'id' => 'product_type'
				),
				'options'=>array(
						'label'=>'Show in Extras <span class="red">*</span>',
		       			'label_attributes' => array(
		       				'class' => 'inline mr5',
		       			),						
						'value_options' => array(
								array(
										'value' => 'Extra',
										'label' => 'Yes',
										'label_attributes' => array(
											'class'=>"left mr5"
										),										
										'selected' => true,
								),
								array(
										'value' => 'Main',
										'label' => 'No',
										'label_attributes' => array(
											'class'=>"left mr5"
										),										
								),
						),
						'label_options' => array('disable_html_escape' => true),
				)
		));

		$this->add(array(
				'name' => 'product_category',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
						'id' => 'product_category',
						//	'required' => 'required',
						'value' => '',
				),
				'options' => array(
						'label' => 'Product Category',
						'label_options' => array('disable_html_escape' => true),
						'value_options' => $this->getProductCategories()
				),
		));

		$this->add(array(
				'name' => 'category',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
					'style'=>"height:75px",
					'multiple' => 'multiple',
					'id' => 'menu',
				//	'required' => 'required',
					'value' => '0',
				),
				'options' => array(
					'label' => 'Menu Type <span class="red">*</span>',
					'label_options' => array('disable_html_escape' => true),
					'value_options' => $this->getMenuTypes(),
					'disable_inarray_validator' => true,
					/*
					'value_options' => array(
						'lunch' => 'Lunch',
						'breakfast' => 'Breakfast',
						'dinner' => 'Dinner'
					),
					*/
					'label_options' => array('disable_html_escape' => true),
				),
		));
		
		
		$kitchenScreens = array();
		
		$kitchenScreens = $this->getKitchenScreens();


		$this->add(array(
				'name' => 'screen',
				'type' => 'Zend\Form\Element\Select',
				'attributes' => array(
					'class' => 'smallinput',
					'id' => 'screen',
					//	'required' => 'required',
					'value' => '',
				),
				'options' => array(
                'label' => 'Kitchen<span class="red">*</span>',
				'label_options' => array('disable_html_escape' => true),
				'value_options' => $kitchenScreens
            ),
		));

       $this->add(array(
            'name' => 'threshold',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'class' => 'smallinput',
                'placeholder' => 'Enter Kitchen Capacity...',
             //   'required' => 'required',
            ),
            'options' => array(
                'label' => 'Kitchen Capacity<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true)
            ),
        ));

        $this->add(array(
        		'name' => 'max_quantity_per_meal',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'class' => 'smallinput',
        				'placeholder' => 'Ex: 1',
        				//'required' => 'required'
        		),
        		'options' => array(
        			'label' => 'Max Quantity<span class="red">*</span>',
        			'label_options' => array('disable_html_escape' => true),
        			'label_options' => array('disable_html_escape' => true),
        		),
        ));

       $this->add(array(
       		'name' => 'image_path',
       		'type'=>'file',
       		'attributes' => array(
       			'class' => 'smallinput',
       			'accept'=>"image/*",
       		),
       		'options' => array(
       			'label' => 'Product Image<span class="red">*</span>',
       			'label_options' => array('disable_html_escape' => true),
       		),
       ));
       
       $this->add(array(
       		'name' => 'quantity',
       		'type' => 'Zend\Form\Element\Text',
       		'attributes' => array(
       			'class' => 'smallinput',
       		),
       		'options' => array(
       			'label' => 'Quantity<span class="red">*</span>',
       			'label_options' => array('disable_html_escape' => true),
       		),
       ));
        
       $this->add(array(
       		'name' => 'unit',
       		'type' => 'Zend\Form\Element\Select',
       		'attributes' => array(
       			'class' => 'smallinput',
       			'id' => 'unit',
       		//	'required' => 'required',
       			'value' => '0',
       		),
       		'options' => array(
       			'label' => 'Screen<span class="red">*</span>',
       			'label_options' => array('disable_html_escape' => true),
       			'value_options' => array(
       				'gram' => 'Gram',
       				'kg' => 'Kg',
       				//'pack' => 'Packs',
       				'piece' => 'Piece',
       				'litre' => 'Litre',
       				'mililitre' => 'Mililitre',
                    'ounce'=>'Ounce',
                    'unit'=>'Unit'
       			),
       		),
       ));
        
        
      $this->add(array(
      		'name' => 'status',
      		'type' => 'Zend\Form\Element\Radio',
      		'attributes' => array(
      				'id' => 'status',
      				//   'required' => 'required',
      		),
      		'options' => array(
      				'label' => 'Status',
      				'value_options' => array(
      						array(
      								'value' => '1',
      								'label' => 'Active',
      								'selected' => true,
      						),
      						array(
      								'value' => '0',
      								'label' => 'Inactive',
      						),
      				),
      		),
      ));
      
      // Create form elements for meal swapping
      
       $this->add(array(
       		'name'=>'is_swappable',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       			'class'=>'swapradio small',
       			'id' => 'is_swappable',
       		),
       		'options'=>array(
       			'label'=>'Swappable <span class="red">*</span>',
       			'label_attributes' => array(
       				'class' => 'inline mr5',
       			),
       			'value_options' => array(
       			        
   			        array(
						'value' => '1',
						'label' => 'Yes',
						'label_attributes' => array(
							'class'=>"swappable left mr5"
						),
   					),
   					array(
						'value' => '0',
						'label' => 'No',
						'label_attributes' => array(
							'class'=>"swappable left mr5"
						),
   					)
       			),
       		       
       			'label_options' => array('disable_html_escape' => true),
       			'disable_inarray_validator' => true,
       		),
           'attributes'=>array(
   		        'value'=>'1'       
   		    ), 
       ));
      
       $this->add(array(
       		'name'=>'swap_with',
       		'type' => 'Zend\Form\Element\Radio',
       		'attributes'=>array(
       			'class'=>'small',
       			'id' => 'swap_with',
       		),
       		'options'=>array(
       			'label'=>'Swap With <span class="red">*</span>',
       			'label_attributes' => array(
       				'class' => 'mr5',
       			),
       			'value_options' => array(
       				'nocharge' => 'No Charge',
       				'askdifference' => 'Ask Difference',
       			    'swapcharge'=>'Swap Charges',
       				'aspercategory'=>'As Per Category'
       			),
       			'label_options' => array('disable_html_escape' => true),
       			'disable_inarray_validator' => true,
       		),
            'attributes'=>array(
   		        'value'=>'nocharge'       
   		    ),
       ));
       
       $this->add(array(
            'name' => 'swap_charges',
            'type' => 'Zend\Form\Element\Text',
            'required'=>false,   
            'attributes' => array(
                'class' => 'smallinput',
                'style' =>'width:150px;',    
                'id'=>'swap_charges', 
                'placeholder' => 'Swapping Charges',
                 'onkeypress' => 'javascript:return isNumber(event)' 
            ),
            'options' => array(
            	'label' => 'Swapping Charges',
                'label' => 'Swapping Charges<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));      

		 $this->add(array(
        		'name' => 'csrf',
        		'type' => 'Zend\Form\Element\Csrf',
        ));

        $this->add(array(
            'name' => 'submit',
            'attributes' => array(
                'type'  => 'submit',
                'value' => 'Go',
                'id' => 'submitbutton',
            ),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'button',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',
        		),
        ));

        $this->add(array(
        	'name'=>'backurl',
        	'type' => 'Zend\Form\Element\Hidden',
        	'attributes'=>array(
        			'id'=>'backurl',
        			'value'=>'/product',
        ),
        ));
    }

    /**
     * 
     * The function `getProductCategories` fetches product categories from the product_category table.
     * 
     * @method getProductCategories
     * @access private
     * @return array
     */
	private function getProductCategories() {
    	$product_category_array = array();
    	$sql = new QSql($this->service_locator);

		$select = $sql->select();
		$select->from('product_category');
		$select->where('type="product"');
		$select->where('status=1');
		//$statement = $sql->prepareStatementForSqlObject($select);
		//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
		// Iterate through all records.
		foreach ($results as $res) {
			// value is the product category name
			$product_category_array[$res['product_category_name']] = $res['product_category_name'];
		}// end of foreach

// 		print_r($product_category_array);exit();
		return $product_category_array;
	}

	/**
	 * The function fetches the static array of food types from ProductTable Model.
	 * 
	 * @method getFoodTypes
	 * @access private
	 * @return array
	 */
	private function getFoodTypes() {
		//return ProductTable::$food_types;
		$libConfig = QSConfig::getInstance($this->service_locator);
		$setting = $libConfig->getSettings();
		
		$food_type = $setting['FOOD_TYPE'];
		
		$arr=array();
		$arr = array('' => 'Select food type');
		foreach( $food_type as $k => $v ) {
			if($v!='all')
			{
				$arr[$v] = strtoupper($v);
			} 
		}
		//echo "<pre>";print_r($arr);die;
		//$setting = new Container('setting');
		//$setting = $setting->setting;	
		//$food_type = explode(',', $setting['FOOD_TYPE']);
		
		//return $food_type['FOOD_TYPE'];
		return $arr;
	}
	
	private function getMenuTypes(){
		$libConfig = QSConfig::getInstance($this->service_locator);
		$setting = $libConfig->getSettings();
		$menu_type = $setting['MENU_TYPE'];
		
		$arr=array();
		
		foreach( $menu_type as $k => $v ) {
			$arr[$v] = strtoupper($v);
		}
				
		//echo "<pre>";print_r($arr);die;
		return $arr;
	}

	private function getKitchenScreens() {
		$kitchen_array = array('' => 'Select the kitchen', '0' => 'All');
		$sql = new QSql($this->service_locator);

		$select = $sql->select();
		$select->from('kitchen_master');
		//$statement = $sql->prepareStatementForSqlObject($select);
		//$results = $statement->execute();
        $results = $sql->execQuery($select);
		// Iterate through all records.
		foreach ($results as $res) {
			// value is the product category name
			$kitchen_array[$res['pk_kitchen_code']] = $res['kitchen_name'].( !empty($res['kitchen_name']) ? ' ('.$res['location'].')' : '' );
		}// end of foreach
		
		return $kitchen_array;
	}

	public function getLocationArray() {
		$location_array = array(0 => 'All Locations');
		$tblLocations = new LocationTable($this->adapter);
    	$temp_location_array = $tblLocations->getActiveLocations();
    	if(is_array($temp_location_array) && !empty($temp_location_array)) {
    		foreach( $temp_location_array as $location_id => $location_name) {
    			$location_array[$location_id] = $location_name;
    		}//end of foreach
    	}
		return $location_array;
	}
	
}

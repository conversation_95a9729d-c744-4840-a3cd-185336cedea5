<?php
/**
 * This File is not used for the backorderprocess
 * As BackOrder access the form library from Front Module
 * This File is not recommended & it will be deleted in future
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: PayForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 * @deprecated No longer used by internal code and not recommended.
 */
namespace Front\Form;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

class PayForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	/**
	 * It adds an input fields which are needed to create pay form
	 *
	 * @var string $name
	 * @return void
	 */
	public function __construct($name = FALSE)
	{

		parent::__construct('pay');
		//$this->adapter = $adapter;
		$this->setAttribute('method', 'post');
		$this->add(array(
				'name'=>'amount',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'mar-top',
						'placeholder' => 'Enter the amount',
						'id'	=> 'amount',
						'required' => 'required'
				),
				'options'=>array(
						'label'=>'Phone'
				)
		));
		$this->add(array(
				'name' => 'csrf',
				'type' => 'Zend\Form\Element\Csrf',
		));
		$this->add(array(
				'name'=>'submit',
				'attributes'=>array(
						'type'=>'submit',
						'value'=>'SUBMIT',
						'class'=>'button',
				)
		));
	}

}
<?php
/**
 * This File is not used for the backorderprocess
 * As BackOrder access the form library from Front Module
 * This File is not recommended & it will be deleted in future
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: FrontForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 * @deprecated No longer used by internal code and not recommended.
 */
namespace Admin\Form;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;
use Zend\Db\Sql\Sql;

class FrontForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
	/**
	 * It adds an input fields which are needed to create back order form
	 *
	 * @var string $name
	 * @return void
	 */
	public function __construct($sm)
	{

		parent::__construct('index');
        $this->service_locator = $sm;
		//$this->adapter = $adapter;
		$this->setAttribute('method', 'post');
		//$this->setAttribute('id', 'login');
		//$this->setAttribute('class', 'stdform');
		$this->add(array(
				'name'=>'phone',
				'type' => 'Zend\Form\Element\Text',
				'attributes'=>array(
						'class'=>'mar-top',
						'placeholder' => 'Enter your registered mobile number OR Email-ID',
						'id'	=> 'cno',
						'required' => 'required'
				),
				'options'=>array(
						'label'=>'Phone'
				)
		));
		$this->add(array(
				'name' => 'csrf',
				'type' => 'Zend\Form\Element\Csrf',
		));
		$this->add(array(
				'name'=>'submit',
				'attributes'=>array(
						'type'=>'submit',
						'value'=>'SUBMIT',
						'class'=>'button',
				)
		));
	}

}
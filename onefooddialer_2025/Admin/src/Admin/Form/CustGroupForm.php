<?php
/**
 * This File provides the input fields which needs to create  add & update customer group
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustGroupForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\Sql\QSql;

class CustGroupForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
    private $service_locator;
	/**
	 * It adds an input fields which are needed to create customer group login form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
	public function __construct($sm)
	{
		parent::__construct('admin');
		$this->service_locator = $sm;
		$this->setAttribute('method','post');

		$this->add(array(
			'name' => 'group_code',
			'type' => 'Zend\Form\Element\Hidden',
			'attributes' => array(
				'id' => 'group_code',
			),
		));

		$this->add(array(
            'name' => 'group_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'id' => 'group_name',
                'placeholder' => 'Enter Group Name',
               // 'required' => 'required',
            	'class'=>'smallinput'
            ),
            'options' => array(
                'label' => 'Group Name<span	class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'fk_location_code',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'fk_location_code',
                'required' => 'required',
            ),
            'options' => array(
                'label' => 'Delivery Location',
                'value_options' => $this->getLocation(),
                ),
            ));

        $this->add(array(
			'name' => 'status',
			'type' => 'Zend\Form\Element\Select',
			'attributes' => array(
				'id' => 'status',
				'required' => 'required',
			),
			'options' => array(
				'label' => 'Status',
				'value_options' => array(
					'1' => 'Active',
					'0' => 'Inactive',
				),
			),
        ));

        $this->add(array(
			'name' => 'submit',
			'attributes' => array(
				'type'  => 'submit',
				'value' => 'Go',
				'id' => 'submitbutton',
			),
        ));

        $this->add(array(
			'name' => 'cancel',
			'attributes' => array(
				'type'  => 'button',
				'value' => 'Cancel',
				'id' => 'cancelbutton',
			),
        ));

        $this->add(array(
			'name'=>'backurl',
			'type' => 'Zend\Form\Element\Hidden',
			'attributes'=>array(
				'id'=>'backurl',
				'value'=>'/custgroup',
			),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
	}
	/**
	 * To get the list of delivery locations
	 *
	 * @return array
	 */
	public function getLocation()
	{
		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('delivery_locations');
		//$statement = $sql->prepareStatementForSqlObject($select);
		//$results = $statement->execute();
        $result = $sql->execQuery($select);
		foreach ($results as $res) {
			$selectData[$res['pk_location_code']] = $res['location'];
		}
		return $selectData;

	}
}
<?php

/**
 * This File provides the input fields which needs to create add & update discount
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: DiscountForm.php 2014-06-19 $
 * @package Admin/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */

namespace Admin\Form;

use Zend\Captcha;
use Zend\Form\Element;
use Zend\Form\Form;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\Sql\QSql;

class DiscountForm extends Form
{
	/**
	 * It has an instance of Adapter model
	 *
	 * @var Adapter $adapter
	 */
	private $adapter;
     private $service_locator;
	/**
	 * It adds an input fields which are needed to create discount form
	 *
	 * @var Adapter $adapter
	 * @return void
	 */
	public function __construct($sm)
	{
		parent::__construct('admin');
		$this->service_locator = $sm;
		$this->setAttribute('method','post');

		$this->add(array(
				'name' => 'pk_discount_code',
				'type' => 'Zend\Form\Element\Hidden',
				'attributes' => array(
						'id' => 'pk_discount_code',
						 'class' => 'smallinput',
				),
		));

		$this->add(array(
            'name' => 'discount_name',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'id' => 'discount_name',
                'placeholder' => 'Enter Discount Name',
               // 'required' => 'required',
            		'class' => 'smallinput',
            ),
            'options' => array(
                'label' => 'Discount Name<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'discount_for',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'discount_for',
               // 'required' => 'required',
            ),
            'options' => array(
                'label' => 'Discount For',
                'value_options' => array(
                 //   'Qty' => 'Quantity',
                    'Group' => 'Group',
                ),
            ),
        ));

        $this->add(array(
            'name' => 'quantity',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'id' => 'quantity',
            	'placeholder' => 'Enter Quantity',
               // 'required' => 'required',
            		'class' => 'smallinput',
            ),
            'options' => array(
            		'label' => 'Quantity<span class="red">*</span>',
            		'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
        		'name' => 'product_id',
        		'type' => 'Zend\Form\Element\Select',
        		'attributes' => array(
        				'id' => 'product_id',

        		),
        		'options' => array(
        				'label' => 'Product Name<span class="red">*</span>',
        				'value_options' => $this->getProduct(),
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));

        $this->add(array(
            'name' => 'group_code',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'group_code',

            ),
            'options' => array(
                'label' => 'Group<span class="red">*</span>',
                'value_options' => $this->getGroup(),
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
            'name' => 'discount_type',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'discount_type',
               // 'required' => 'required',
            ),
            'options' => array(
                'label' => 'Discount Type<span class="red">*</span>',
                'value_options' => array(
                    '0' => 'Fixed',
                    '1' => 'Percent',
                ),
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));

        $this->add(array(
        		'name' => 'discount_rate',
        		'type' => 'Zend\Form\Element\Text',
        		'attributes' => array(
        				'id' => 'discount_rate',
        				'placeholder' => 'Enter Discount Rate',
        				//'required' => 'required',
        				'class' => 'smallinput',
        		),
        		'options' => array(
        				'label' => 'Discount Rate<span class="red">*</span>',
        				'label_options' => array('disable_html_escape' => true),
        		),
        ));


        $this->add(array(
            'name' => 'till_date',
            'type' => 'Zend\Form\Element\Text',
            'attributes' => array(
                'id' => 'till_date',
            	'placeholder' => 'Expiry Date',
            	'class'=>'calender smallinput ',
                'placeholder' => 'Select Till Date ....',


            ),
            'options' => array(
                'label' => 'Discount Expiry Date<span class="red">*</span>',
            	'label_options' => array('disable_html_escape' => true),
            ),
        ));
        
        
       
        $this->add(array(
            'name' => 'status',
            'type' => 'Zend\Form\Element\Select',
            'attributes' => array(
                'id' => 'status',
            ),
            'options' => array(
                'label' => 'Status',
                'value_options' => array(
                    '1' => 'Active',
                    '0' => 'Inactive',
                ),
            ),
        ));

        $this->add(array(
        		'name' => 'submit',
        		'attributes' => array(
        				'type'  => 'submit',
        				'value' => 'Go',
        				'id' => 'submitbutton',
        		),
        ));

        $this->add(array(
        		'name' => 'cancel',
        		'attributes' => array(
        				'type'  => 'button',
        				'value' => 'Cancel',
        				'id' => 'cancelbutton',

        		),
        ));

        $this->add(array(
        		'name'=>'backurl',
        		'type' => 'Zend\Form\Element\Hidden',
        		'attributes'=>array(
        				'id'=>'backurl',
        				'value'=>'/discount',
        		),
        ));

        $this->add(array(
            'name' => 'csrf',
            'type' => 'Zend\Form\Element\Csrf',
        ));
	}
	/**
	 * To get the list of customer groups
	 *
	 * @return array
	 */
	public function getGroup()
	{
		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('groups');
		$select->where('status',1);
		//$select->where('status',1);
		//$statement = $sql->prepareStatementForSqlObject($select);
		//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
		$selectData['']= "Select Group";
		foreach ($results as $res) {
			$selectData[$res['group_code']] = $res['group_name'];
		}
		//echo '<pre>';print_r($selectData);exit();
		return $selectData;
	}
	/**
	 * To get the list of products
	 *
	 * @return array
	 */
	public function getProduct()
	{
        $sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('products');
		$select->where(array('product_type'=>'Meal','status'=>1));
		$select->order('name');
		//$statement = $sql->prepareStatementForSqlObject($select);
		//echo $select->getSqlString();
		//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
		$selectData['']= "Select Product";
		foreach ($results as $res) {
			$selectData[$res['pk_product_code']] = $res['name'];
		}
		//echo '<pre>';print_r($selectData);exit();
		return $selectData;
	}
}
<?php
/**
 * This file manages the invoices on fooddialer system
 * The payment status of invoices are managed through this file
 * Invoice bill can be paid through this file
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ReportController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use DOMPDFModule\View\Model\PdfModel;

use Zend\Db\Adapter\Adapter;
use Zend\Db\Sql\Expression;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use PHPExcel;
use PHPExcel_IOFactory;
use PHPCsv;
use Zend\Config\Reader\Xml as xml;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\Utility;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Order as QSOrder;
use Lib\QuickServe\Admin\Report as QSReport;

use Admin\Form\FilterForm;



class ReportController extends AbstractActionController
{
	protected $frontTable;
	/**
	 * It has an instance of QuickServe\Model\InvoiceTable model
	 *
	 * @var QuickServe\Model\InvoiceTable $frontTable
	 */
	protected $reporttable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	/**
	 * It has an instance of QuickServe\Model\OrderTable model
	 *
	 * @var QuickServe\Model\OrderTable $ordertable
	 */
	protected $ordertable;
	/**
	 * It has an instance of QuickServe\Model\CustomerTable model
	 *
	 * @var QuickServe\Model\CustomerTablee $customertable
	 */
	protected $customertable;
	protected $authservice;
    
	/**
	 * This function displays the list of invoices
	 *
	 * @return \Zend\View\Model\ViewModel
	 */

	public function getAjaxWeeksUsingMonthAction(){
		$month = $this->request->getPost('month');
		$year = $this->request->getPost('year');
        
        
        return new JsonModel(array(
			'year' => $year,
			'month' => $month,
			'options' => $this->getReportTable()->getWeeksOfMonth($month, $year),
		));
	}
       
	public function ordersAction()
	{
		$kitchen_data=$this->getOrderTable()->getKitchenKitchenScreen();
		//echo 'hello';exit;
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		
		$setting_session = new Container('setting');
		$menus = $setting_session->setting['MENU_TYPE'];
		
		$location_data=$this->getOrderTable()->getLocationData();
		
		$request = $this->getRequest();
		$iden = $this->authservice->getIdentity();
		$search_form = new FilterForm($this->getServiceLocator());
       
		// 1. usertable -> deliveryPerson function
                // 2. parse to array => --all --
                //3. $search_form->get('delivery_person')->setValueOptions($returnData);
                if ($request->isPost()) {
			if($request->getPost('filter_year') && $request->getPost('filter_month')){
				$search_form->setMonthYear($request->getPost('filter_year'),$request->getPost('filter_month'));
			}
		}
		$search_form->getForm();

		$searchData['minDate'] = date("m/d/Y",strtotime("first day of this month"));
		$searchData['maxDate'] = date("m/d/Y",strtotime("last day of this month"));

		$search_form->setData($searchData);

		$layoutviewModel = $this->layout();
		$acl = $layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		//echo "s";exit();
		if ($request->isPost()) {
			//echo '<pre>';print_r($request->getPost());exit;
			$search_form->setData($request->getPost());
			//$orders = $this->getReportTable()->getOrders();
			//$orders = $this->getReportTable()->getOrders($this->request->getPost(),$search,0,10);
			$orders = "";
			$params = $this->params()->fromPost();

			if(isset($params['subaction'])){
			//	$orders_send = $this->getReportTable()->getOrders($this->request->getPost());
			
				if($params['subaction']=='print'){
					$purpose = 'export';
				}else{
					$purpose ='view';
				}
				$orders_send = $this->getReportTable()->getOrderExportData($this->request->getPost(),false,false,false,true,$purpose,'orders');
				
				switch($params['subaction']){

					case "print":
						$formData = $request->getPost();
						$formData['export_type'] = $params['subaction'];
						return $this->forward()->dispatch('Admin\Controller\Report', array(
								'printData' => $orders_send,
								'action' => 'printReport',
								'formData' =>$formData
						));
						break;
					case "export":
						return $this->forward()->dispatch('Admin\Controller\Report', array(
						'printData' => $orders_send,
						'service' => $params['service'],
						'action' => 'printReport',
						'subaction' => $params['subaction'],
						'minDate' => $request->getPost('minDate'),
						'maxDate'	=> $request->getPost('maxDate')
						));
						break;
					case "search";
					break;

				}
			}

		}else {
			//$orders = $this->getReportTable()->getOrders();
			//echo '<pre>';print_r($orders);exit;
		}

		//echo '<pre>';print_r($search_form);exit;
		
		$this->layout()->setVariables(array('page_title'=>"Order Report",'breadcrumb'=>"Order Report"));
		return new ViewModel(array(
				'acl' => $acl,
				'loggedUser' => $loguser,
				'filter'	=> $search_form,
				'kitchen_data' =>$kitchen_data,
				'menus' => $menus,
				'location_data' => $location_data,
                'hideClass' => (array_key_exists('GLOBAL_DELIVERY_TYPE', $setting_session->setting) && (strpos($setting_session->setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ) ? 'show': 'hide',
				
		));
	}
	
	public function salesAction()
	{
		$kitchen_data=$this->getOrderTable()->getKitchenKitchenScreen();
		$location_data=$this->getOrderTable()->getLocationData();
		
		$setting_session = new Container('setting');
		$menus = $setting_session->setting['MENU_TYPE'];
		
		$type = $this->params()->fromQuery('type');
		$location_code = $this->params()->fromQuery('location_code');
		$menuselected = $this->params()->fromQuery('menu');
		
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		$request = $this->getRequest();
		$iden = $this->authservice->getIdentity();
		$search_form = new FilterForm($this->getServiceLocator());
		
		if ($request->isPost()) {			
			if($request->getPost('filter_year') && $request->getPost('filter_month')){
				$search_form->setMonthYear($request->getPost('filter_year'),$request->getPost('filter_month'));
			}		
		}
		#echo $request->getPost('minDate');exit;
		$search_form->getForm();

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		$orders = "";
		$select = new QSelect();

		$searchData['minDate'] = date("m/d/Y",strtotime("first day of this month"));
		$searchData['maxDate'] = date("m/d/Y",strtotime("last day of this month"));

		$search_form->setData($searchData);

		if($location_code!='all' && $location_code !='')
		{
			$select->where("location_code = '$location_code'");
		}
		if(!empty($menuselected)){
			$select->where("order_menu=". "'$menuselected'");
		}
	
		//echo "s";exit();
		if ($request->isPost()) {
		
			
			$search_form->setData($request->getPost());
			//$orders = $this->getReportTable()->getSales($this->request->getPost());
		
			$params = $this->params()->fromPost();
			
			//echo "<pre>Params";print_r($params);die;
			if(isset($params['subaction'])){
			   
				//$orders_send = $this->getReportTable()->getSales($this->request->getPost(),false);
				
				if($params['subaction']=='print' || $params['subaction']=='quickbook' || $params['subaction']=='tally'){
					$purpose = 'export';
				}else{
					$purpose ='view';
				}
		
				$orders_send = $this->getReportTable()->getOrderExportData($this->request->getPost(),false,false,false,true,$purpose,'sales',$select);
				
				switch($params['subaction']){
				
					case "tally":
					case "quickbook":
					case "print":
						$formData = $request->getPost();
						
						$formData['export_type'] = $params['subaction'];
						return $this->forward()->dispatch('Admin\Controller\Report', array(
								'printData' => $orders_send,
								'action' => 'printReport',
								'formData' =>$formData
						));
						break;
						
					case "export":
						return $this->forward()->dispatch('Admin\Controller\Report', array(
						'printData' => $orders_send,
						'service' => $params['service'],
						'action' => 'printReport',
						'subaction' => $params['subaction'],
						'minDate' => $request->getPost('minDate'),
						'maxDate'	=> $request->getPost('maxDate')
						));
						break;
					case "search";
					break;
				
				}
			}

		}else {
		    
			//$orders = $this->getReportTable()->getSales();
			//echo '<pre>';print_r($orders);exit;
		}

		
		$this->layout()->setVariables(array('page_title'=>"Sales Report",'breadcrumb'=>"Sales Report"));
		return new ViewModel(array(
				'paginator' => $orders,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'filter'	=> $search_form,
				'kitchen_data' =>$kitchen_data,
				'location_data'=>$location_data,
				'menus'   => $menus
		));
	}
	
//    public function thirdpartyAction(){
//        
//        $kitchen_data=$this->getOrderTable()->getKitchenKitchenScreen();
//		
//        if (! $this->authservice)
//		{
//			$this->authservice = $this->getServiceLocator()->get('AuthService');
//		}
//		
//		$request = $this->getRequest();
//		
//		$search_form = new FilterForm($this->getServiceLocator()->get('Write_Adapter'));
//        
//		if ($request->isPost()) {
//			if($request->getPost('filter_year') && $request->getPost('filter_month')){
//				$search_form->setMonthYear($request->getPost('filter_year'),$request->getPost('filter_month'));
//			}
//		}
//        
//		$search_form->getForm();
//
//		$layoutviewModel = $this->layout();
//		$acl = $layoutviewModel->acl;
//		$loguser = $layoutviewModel->loggedUser;
//		
//		if ($request->isPost()) {
//			
//			$search_form->setData($request->getPost());
//			
//			$params = $request->getPost();
//			
//			if(isset($params['subaction'])){
//			
//				if($params['subaction']=='print'){
//					$purpose = 'export';
//				}else{
//					$purpose ='view';
//				}
//                
//                $thirdparty_type = $params['thirdparty_type'];
//                $thirdparty_option = $params['thirdparty_option'];
//
//                $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
//                $arrColumns = array('0' => 'third_party', '1'=>'order_date','2'=>'total_orders','3'=>'total_amount');
//
//                $order_by = $arrColumns[$arrOrder[0]['column']];
//                $order = $arrOrder[0]['dir'];
//
//                $select = $this->thirdPartySelectStatement($_SESSION['adminkitchen'],$thirdparty_type, $thirdparty_option, $order_by, $order);
//                
//				$thirdPartyReportData = $this->getReportTable()->getThirdPartyData($select, $request->getPost(), false, $purpose, $page);
//				
//				switch($params['subaction']){
//
//					case "print":
//						$formData = $request->getPost(); 
//						$formData['export_type'] = $params['subaction'];
//                        $formData['selected_columns'] = implode(',',$arrColumns);
//                        
//						return $this->forward()->dispatch('Admin\Controller\Report', array(
//								'printData' => $thirdPartyReportData,
//								'action' => 'printReport',
//								'formData' =>$formData
//						));
//						break;
//					case "export":
//						return $this->forward()->dispatch('Admin\Controller\Report', array(
//						'printData' => $thirdPartyReportData,
//						'action' => 'printReport',
//                        'formData' => $request->getPost()
//                        ));
//						break;
//
//				}
//			}
//
//		}
//
//		$this->layout()->setVariables(array('page_title'=>"Third Party Report",'breadcrumb'=>"Third Party Report"));
//		
//        return new ViewModel(array(
//				'acl' => $acl,
//				'loggedUser' => $loguser,
//				'filter'	=> $search_form,
//				'kitchen_data' =>$kitchen_data
//				
//		));
//    }
    
    public function ajaxThirdpartyAction()
	{
		if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}

		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
        
		$utility = Utility::getInstance();

        $thirdparty_type = $this->params()->fromQuery('thirdparty_type');
        $thirdparty_option = $this->params()->fromQuery('thirdparty_option');
		
        $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0' => 'third_party', '1'=>'order_date','2'=>'total_orders','3'=>'total_amount');
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];

		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		
		$kitchenscreen = $_SESSION['adminkitchen'];
		
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		
        $select = $this->thirdPartySelectStatement($kitchenscreen,$thirdparty_type,$thirdparty_option, $order_by, $order);
		
		$orders = $this->getReportTable()->getThirdPartyData($select, $this->params()->fromQuery(), $search, 'view', $page);
		
		$orders->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $orders->getTotalItemCount();
		$returnVar['recordsFiltered'] = $orders->getTotalItemCount();
		$returnVar['data'] = array();
	
		foreach($orders as $order){
            
			$arrTmp = array();
            array_push($arrTmp,$order['third_party']);
			array_push($arrTmp,$utility->displayDate($order['order_date'],$setting['DATE_FORMAT']));
			array_push($arrTmp,$order['total_orders']);
			array_push($arrTmp,$utility->getLocalCurrency($order['total_amount']));
				
			array_push($returnVar['data'],$arrTmp);
		}

		return new JsonModel($returnVar);
	}
    
    public function thirdpartySelectStatement($kitchenscreen,$thirdparty_type,$thirdparty_option, $order_by = false, $order = false){
        
		$select = new QSelect();
        
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
        
        $iden = $this->authservice->getIdentity();
        
		if($kitchenscreen!='all'){
			$select->where("fk_kitchen_code = $kitchenscreen");
		}else{
            $select->where->in('fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
		
        $columns = array('order_date',
                'total_orders' => new Expression("COUNT(pk_order_no)"),
            );

        if($thirdparty_type  == 0){ // thirdparty delivery 
            $selectColumn = 'tp_delivery_charges'; $whereColumn = 'tp_delivery';
        }else{
            $selectColumn = 'tp_aggregator_charges'; $whereColumn = 'tp_aggregator';
        }
        $select->join('third_party', 'third_party.third_party_id = orders.'.$whereColumn, array('third_party' => 'name'));
        $columns['total_amount'] = new Expression("SUM($selectColumn)");
        
        $select->where("$whereColumn IS NOT NULL");
        $select->where("$whereColumn != 0");
       
        $select->where("orders.order_status != 'Cancel' ");

        if($thirdparty_option){
            $select->where("$whereColumn = $thirdparty_option");
            $select->group('order_date');
        }else{
            $select->group(new Expression($whereColumn.',order_date'));
        }
         
        $select->columns($columns);
        
        if($order_by && $order ){
            $select->order($order_by . ' ' . $order);
        }
        
        return $select;
    }
    
	public function invoiceAction()
	{
		$utility = Utility::getInstance();

		$kitchen_data=$this->getOrderTable()->getKitchenKitchenScreen();
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		$request = $this->getRequest();
		$iden = $this->authservice->getIdentity();
		$search_form = new FilterForm($this->getServiceLocator());
		if ($request->isPost()) {
			if($request->getPost('filter_year') && $request->getPost('filter_month')){
				$search_form->setMonthYear($request->getPost('filter_year'),$request->getPost('filter_month'));
			}
		}
		$search_form->getForm();

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		$invoices = "";
		
		if ($request->isPost()) {
			
			$search_form->setData($request->getPost());
		
			$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
			
			$invoices = $this->getReportTable()->getInvoices($this->request->getPost(),false,false,$itemsPerPage);
		
			$params = $this->params()->fromPost();
			
			if(isset($params['subaction'])){
			
				$invoices_send = $this->getReportTable()->getInvoices($this->request->getPost(),false,false,$itemsPerPage,null,$purpose='export');
				
				//echo "<pre>"; print_r($invoices_send); die;
				
/* 				foreach($invoices_send as $key => $val) {
					$invoices_send[$key]['actual_invoice_amount'] = $utility->getLocalCurrency($val['actual_invoice_amount']);
					$invoices_send[$key]['invoice_amount'] = $utility->getLocalCurrency($val['invoice_amount']);
					$invoices_send[$key]['discounted_amount'] = $utility->getLocalCurrency($val['discounted_amount']);
					$invoices_send[$key]['tax'] = $utility->getLocalCurrency($val['tax']);
					$invoices_send[$key]['delivery_charges'] = $utility->getLocalCurrency($val['delivery_charges']);
					$invoices_send[$key]['service_charges'] = $utility->getLocalCurrency($val['service_charges']);
					$invoices_send[$key]['amount_paid'] = $utility->getLocalCurrency($val['amount_paid']);
					$invoices_send[$key]['amount_due'] = $utility->getLocalCurrency($val['amount_due']);
					$invoices_send[$key]['current_amount_paid'] = $utility->getLocalCurrency($val['current_amount_paid']);
					$invoices_send[$key]['amount'] = $utility->getLocalCurrency($val['amount']);
					$invoices_send[$key]['discount'] = $utility->getLocalCurrency($val['discount']);
				} */
		
				switch($params['subaction']){

					case "print":					
					case "export":
						
							/*return $this->forward()->dispatch('Admin\Controller\Report', array(
					 	'printData' => $invoices_send,
						'service' => $params['service'],
						'action' => 'printReport',
						'subaction' => $params['subaction'],
						'minDate' => $request->getPost('minDate'),
						'maxDate'	=> $request->getPost('maxDate')
						)); */
						$formData = $request->getPost();
						$formData['export_type'] = 'pdf';
						$formData['selected_columns'] = 'invoice';
						return $this->forward()->dispatch('Admin\Controller\Report', array(
								'printData' => $invoices_send,
								'action' => 'printReport',
								'formData' =>$formData
						));
						break;	
					case "xls": $formData = $request->getPost();
						$formData['export_type'] = 'xls';
						$formData['selected_columns'] = 'invoice';
						return $this->forward()->dispatch('Admin\Controller\Report', array(
								'printData' => $invoices_send,
								'action' => 'printReport',
								'formData' =>$formData
						));
						break;
					case "search";
					break;

				}
			}

		}else {
			//$invoices = $this->getReportTable()->getInvoices();
			//echo '<pre>';print_r($invoices);exit;
		}

	
		$this->layout()->setVariables(array('page_title'=>"Invoice & Collection Report",'breadcrumb'=>"Invoice & Collection Report"));
		
		return new ViewModel(array(
				'paginator' => $invoices ,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'filter'	=> $search_form,
				'kitchen_data' =>$kitchen_data				
		));

	}
        
	public function getReportTable()
	{
		if (!$this->reporttable)
		{
			$sm = $this->getServiceLocator();
			$this->reporttable = $sm->get('QuickServe\Model\ReportTable');
		}
		return $this->reporttable;
	}

	/**
	 * Print report as list or export it in pdf format.
	 */
	
	public function printReportAction(){
		
		$utility = Utility::getInstance();
		
		ini_set("max_execution_time", 0);
		ini_set('memory_limit', '500M');
		$data = $this->params()->fromRoute('printData'); 
//        /dd($data);
		$taxinfo = $this->params()->fromRoute('taxinfo'); 

		$totalgross = 0;
		$totalnet = 0;
		$totaldiscount = 0;
		$taxarray = $this->params()->fromRoute('taxarray');
		$formData = $this->params()->fromRoute('formData');
		$config = $this->getServiceLocator()->get("config");
		/***/
		
		$totaltaxarr = array();
		$salestax_summary = array();
		
		if($formData['selected_columns']=='salestax'){
			//dd($data);
			//dd($taxinfo);
			//dd($taxarray);

			foreach ($data as $key => $val) {
				
				/*
				$taxinfo = $this->getReportTable()->getTaxInfo($val['pk_order_no'],$taxarray);
				foreach ( $taxarray as $k1 => $v1)
				{
					//array_push($neelam, $value1)
					if(!isset($totaltaxarr[$v1['tax_name']])){
						$totaltaxarr[$v1['tax_name']]=0;
					}
							
					foreach ($taxinfo as $k2 => $v2)
					{
						if($v1['tax_name']==$k2){
							$totaltaxarr[$v1['tax_name']]+=$v2;
						}
					}
				}
				*/

				$totalgross = $totalgross + $val['grossamount'];
				$totalnet = $totalnet + $val['netamount'];
				$totaldiscount = $totaldiscount + $val['applied_discount'];
			}

			foreach($taxarray as $tak => $tav) {

				if(!isset($totaltaxarr[$tav['tax_name']])) {
					$totaltaxarr[$tav['tax_name']] = 0.00;
				}
				if (is_array($values) || is_object($values))
                {
                    foreach($taxinfo as $key => $value) {
                        if(key($value) == $tav['tax_name']) {
                            $totaltaxarr[$tav['tax_name']] += $value[key($value)];
                        }
                    }
                }
			}			

			//dd($totaltaxarr);	

			foreach( $data as $k => $v ) {
				foreach( $data[$k] as $k1 => $v1 ) {
					foreach($totaltaxarr as $tk => $tv ) {
						if($k1 == $tk) {
							$data[$k][$tk]= $v1;
						}						
					}
				}
			}

			//echo "<pre>"; print_r($data);die; 
			
			$salestax_summary['totalgross'] = $totalgross;
			$salestax_summary['totalnet'] = $totalnet;
			$salestax_summary['totaldiscount'] = $totaldiscount;
			$salestax_summary['taxarra'] = $totaltaxarr;
			$salestax_summary['tax'] = $taxarray;
			
			/***/
			
			$selected_columns= array();
			
			$tax = $this->getTaxTable()->fetchAll();
			
			$taxname = array();
			
			foreach ($tax->toArray() as $key=> $val)
			{
				$taxname[] = $val['tax_name'];
			}
		
		}
		
		if(isset($formData['selected_columns']) && $formData['selected_columns']=='salestax' )
		{
			$selected_columns = array('pk_order_no','customer_name','product_name','grossamount','netamount','applied_discount','delivery_charges');
			$selected_columns = array_merge($selected_columns, $taxname);
			//echo "<pre>";print_r($selected_columns);die;
		}
		elseif(isset($formData['selected_columns']) && $formData['selected_columns']=='invoice' )
		{
			$selected_columns = array('invoice_no','cust_name','invdate','due_date','actual_invoice_amount','discounted_amount','tax','invoice_amount','amount_paid','amount_due','status');
				
		}
		elseif(isset($formData['selected_columns']) &&  $formData['selected_columns']=='customer')
		{
			$selected_columns = array('customer_name','ship_address','phone','email_address','city_name','registered_on','source','order_date','order_menu');
		}
		elseif(isset($formData['selected_columns']) && $formData['selected_columns']!='wallet'){
			$selected_columns = explode(',',$formData['selected_columns']);
			//array_unshift($selected_columns,'pk_order_no');
		}
		elseif(isset($formData['selected_columns']) &&  $formData['selected_columns']=='wallet')
		{
				
			$selected_columns = array('pk_customer_code','customer_name','phone','email_address','avail_bal','lockedamt','total_bal');//add email id field by ashwini
		}
		else{
			//$selected_columns = array('pk_order_no','customer_name','phone','location_name','product_name','amount','order_status','delivery_status','order_date');
			$selected_columns = array('order_no','customer_name','phone','location_name','product_name','amount','order_status','delivery_status','order_date');
            $session_setting = new Container("setting");
            $setting = $session_setting->setting;
                                
            if(array_key_exists('GLOBAL_DELIVERY_TYPE', $setting) && (strpos($setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ){
               $selected_columns[] = 'delivery_type';
            }
		}
		
        if(array_key_exists('status',$formData)){
            $selected_columns = explode(',',$formData['selected_columns']);
        }
		switch($formData['export_type'])
		{
			case "pdf":
                            
				$model = new PdfModel();
				$model->setOption('fileName', 'invoice-45');
				$model->setOption('paperSize', 'A4');
				$model->setOption('paperOrientation', 'portrait');
				$sm = $this->getServiceLocator();
				$config = $sm->get('config');
				$variables = array();
				
				$variables['data'] = $data;
				
				$variables['alltax'] = isset($taxarray)?$taxarray:0;
				$variables['salestax_summary'] = isset($salestax_summary)?$salestax_summary:0;
				$variables['root_url'] = $config['root_url'];
				$variables['minDate'] = $formData['minDate'];
				$variables['maxDate'] = $formData['maxDate'];
				$variables['columns'] = $selected_columns;
				$model->setVariables($variables);
                                
				$model->setTemplate('admin/report/templates/'.$formData['service']);   
				return $model;
				break;
	
			case "xls":
                                
				$objPHPExcel = new PHPExcel();
				
				//echo "<pre>"; print_r($data); die;
					
				// Set document properties
		
				$objPHPExcel->getProperties()->setCreator("Fooddialer")
				->setLastModifiedBy("Fooddialer")
				->setTitle("PHPExcel Document")
				->setSubject("Fooddialer Report")
				->setDescription("Report")
				->setKeywords("Fooddialer")
				->setCategory("Fooddialer");

				$count = count($selected_columns);
				//$range = range('a','z');
				//$last = $range[$count-1];
				//$colRange = range('a',$last);
				//$alpha ='a';
				
				$headerSheet =array();
				foreach ($selected_columns as $key=>$column){
					$colname = str_replace("_", " ", $column);
					$colname = ($colname == 'order no')?'order no':$colname;
					$columnName =  ucwords($colname);
					$headerSheet[] = $columnName;
				}
			
//				echo $highestColumn;die;
				$objPHPExcel->getActiveSheet()->fromArray($headerSheet, '', 'A1');
			
				
				$highestColumn = $objPHPExcel->getActiveSheet()->getHighestColumn();
				
//				echo $highestColumn;die;
				
				for ($col = ord('a'); $col <= ord($highestColumn); $col++)
				{
					$objPHPExcel->getActiveSheet()->getColumnDimension(chr($col))->setAutoSize(true);
				}
				
				$header_range = "a1:{$highestColumn}1";
				$objPHPExcel->getActiveSheet()->getStyle($header_range)->getFont()->setBold(true);
				$objPHPExcel->getActiveSheet()->getDefaultRowDimension()->setRowHeight(15);
				
				$writeData = array();
				
			 	foreach ($data as $key=>$orders) {
			 		$tempArray = array();
					foreach ($selected_columns as $col)
					{
                                            $tempArray[$col] = $orders[$col];
                                            
//						if( $col=='total_bal' || $col=='lockedamt' || $col=='avail_bal' ||  $col=='actual_invoice_amount' || $col=='invoice_amount' || $col=='discounted_amount' || $col=='amount' || $col=='applied_discount' || $col=='amount_paid' || $col=='amount_due' || $col=='current_amount_paid' || $col=='tax' || $col=='delivery_charges' || $col=='service_charges' || $col=='grossamount' || $col=='netamount' || $col=='tp_delivery_charges' || $col=='tp_aggregator_charges'|| $col=='total_amount' ) {
//							$tempArray[$col] = $utility->getLocalCurrency($orders[$col]);
//						}
//						else {
//							
//						}
						
					}
                                       
					$writeData[] = $tempArray;
				 }
                                 				 
				$objPHPExcel->getActiveSheet()->fromArray($writeData, ' ', 'A2');
			
				$objPHPExcel->getActiveSheet()->setTitle($formData['service'].'report');
					
				// Set active sheet index to the first sheet, so Excel opens this as the first sheet
				$objPHPExcel->setActiveSheetIndex(0);
					
				if(isset($formData['export_filename'])){
					$filename = "{$formData['export_filename']}.xls";
				}else{
					$filename = "{$formData['service']}_report.xls";
				}
				
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
				$filenamePath = $_SERVER['DOCUMENT_ROOT'].'/data/tmp/'.$filename;
				$objWriter->save($filenamePath);
			
				header('Content-type: application/vnd.ms-excel');
				
				// It will be called file.xls
				header('Content-Disposition: attachment; filename="'.$filename.'"');
				
				// Write file to the browser
				$objWriter->save('php://output');
			
				$objPHPExcel->disconnectWorksheets();
				unset($objPHPExcel);
				unlink($filenamePath);
				break;

				
            case "quickbook":
					
					$strContent = '';
					$filename = "{$formData['service']}_report_quickbook.csv";
					ob_clean();
					header('Content-type: application/csv');
			    	header('Content-Disposition: attachment; filename=' . $filename);
			    	
			    	
			    	$fp = fopen('php://output', 'w');
			    	$headersArr = array('"Type"','"Num"','"Date"','"Account"','"Amount"');
			    	$headers = implode(',',$headersArr);
			    	$strContent .= "{$headers}\r\n";
			    	$strContent.= ",,,,\r\n";
			    	
			    	foreach($data AS $values){
			    	
			    		$order_date = date('d-m-Y',strtotime($values['order_date']));
			    	//	fputcsv($fp, $values,",",'"',",");
			    		$dataArr= array('"Sales Receipt"','"'.$values['pk_order_no'].'"','"'.$order_date.'"','"Undeposited Funds"','"'.$values['amount'].'"');
			    		//$dataArr= array('"Sales Receipt"',"'.$values['pk_order_no'].'","'.$values['order_date'].'",'"Undeposited Funds"',$values['amount']);
			    		
			    		$strContent.= implode(',',$dataArr);
			    		$strContent.= "\r\n";
			    	}
			    	
			    	fwrite($fp, $strContent);
			    	fclose($fp);
			    	
			    	die;
			  	
            /* 
            case default:
            break; */
            case "print":
                    $view = new ViewModel();
                    $view->setTemplate('admin/report/templates/'.$formData['service']);
                    $variables = array();
                    $variables['data'] = $data;
                    $variables['alltax'] = isset($taxarray)?$taxarray:0;
                    $variables['salestax_summary'] = isset($salestax_summary)?$salestax_summary:0;
                    $variables['root_url'] = $config['root_url'];
                    $variables['minDate'] = $formData['minDate'];
                    $variables['maxDate'] = $formData['maxDate'];
                    $variables['columns'] = $selected_columns;
                    $variables['export_type'] = $formData['export_type']; 
                    //echo "rere";print_r($variables);die;
                    $view->setTerminal(true);
                    $view->setVariables($variables);
                    return $view;
                    break;

            case "tally":

            $filename = "{$formData['service']}_report_tally.xml";
            header('Content-Type: text/xml');
            header('Content-Disposition: attachment; filename=' . $filename);
            $fp = fopen('php://output', 'w');
            $xmlstr.="<ENVELOPE>\r\n";

            foreach ($data as $order){
                $order_date = date('d-m-Y',strtotime($order['order_date']));
                $xmlstr.="<DSPVCHDATE>".$order_date."</DSPVCHDATE>\r\n";
                $xmlstr.="<DSPVCHLEDACCOUNT>Sales</DSPVCHLEDACCOUNT>\r\n";
                $xmlstr.="<DSPVCHTYPE>Sales Receipt</DSPVCHTYPE>\r\n";
                $xmlstr.="<DSPVCHNARR>Rs.".$order['amount']." has been received against order no .".$order['pk_order_no']."</DSPVCHNARR>\r\n";

           }
            $xmlstr.='</ENVELOPE>';
            fwrite($fp, $xmlstr);
            fclose($fp);
            die;
            break;
				
		}
	
	}

	public function testCsvAction(){
		
		$filename = "first.csv";
		$assocDataArray = array(
				array('item' => 'ere', 'cost' => 10000, 'approved by' => 'Joe'),
				array('item' => 'Mt Dew', 'cost' => 1.25, 'approved by' => 'John'),
				array('item' => 'IntelliJ IDEA', 'cost' => 500, 'approved by' => 'James')
		);
		
		ob_clean();
		header('Content-type: application/csv');
    	header('Content-Disposition: attachment; filename=' . $filename);
  	  
		if(isset($assocDataArray[0])){
			$fp = fopen('php://output', 'w');
			fputcsv($fp, array_keys($assocDataArray[0]));
			foreach($assocDataArray AS $values){
				fputcsv($fp, $values);
			}
			fclose($fp);
		}die;
		ob_flush();
	}
	
        public function exportPdfSaveAction()
	{
	    ini_set("max_execution_time", "360");
	    
		$options = array();
		
		$params = $this->params()->fromPost();
	
		$options['POSTFIELDS'] = $this->params()->fromPost();

	
		$config = $this->getServiceLocator()->get("config");
		
		$options['POST'] = 1;
		$url = $config['root_url'].$params['exportUrl'];
	
		$output = $this->getCurlResponse($url,$options);
		
		$filename = $_SERVER['DOCUMENT_ROOT']."/data/test.pdf";
		file_put_contents($filename, $output);
		
		$this->downloadOrderDispatch($filename,"export-data.pdf");
		die;
		
	}
	
	public function getCurlResponse($url,$options=array())
	{
		
		// create curl resource
		//$url = "http://www.fooddialer.com";
		//echo $url;exit;
		$ch = curl_init();
		// set url
		curl_setopt($ch, CURLOPT_URL, $url);
		//return the transfer as a string
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
		
		if(isset($options['POST']) && $options['POST'] == 1)
		{
			curl_setopt($ch, CURLOPT_POST, 1);
			//echo $url;exit;
			if(isset($options['POSTFIELDS'])) {
				//echo '<pre>';print_r($options['POSTFIELDS']);exit;
				$postfields = http_build_query($options['POSTFIELDS']);
				
				curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
			}
			
		}

		if(isset($options['SSL']) && $options['SSL'] == 1)
		{
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 1);
		}
		if(isset($options['PEER']) && $options['PEER'] == 1)
		{
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
		}
		// $output contains the output string
		$output = curl_exec($ch);
		//echo '<pre>';print_r(curl_getinfo($ch));exit;
		//echo $output;die;
		// close curl resource to free up system resources
		
		if(curl_errno($ch)){
		    echo curl_error($ch);
		}
		
		curl_close($ch);
		
		return $output;
	}

	public function generatePDF($outfile,$option,$filename,$service)
	{
		$config = $this->getServiceLocator()->get('config');
		$url = $config['root_url']."report/export-pdf-save";
		//echo $url;exit;
		$options['POST'] = 1;
		//echo "<pre>";print_r($option);exit;
		$options['POSTFIELDS'] = array('data' => $option);
		//echo '<pre>';print_r($options);exit;
		//echo '<pre>';print_r($options);exit;
		$pdfContent = $this->getCurlResponse($url,$options);
		//$pdfContent ='<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd"><html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"></head><body>Look at the HTML source !</body></html>';

		//echo "S".$pdfContent;exit;
		file_put_contents($outfile,$pdfContent);
		$this->downloadOrderDispatch($outfile,$filename);
		//echo file_get_contents($outfile);
	}
	public function downloadOrderDispatch($fileName,$filemainname) {
		if(!is_file($fileName)) {
			echo "Error while generating pdf file";exit;
		}

		$fileContents = file_get_contents($fileName);
        
		/*
		$response = $this->getResponse();
		$response->setContent($fileContents);

		$headers = $response->getHeaders();
		$headers->clearHeaders()
		->addHeaderLine('Content-Type', 'application/pdf')
		->addHeaderLine('Content-Disposition', 'attachment; filename="' . $filemainname . '"')
		->addHeaderLine('Content-Length', strlen($fileContents));
		*/
		
		header("Content-disposition: attachment; filename={$filemainname}");
		header("Content-type: application/pdf");
		readfile($fileName);

	}
	public function ajaxOrderAction()
	{
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$location_code = $this->params()->fromQuery('location_code');
		$menus = $this->params()->fromQuery('menu');

		$iden = $this->authservice->getIdentity();
		
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
			
		$utility = Utility::getInstance();		

		$layout = $this->layout();
		$acl = $layout->acl;

		$viewModel = new ViewModel();

		$loggedUser = $layout->loggedUser;

		$select = new QSelect();
		
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'order_no','1'=>'customer_name','2'=>'phone','3'=>'email_address','4'=>'location_name','5'=>'product_name','6'=>'amount','7'=>'order_status','8'=>'delivery_status','9'=>'order_date', '10' => 'delivery_type');
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
	
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;

		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		
		//$kitchenscreen = $this->params()->fromQuery('kitchenscreen');
		$kitchenscreen = $_SESSION['adminkitchen'];
		if($kitchenscreen!='all')
		{
			$select->where("fk_kitchen_code = $kitchenscreen");
        }else{
            $select->where->in('fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
        
		$select->order($order_by . ' ' . $order);
		
		$orders = $this->getReportTable()->getOrderExportData($this->params()->fromQuery(),$search,$start,$itemsPerPage,true,'view','orders',$select,$page);
		$orders->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $orders->getTotalItemCount();
		$returnVar['recordsFiltered'] = $orders->getTotalItemCount();
		$returnVar['data'] = array();

		foreach($orders as $order){
		
			$arrTmp = array();
			array_push($arrTmp,$order['order_no']);
			array_push($arrTmp,$order['customer_name']);
			array_push($arrTmp,$order['phone']);
			array_push($arrTmp,$order['email_address']);
  
			array_push($arrTmp,$order['location_name']);
			array_push($arrTmp,$order['product_name']);
			array_push($arrTmp,$utility->getLocalCurrency($order['amount']));
			$orderStatus = ($order['order_status']=='Cancel') ? 'Cancelled' : $order['order_status'];
			array_push($arrTmp,$orderStatus);
			//$del_status = ($order['delivery_status'])?$order['delivery_status']:'Pending';
			$del_status = "";
			if(isset($order['delivery_status'])){
				$del_status = $order['delivery_status'];
			}elseif($order['order_status']=='Cancel'){
				$del_status = "Cancelled";
			}else{
				$del_status = 'Pending';
			}
			//$del_status = ;
			array_push($arrTmp,$del_status);
			array_push($arrTmp,$utility->displayDate($order['order_date'],$setting['DATE_FORMAT']));
                        
            if(array_key_exists('GLOBAL_DELIVERY_TYPE', $setting) && (strpos($setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ){

                $strHtml = '';
                if($order['delivery_type'] == 'pickup'){
                    $strHtml = '<button class="smBtn yellowBg has-tip tip-top" data-selector="tooltip-ir5zd4uo2" title="'.$order['delivery_type'].'">
                            <i class="fa fa-map-marker" aria-hidden="true"></i>
                    </button>';
                }else{
                    $strHtml = '<button class="smBtn greenBg has-tip tip-top" data-selector="tooltip-ir5zd4uo0" title="'.$order['delivery_type'].'">
                            <i class="fa fa-truck" aria-hidden="true"></i>
                    </button>';
                }

                array_push($arrTmp, $strHtml);
            }
        	
			array_push($returnVar['data'],$arrTmp);
            //echo $select->getSqlString();die;    
		}
        
		return new JsonModel($returnVar);
	}
	
	public function ajaxSummaryAction(){
		
		$utility = Utility::getInstance();
		
        if (! $this->authservice)
	    {
	        $this->authservice = $this->getServiceLocator()->get('AuthService');
	    }
	
	    $iden = $this->authservice->getIdentity();
                
		$sm = $this->getServiceLocator();
		//$adapt = $sm->get('Write_Adapter');

		$data = $this->params()->fromPost();
		//print_r($data);die;
		$libReport = QSReport::getInstance($sm);

		$sales_summary = $libReport->getReportSummary($data,  array_column($iden->kitchens, 'fk_kitchen_code'));
		
		$summary = array(
				'Total' => $sales_summary[0]['Total'],
				'TotalWithoutCancel' => $sales_summary[0]['TotalWithoutCancel'],
				'Delivered' => $sales_summary[0]['Delivered'],
				'Dispatched' => $sales_summary[0]['Dispatched'],
				'Rejected' => $sales_summary[0]['Rejected'],
				'Pending' => $sales_summary[0]['Pending'],
				'InProcess' => $sales_summary[0]['InProcess'],
				'Cancelled' => $sales_summary[0]['Cancelled'],
				'Preorder' => $sales_summary[0]['Preorder'],
				'Undelivered' => $sales_summary[0]['Undelivered'],
				'Rejected' => $sales_summary[0]['Rejected'],
				'Unbilled' => $sales_summary[0]['Unbilled'],
				'Amount' => $utility->getLocalCurrency($sales_summary[0]['Amount']),
				'TotalSalesQuantity' => $sales_summary[0]['TotalSalesQuantity'],
				'TotalOrderQuantity' => $sales_summary[0]['TotalOrderQuantity'],
				'AmountDue' => $utility->getLocalCurrency($sales_summary[0]['AmountDue'])
		);
		
		return new JsonModel($summary);		
	}
    
	public function ajaxSalesAction()
	{
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}

		$iden = $this->authservice->getIdentity();
		
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
		$type = $this->params()->fromQuery('type');
		$location_code = $this->params()->fromQuery('location_code');
		$menus = $this->params()->fromQuery('menu');
		
		$utility = Utility::getInstance();

		$layout = $this->layout();
		$acl = $layout->acl;

		$viewModel = new ViewModel();

		$loggedUser = $layout->loggedUser;

		$select = new QSelect();

		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'order_no','1'=>'customer_name','2'=>'phone','3'=>'email_address','4'=>'location_name','5'=>'product_name','6'=>'amount','7'=>'order_status','8'=>'delivery_status','9'=>'order_date');
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];

		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		//$kitchenscreen = $this->params()->fromQuery('kitchenscreen');
		$kitchenscreen = $_SESSION['adminkitchen'];
		
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		
		//echo "screen".$kitchenscreen; exit();
		if($kitchenscreen!='all')
		{
			$select->where("fk_kitchen_code = $kitchenscreen");
        }else{
            $select->where->in('fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
		
		if($type == 'Delivered')
		{
			$select->where("delivery_status = '$type' AND order_status = 'Complete'");
		}
		else if($type == 'Rejected')
		{
			$select->where("delivery_status = '$type' AND order_status = 'Rejected'");
		}
		else if($type == 'UnDelivered')
		{
			$select->where("delivery_status = '$type' AND order_status = 'UnDelivered'");
		}else{

			$select->where("delivery_status IN ('Delivered','Rejected','UnDelivered')");
		}
	//	echo $location_code;die;
		if($location_code!='all' && $location_code !='')
		{
			$select->where("location_code = '$location_code'");
		}
		if(!empty($menus)){
			$select->where("order_menu=". "'$menus'");
		} 
		//echo "<pre> select =";print_r($select->getSqlString());die;
		$select->order($order_by . ' ' . $order);
		
		$orders = $this->getReportTable()->getOrderExportData($this->params()->fromQuery(),$search,$start,$itemsPerPage,true,'view','sales',$select,$page);
		
		$orders->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $orders->getTotalItemCount();
		$returnVar['recordsFiltered'] = $orders->getTotalItemCount();
		$returnVar['data'] = array();
	
		
		foreach($orders as $order){

			$arrTmp = array();
			array_push($arrTmp,$order['order_no']);
			array_push($arrTmp,$order['customer_name']);
			array_push($arrTmp,$order['phone']);

            array_push($arrTmp,$order['email_address']);
			array_push($arrTmp,$order['location_name']);
			array_push($arrTmp,$order['product_name']);
			array_push($arrTmp,$utility->getLocalCurrency($order['amount']));
			$orderStatus = ($order['order_status']=='Cancel') ? 'Cancelled' : $order['order_status'];
			array_push($arrTmp,$orderStatus);
			//$del_status = ($order['delivery_status'])?$order['delivery_status']:'Pending';
			$del_status = "";
			if(isset($order['delivery_status'])){
				$del_status = $order['delivery_status'];
			}elseif($order['order_status']=='Cancel'){
				$del_status = "Cancelled";
			}else{
				$del_status = 'Pending';
			}
			//$del_status = ;
			array_push($arrTmp,$del_status);
			array_push($arrTmp,$utility->displayDate($order['order_date'],$setting['DATE_FORMAT']));
			array_push($returnVar['data'],$arrTmp);
				
		}

		return new JsonModel($returnVar);
	}
    
	public function ajaxInvoiceAction()
	{
	/* 	error_reporting(E_ALL);
		ini_set('display_errors','On'); */
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}

		$iden = $this->authservice->getIdentity();
		
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
			
		$utility = Utility::getInstance();
		
		$layout = $this->layout();
		$acl = $layout->acl;

		$viewModel = new ViewModel();

		$loggedUser = $layout->loggedUser;

		$select = new QSelect();
	/* 	$qryParams = $this->params()->fromQuery();
		$qryPost = $this->params()->fromPost();

	    $order_by = $this->params()->fromRoute('order_by') ?
		$this->params()->fromRoute('order_by') : 'pk_customer_code'; */
	 	
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'invoice_no','1'=>'cust_name','2'=>'date','3'=>'due_date','4'=>'actual_invoice_amount','5'=>'discounted_amount','6'=>'tax','7'=>'delivery_charges','8'=>'invoice_amount','9'=>'amount_paid','10'=>'amount_due','11'=>'status');
		 
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
	
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		//$kitchenscreen = $this->params()->fromQuery('kitchenscreen');
		
		$kitchenscreen = $_SESSION['adminkitchen'];
		
		if($kitchenscreen!='all')
		{
			$select->where("invoice.fk_kitchen_code = $kitchenscreen");
        }else{
            $select->where->in('fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
				
		$select->order($order_by . ' ' . $order);
		
		$invoices = $this->getReportTable()->getInvoices($this->params()->fromQuery(),$search,$start,$itemsPerPage,$select);
		
		//echo "params<pre>"; print_r($this->params()->fromQuery()); exit();
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $this->getReportTable()->totalInvoices($this->params()->fromQuery(),$search);
		$returnVar['recordsFiltered'] = $this->getReportTable()->totalInvoices($this->params()->fromQuery(),$search);
		$returnVar['data'] = array();

		//echo "<pre>"; print_r($returnVar); exit();
		//return new JsonModel($orders);
		foreach($invoices as $invoice){
			//echo $customer->customer_name."<br />";
			$arrTmp = array();
			array_push($arrTmp,$invoice['invoice_no']);
			array_push($arrTmp,$invoice['cust_name']);
			array_push($arrTmp,$utility->displayDate($invoice['invdate'],$setting['DATE_FORMAT']));
			array_push($arrTmp,$utility->displayDate($invoice['due_date'],$setting['DATE_FORMAT']));
			array_push($arrTmp,$utility->getLocalCurrency($invoice['actual_invoice_amount']));
			array_push($arrTmp,$utility->getLocalCurrency($invoice['discounted_amount']));
			array_push($arrTmp,$utility->getLocalCurrency($invoice['tax']));
			array_push($arrTmp,$utility->getLocalCurrency($invoice['delivery_charges']));
			array_push($arrTmp,$utility->getLocalCurrency($invoice['invoice_amount']));
			array_push($arrTmp,$utility->getLocalCurrency($invoice['amount_paid']));
			array_push($arrTmp,$utility->getLocalCurrency($invoice['amount_due']));
			$status = ($invoice['status'])?'Paid':'Unpaid';
			array_push($arrTmp,$status);
			array_push($returnVar['data'],$arrTmp);
		}

		return new JsonModel($returnVar);
	}
	
	public function ajaxInvoiceSummaryAction(){
	
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
                        
		$data = $this->params()->fromPost();
                //print_r($data);die;
        if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}

	    $iden = $this->authservice->getIdentity();

		$libReport = QSReport::getInstance($sm);
		
		$invoice_summary = $libReport->getInvoiceSummary(array_column($iden->kitchens, 'fk_kitchen_code'),$data);
	
		return new JsonModel($invoice_summary);
	}

	
	public function exportDataAction(){
        dd("gotcha here....");  
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
   
		$view = new ViewModel();
		$view->setTerminal(true);
		$type = $this->params()->fromQuery('table');
		$exporttype = $this->params()->fromQuery('exporttype');
        $order_type =$this->params()->fromQuery('order_type');
        
		$filterForm = array();
		$filterSring = $this->params()->fromQuery('form');
                
		parse_str($filterSring,$filterForm); 
		$table = ($type=='orders' || $type=="sales") ? "orders" : (($type == 'third_party')?"third_party":"invoice");
		
		$columns = $this->getReportTable()->getColumnsFromTable($table);
        if($table == 'third_party'){
            $columns = array('third_party', 'order_date', 'total_orders','total_amount');
        }
		
		$request = $this->getRequest();
            
       /***************Ashwini********************************/
        $select = new QSelect();
        $select->where("order_status !='Cancel'");
        
		if($request->isPost()){
            $formData = $request->getPost();

            switch ($formData['table']){
                case 'orders':
                        $reportType = $formData['table'];
                        $exportData = $this->getReportTable()->getOrderExportData($formData,false,false,false,false,'export',$reportType,$select);
                        return $this->forward()->dispatch('Admin\Controller\Report', array(
                                'printData' => $exportData,
                                'action'    => 'printReport',
                                'formData'  => $formData
                        ));
                        break;					
                case 'sales':
                        $reportType = $formData['table'];
                        $exportData = $this->getReportTable()->getOrderExportData($formData,false,false,false,false,'export',$reportType);
                        return $this->forward()->dispatch('Admin\Controller\Report', array(
                'printData' => $exportData,
                'action'    => 'printReport',
                'formData'  => $formData
                )); 
                break;

                case 'invoice':
                break;
                case 'third_party':
                    $reportType = $formData['table']; 

                    $arrColumns = array('0' => 'third_party', '1'=>'order_date','2'=>'total_orders','3'=>'total_amount');

                    $select = $this->thirdPartySelectStatement($_SESSION['adminkitchen'],$formData['filter_thirdparty_type'], $formData['filter_thirdparty_options'], $arrColumns[0], 'asc');

                    $thirdPartyReportData = $this->getReportTable()->getThirdPartyData($select, $request->getPost(), false, 'export', false);

                        return $this->forward()->dispatch('Admin\Controller\Report', array(
                        'printData' => $thirdPartyReportData,
                        'action'    => 'printReport',
                        'formData'  =>$formData
                        )); 
                break;
            }
		}
		$view->setVariables(array('columns' =>$columns,'table' => $type,'filterForm'=>$filterForm,'exporttype'=>$exporttype,'order_type'=>$order_type));
		return $view;
	}
	
	/**
	 * Get instance of QuickServe\Model\CustomerTable
	 * @method getCustomerTable()
	 * @return QuickServe\Model\CustomerTable
	 */
	public function getCustomerTable()
	{
		if (!$this->customertable)
		{
			$sm = $this->getServiceLocator();
			$this->customertable = $sm->get('QuickServe\Model\CustomerTable');
		}
		return $this->customertable;
	}	
	
	/**
	 * Get instance of QuickServe\Model\OrderTable
	 *
	 * @return QuickServe\Model\OrderTable
	 */
	public function getOrderTable()
	{
		if (!$this->ordertable)
		{
			$sm = $this->getServiceLocator();
			$this->ordertable = $sm->get('QuickServe\Model\OrderTable');
		}
		return $this->ordertable;
	}
	
	
	/***added by Neelam for wallet report***/
	
	public function walletAction()
	{
		$utility = Utility::getInstance();
		
		$kitchen_data=$this->getOrderTable()->getKitchenKitchenScreen();
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		$request = $this->getRequest();
		$iden = $this->authservice->getIdentity();
		$search_form = new FilterForm($this->getServiceLocator());
		if ($request->isPost()) {
			if($request->getPost('filter_year') && $request->getPost('filter_month')){
				$search_form->setMonthYear($request->getPost('filter_year'),$request->getPost('filter_month'));
			}
		}
		
		$search_form->getForm();
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		$invoices = "";
	
		if ($request->isPost()) {
		
			$params = $this->params()->fromPost();
		
			if(isset($params['subaction'])){
					
				$walletdata = $this->getReportTable()->getwallethistory();
				switch($params['subaction']){
					case "print":
						$formData = $request->getPost();
						$formData['export_type'] = $params['subaction'];
						$formData['selected_columns'] = 'wallet';
						return $this->forward()->dispatch('Admin\Controller\Report', array(
								'printData' => $walletdata,
								'service' => $params['service'],
								'action' => 'printReport',
								'subaction' => $params['subaction'],
								'formData' => $formData
						));
						break;
					case "xls":	
						$formData = $request->getPost();
						$formData['export_type'] = 'xls';
						$formData['selected_columns'] = 'wallet';
						return $this->forward()->dispatch('Admin\Controller\Report', array(
						'printData' => $walletdata,
						'service' => $params['service'],
						'action' => 'printReport',
						'subaction' => $params['subaction'],
						'formData' => $formData
						));
						break;
					case "export":
						$formData = $request->getPost();
						$formData['export_type'] = 'pdf';
						$formData['selected_columns'] = 'wallet';
						return $this->forward()->dispatch('Admin\Controller\Report', array(
						'printData' => $walletdata,
						'service' => $params['service'],
						'action' => 'printReport',
						'subaction' => $params['subaction'],
						'formData' => $formData
						));
						break;
					case "search";
					break;
				
				}
			}
		
		}else {
			//$invoices = $this->getReportTable()->getInvoices();
			//echo '<pre>';print_r($invoices);exit;
		}
		
		$this->layout()->setVariables(array('page_title'=>"Wallet",'breadcrumb'=>"Wallet History"));
	
		return new ViewModel(array(
				'paginator' => $invoices ,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'filter'	=> $search_form,
				'kitchen_data' =>$kitchen_data
		));
		
	}
	
	public function ajaxWalletAction()
	{
	 	if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
	
		$iden = $this->authservice->getIdentity();
	
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
			
		$utility = Utility::getInstance();
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$libCustomer = QSCustomer::getInstance($sm);
		
		$layout = $this->layout();
		$acl = $layout->acl;
	
		$viewModel = new ViewModel();
	
		$loggedUser = $layout->loggedUser;
	
		$select = new QSelect();
		 
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'pk_customer_code','1'=>'customer_name','2'=>'phone','3'=>'email_address','4'=>'status'); //add email_id feild Ashwini
			
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
	
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
	
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
	
		$select->where(
		
				new \Zend\Db\Sql\Predicate\PredicateSet(
						array(
								new \Zend\Db\Sql\Predicate\Operator('pk_customer_code', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('customer_name', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('phone', 'LIKE', '%'.$search.'%'),
                                                              
						),
						// optional; OP_AND is default
						\Zend\Db\Sql\Predicate\PredicateSet::OP_OR
				)
		
		);
		
		$select->order($order_by . ' ' . $order);
	
		$customers  = $this->getReportTable()->getAllCustomer($select,$page);
		
		$customers->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $customers->getTotalItemCount();
		$returnVar['recordsFiltered'] = $customers->getTotalItemCount();
		$returnVar['data'] = array();
		
		$customer_balance =array();
		foreach($customers as $customer){ 
			
			$arrTmp = array();
			$customer_balance[$customer['pk_customer_code']]= $libCustomer->getBal($customer['pk_customer_code'],true,true,true);
			array_push($arrTmp,$customer['pk_customer_code']);
			array_push($arrTmp,$customer['customer_name']);
			array_push($arrTmp,$customer['phone']);
            array_push($arrTmp,$customer['email_address']);//added by Ashwini
			array_push($arrTmp,$utility->getLocalCurrency($customer_balance[$customer['pk_customer_code']]['lockedamt']));
			array_push($arrTmp,$utility->getLocalCurrency($customer_balance[$customer['pk_customer_code']]['avail_bal']));
			$total_bal = $customer_balance[$customer['pk_customer_code']]['avail_bal'] + $customer_balance[$customer['pk_customer_code']]['lockedamt'];
			array_push($arrTmp,$utility->getLocalCurrency($total_bal));
			array_push($returnVar['data'],$arrTmp);
		}
		
		return new JsonModel($returnVar);
	}
	
	
	function salestaxAction()
	{
		//error_reporting(E_ALL);
		//ini_set("display_errors", "on");
		
		$utility = Utility::getInstance();
		
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}

		$layout = $this->layout();
	    $loggedUser = $layout->loggedUser;
	
		$request = $this->getRequest();
		$iden = $this->authservice->getIdentity();
		$search_form = new FilterForm($this->getServiceLocator());
		
		if ($request->isPost()) {
			if($request->getPost('filter_year') && $request->getPost('filter_month')){
				$search_form->setMonthYear($request->getPost('filter_year'),$request->getPost('filter_month'));
			}
		}
		//echo $request->getPost('minDate');exit;
		$search_form->getForm();

		$searchData['minDate'] = date("m/d/Y",strtotime("first day of this month"));
		$searchData['maxDate'] = date("m/d/Y",strtotime("last day of this month"));

		$search_form->setData($searchData);
		
		$select = new QSelect();
		$select->test = 1;
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
		$globaltaxapply = $setting['GLOBAL_APPLY_TAX'];
		$globaltaxmethod = $setting['GLOBAL_TAX_METHOD'];
		$tax = $this->getTaxTable()->fetchAll();
		$taxarray = $tax->toArray();
		$paged = '';
		
		$request = $this->getRequest();
		if ($request->isPost()) {

			
			$params = $this->params()->fromPost();

			if(isset($params['subaction'])){
			
				//$orders_send = $this->getReportTable()->getSales($this->request->getPost(),false);
			
				if($params['subaction']=='print' || $params['subaction']=='quickbook' || $params['subaction']=='tally'){
					$purpose = 'export';
				}else{
					$purpose ='view';
				}

				$kitchens = array_column($loggedUser->kitchens, 'fk_kitchen_code');
			
				$salestaxdata = $this->getReportTable()->getSalesTaxInfo($this->request->getPost(),false,$select,$paged,$kitchens);
				
				$arr_order_no = array();
				//$arr_tax_id = array();

				foreach ($salestaxdata as $key => $value) {
					array_push($arr_order_no, $value['pk_order_no']);
				}

				$arr_order_no_csv = implode(",", $arr_order_no);

				//dd($arr_order_no_csv);
                if(!empty($arr_order_no_csv)){
                    $taxinfo = $this->getReportTable()->getTaxInfo($arr_order_no_csv,$taxarray,'export');
                }
				//dd($salestaxdata);

				foreach($salestaxdata as $key => $val)
				{
					foreach($taxinfo as $tk=>$tv){
						if($val['pk_order_no'] == $tk) {
							$salestaxdata[$key] = array_merge($salestaxdata[$key],$tv);
						}
					}
				}

				//dd($salestaxdata);
				//echo "<pre>"; print_r($taxarray); die;
				
				switch($params['subaction']){
			
					case "tally":
					case "quickbook":
					case "print":
						$formData = $request->getPost();
						$formData['export_type'] = $params['subaction'];
						$formData['selected_columns'] = 'salestax';
						return $this->forward()->dispatch('Admin\Controller\Report', array(
								'printData' => $salestaxdata,
								'action' => 'printReport',
								'formData' =>$formData , 
								'taxarray' => $taxarray
								
						));
						break;
					case "xls":
							$formData = $request->getPost();
							$formData['export_type'] = 'xls';
							$formData['selected_columns'] = 'salestax';
							return $this->forward()->dispatch('Admin\Controller\Report', array(
									'printData' => $salestaxdata,
									'service' => $params['service'],
									'action' => 'printReport',
									'subaction' => $params['subaction'],
									'minDate' => $request->getPost('minDate'),
									'maxDate'	=> $request->getPost('maxDate'),
									'formData' =>$formData,
									'taxarray' => $taxarray,
									'taxinfo' => $taxinfo
							));
							break;
					case "export":
						$formData = $request->getPost();
						$formData['export_type'] = 'pdf';
						$formData['selected_columns'] = 'salestax';
						return $this->forward()->dispatch('Admin\Controller\Report', array(
						'printData' => $salestaxdata,
						'service' => $params['service'],
						'action' => 'printReport',
						'subaction' => $params['subaction'],
						'minDate' => $request->getPost('minDate'),
						'maxDate'	=> $request->getPost('maxDate'),
						'formData' =>$formData ,
						'taxarray' => $taxarray
						));
						break;
					case "search";
					break;
			
				}
			}
		}
		$layoutviewModel = $this->layout();
		$acl = $layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$this->layout()->setVariables(array('page_title'=>"Sales Tax Report",'breadcrumb'=>"Sales Tax Report"));
		return new ViewModel(array(
				/* 'paginator' => $orders,*/
				'acl' => $acl,
				'loggedUser' => $loguser,
				'alltax' => $taxarray,
				'filter'	=> $search_form,
				'alltax'  => $taxarray,
				/*'kitchen_data' =>$kitchen_data,
				'location_data'=>$location_data,
				'menus'   => $menus */
		));
		
	}
	
	function ajaxSalestaxAction()
	{

		//error_reporting(E_ALL);
		//ini_set('display_errors', 'On');		

		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
			
		$iden = $this->authservice->getIdentity();

		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
			
		$kitchenscreen = $_SESSION['adminkitchen'];
		$globaltaxapply = $setting['GLOBAL_APPLY_TAX'];
		$globaltaxmethod = $setting['GLOBAL_TAX_METHOD'];
		
		$utility = Utility::getInstance();
		
		$layout = $this->layout();
		$acl = $layout->acl;
		
		$viewModel = new ViewModel();
		
		$loggedUser = $layout->loggedUser;
		
		$select = new QSelect();
		
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'pk_order_no','1'=>'customer_name','2'=>'product_name','3'=>'grossamount','4'=>'applied_discount','5'=>'netamount','6'=>'tax','7'=>'tax','8'=>'delivery_charges');
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
		
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		
		//$kitchenscreen = $this->params()->fromQuery('kitchenscreen');
		$tax = $this->getTaxTable()->fetchAll();
		$taxarray = $tax->toArray();
		
		if($kitchenscreen!='all')
		{
			$select->where("fk_kitchen_code = $kitchenscreen");
        }else{
            $select->where->in('fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
		
		$totalgross = 0;
		$totalnet = 0;
		
		$select->order($order_by . ' ' . $order);
		
//		$orders = $this->getReportTable()->getSalesTaxInfo($this->params()->fromQuery(),$search,$select,$page,$globaltaxapply,$globaltaxmethod);
		$orders = $this->getReportTable()->getSalesTaxInfo($this->params()->fromQuery(),$search,$select,$page, array_column($iden->kitchens, 'fk_kitchen_code'));
		
		 $orders->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
//		dd($orders);
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $orders->getTotalItemCount();
		$returnVar['recordsFiltered'] = $orders->getTotalItemCount();
		$returnVar['data'] = array(); 
		//echo "saf";
		$count=0;
		foreach($orders as $order){
			
			$taxinfo = $this->getReportTable()->getTaxInfo($order['pk_order_no'],$taxarray);

			$arrTmp = array();
			array_push($arrTmp,$order['pk_order_no']);
		
			array_push($arrTmp,$order['customer_name']);
			array_push($arrTmp,$order['product_name']);
			array_push($arrTmp,$utility->getLocalCurrency($order['grossamount']));
			
			$totalgross = $totalgross + $order['grossamount'];
			
			array_push($arrTmp,$utility->getLocalCurrency($order['applied_discount']));
			array_push($arrTmp,$utility->getLocalCurrency($order['netamount']));
			foreach($taxinfo as $tk=>$tv){
				//$arrTmp[$tk]=$tv;
				array_push($arrTmp,$utility->getLocalCurrency($tv));
			}
			array_push($arrTmp,$utility->getLocalCurrency($order['delivery_charges']));

			$count++;
			array_push($returnVar['data'],$arrTmp);
		} 
		
		//echo $count;exit;
		
		return new JsonModel($returnVar);
	}
	
	/**
	 * Get instance of QuickServe\Model\TaxTable
	 *
	 * @return QuickServe\Model\TaxTable
	 *
	 */
	public function getTaxTable()
	{
        $taxtable=$this->taxtable;
		if (!$taxtable)
		{
			$sm = $this->getServiceLocator();
			$taxtable = $sm->get('QuickServe\Model\TaxTable');
		}
		return $taxtable;
	}
	
	public function ajaxSalestaxSummaryAction()
	{
        
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
        //echo "<pre> data = ";print_r($_SESSION);die;
        $iden = $this->authservice->getIdentity();
        
		$utility = Utility::getInstance();
		
		$data = $this->params()->fromPost();

		$orders = $this->getReportTable()->getSalesTaxInfo($data, null, null, null, array_column($iden->kitchens, 'fk_kitchen_code'));
		$totalgross = 0;
		$totalnet = 0;
		$totaldiscount = 0;
		$tax = $this->getTaxTable()->fetchAll();
		$taxarray = $tax->toArray();
		//dd($taxarray);	
	
		$totaltaxarr = array();
		$salestax_summary = array();

		$arr_order_no = array();
		//$arr_tax_id = array();

		foreach ($orders as $key => $value) {
			array_push($arr_order_no, $value['pk_order_no']);
		}

		$arr_order_no_csv = implode(",", $arr_order_no);
        
		if(!empty($arr_order_no_csv)) {
			$taxinfo = $this->getReportTable()->getTaxInfo($arr_order_no_csv,$taxarray,'export');	
		}

		//dd($taxinfo);		

		foreach ($orders as $key => $val)
		{
			
			$totalgross = $totalgross + $val['grossamount'];
			$totalnet = $totalnet + $val['netamount'];
			$totaldiscount = $totaldiscount + $val['applied_discount'];

		}

		$taxnames = array();

		foreach($taxarray as $key => $value) {
			array_push($taxnames, $value['tax_name']);
		}
        
		//dd($taxnames);
        if(!empty($taxnames) && !empty($taxinfo)){
            foreach($taxnames as $taxname) {
                foreach($taxinfo as $tkey => $tvalue) {
                    if(array_key_exists($taxname, $tvalue)) {
                        //echo "<pre>"; print_r($tvalue[$taxname]);
                        $totaltaxarr[$taxname] += $tvalue[$taxname];
                    }
                }

            }
        }
		$salestax_summary['totalgross'] = $utility->getLocalCurrency($totalgross);
		$salestax_summary['totalnet'] = $utility->getLocalCurrency($totalnet);
		$salestax_summary['totaldiscount'] = $utility->getLocalCurrency($totaldiscount);
 		$salestax_summary['taxarra'] = array(
				'GST' => $utility->getLocalCurrency($totaltaxarr['GST']),
 				'VAT' => $utility->getLocalCurrency($totaltaxarr['VAT']),
 				'STService' => $utility->getLocalCurrency($totaltaxarr['STService']),
		); 
		$salestax_summary['tax'] = $taxarray;

		//echo "<pre>";print_r($salestax_summary);die;
	
		return new JsonModel($salestax_summary);
		
	}
	public function customerAction(){
		
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$request = $this->getRequest();
		$iden = $this->authservice->getIdentity();
		$search_form = new FilterForm($this->getServiceLocator());
		
		if ($request->isPost()) {
			if($request->getPost('filter_year') && $request->getPost('filter_month')){
				$search_form->setMonthYear($request->getPost('filter_year'),$request->getPost('filter_month'));
			}
		}
		#echo $request->getPost('minDate');exit;
		$search_form->getForm();
		
		$select = new QSelect();
		$select->test = 1;
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
		$menus = $setting['MENU_TYPE'];
		$paged = '';
		
		$request = $this->getRequest();
		
		if ($request->isPost()) {
			$params = $this->params()->fromPost();
			
			if(isset($params['subaction'])){

				$customerdata  = $this->getReportTable()->getFirstDeliveredCustomer($select,$paged, $params['minDate'], $params['menu']);
				
				switch($params['subaction']){
			
					case "tally":
					case "quickbook":
					case "print":
						$formData = $request->getPost();
						$formData['export_type'] = $params['subaction'];
						$formData['selected_columns'] = 'customer';
						return $this->forward()->dispatch('Admin\Controller\Report', array(
								'printData' => $customerdata,
								'action' => 'printReport',
								'formData' =>$formData , 
								
						));
						break;
			
					case "export":
						$formData = $request->getPost();
						$formData['export_type'] = $params['exportType'];
						$formData['selected_columns'] = 'customer';
						return $this->forward()->dispatch('Admin\Controller\Report', array(
						'printData' => $customerdata,
						'service' => $params['service'],
						'action' => 'printReport',
						'subaction' => $params['subaction'],
						'formData' =>$formData ,
					
						));
						break;
					case "search";
					break;
			
				}
			}
		}
		$layoutviewModel = $this->layout();
		$acl = $layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$this->layout()->setVariables(array('page_title'=>"New customer report",'breadcrumb'=>"New customer report"));
		return new ViewModel(array(
				'acl' => $acl,
				'loggedUser' => $loguser,
				'filter'	=> $search_form,
				'menus'		=> $menus,
		));
	}

	public function ajaxCustomerAction(){
	
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$location_code = $this->params()->fromQuery('location_code');
		$menus = $this->params()->fromQuery('menu');
		$minDate = $this->params()->fromQuery('minDate');
		//echo "<pre>"; print_r($minDate); die;
		
		$iden = $this->authservice->getIdentity();
		
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
			
		$utility = Utility::getInstance();
		
		$layout = $this->layout();
		$acl = $layout->acl;
		
		$viewModel = new ViewModel();
		
		$loggedUser = $layout->loggedUser;
		
		$select = new QSelect();
		
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'customer_name','1'=>'ship_address','2'=>'phone','3'=>'email_address','4'=>'registered_on','5'=>'source','6'=>'order_status','7'=>'delivery_status');
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
		
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		
		$kitchenscreen = $_SESSION['adminkitchen'];
		
		if($kitchenscreen!='all')
		{
			$select->where("fk_kitchen_code = $kitchenscreen");
		}
		$select->order($order_by . ' ' . $order);
		
		$custinfo = $this->getReportTable()->getFirstDeliveredCustomer($select,$page,$minDate,$menus);
		
		 $custinfo->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $custinfo->getTotalItemCount();
		$returnVar['recordsFiltered'] = $custinfo->getTotalItemCount();
		$returnVar['data'] = array();

		foreach($custinfo as $custdata){
			$arrTmp = array();
			
			array_push($arrTmp,$custdata['customer_name']);
			array_push($arrTmp,$custdata['ship_address']);
			array_push($arrTmp,$custdata['phone']);
            array_push($arrTmp,$custdata['email_address']);
			
			array_push($arrTmp,$custdata['email_address']);
			
			array_push($arrTmp,$utility->displayDate($custdata['registered_on'],$setting['DATE_FORMAT']));
			//array_push($arrTmp,$custdata['registered_on']);
			array_push($arrTmp,$custdata['source']);
			array_push($arrTmp,$utility->displayDate($custdata['order_date'],$setting['DATE_FORMAT']));
			array_push($arrTmp,$custdata['order_menu']);
			array_push($arrTmp,$custdata['order_status']);
			array_push($arrTmp,$custdata['delivery_status']);
			
			array_push($returnVar['data'],$arrTmp);
			
		}
		return new JsonModel($returnVar);
	}
	
	public function ajaxWalletSummaryAction(){
		
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
        
		$utility = Utility::getInstance();
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$libCustomer = QSCustomer::getInstance($sm);
		
		$customers  = $this->getReportTable()->getAllCustomer();
		$totallocked = 0;
		$totalusable = 0;
		$totalavail = 0;

		$customer_balance =array();
		$data = array();
		
		foreach ($customers as $key => $val){
			$customer_balance[$val['pk_customer_code']]= $libCustomer->getBal($val['pk_customer_code'],true,true,true);
			
			$totallocked += $customer_balance[$val['pk_customer_code']]['lockedamt'];
			$totalusable += $customer_balance[$val['pk_customer_code']]['avail_bal'];
			$totalavail  += $customer_balance[$val['pk_customer_code']]['avail_bal'] + $customer_balance[$val['pk_customer_code']]['lockedamt'];
			
		}
		
		$data['totallocked'] = $utility->getLocalCurrency($totallocked);
		$data['totalusable'] = $utility->getLocalCurrency($totalusable);
		$data['totalavail'] = $utility->getLocalCurrency($totalavail);
		
		return new JsonModel($data);	
	}

	/**
	* Daily and periodically collection report of customers.
	*/
	public function customerReceiptAction(){

		//error_reporting(E_ALL);
		//ini_set("display_errors", "on");
		$request = $this->getRequest();

        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
        
		if($request->isPost()){

			$sm = $this->getServiceLocator();

			$tblReport = $this->getReportTable();
			$utility = \Lib\Utility::getInstance();

			$libCommon = QSCommon::getInstance($sm);
			$settings = $libCommon->getSettings();

			$formData = $request->getPost();

			$customerReceipt = $tblReport->getCustomerReceipt(null,null,$formData['fromDate'],$formData['toDate']);

			switch($formData['export_type']){

				case "xls":

					$selected_columns = array("Customer Name","Total Receipt Amount");

					$objPHPExcel = new PHPExcel();
						
					// Set document properties

					$objPHPExcel->getProperties()->setCreator("Fooddialer")
					->setLastModifiedBy("Fooddialer")
					->setTitle("PHPExcel Document")
					->setSubject("Fooddialer Report")
					->setDescription("Report")
					->setKeywords("Fooddialer")
					->setCategory("Fooddialer");

					$count = count($selected_columns);
					
					$headerSheet =array();

					foreach ($selected_columns as $key=>$column){
						$colname = str_replace("_", " ", $column);
						$colname = ($colname == 'order no')?'order no':$colname;
						$columnName =  ucwords($colname);
						$headerSheet[] = $columnName;
					}
				
					
					$writeData = array();
					
				 	foreach ($customerReceipt as $key=>$receipt) {

				 		$receiptDetails = $tblReport->getReceiptDetail($receipt['cust_id'],$formData['fromDate'],$formData['toDate']);

				 		$objPHPExcel->createSheet();

						$objPHPExcel->setActiveSheetIndex($key);

						$activeSheet = $objPHPExcel->getActiveSheet();

						$activeSheet->setTitle(substr($receipt['customer_name'],0,29));

						$rowIndex = 1;
						$rowHeight = 20;

						$activeSheet->fromArray($headerSheet, '', "A{$rowIndex}");
				
						$highestColumn = $activeSheet->getHighestColumn();
						$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

					
						for ($col = ord('a'); $col <= ord($highestColumn); $col++){
							$activeSheet->getColumnDimension(chr($col))->setAutoSize(true);
						}
					
						$header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
						$activeSheet->getStyle($header_range)->getFont()->setBold(true);
					
		 				$tempArray = array();

						if(!empty($receipt['subs_date'])){

							$arrSubsDate = explode("#",$receipt['subs_date']);	
							$strSubsDate = "";

							if(trim($arrSubsDate[0]) == trim($arrSubsDate[1])){
								
								$strSubsDate = $arrSubsDate[0];

							}else{

								foreach ($arrSubsDate as $key => $value) {
									$strSubsDate .= $utility->displayDate($value,$settings['DATE_FORMAT'])." - ";
								}

								$strSubsDate = rtrim($strSubsDate," - ");
							}
							
						}

						$amount = (empty($receipt['total_amt'])) ? '0' : $receipt['total_amt'];

		 				$tempArray[] = $receipt['customer_name'];
		 				//$tempArray[] = $strSubsDate;
		 				$tempArray[] = $utility->getLocalCurrency($amount);

		 				$rowIndex++;
		 				$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

		 				$activeSheet->fromArray($tempArray, ' ', "A{$rowIndex}");

		 				foreach ($tempArray as $tr) {
		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
		 				}

		 				$rowIndex++;
		 				$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

		 				// Loop through data

		 				if(!empty($receiptDetails)){

		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

		 					$detailHeader =array("Date","Description","Payment Mode","Transacted By","Reference No.","Amount");

		 					$activeSheet->fromArray($detailHeader, ' ', "A{$rowIndex}");

		 					$highestColumn = $activeSheet->getHighestColumn();

		 					$header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
		 					$activeSheet->getStyle($header_range)->getFont()->setBold(true);

		 					$arrDetails = array();

		 					foreach ($receiptDetails as $detail) {
		 						$arrTemp = array();
		 						$arrTemp[] = $utility->displayDate($detail['payment_date'],$settings['DATE_FORMAT']);
		 						$arrTemp[] = $detail['description'];
		 						$arrTemp[] = $detail['payment_type'];
		 						$arrTemp[] = $detail['context'];
		 						$arrTemp[] = (empty($detail['reference_no'])) ? " - " : ( (empty($detail['bank_name'])) ? $detail['reference_no'] : strtoupper($detail['bank_name']).": ".$detail['reference_no'] );
		 						$arrTemp[] = $utility->getLocalCurrency($detail['wallet_amount']);

		 						$arrDetails[] = $arrTemp;

		 						//echo "<pre>";print_r($detail);die;
		 					}

		 					//echo "<pre>";print_r($arrDetails);die;

		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

							$activeSheet->fromArray($arrDetails, ' ', "A{$rowIndex}");

							foreach ($arrDetails as $ad) {
		 						$rowIndex++;
		 						$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
		 					}
		 					
		 				}

		 				$rowIndex++;
		 				$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

		 				$receiptOrderDetails = $tblReport->getOrderReceiptDetail($receipt['cust_id'],$formData['fromDate'],$formData['toDate']);

		 				if(!empty($receiptOrderDetails)){

		 					//$rowsIndex = count($arrDetails) + 6;
		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

							////////////////////////////////////////////////////////////////////////
							$detailHeader1 =array("Order No", "Order Menu","Meals","Quantity","Amount","Start Date","End Date","Payment Mode");

							$activeSheet->fromArray($detailHeader1, ' ', "A{$rowIndex}");

							$highestColumn = $activeSheet->getHighestColumn();

		 					$header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
		 					$activeSheet->getStyle($header_range)->getFont()->setBold(true);

							$arrOrderDetails = array();

		 					foreach ($receiptOrderDetails as $orderDetail) {
		 						$arrTemp = array();
		 						$arrTemp[] = $orderDetail['order_no'];
		 						$arrTemp[] = $orderDetail['order_menu'];
		 						$arrTemp[] = $orderDetail['meals'];
		 						$arrTemp[] = $orderDetail['qty'];
		 						$arrTemp[] = $utility->getLocalCurrency(round($orderDetail['net_amount'],2));
		 						$arrTemp[] = $utility->displayDate($orderDetail['start_date'],$settings['DATE_FORMAT']);
		 						$arrTemp[] = $utility->displayDate($orderDetail['end_date'],$settings['DATE_FORMAT']);
		 						$arrTemp[] = $orderDetail['payment_mode'];

		 						$arrOrderDetails[] = $arrTemp;

		 					}

		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

		 					$strRowIndex = 'A'.($rowIndex);

							$activeSheet->fromArray($arrOrderDetails, ' ', $strRowIndex);

							foreach ($arrOrderDetails as $aod) {
		 						$rowIndex++;
		 						$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
		 					}

		 				}

						
					}
				
					
					//$objPHPExcel->getActiveSheet()->setTitle($formData['service'].'report');
						
					// Set active sheet index to the first sheet, so Excel opens this as the first sheet
					$objPHPExcel->setActiveSheetIndex(0);
						
					$filename = "{$formData['service']}_report.xls";
					$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
					$filenamePath = $_SERVER['DOCUMENT_ROOT'].'/data/tmp/'.$filename;
					$objWriter->save($filenamePath);
				
					header('Content-type: application/vnd.ms-excel');
					
					// It will be called file.xls
					header('Content-Disposition: attachment; filename="'.$filename.'"');
					
					// Write file to the browser
					$objWriter->save('php://output');
				
					$objPHPExcel->disconnectWorksheets();
					unset($objPHPExcel);
					unlink($filenamePath);

				break;

			}
	
		}
		
		$this->layout()->setVariables(array('page_title'=>"Customer Receipts Report",'breadcrumb'=>"Customer Receipts Report"));

	}

	/**
	* Daily and periodically collection report of customers.
	*/
	public function ajxCustomerReceiptAction(){

        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		$sm = $this->getServiceLocator();

		$tblReport = $this->getReportTable();
		$utility = \Lib\Utility::getInstance();

		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();

		$fromDate = $this->params()->fromQuery('from',date("Y-m-d"));
		$toDate = $this->params()->fromQuery('to',date("Y-m-d"));

		//echo $fromDate." ".$toDate;die;

		$select = new QSelect();
		
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'pk_customer_code','1'=>'customer_name','2'=>'total_amt');
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
		
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";

		$select = new QSelect();
		$select->order($order_by . ' ' . $order);
		
		if(!empty($search)){
			$select->where(" ( customer_name LIKE '%$search%' ) ");
		}

		$customerReceipt = $tblReport->getCustomerReceipt($select,$page,$fromDate,$toDate);
		
		$customerReceipt->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);

		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $customerReceipt->getTotalItemCount();
		$returnVar['recordsFiltered'] = $customerReceipt->getTotalItemCount();
		$returnVar['data'] = array();

		foreach ($customerReceipt as $key => $receipt) {

			$arrTmp = array();

			if(!empty($receipt['subs_date'])){

				$arrSubsDate = explode("#",$receipt['subs_date']);	
				$strSubsDate = "";

				if(trim($arrSubsDate[0]) == trim($arrSubsDate[1])){
					
					$strSubsDate = $arrSubsDate[0];

				}else{

					foreach ($arrSubsDate as $key => $value) {
						$strSubsDate .= $utility->displayDate($value,$settings['DATE_FORMAT'])." - ";
					}

					$strSubsDate = rtrim($strSubsDate," - ");
				}
				
			}
			

			$arrTmp['DT_RowId'] = "row_".$receipt['cust_id'];
			$arrTmp['customer_name'] = $receipt['customer_name'];
			//$arrTmp['meal'] = $receipt['meals'];
			//$arrTmp['order_dates'] = $strSubsDate;
			$arrTmp['amount'] = (empty($receipt['total_amt'])) ? 0 : $utility->getLocalCurrency($receipt['total_amt']);
			
			array_push($returnVar['data'],$arrTmp);
		}

		return new JsonModel($returnVar);

	}

	/**
	* Daily and periodically collection report of customers.
	*/
	public function ajxCustomerReceiptDetailAction(){
		
		$sm = $this->getServiceLocator();
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		$tblReport = $this->getReportTable();
		$utility = \Lib\Utility::getInstance();

		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();

		$cid = $this->params()->fromQuery('c_code',null);
		$fromDate = $this->params()->fromQuery('from',date("Y-m-d"));
		$toDate = $this->params()->fromQuery('to',date("Y-m-d"));

		$receiptDetails = $tblReport->getReceiptDetail($cid,$fromDate,$toDate);
		
		$receiptOrderDetails = $tblReport->getOrderReceiptDetail($cid,$fromDate,$toDate);
		
		$view = new ViewModel(array(
			"receiptDetails"=>$receiptDetails,
			"receiptOrderDetails"=>$receiptOrderDetails,
			"settings"=>$settings
		));
		$view->setTerminal(true);
		
		return $view;

	}
	
	/**
	* Daily and periodically customer account report.
	*/
	public function customerAccountAction(){
         
		$request = $this->getRequest();
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		if($request->isPost()){
			
			$sm = $this->getServiceLocator();
			
			$tblReport = $this->getReportTable();
			$utility = \Lib\Utility::getInstance();

			$libCommon = QSCommon::getInstance($sm);
			$settings = $libCommon->getSettings();

			$formData = $request->getPost();
            
			$customerAccount = $tblReport->getCustomerAccount(null,null,$formData['fromDate'],$formData['toDate']);
			
			switch($formData['export_type']){

				case "xls":

					$selected_columns = array("Customer Name","Receipt Amount","Ordered Amount","Order Delivered Amt","Delivery Amount","Tax Amount","Amount Due","Owned Amount");

					$objPHPExcel = new PHPExcel();
						
					// Set document properties

					$objPHPExcel->getProperties()->setCreator("Fooddialer")
					->setLastModifiedBy("Fooddialer")
					->setTitle("PHPExcel Document")
					->setSubject("Fooddialer Report")
					->setDescription("Report")
					->setKeywords("Fooddialer")
					->setCategory("Fooddialer");

					$count = count($selected_columns);
					
					$headerSheet =array();

					foreach ($selected_columns as $key=>$column){
						$colname = str_replace("_", " ", $column);
						$colname = ($colname == 'order no')?'order no':$colname;
						$columnName =  ucwords($colname);
						$headerSheet[] = $columnName;
					}
				
					
					$writeData = array();
					
				 	foreach ($customerAccount as $key=>$account) {
				 		
				 		$objPHPExcel->createSheet();

						$objPHPExcel->setActiveSheetIndex($key);

						$activeSheet = $objPHPExcel->getActiveSheet();

						$activeSheet->setTitle(substr($account['customer_name'],0,29));

						$rowIndex = 1;
						$rowHeight = 20;

						$activeSheet->fromArray($headerSheet, '', "A{$rowIndex}");
				
						$highestColumn = $activeSheet->getHighestColumn();
						$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

					
						for ($col = ord('a'); $col <= ord($highestColumn); $col++){
							$activeSheet->getColumnDimension(chr($col))->setAutoSize(true);
						}
					
						$header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
						$activeSheet->getStyle($header_range)->getFont()->setBold(true);
					
		 				$tempArray = array();

		 				$tempArray[] = $account['customer_name'];
		 				$tempArray[] = $utility->getLocalCurrency($account['received_amt']);
		 				$tempArray[] = $utility->getLocalCurrency($account['order_amt']);
		 				$tempArray[] = $utility->getLocalCurrency($account['delivered_amt']);
		 				$tempArray[] = $utility->getLocalCurrency($account['delivery_charges_amt']);
		 				$tempArray[] = $utility->getLocalCurrency($account['tax_amount']);
		 				$tempArray[] = $utility->getLocalCurrency($account['due_amt']);
		 				$tempArray[] = $utility->getLocalCurrency($account['ownbycompany_amt']);

		 				$rowIndex++;
		 				$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

		 				$activeSheet->fromArray($tempArray, ' ', "A{$rowIndex}");

		 				$rowIndex++;
		 				$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

		 				// Print Receipts
		 				
		 				//$receiptOrderDetails = $tblReport->getOrderReceiptDetail($cid,$fromDate,$toDate);
		 					
		 				//$receiptCollectionDetails = $tblReport->getCollectionDetail($cid,$fromDate,$toDate);
		 					
		 				$receiptDetails = $tblReport->getReceiptDetail($account['pk_customer_code'],$formData['fromDate'],$formData['toDate']);
		 					
		 				if(!empty($receiptDetails)){

		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

		 					$detailHeader =array("Date","Description","Payment Mode","Transacted By","Reference No.","Amount");

		 					$activeSheet->fromArray($detailHeader, ' ', "A{$rowIndex}");

		 					$highestColumn = $activeSheet->getHighestColumn();

		 					$header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
		 					$activeSheet->getStyle($header_range)->getFont()->setBold(true);

		 					$arrDetails = array();

		 					foreach ($receiptDetails as $detail) {
		 						$arrTemp = array();
		 						$arrTemp[] = $utility->displayDate($detail['payment_date'],$settings['DATE_FORMAT']);
		 						$arrTemp[] = $detail['description'];
		 						$arrTemp[] = $detail['payment_type'];
		 						$arrTemp[] = $detail['context'];
		 						$arrTemp[] = (empty($detail['reference_no'])) ? " - " : ( (empty($detail['bank_name'])) ? $detail['reference_no'] : strtoupper($detail['bank_name']).": ".$detail['reference_no'] );
		 						$arrTemp[] = $utility->getLocalCurrency($detail['wallet_amount']);

		 						$arrDetails[] = $arrTemp;

		 						//echo "<pre>";print_r($detail);die;
		 					}

		 					//echo "<pre>";print_r($arrDetails);die;

		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

							$activeSheet->fromArray($arrDetails, ' ', "A{$rowIndex}");

							foreach ($arrDetails as $ad) {
		 						$rowIndex++;
		 						$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
		 					}
		 					
		 				}


		 				$receiptOrderDetails = $tblReport->getOrderReceiptDetail($account['pk_customer_code'],$formData['fromDate'],$formData['toDate']);

		 				if(!empty($receiptOrderDetails)){

		 					//$rowsIndex = count($arrDetails) + 6;
		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

							////////////////////////////////////////////////////////////////////////
							$detailHeader1 =array("Order No", "Order Menu","Meal (Qty)","Order Amt","Price","Discount","Delivery","Service Charge","Tax","Payment Mode");

							$activeSheet->fromArray($detailHeader1, ' ', "A{$rowIndex}");

							$highestColumn = $activeSheet->getHighestColumn();

		 					$header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
		 					$activeSheet->getStyle($header_range)->getFont()->setBold(true);

							$arrOrderDetails = array();

		 					foreach ($receiptOrderDetails as $orderDetail) {
		 						$arrTemp = array();
		 						$arrTemp[] = $orderDetail['order_no'];
		 						$arrTemp[] = $orderDetail['order_menu'];
		 						$arrTemp[] = $orderDetail['meals']." (".$orderDetail['qty'].") ";
		 						$arrTemp[] = $utility->getLocalCurrency($orderDetail['net_amount']);
		 						$arrTemp[] = $utility->getLocalCurrency(round($orderDetail['price'],2));
		 						$arrTemp[] = $utility->getLocalCurrency(round($orderDetail['discount'],2));
		 						$arrTemp[] = $utility->getLocalCurrency(round($orderDetail['delivery_charges'],2));
		 						$arrTemp[] = $utility->getLocalCurrency(round($orderDetail['service_charges'],2));
		 						$arrTemp[] = $utility->getLocalCurrency(round($orderDetail['tax'],2));
		 						$arrTemp[] = $orderDetail['payment_mode'];

		 						$arrOrderDetails[] = $arrTemp;

		 					}

		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);

		 					$strRowIndex = 'A'.($rowIndex);

							$activeSheet->fromArray($arrOrderDetails, ' ', $strRowIndex);

							foreach ($arrOrderDetails as $aod) {
		 						$rowIndex++;
		 						$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
		 					}

		 				}
		 				
		 				
		 				$receiptCollectionDetails = $tblReport->getCollectionDetail($account['pk_customer_code'],$formData['fromDate'],$formData['toDate']);
		 				
		 				if(!empty($receiptCollectionDetails)){
		 				
		 					//$rowsIndex = count($arrDetails) + 6;
		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
		 				
		 					////////////////////////////////////////////////////////////////////////
		 					$detailHeader1 =array("Invoice No", "Total Amt","Due Amt");
		 				
		 					$activeSheet->fromArray($detailHeader1, ' ', "A{$rowIndex}");
		 				
		 					$highestColumn = $activeSheet->getHighestColumn();
		 				
		 					$header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
		 					$activeSheet->getStyle($header_range)->getFont()->setBold(true);
		 				
		 					$arrCollections = array();
		 				
		 					foreach ($receiptCollectionDetails as $collection) {
		 						
		 						$arrTemp = array();
		 						$arrTemp[] = $collection['invoice_no'];
		 						$arrTemp[] = $utility->getLocalCurrency($collection['invoice_amount']);
		 						$arrTemp[] = $utility->getLocalCurrency($collection['amount_due']);
		 				
		 						$arrCollections[] = $arrTemp;
		 				
		 					}
		 				
		 					$rowIndex++;
		 					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
		 				
		 					$strRowIndex = 'A'.($rowIndex);
		 				
		 					$activeSheet->fromArray($arrCollections, ' ', $strRowIndex);
		 				
		 					foreach ($arrCollections as $aod) {
		 						$rowIndex++;
		 						$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
		 					}
		 				
		 				}
						
					}
				
					
					//$objPHPExcel->getActiveSheet()->setTitle($formData['service'].'report');
						
					// Set active sheet index to the first sheet, so Excel opens this as the first sheet
					$objPHPExcel->setActiveSheetIndex(0);
						
					$filename = "{$formData['service']}_report.xls";
					$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
					$filenamePath = $_SERVER['DOCUMENT_ROOT'].'/data/tmp/'.$filename;
					$objWriter->save($filenamePath);
				
					header('Content-type: application/vnd.ms-excel');
					
					// It will be called file.xls
					header('Content-Disposition: attachment; filename="'.$filename.'"');
					
					// Write file to the browser
					$objWriter->save('php://output');
				
					$objPHPExcel->disconnectWorksheets();
					unset($objPHPExcel);
					unlink($filenamePath);

				break;

			}
	
		}
		
		$this->layout()->setVariables(array('page_title'=>"Customer Accounts Report",'breadcrumb'=>"Customer Accounts Report"));

	}
	
	/**
	 * Daily and periodically collection report of customers.
	 */
	public function ajxCustomerAccountAction(){
	
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		$sm = $this->getServiceLocator();
	
		$tblReport = $this->getReportTable();
		$utility = \Lib\Utility::getInstance();
	
		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();
	
		$fromDate = $this->params()->fromQuery('from',date("Y-m-d"));
		$toDate = $this->params()->fromQuery('to',date("Y-m-d"));
	
		$select = new QSelect();
	
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'pk_customer_code','1'=>'customer_name','2'=>'received_amt','3'=>'order_amt','4'=>'delivered_amt','5'=>'delivery_charges_amt','6'=>'tax_amount','7'=>'due_amt','8'=>'ownbycompany_amt');
	
		$order_by = $arrColumns[$arrOrder[0]['column']];
       
		$order = $arrOrder[0]['dir'];
	
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
        
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
	
		$select = new QSelect();
		$select->order($order_by . ' ' . $order);
		
		if(!empty($search)){
			$select->where(" customer_name LIKE '%$search%' ");
		}
	
		$customerAccount = $tblReport->getCustomerAccount($select,$page,$fromDate,$toDate);
		
		$customerAccount->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
	
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $customerAccount->getTotalItemCount();
		$returnVar['recordsFiltered'] = $customerAccount->getTotalItemCount();
		$returnVar['data'] = array();
	
		foreach ($customerAccount as $key => $account) {
            
			$arrTmp = array();
	
			$arrTmp['DT_RowId'] = "row_".$account['pk_customer_code'];
			$arrTmp['customer_name'] = $account['customer_name'];
			$arrTmp['received_amt'] = $utility->getLocalCurrency($account['received_amt']);
			$arrTmp['order_amt'] = $utility->getLocalCurrency($account['order_amt']);
			$arrTmp['delivered_amt'] = $utility->getLocalCurrency($account['delivered_amt']);
			$arrTmp['delivery_charges_amt'] = $utility->getLocalCurrency($account['delivery_charges_amt']);
			$arrTmp['tax_amount'] = $utility->getLocalCurrency($account['tax_amount']);
			$arrTmp['due_amount'] = $utility->getLocalCurrency($account['due_amt']);
			$arrTmp['ownbycompany'] = $utility->getLocalCurrency($account['ownbycompany_amt']);
				
			array_push($returnVar['data'],$arrTmp);
		}
	
		return new JsonModel($returnVar);
	
	}
	
	
	/**
	 * Customer Account Details 
	 */
	public function ajxCustomerAccountDetailAction(){
	
		$sm = $this->getServiceLocator();
        
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
        
		$tblReport = $this->getReportTable();
		$utility = \Lib\Utility::getInstance();
	
		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();
	
		$cid = $this->params()->fromQuery('c_code',null);
		$fromDate = $this->params()->fromQuery('from',date("Y-m-d"));
		$toDate = $this->params()->fromQuery('to',date("Y-m-d"));
	
		$receiptDetails = $tblReport->getReceiptDetail($cid,$fromDate,$toDate);
	
		$receiptOrderDetails = $tblReport->getOrderReceiptDetail($cid,$fromDate,$toDate);
		
		$receiptCollectionDetails = $tblReport->getCollectionDetail($cid,$fromDate,$toDate);
        
		$view = new ViewModel(array(
				"receiptDetails"=>$receiptDetails,
				"receiptOrderDetails"=>$receiptOrderDetails,
				"receiptCollectionDetails"=>$receiptCollectionDetails,
				"settings"=>$settings
		));
		$view->setTerminal(true);
        
		return $view;
	
	}	
	
	/**
	 * 
	 */
	public function subscriptionsAction(){
		
		$request = $this->getRequest();
		if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		$fromDate = date("Y-m-d",strtotime("first day of this month"));
		$toDate = date("Y-m-d",strtotime("last day of this month"));
		
		if($request->isPost()){
				
			$sm = $this->getServiceLocator();
				
			$tblReport = $this->getReportTable();
			$utility = \Lib\Utility::getInstance();
		
			$libCommon = QSCommon::getInstance($sm);
			$settings = $libCommon->getSettings();
			
			$formData = $request->getPost();

			$fromDate = $this->params()->fromPost('fromDate',date("Y-m-d",strtotime("first day of this month")));
			$toDate = $this->params()->fromPost('toDate',date("Y-m-d",strtotime("last day of this month")));
			$reportType = $this->params()->fromPost('report_type','expired');
			
			$subscriptions = $tblReport->getSubscriptions($select,$page,null,$reportType,$fromDate,$toDate);
			
//			echo "<pre>";print_r($subscriptions);die;
			
			switch($formData['export_type']){
		
				case "xls":
					
					if($formData['report_type']=='meal'){

                        $selected_columns = array("Order No","Customer Name","Customer Phone","Email Address","Menu","Subscribed On","Start Date","End Date","Meals Delivered","Meals Pending","Status");	
					}else{
						$selected_columns = array("Customer Name","Customer Phone","Email Address","Menu","Subscribed On","Start Date","End Date","Meals Delivered","Meals Pending");	
					}
					
		
					$objPHPExcel = new PHPExcel();
		
					// Set document properties
		
					$objPHPExcel->getProperties()->setCreator("Fooddialer")
					->setLastModifiedBy("Fooddialer")
					->setTitle("PHPExcel Document")
					->setSubject("Fooddialer Report")
					->setDescription("Report")
					->setKeywords("Fooddialer")
					->setCategory("Fooddialer");
		
					$count = count($selected_columns);
						
					$headerSheet =array();
		
					foreach ($selected_columns as $key=>$column){
						$colname = str_replace("_", " ", $column);
						$colname = ($colname == 'order no')?'order no':$colname;
						$columnName =  ucwords($colname);
						$headerSheet[] = $columnName;
					}

					$activeSheet = $objPHPExcel->getActiveSheet();
					
					$activeSheet->setTitle("Subscriptions");
					$rowIndex = 1;
					$rowHeight = 20;
					
					$activeSheet->fromArray($headerSheet, '', "A{$rowIndex}");
					
					$highestColumn = $activeSheet->getHighestColumn();
					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
					
						
					for ($col = ord('a'); $col <= ord($highestColumn); $col++){
						$activeSheet->getColumnDimension(chr($col))->setAutoSize(true);
					}
						
					$header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
					$activeSheet->getStyle($header_range)->getFont()->setBold(true);
					
					$rowIndex++;
					
					$writeData = array();
						
					foreach ($subscriptions as $key=>$subscription) {
                        
						$tempArray = array();
						if($formData['report_type']=='meal'){
							$tempArray[] = $subscription['order_no'];
						}
						$tempArray[] = $subscription['customer_name'];
						$tempArray[] = $subscription['phone'];
                                                $tempArray[] = $subscription['email_address'];
						//$tempArray[] = $subscription['customer_name'];
						$tempArray[] = ucfirst($subscription['order_menu']);
						$tempArray[] = $utility->displayDate($subscription['created_date'],$settings['DATE_FORMAT']);
						$tempArray[] = $utility->displayDate($subscription['start_date'],$settings['DATE_FORMAT']);
						$tempArray[] = $utility->displayDate($subscription['end_date'],$settings['DATE_FORMAT']);
						$tempArray[] = $subscription['meals_delivered'];
						$tempArray[] = $subscription['meals_pending'];

						
						if($formData['report_type']=='meal'){
							$tempArray[] = $subscription['status'];
						}
		
						$activeSheet->fromArray($tempArray, ' ', "A{$rowIndex}");
						$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
						$rowIndex++;
					}
		
						
					//$objPHPExcel->getActiveSheet()->setTitle($formData['service'].'report');
		
					// Set active sheet index to the first sheet, so Excel opens this as the first sheet
					$objPHPExcel->setActiveSheetIndex(0);
		
					$filename = "{$formData['service']}_report.xls";
					$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
					$filenamePath = $_SERVER['DOCUMENT_ROOT'].'/data/tmp/'.$filename;
					$objWriter->save($filenamePath);
		
					header('Content-type: application/vnd.ms-excel');
						
					// It will be called file.xls
					header('Content-Disposition: attachment; filename="'.$filename.'"');
						
					// Write file to the browser
					$objWriter->save('php://output');
		
					$objPHPExcel->disconnectWorksheets();
					unset($objPHPExcel);
					unlink($filenamePath);
		
					break;
		
			}
		
		}
		
		$this->layout()->setVariables(array('page_title'=>"Subscription Report",'breadcrumb'=>"Subscription Report "));
		
		return new ViewModel(array("startDate"=>$fromDate,"endDate"=>$toDate));
		 
	}
	
	/**
	 * Subscription list
	 */
	public function ajxSubscriptionAction(){
		
		$sm = $this->getServiceLocator();
		if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		$tblReport = $this->getReportTable();
		$utility = \Lib\Utility::getInstance();
		
		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();
		
		$fromDate = $this->params()->fromQuery('from',date("Y-m-d",strtotime("first day of this month")));
		
		$toDate = $this->params()->fromQuery('to',date("Y-m-d",strtotime("last day of this month")));
		$reportType = $this->params()->fromQuery('report','expired');
		
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'customer_name','1'=>'phone','2'=>'order_menu','3'=>'order_no','4'=>'created_date','5'=>'start_date','6'=>'end_date','7'=>'meals_delivered','8'=>'meals_pending','9'=>'status');
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
		
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		
		$select = new QSelect();
		
		if(!empty($search)){
			$select->where(" o.customer_name LIKE '%$search%' ");
		}
		
		$select->order($order_by . ' ' . $order);
		
		$subscriptions = $tblReport->getSubscriptions($select,$page,null,$reportType,$fromDate,$toDate);
		
		$subscriptions->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$today = date("Y-m-d");
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $subscriptions->getTotalItemCount();
		$returnVar['recordsFiltered'] = $subscriptions->getTotalItemCount();
		$returnVar['data'] = array();
		
		foreach ($subscriptions as $key => $subscription) {
                       
			$arrTmp = array();
		
			$arrTmp['DT_RowId'] = "row_".$subscription['customer_code']."_".$subscription['order_menu'];
			$arrTmp['customer_name'] = $subscription['customer_name'];
			$arrTmp['customer_phone'] = $subscription['phone'];
            $arrTmp['email_address'] = $subscription['email_address'];

			$arrTmp['order_menu'] = ucfirst($subscription['order_menu']);
			$arrTmp['subscription_date'] = $utility->displayDate($subscription['created_date'],$settings['DATE_FORMAT']);
			$arrTmp['start_date'] = $utility->displayDate($subscription['start_date'],$settings['DATE_FORMAT']);
			$arrTmp['end_date'] = $utility->displayDate($subscription['end_date'],$settings['DATE_FORMAT']);
			$arrTmp['meals_delivered'] = $subscription['meals_delivered'];
			$arrTmp['meals_pending'] = $subscription['meals_pending'];
			
			if($subscription['status'] =='Renewed' || $subscription['status'] =='Complete'){
				$arrTmp['status'] = "<span class='active' />".$subscription['status']."</span>";
			}elseif($subscription['status'] =='Expired' || $subscription['status'] =='Pending'){
				$arrTmp['status'] = "<span class='inactive' />".$subscription['status']."</span>";
			}

			$arrTmp['order_no'] = $subscription['order_no'];

			array_push($returnVar['data'],$arrTmp);
		}
		
		return new JsonModel($returnVar);		

	}


	/**
	 * Subscription list
	 */
	public function ajxSubscriptionDetailAction(){

		$sm = $this->getServiceLocator();
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		$tblReport = $this->getReportTable();
		$utility = \Lib\Utility::getInstance();
	
		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();
	
		$cid = $this->params()->fromQuery('c_code',null);
		$fromDate = $this->params()->fromQuery('from',date("Y-m-d"));
		$toDate = $this->params()->fromQuery('to',date("Y-m-d"));
		$menu = $this->params()->fromQuery('menu');

		$select = new QSelect();
		$select->where(array("customer_code"=>$cid,"order_menu"=>$menu));
		$select->order('pk_order_no asc');

		$group = array("customer_code","order_no");
	
		$subscriptionsDetails = $tblReport->getSubscriptions($select,$page,$group,'expired',$fromDate,$toDate);
	
		$view = new ViewModel(array(
			"subscriptionDetails"=>$subscriptionsDetails,
			"settings"=>$settings
		));
		$view->setTerminal(true);
	
		return $view;


	}
	
	/**
	 *
	 */
	public function mealAction(){
	
      	$request = $this->getRequest();
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		$fromDate = date("Y-m-d",strtotime("first day of this month"));
		$toDate = date("Y-m-d",strtotime("last day of this month"));
	
		if($request->isPost()){
	
			$sm = $this->getServiceLocator();
	
			$tblReport = $this->getReportTable();
			$utility = \Lib\Utility::getInstance();
	
			$libCommon = QSCommon::getInstance($sm);
			$settings = $libCommon->getSettings();
				
			$formData = $request->getPost();
				
			$fromDate = $this->params()->fromPost('fromDate',date("Y-m-d",strtotime("first day of this month")));
			$toDate = $this->params()->fromPost('toDate',date("Y-m-d",strtotime("last day of this month")));
			$reportType = $this->params()->fromPost('report_type','expired');
				
			$subscriptions = $tblReport->getSubscriptions($select,$page,null,$reportType,$fromDate,$toDate);
				
			//echo "<pre>";print_r($subscriptions);die;
				
			switch($formData['export_type']){
	
				case "xls":
	
					$selected_columns = array("Customer Name","Customer Phone","Order No","Subscribed Date","Start Date","End Date","Meals Delivered","Meals Pending","Status");
	
					$objPHPExcel = new PHPExcel();
	
					// Set document properties
	
					$objPHPExcel->getProperties()->setCreator("Fooddialer")
					->setLastModifiedBy("Fooddialer")
					->setTitle("PHPExcel Document")
					->setSubject("Fooddialer Report")
					->setDescription("Report")
					->setKeywords("Fooddialer")
					->setCategory("Fooddialer");
	
					$count = count($selected_columns);
	
					$headerSheet =array();
	
					foreach ($selected_columns as $key=>$column){
						$colname = str_replace("_", " ", $column);
						$colname = ($colname == 'order no')?'order no':$colname;
						$columnName =  ucwords($colname);
						$headerSheet[] = $columnName;
					}
	
					$activeSheet = $objPHPExcel->getActiveSheet();
						
					$activeSheet->setTitle("Subscriptions");
					$rowIndex = 1;
					$rowHeight = 20;
						
					$activeSheet->fromArray($headerSheet, '', "A{$rowIndex}");
						
					$highestColumn = $activeSheet->getHighestColumn();
					$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
						
	
					for ($col = ord('a'); $col <= ord($highestColumn); $col++){
						$activeSheet->getColumnDimension(chr($col))->setAutoSize(true);
					}
	
					$header_range = "a{$rowIndex}:{$highestColumn}{$rowIndex}";
					$activeSheet->getStyle($header_range)->getFont()->setBold(true);
						
					$rowIndex++;
						
					$writeData = array();
	
					foreach ($subscriptions as $key=>$subscription) {
	
						$tempArray = array();
	
						$tempArray[] = $subscription['customer_name'];
						$tempArray[] = $subscription['phone'];
						//$tempArray[] = $subscription['customer_name'];
						$tempArray[] = $subscription['order_no'];
						$tempArray[] = $utility->displayDate($subscription['created_date'],$settings['DATE_FORMAT']);
						$tempArray[] = $utility->displayDate($subscription['start_date'],$settings['DATE_FORMAT']);
						$tempArray[] = $utility->displayDate($subscription['end_date'],$settings['DATE_FORMAT']);
						$tempArray[] = $subscription['meals_delivered'];
						$tempArray[] = $subscription['meals_pending'];
						$tempArray[] = $subscription['status'];
	
						$activeSheet->fromArray($tempArray, ' ', "A{$rowIndex}");
						$activeSheet->getRowDimension($rowIndex)->setRowHeight($rowHeight);
						$rowIndex++;
					}
	
	
					//$objPHPExcel->getActiveSheet()->setTitle($formData['service'].'report');
	
					// Set active sheet index to the first sheet, so Excel opens this as the first sheet
					$objPHPExcel->setActiveSheetIndex(0);
	
					$filename = "{$formData['service']}_report.xls";
					$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
					$filenamePath = $_SERVER['DOCUMENT_ROOT'].'/data/tmp/'.$filename;
					$objWriter->save($filenamePath);
	
					header('Content-type: application/vnd.ms-excel');
	
					// It will be called file.xls
					header('Content-Disposition: attachment; filename="'.$filename.'"');
	
					// Write file to the browser
					$objWriter->save('php://output');
	
					$objPHPExcel->disconnectWorksheets();
					unset($objPHPExcel);
					unlink($filenamePath);
	
					break;
	
			}
	
		}
	
		$this->layout()->setVariables(array('page_title'=>"Subscription Report",'breadcrumb'=>"Subscription Report "));
	
		return new ViewModel(array("startDate"=>$fromDate,"endDate"=>$toDate));
			
	}
	
	
	/**
	 * Subscription list
	 */
	public function ajxMealAction(){
	
		$sm = $this->getServiceLocator();
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
		$tblReport = $this->getReportTable();
		$utility = \Lib\Utility::getInstance();
	
		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();
	
		$fromDate = $this->params()->fromQuery('from',date("Y-m-d",strtotime("first day of this month")));
	
		$toDate = $this->params()->fromQuery('to',date("Y-m-d",strtotime("last day of this month")));
		$reportType = $this->params()->fromQuery('report','expired');
	
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'customer_name','1'=>'phone','2'=>'order_menu','3'=>'order_no','4'=>'created_date','5'=>'start_date','6'=>'end_date','7'=>'meals_delivered','8'=>'meals_pending','9'=>'status');
	
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
	
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
	
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : false;
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
	
		$select = new QSelect();
	
		if(!empty($search)){
			$select->where(" o.customer_name LIKE '%$search%' ");
		}
	
		$select->order($order_by . ' ' . $order);
	
		$subscriptions = $tblReport->getSubscriptions($select,$page,null,$reportType,$fromDate,$toDate);
	
		$subscriptions->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
	
		$today = date("Y-m-d");
	
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $subscriptions->getTotalItemCount();
		$returnVar['recordsFiltered'] = $subscriptions->getTotalItemCount();
		$returnVar['data'] = array();
	
		foreach ($subscriptions as $key => $subscription) {
	
			$arrTmp = array();
	
			//$arrTmp['DT_RowId'] = "row_".$subscription['pk_customer_code'];
			$arrTmp['customer_name'] = $subscription['customer_name'];
			$arrTmp['customer_phone'] = $subscription['phone'];
			$arrTmp['order_menu'] = ucfirst($subscription['order_menu']);
			$arrTmp['order_no'] = $subscription['order_no'];
			$arrTmp['subscription_date'] = $utility->displayDate($subscription['created_date'],$settings['DATE_FORMAT']);
			$arrTmp['start_date'] = $utility->displayDate($subscription['start_date'],$settings['DATE_FORMAT']);
			$arrTmp['end_date'] = $utility->displayDate($subscription['end_date'],$settings['DATE_FORMAT']);
			$arrTmp['meals_delivered'] = $subscription['meals_delivered'];
			$arrTmp['meals_pending'] = $subscription['meals_pending'];
				
			if($subscription['status'] =='Renewed' || $subscription['status'] =='Complete'){
				$arrTmp['status'] = "<span class='active' />".$subscription['status']."</span>";
			}elseif($subscription['status'] =='Expired' || $subscription['status'] =='Pending'){
				$arrTmp['status'] = "<span class='inactive' />".$subscription['status']."</span>";
			}
	
			array_push($returnVar['data'],$arrTmp);
		}
	
		return new JsonModel($returnVar);
	
	}	
	
	public function removeOldOrderAction(){
		
		error_reporting(E_ALL);
		ini_set("display_errors", "On");
		
		ini_set("max_execution_time", 0);
		ini_set('memory_limit', '300M');
		
		$sm = $this->getServiceLocator();
		
		$checked = $this->params()->fromQuery("checked",false);
		
		$libCommon = QSCommon::getInstance($sm);
		
		//$strOrders = "'0GH5160316','0GH5160316','0GH5160316','0GH5160316','0GH5160316','0GH5160316','0GH5160316','0GH5160316','0GH5160316','0GH5160316','30P6160316','PVIT160316','9VT9160407','9VT9160407','KODV160316','ZIAP160316','ZIAP160316','TGL9160317','TGL9160317','TGL9160317','TGL9160317','TGL9160317','TGL9160317','TGL9160317','TGL9160317','TGL9160317','TGL9160317','TGL9160317','SPTW160316','SPTW160316','SPTW160316','SPTW160316','XGQL160316','B4IG160321','B4IG160321','B4IG160321','B4IG160321','B4IG160321','B4IG160321','NP1K160326','NP1K160326','NP1K160326','NP1K160326','NP1K160326','NP1K160326','NP1K160326','NP1K160326','NP1K160326','NP1K160326','NP1K160326','NP1K160326','C5NJ160317','C5NJ160317','C5NJ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','7UOQ160317','BKGZ160316','CQ05160316','CQ05160316','HM7X160316','HM7X160316','HM7X160316','HM7X160316','HM7X160316','HM7X160316','HM7X160316','HM7X160316','0WZ8160321','E6T7160321','PJSQ160321','PN9T160321','GBPK160317','GBPK160317','GBPK160317','GBPK160317','GBPK160317','GBPK160317','GBPK160317','GBPK160317','GBPK160317','6C9E160325','6C9E160325','6C9E160325','6C9E160325','6C9E160325','6C9E160325','6C9E160325','6C9E160325','E03W160325','E03W160325','E03W160325','E03W160325','E03W160325','FTN0160316','JG8X160316','JG8X160316','6QDW160316','6QDW160316','6QDW160316','6QDW160316','6QDW160316','6QDW160316','6QDW160316','6QDW160316','8S3H160316','8S3H160316','8S3H160316','8S3H160316','8S3H160316','8S3H160316','8S3H160316','8S3H160316','8S3H160316','8S3H160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','J5MI160316','YVHU160317','YVHU160317','YVHU160317','YVHU160317','YVHU160317','YVHU160317','GEOD160315','GEOD160315','GEOD160315','GEOD160315','GEOD160315','GEOD160315','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','1QW2160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','EQ7F160316','WRD6160316','WRD6160316','WRD6160316','WRD6160316','WRD6160316','WRD6160316','WRD6160316','WRD6160316','WRD6160316','WRD6160316','3BQ9160329','3BQ9160329','3BQ9160329','EQNG160329','EQNG160329','EQNG160329','IITT160329','IITT160329','IITT160329','IITT160329','IITT160329','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GPGM160316','GTPA160316','GTPA160316','GTPA160316','GTPA160316','GTPA160316','GTPA160316','GTPA160316','GTPA160316','6VT1160317','6VT1160317','6VT1160317','6VT1160317','6VT1160317','6VT1160317','6VT1160317','6VT1160317','6VT1160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','P1R0160317','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','MFP9160316','TII0160316','TII0160316','TII0160316','TII0160316','TII0160316','TII0160316','TII0160316','TII0160316','TII0160316','TII0160316','XZU0160316','XZU0160316','XZU0160316','XZU0160316','XZU0160316','XZU0160316','XZU0160316','XZU0160316','XZU0160316','XZU0160316','OREC160315','OREC160315','OREC160315','OREC160315','OREC160315','OREC160315','OREC160315','OREC160315','OREC160315','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','47EM160402','0GL5160317','0GL5160317','0GL5160317','0GL5160317','0GL5160317','9WO1160316','9WO1160316','9WO1160316','9WO1160316','9WO1160316','9WO1160316','9WO1160316','9WO1160316','9WO1160316','9WO1160316','9WO1160316','9WO1160316','AYY7160316','AYY7160316','AYY7160316','AYY7160316','AYY7160316','AYY7160316','AYY7160316','AYY7160316','AYY7160316','AYY7160316','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','JW1D160406','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','9HOK160316','OZS8160402','OZS8160402','OZS8160402','OZS8160402','OZS8160402','OZS8160402','OZS8160402','OZS8160402','OZS8160402','OZS8160402','OZS8160402','OZS8160402','OZS8160402','Z27X160315','Z27X160315','Z27X160315','Z27X160315','Z27X160315','Z27X160315','1HY8160317','1HY8160317','1HY8160317','1HY8160317','1HY8160317','1HY8160317','QUID160319','QR2Y160315','YS6D160329','YS6D160329','YS6D160329','A4KK160331','A4KK160331','A4KK160331','A4KK160331','A4KK160331','PIVD160331','PIVD160331','PIVD160331','PIVD160331','PIVD160331','B5MQ160317','B5MQ160317','B5MQ160317','B5MQ160317','B5MQ160317','B5MQ160317','B5MQ160317','B5MQ160317','B5MQ160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','C5T7160317','XX2J160317','XX2J160317','XX2J160317','XX2J160317','XX2J160317','XX2J160317','XX2J160317','XX2J160317','XX2J160317','XX2J160317','EB3G160317','EB3G160317','EB3G160317','EB3G160317','EB3G160317','EB3G160317','EB3G160317','EB3G160317','EB3G160317','T0PU160315','KZ9G160326','KZ9G160326','KZ9G160326','KZ9G160326','KZ9G160326','KZ9G160326','KZ9G160326','RGHA160326','RGHA160326','RGHA160326','RGHA160326','RGHA160326','RGHA160326','RGHA160326','C18J160328','5BRK160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','JS3R160321','M0GF160321','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','3DRJ160401','4K06160401','4K06160401','4K06160401','4K06160401','4K06160401','51K7160315','D7SJ160401','D7SJ160401','D7SJ160401','D7SJ160401','D7SJ160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','QIT2160401','90RH160315','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','VNS5160404','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','W67S160321','E445160328','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402','Q6WH160402'";	
		
		//$sql = "SELECT order_no , COUNT(*) FROM orders WHERE order_no IN ($strOrders) GROUP BY order_no ";
		
		//$strOrders = "'T69D160412'";
		
		//$adapter = $sm->get("Write_Adapter");
		
		
		$sql = new QSql($sm);
		
		$select = new QSelect();
		$select->from("orders");
		$select->columns(array(
				"pk_order_no",
				"fk_kitchen_code",
				"order_no",
				"order_menu",
				"order_date",
				"customer_name",
				"tax_method",
				'net_amount'=>new Expression(" IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )"),
				'amount'=>new Expression(" SUM(amount) "),
				'delivery_charges'=>new Expression(" SUM(delivery_charges) ")
			)
		);
		
		/*$select->columns(array(
				"total"=>new Expression("COUNT(*)"),
				"order_no",
				//'net_amount'=>new Expression(" IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )"),
				//'amount'=>new Expression(" SUM(amount) "),
			)
		);*/
		
		//$select->where(" order_no IN ($strOrders) ");
		
		$select->where("tax_method='exclusive'");
		
		//$select->where("DATE(created_date) <= '2016-03-31'");
		
		$select->group(array("order_no","order_date"));
		//$select->group(array("order_no"));
		
		$select->order('pk_order_no');
		
		$selectString = $sql->getSqlStringForSqlObject($select);
		
		echo $selectString."<br /><br />";
		
		$orders = $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
		
		$tblOrder = $sm->get('QuickServe\Model\OrderTable');
		
		/*echo $orders->count()." Orders Found <br /> <br />";
		echo "<pre>";print_r($orders->toArray());
		die;*/
		
		echo $orders->count()." Orders Found <br />";//die;
		
		if($orders->count() > 0){
			
			$totalInv = 0;
			$arrInvDel = array();
			
			foreach ($orders as $order){
				
				$adapter->getDriver()->getConnection()->beginTransaction();
				
				try{
				
					echo " <h2> Bill No . ".$order->pk_order_no."</h2><br />";
					
					// get invoice 
					$sel_inv = new QSelect();
					$sel_inv->from("invoice");
					$sel_inv->join("invoice_payments","invoice_id = invoice_ref_id");
					$sel_inv->where(" FIND_IN_SET('{$order->pk_order_no}',order_bill_no) ");
					
					$selectInv = $sql->getSqlStringForSqlObject($sel_inv);
					$invoices = $adapter->query($selectInv, Adapter::QUERY_MODE_EXECUTE);
					
					echo $selectInv."<br />";
					
					$invoicesCnt = $invoices->count();
					
					$totalInv += $invoicesCnt;
					
					if($invoices->count() > 0){
						
						$orderTaxDetails = $tblOrder->getOrderTaxDetails(null,array($order->pk_order_no));
						
			    		$taxes = array();
	
			    		foreach($orderTaxDetails as $tax){
			    			$tempTax = array();
			    			$tempTax['tax_type'] = $tax['tax_type'];
			    			$tempTax['tax_id'] = $tax['tax_ref_id'];
			    			$tempTax['tax'] = $tax['tax_rate'];
			    			$tempTax['priority'] = $tax['tax_priority'];
			    			$tempTax['base_amount'] = $tax['tax_base_amount'];
			    			$taxes[] = $tempTax;
			    		}
	
						echo "<h3> Invoice Found for Bill {$order->pk_order_no}</h3>";
						
						foreach($invoices as $invoice){
							
							if(!in_array($invoice->invoice_no,$arrInvDel)){
								array_push($arrInvDel, $invoice->invoice_no);
							}
							
							//echo "<pre>";print_r($invoice);die;
							
							$arrBills = explode(",",$invoice->order_bill_no);
							
							if(count($arrBills) == 1){
								
								// Delete this invoice and its respective payment details.
								echo "Deleting Invoice Tax: <br />";
								
								$delete_inv_tax = new \Zend\Db\Sql\Delete("invoice_tax_details");
								$delete_inv_tax->where("inv_ref_id = '{$invoice->invoice_id}'");
								
								$deleteInvTax = $sql->getSqlStringForSqlObject($delete_inv_tax);
								echo $deleteInvTax."<br />";
								
								if($checked){
									$adapter->query ( $deleteInvTax, Adapter::QUERY_MODE_EXECUTE );
									echo "Invoice Tax Deleted Successfully <br />";
								}
								
								echo "<br />";
								
								////////////////// Delete payments ///////////////
								echo "Deleting Invoice Payments: <br />";
								$delete_inv_pay = new \Zend\Db\Sql\Delete("invoice_payments");
								$delete_inv_pay->where("invoice_ref_id = '{$invoice->invoice_id}'");
									
								$deleteInvPay = $sql->getSqlStringForSqlObject($delete_inv_pay);
								echo $deleteInvPay."<br /><br />";
								
								if($checked){
									$adapter->query ( $deleteInvPay, Adapter::QUERY_MODE_EXECUTE );
									echo "Invoice Payments Deleted Successfully <br />";
								}
								
								echo "<br />";
								
								////////////////// Delete invoice details  ///////////////
								echo "Deleting Invoice Details: <br />";
								$delete_inv_det = new \Zend\Db\Sql\Delete("invoice_details");
								$delete_inv_det->where("invoice_ref_id = '{$invoice->invoice_id}'");
								
								$deleteInvDet = $sql->getSqlStringForSqlObject($delete_inv_det);
								echo $deleteInvDet."<br /><br />";
								
								if($checked){
									$adapter->query ( $deleteInvDet, Adapter::QUERY_MODE_EXECUTE );
									echo "Invoice Details Deleted Successfully <br />";
								}
								
								echo "<br />";
								
								////////////////// Delete invoice discount  ///////////////
								echo "Deleting Invoice Details: <br />";
								$delete_inv_dis = new \Zend\Db\Sql\Delete("invoice_discount_details");
								$delete_inv_dis->where("inv_ref_id = '{$invoice->invoice_id}'");
									
								$deleteInvDis = $sql->getSqlStringForSqlObject($delete_inv_dis);
								echo $deleteInvDis."<br /><br />";
								
								if($checked){
									$adapter->query ( $deleteInvDis, Adapter::QUERY_MODE_EXECUTE );
									echo "Invoice Discounts Deleted Successfully <br />";
								}
								
								echo "<br />";
								
								////////////////// Delete invoice ///////////////
								echo "Deleting Invoice: <br />";
								$delete_inv = new \Zend\Db\Sql\Delete("invoice");
								$delete_inv->where("invoice_id = '{$invoice->invoice_id}'");
								
								$deleteInv = $sql->getSqlStringForSqlObject($delete_inv);
								echo $deleteInv."<br /><br />";
								
								if($checked){
									$adapter->query ( $deleteInv, Adapter::QUERY_MODE_EXECUTE );
									echo "Invoice Deleted Successfully <br />";
								}
								
								echo "<br />";
								
								
							}else{
								
								// Update invoice and its payment details.
													
								////////////////// Delete invoice details  ///////////////
								echo "Deleting Invoice Details for bills: <br />";
								$delete_inv_det = new \Zend\Db\Sql\Delete("invoice_details");
								$delete_inv_det->where(" invoice_ref_id = '{$invoice->invoice_id}' AND order_bill_no = {$order->pk_order_no} ");
								
								$deleteInvDet = $sql->getSqlStringForSqlObject($delete_inv_det);
								echo $deleteInvDet."<br /><br />";
								
								if($checked){
									$adapter->query ( $deleteInvDet, Adapter::QUERY_MODE_EXECUTE );
									echo "Invoice Details Deleted Successfully <br />";
								}
								
								echo "<br />";
								
								////////////////// Update invoice payment Details  ///////////////
								
								$amount = ( $invoice->actual_invoice_amount -  $order->amount);
								$revisedTaxDetails = $libCommon->calculateTax($amount,$order->tax_method,$taxes,'yes');
								
								$revisedTax = 0;
								
								foreach($revisedTaxDetails as $k => $v){
									
									if($k=='total' || $k=='price'){
										continue;
									}
									
									$revisedTax += $v;
								}
								
								$newTax = $invoice->tax - $revisedTax;
								
								echo "Updating invoice payments for bills: <br />";
								$update_inv_pay = new \Zend\Db\Sql\Update("invoice_payments");
								$update_inv_pay->where(" invoice_ref_id = '{$invoice->invoice_id}'");
								
								$deliveryCharges = $invoice->delivery_charges - $order->delivery_charges;
								
								$invoiceAmount = ($amount + $revisedTax + $deliveryCharges + $invoice->service_charges - $invoice->discounted_amount);
								
								
								$update_inv_pay->set(array(
										'actual_invoice_amount' => $amount,
										'invoice_amount'=> $invoiceAmount,
										'tax'=> $revisedTax,
										'delivery_charges'=> $deliveryCharges,
										'amount_due'=> $invoiceAmount - $invoice->amount_paid
									)
								);
								
								$updateInvPay = $sql->getSqlStringForSqlObject($update_inv_pay);
								echo $updateInvPay."<br /><br />";
								
								if($checked){
									$adapter->query ( $updateInvPay, Adapter::QUERY_MODE_EXECUTE );
									echo "Invoice Payment Updated Successfully <br />";
								}
								
								////////////////// Update invoice tax Details  ///////////////
								echo "Updating invoice tax for bills: <br />";
								foreach ($revisedTaxDetails as $taxId=>$taxAmount){
									
									if($taxId=='total' || $taxId=='price'){
										continue;
									}
									
									$update_inv_tax = new \Zend\Db\Sql\Update("invoice_tax_details");
									$update_inv_tax->where(" inv_ref_id = '{$invoice->invoice_id}' AND tax_ref_id = '{$taxId}'");
									
									$update_inv_tax->set(array(
											'amount' => $taxAmount,
										)
									);
									
									$updateInvTax = $sql->getSqlStringForSqlObject($update_inv_tax);
									echo $updateInvTax."<br /><br />";
									
									if($checked){
										$adapter->query ( $updateInvTax, Adapter::QUERY_MODE_EXECUTE );
										echo "Invoice tax Updated Successfully <br />";
									}
									
								}
								
								$input = array("red", "green", "blue", "yellow");
								
								$keys = array_keys($input,'blue');
								
								array_splice($input, $keys[0],1);
								// $input is now array("red", "green")
								
								//echo "<pre>";print_r($input);die;
								
								////////////////// Update invoice ///////////////
								echo "Updating invoice for bills: <br />";
								$update_inv = new \Zend\Db\Sql\Update("invoice");
								$update_inv->where(" invoice_id = '{$invoice->invoice_id}'");
								
								$arrInvoiceOrderDates = explode(",",$invoice->order_dates);
								$arrBills = explode(",",$invoice->order_bill_no);
								
								$keysDate = array_keys($arrInvoiceOrderDates,$order->order_date);
								$keysBill = array_keys($arrBills,$order->pk_order_no);
								
								array_splice($arrInvoiceOrderDates, $keysDate[0],1);
								array_splice($arrBills, $keysBill[0],1);
								
								$strNewOrderDates = implode(",",$arrInvoiceOrderDates);
								$strNewBills = implode(",",$arrBills);
								
								$update_inv->set(array(
										'order_dates' => $strNewOrderDates,
										'order_bill_no'=> $strNewBills,
									)
								);
								
								$updateInv = $sql->getSqlStringForSqlObject($update_inv);
								echo $updateInv."<br /><br />";
								
								if($checked){
									$adapter->query ( $updateInv, Adapter::QUERY_MODE_EXECUTE );
									echo "Invoice Updated Successfully <br />";
								}
								
								echo "<br />";
								
							}
						}
						
					}else{
						
						echo "No Invoice Found. <br />";
					}
					
					$today = date("Y-m-d");
					$orderDate = $order->order_date;
					
					
					////////////////// Updating order Details and Kitchens //////////////////	
					
					echo "<h3> Deleting Order Details and kitchens </h3> <br />";
					
					$orderDetails = $tblOrder->getOrderDetails($order->order_no,null,$orderDate);
					
					$arrProducts = array();
					
					foreach ($orderDetails as $key => $detail) {
					
						$arrProducts[$detail['product_code']] = $detail;
						$arrProducts[$detail['product_code']]['quantity'] = $detail['quantity'];
					}
					
					//echo "<pre>";print_r($arrProducts);die;
	
		    		foreach ($arrProducts as $pCode => $pDetail) {
		    			
	
						/************* updating kitchen ********************/
						
						if($orderDate >= $today){
							
							echo "<h3> Updating Kitchen </h3>";
	
							$pinfo = $tblOrder->getProductInfo($pDetail['product_code'],$orderDate,$order->order_menu);
	
							$update2 = $sql->update ( 'kitchen' ); // @return ZendDbSqlUpdate
	
							if($pinfo['prepared'] > 0){
									
								$data = array (
										'total_order' => new \Zend\Db\Sql\Expression("total_order - ".((int)$pDetail['quantity'])),
										'prepared' => new \Zend\Db\Sql\Expression("prepared - ".((int)$pDetail['quantity'])),
								);
							
							}else{
								$data = array (
										'total_order' => new \Zend\Db\Sql\Expression("total_order - ".((int)$pDetail['quantity'])),
								);
							}
							
							$update2->set ( $data );
							$update2->where ( 
								array (
									new \Zend\Db\Sql\Predicate\PredicateSet ( array (
										new \Zend\Db\Sql\Predicate\Operator('date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderDate),
										new \Zend\Db\Sql\Predicate\Operator('fk_product_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $pDetail['product_code']),
										new \Zend\Db\Sql\Predicate\Operator('order_menu', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $order->order_menu),
										new \Zend\Db\Sql\Predicate\Operator('fk_kitchen_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $order->fk_kitchen_code)
									), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
								) 
							);
	
							$selectString2 = $sql->getSqlStringForSqlObject ( $update2 );
							echo $selectString2."<br />";
							
							if($checked){
								$adapter->query ( $selectString2, Adapter::QUERY_MODE_EXECUTE );
								echo "Kitchen Updated for product ".$pDetail['product_name'];
							}
	
						}
	
		    		}		
		    		
		    		$del_order_det = $sql->delete ( 'order_details' ); // @return ZendDbSqlUpdate
		    		
		    		$del_order_det->where (
	    				array (
    						new \Zend\Db\Sql\Predicate\PredicateSet ( array (
    								new \Zend\Db\Sql\Predicate\Operator('ref_order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $order->order_no),
    								new \Zend\Db\Sql\Predicate\Operator('order_date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderDate),
    						), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND )
	    				)
		    		);
		    		
		    		$deOrderDet = $sql->getSqlStringForSqlObject ( $del_order_det );
		    		echo $deOrderDet."<br />";
		    		
		    		if($checked){
		    			$adapter->query ( $deOrderDet, Adapter::QUERY_MODE_EXECUTE );
		    			echo "Order Details Deleted Successfully <br />";
		    		}
		    		
					///////////////// Deleting order tax details ////////////////////////////////
					
		    		echo "<h3> Deleting order tax details </h3> <br />";
		    		$del_order_tax = $sql->delete("order_tax_details");
		    		$del_order_tax->where("ord_ref_id = '{$order->order_no}' AND bill_no='{$order->pk_order_no}'");
		    		
		    		$delOrderTax = $sql->getSqlStringForSqlObject ( $del_order_tax );
		    		echo $delOrderTax."<br />";
		    		
		    		if($checked){
		    			
		    			$adapter->query ( $delOrderTax, Adapter::QUERY_MODE_EXECUTE );
		    			echo "Order tax of {$order->order_no} and bill no {$order->pk_order_no} deleted successfully ";
		    			
		    		}
					
					///////////////// Deleting order ////////////////////////////////////
					
		    		echo "<h3> Deleting order </h3> <br />";
		    		$del_order = $sql->delete("orders");
		    		$del_order->where("order_no = '{$order->order_no}' AND order_date = '{$order->order_date}'");
		    		
		    		$delOrder = $sql->getSqlStringForSqlObject ( $del_order );
		    		echo $delOrder."<br />";
		    		
		    		if($checked){
		    			 
		    			$adapter->query ( $delOrder, Adapter::QUERY_MODE_EXECUTE );
		    			echo "Order {$order->order_no} and date {$order->order_date} deleted successfully ";
		    			 
		    		}
					
					echo "<br />/////////////////////////////////////////////////////////////////////////////////// <br /><br />";
					
					if($checked){
						$adapter->getDriver()->getConnection()->commit();
					}
					
				}catch(\Exception $e){
				
					if($checked){
						$adapter->getDriver()->getConnection()->rollback();
					}
				}
			}
		}
		
		echo "Toatl Invices ".$totalInv."<br />";
		
		echo implode("<br />",$arrInvDel);
		
		die;

		return new JsonModel(array("success"));
	}

    
    public function ajaxThirdPartySummaryAction(){
        
        if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
        
        $iden = $this->authservice->getIdentity();

        $utility = Utility::getInstance();
    	
		$sm = $this->getServiceLocator();
        
		$libOrder = QSOrder::getInstance($sm);

        $params = $this->params()->fromPost();
        
        $third_party = ($params['third_party_option']) ? $params['third_party_option'] : NULL;
        
		$summary = $libOrder->getOrderTable()->getThirdPartySummary($params['is_aggregator'], $third_party, array_column($iden->kitchens, 'fk_kitchen_code'));
		
		$tp_summary = array(
				'total_orders' => $summary[0]['total_orders'],
				'total_amount' => $utility->getLocalCurrency($summary[0]['total_amount'])
		);
		
		return new JsonModel($tp_summary);		
	}
	 
    
    public function ajaxGetThirdPartyByTypeAction(){
        
        $sm = $this->getServiceLocator();
		
		$libOrder = QSOrder::getInstance($sm);
        
        $params = $this->params()->fromPost();
        
        $is_aggregator = $params['is_aggregator'];
        
		return new JsonModel($libOrder->getThirdpartyTable()->fetchAll(null, $is_aggregator)->toArray());	
    }
    
    public function sendNotificationAction(){

    	$view = new ViewModel();
    	$view->setTerminal(true);
    	$sm = $this->getServiceLocator();
        $libCommon = QSCommon::getInstance($sm);
        $setting_session = new Container('setting');
    	$setting = $setting_session->setting;
        
    	$sms_templates = $libCommon->getNotificationTemplates();
    	$request = $this->getRequest();
    	
    	$order_date = $this->params()->fromRoute('order_date');
    	$menu = $this->params()->fromRoute('menu');
    	
    	$filter = array(
    		'orderdate' => $order_date,
    		'menu' => $menu
    	);

    	$custinfo = $this->getReportTable()->getFirstDeliveredCustomer(null,null,$order_date, $menu);
    	
    	$count = count($custinfo);
    	
    	if($request->isPost())
    	{
    		$templates = $request->getPost('templates');
    		$menu = $request->getPost('menu');
    		$location = $request->getPost('location');
    		$deliverypers = $request->getPost('deliverypers');
    		$order_date = $request->getPost('order_date');
    		$operation = $request->getPost('operations');
    		
    		$custinfo = $this->getReportTable()->getFirstDeliveredCustomer(null,null,$order_date, $menu);
    		
    		$post = $request->getPost();

    		foreach ($templates as $key=>$post){
    
    			$notification_template = $libCommon->getTemplateById($post['templateId']);
    			    
    			$matches = array();
    			preg_match_all('/\#[a-zA-Z][a-zA-Z0-9_-]*\#/', $notification_template->sms_content, $matches);
    				
    			$mailer = new \Lib\Email\Email();
    				
    			$mailer->setAdapter($sm);
    				
    			//get sms configuration
    			$sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
    			//SET sms configuration to mailer
    			$mailer->setSMSConfiguration($sms_config);
    			//check for mobile no and give it to
    			$var_keys = array();
    				
    			foreach ($matches[0] as $match){
    				$str = str_replace('#','',$match);
    				$var_keys[] = $str;
    			}
    			$sms_array = array_combine($var_keys,$post['str']);
    			
    			foreach($custinfo as $custdata){
    				$mailer->setMobileNo($custdata['phone']);
    				$sms_common = $libCommon->getSmsConfig($setting);
    				$mailer->setMerchantData($sms_common);
    					
    				$message = $libCommon->getSMSTemplateMsg($notification_template->template_key,$sms_array);
    				if($message){
    					$mailer->setSMSMessage($message);
    					$sms_returndata = $mailer->sendmessage();
    				}
    			}    			
    		}
    
    		return new JsonModel( array('success'=>true,'msg'=>'Message has been successfully sent'));
    	}
    	//echo "<pre>"; print_r($filter); die;
    	$view->setVariables(array('sms_templates' =>$sms_templates,'filter' => $filter,'count'=>$count));
    
    	return $view;
    }    
}

<?php

/**
 * This File previouslly used for Delivery location
 * But now this file is not used in the project and not recommended in future
 *
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: AllocateDeliveryController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 * @deprecated No longer used by internal code and not recommended.
 *
 */

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

class AllocateDeliveryController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\AllocateDeliveryTable model
	 *
	 * @var QuickServe\Model\AllocateDeliveryTable $allocatedeliverytable
	 */
	protected $allocatedeliverytable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * This function used to get the list of orders which are not delivered
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		if (! $this->authservice)
		{
     		$this->authservice = $this->getServiceLocator()
     		->get('AuthService');
     	}

     	$iden = $this->authservice->getIdentity();

     	$select = New QSelect();
		$order_by = $this->params()->fromRoute('order_by')?
					$this->params()->fromRoute('order_by'):'pk_order_no';
		$order = $this->params()->fromRoute('allocatedelivery')?
				 $this->params()->fromRoute('order'): QSelect::ORDER_ASCENDING;
		$page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;

		$allocatedelivery = $this->getOrderTable()->fetchToday($select->order($order_by . ' ' . $order));

		$returnvar = $allocatedelivery;
		$itemsPerPage = 2;

		//echo "<pre>"; print_r($returnvar); exit;

		$allocatedelivery->current();
		$paginator = new Paginator(new paginatorIterator($allocatedelivery));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		return new ViewModel(array(
				'order_by' => $order_by,
				'order' => $order,
				'page' => $page,
				'paginator' => $returnvar,
				'acl' => $acl,
				'loggedUser' => $loguser,
		));
	}

	/**
	 * Creates & assigns the Instance Of QuickServe\Model\AllocateDeliveryTable model
	 *
	 * @return QuickServe\Model\AllocateDeliveryTable $allocatedeliverytable
	 */
	public function getOrderTable()
	{
		if (!$this->allocatedeliverytable)
		{
			$sm = $this->getServiceLocator();
			$this->allocatedeliverytable = $sm->get('Quickserve\Model\AllocateDeliveryTable');
		}
		return $this->allocatedeliverytable;
	}
}
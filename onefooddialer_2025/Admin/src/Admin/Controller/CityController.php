<?php

/**
 * This file manages the cms on fooddialer system
 * The activity includes add,update and delete cms pages
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CmsController.php 2017-06-19 
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\JsonModel;
use Zend\View\Model\ViewModel;
use Zend\Session\Container;
use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\Utility;
use Lib\QuickServe\CommonConfig as Qscommon;
use Admin\Form\CityForm;
use QuickServe\Model\CityValidator;

class CityController extends AbstractActionController
{
/**
* It has an instance of QuickServe\Model\cityTable model
*
* @var QuickServe\Model\CityTable $cityTable
*/
protected $cityTable;
/**
* It has an instance of AuthService model
*
* @var AuthService $authservice
*/
protected $authservice;

    
    public function indexAction()
	{
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$iden = $this->authservice->getIdentity();
		
		$session_setting = new Container('setting');
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$this->layout()->setVariables(array('page_title'=>"City",'description'=>"City",'breadcrumb'=>"City"));
		
		return new ViewModel(array(
				'acl' => $acl,
				'loggedUser' => $loguser,
                'flashMessages'=> $this->flashMessenger()->getMessages()
		));			
	}
    /**
     * To add new city
     * 
     * @return \Admin\Form\CityForm
     * 
     */
    public function addAction(){
        
        $layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$sm = $this->getServiceLocator();
		
		
		$adapt = $sm->get('Write_Adapter');
		$libCommon = Qscommon::getInstance($sm);
		$config_variables = $sm->get('config');
        $form = new CityForm($sm);

		$form->get('submit')->setAttribute('value', 'Add');
        
        $request = $this->getRequest();
		if ($request->isPost()) {
			
			$city = new CityValidator();
			$city->getInputFilter()->get('city')
			->getValidatorChain()  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'city',
					'field'     => 'city',
					'adapter'   => $adapt,
					'message'   => 'City Already Exists',
			)
			));
            $form->setInputFilter($city->getInputFilter());
            $form->setData($request->getPost());
            if ($form->isValid()) {

                $city->exchangeArray($form->getData());

                $data_city = $this->getCityTable()->saveCity($city);

                ($data_city) ?$this->flashMessenger()->addSuccessMessage("City Page added successfully"):$this->flashMessenger()->addErrorMessage("Error adding page.");
                if($data_City)
                {					
                    $full_name=$loguser->first_name." ".$loguser->last_name;
                    $City_name=$City->City;
                    $activity_log_data=array();
                    $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                    $activity_log_data['context_name']= $full_name;
                    $activity_log_data['context_type']= 'user';
                    $activity_log_data['controller']= 'city';
                    $activity_log_data['action']= 'add';
                    $activity_log_data['description']= "City : New city $city_name created.";                
                    $libCommon->saveActivityLog($activity_log_data);

                }
                // Redirect to list of albums
                return $this->redirect()->toRoute('city');
            }
		}
		$this->layout()->setVariables(array('page_title'=>"Add city",'breadcrumb'=>"Add city"));
		return array('form' => $form,
    		'language_array' => $config_variables['supported_nonenglish_languages']
		);            
    }
    
    /**
     * 
     * 
     * @return JsonModel $returnVar
     * 
     */
    public function ajxCityAction()
	{

		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}

		$iden = $this->authservice->getIdentity();
		
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;		
		$utility = Utility::getInstance();
		
		$layout = $this->layout();
		$acl = $layout->acl;
		
		$viewModel = new ViewModel();
		
		$loggedUser = $layout->loggedUser;
		
		$select = new QSelect();
		
		$arrOrder = $this->params()->fromQuery('city') ? $this->params()->fromQuery('city') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'city','1'=>"status");
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
		 
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		 
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		 
		$status = $this->params()->fromQuery('status');		 
		
		$select->where(
		
            new \Zend\Db\Sql\Predicate\PredicateSet(
                array(															
                    new \Zend\Db\Sql\Predicate\Operator('city', 'LIKE', '%'.$search.'%'),								
                    new \Zend\Db\Sql\Predicate\Operator('status', 'LIKE', '%'.$search.'%'),
                ),
                // optional; OP_AND is default
                \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
            )		
		);
		
		$select->order($order_by . ' ' . $order);
		$citys = $this->getCityTable()->fetchAll($select,$page);		
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $citys->getTotalItemCount();
		$returnVar['recordsFiltered'] = $citys->getTotalItemCount();
		$returnVar['data'] = array();
		
		foreach($citys as $city){
			$arrTmp = array();			
			array_push($arrTmp,$city['city']);									
			$status =  ($city['status']=="1" )? '<span class="active">Active</span>':'<span class="inactive">Inactive</span>';
			array_push($arrTmp,$status);
			$str = "";

			$textadd = ($city['status']==1)?'Deactive' :'Activate';
				
			if($acl->isAllowed($loggedUser->rolename,'city','edit')){
			
				$str.='<a href="'.$this->url()->fromRoute('city', array('action' => 'edit', 'id' => $city['pk_city_id'])).'" class="btn btn5 btn_pencil5">';
					
				$str.= '<button class="smBtn blueBg has-tip tip-top" data-tooltip title="Edit"><i class="fa fa-edit"></i></button></a>';
			}
			if($acl->isAllowed($loggedUser->rolename,'city','delete')){
				$str.='<a onclick="return confirm("Are you sure you want to '.$textadd.' this city ?")" href="'.$this->url()->fromRoute('city', array('action' => 'delete', 'id' => $city['pk_city_id'])).'" class="btn btn5 btn_trash5">';
			
				if($textadd == 'Deactive') {
					$str.= '<button class="smBtn redBg has-tip tip-top" data-tooltip  title="Delete"><i class="fa fa-ban"></i></button>';
				}else if($textadd == 'Activate'){
					$str.=' <button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>';
				}
			}
			array_push($arrTmp,$str);
			
			array_push($returnVar['data'],$arrTmp);
		}
		return new JsonModel($returnVar);
	}
    
    /**
	 * To update the city of given city id
	 *
	 * @return \Admin\Form\CityForm
	 */
	public function editAction()
	{
        error_reporting(E_ALL);
        ini_set("display_errors","On");
        
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('city', array('action' => 'add'));
		}
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$city = $this->getCityTable()->getCity($id);
        
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		$config_variables = $sm->get('config');		
		$form = new CityForm($adapt);				
        
		$form->bind($city);        
		$form->get('submit')->setAttribute('value', 'Edit');

		$val = $city->city;	
        
		$request = $this->getRequest();

		if ($request->isPost())
		 {
		 	//echo "<pre>"; print_r($request->getPost()); exit;
			$city = new CityValidator();	
			$city->getInputFilter()->get('city')
			->getValidatorChain() // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'city',
					'field'     => 'city',
					'adapter'   => $adapt,
					'message'   => 'city Already exists',
					'exclude' => array(
                        'field' => 'city',
                        'value' => $val,
					)
			)
			));
			$form->setInputFilter($city->getInputFilter());
			$form->setData($request->getPost());
			if ($form->isValid()) {
			
				$city->exchangeArray($form->getData());
				$data_city=$this->getCityTable()->saveCity($city);
                if($data_City)
                {					
                    $full_name=$loguser->first_name." ".$loguser->last_name;
                    $City_name=$City->City;
                    $activity_log_data=array();
                    $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                    $activity_log_data['context_name']= $full_name;
                    $activity_log_data['context_type']= 'user';
                    $activity_log_data['controller']= 'city';
                    $activity_log_data['action']= 'edit';
                    $activity_log_data['description']= "City : City $city_name updated.";
                    $libCommon->saveActivityLog($activity_log_data);
                    $this->flashMessenger()->addSuccessMessage("City updated successfully");
                }
				// Redirect to list of albums
				return $this->redirect()->toRoute('city');
			}			
		}

		$this->layout()->setVariables(array('page_title'=>"Edit City",'breadcrumb'=>"Edit City"));
		
		return array(
			'id' => $id,
			'form' => $form,
    		'language_array' => $config_variables['supported_nonenglish_languages'],    	
		);
	}
    
    /**
	 * To delete the city of given city id
	 *
	 * @param int id
	 * @return route city
	 */
	public function deleteAction() {
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('city');
		}
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		$select = new QSelect();
		$select->where(array('pk_city_id'=>$id));
		$citys = $this->getCityTable()->fetchAll($select);
		$arrcity = $citys->toArray();
		$city_name = $arrcity[0]['city'];
		$city_status = ($arrcity[0]['status'])=='1'?'deactivated':'activated';
		
		$data_city=$this->getCityTable()->deleteCity($id);
		($data_city) ?$this->flashMessenger()->addSuccessMessage("City updated successfully"):$this->flashMessenger()->addErrorMessage("Error updating city.");
		
		if($data_city)
		{

			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'city';
			$activity_log_data['action']= 'delete';
			$activity_log_data['description']= "City : City $city_name $city_status.";
		
			$libCommon->saveActivityLog($activity_log_data);
		}
		return $this->redirect()->toRoute('city');
	}
        
    /**
	 * Get instance of QuickServe\Model\CityTable
	 *
	 * @return QuickServe\Model\CityTable
	 */
	public function getCityTable()
	{
		if (!$this->cityTable) {
			$sm = $this->getServiceLocator();
			$this->cityTable = $sm->get('QuickServe\Model\CityTable');
		}
		return $this->cityTable;
	}
}
?>
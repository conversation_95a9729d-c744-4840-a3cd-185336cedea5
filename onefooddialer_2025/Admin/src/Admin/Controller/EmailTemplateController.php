<?php

/**
 * This File mainly used for Order process at through BackEnd
* <PERSON><PERSON> can place customer's order by logging into his account
* He can also recharge into customer's account through Admin Panel
*
* PHP versions 5.4
*
* Project name FoodDialer
* @version 1.1: BackorderController.php 2015-05-04 $
* @package Admin/Controller
* @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
* @license Copyright (C) 2014 � Futurescape Technology
* @license http://www.futurescapetech.com/copyleft/gpl.html
* @link http://www.futurescapetech.com
* @category <Controller Admin>
* <AUTHOR> <<EMAIL>>
* @since File available since Release 1.1.0
*
*/

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use Admin\Form\EmailForm; 
use QuickServe\Model\EmailValidator;

use Lib\Utility;
use Lib\Email\Email;

class EmailTemplateController extends AbstractActionController {

	/**
	 * It has an instance of QuickServe\Model\EmailTable model
	 *
	 * @var QuickServe\Model\DiscountTable $emailTable
	 */
	protected $emailTable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * This function used to display the list of emailsets
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	
	public function indexAction()
	{ 
		if (! $this->authservice) {
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$iden = $this->authservice->getIdentity();
		
		$select = New QSelect();
		$order_by = $this->params()->fromRoute('order_by')?
		$this->params()->fromRoute('order_by'):'pk_set_id';
		$order = $this->params()->fromRoute('order')?
		$this->params()->fromRoute('order'): QSelect::ORDER_DESCENDING;
		$page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;
		$select1 = $select->order($order_by . ' ' . $order);
		
		$emailset = $this->getEmailTable()->fetchAll($select1);
		
		$returnvar = $emailset->toArray();
		$itemsPerPage = 2;
		
		$emailset->current();
		$paginator = new Paginator(new paginatorIterator($emailset));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);
		
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser; 
		
		$this->layout()->setVariables(array('page_title'=>"Email Templates",'breadcrumb'=>"Email Templates"));
		return new ViewModel(array(
			'order_by' => $order_by,
			'order' => $order,
			'page' => $page,
			'paginator' => $returnvar, 
			'acl' => $acl,
			'loggedUser' => $loguser,
			'flashMessages'=> $this->flashMessenger()->getMessages()
		));
	}
	public function emaillogAction()
	{
		
		if (! $this->authservice) {
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$iden = $this->authservice->getIdentity();
		
		
		$emailqueue= $this->getEmailTable()->getemailqueue();
		$returnvar =  $emailqueue->toArray();
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$this->layout()->setVariables(array('page_title'=>"Email Log",'breadcrumb'=>"Email Log"));
		return new ViewModel(array(
			'paginator' => $returnvar,
			'acl' => $acl,
			'loggedUser' => $loguser,
			'flashMessages'=> $this->flashMessenger()->getMessages()
		));
	}
	public function templateaddAction()
	{
		$sm = $this->getServiceLocator();
		$form = new EmailForm($sm);
		
		$request = $this->getRequest();
		
		if ($request->isPost())
		{
			$email = new EmailValidator();
			 
			$form->setValidationGroup('name','purpose','pokemonRed','status','copy_template');
			
			//$email->setAdapter($adapt);
			$form->setInputFilter($email->getInputFilter());
			$form->setData($request->getPost());

            if ($form->isValid())
			{
				$email->exchangeArray($form->getData());
                
                $authservice = $this->getServiceLocator()->get('AuthService')->getIdentity();
                $email->created_by = $authservice->pk_user_code;
                
                $email->modified_by = $authservice->pk_user_code;
				$emailset = $this->getEmailTable()->saveEmailset($email);
				return $this->redirect()->toRoute('emailtemplate');
			}
			/* else
			{
				echo '<pre>ddd';print_r($form->getMessages());exit;
			}
			 */
		}
		
		
		$this->layout()->setVariables(array('page_title'=>"Add Email Template",'breadcrumb'=>"Email Template"));
	
		return array('form' => $form);
		
	}
	public function emaildetailsAction()
	{
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('emailtemplate');
		}
		$emaildetailsarray = $this->getEmailTable()->getEmailTemplates($id);
		$emaildetails = $emaildetailsarray->toArray();
		
		$this->layout()->setVariables(array('page_title'=>"Email Details",'breadcrumb'=>"Email Template","back"=>"emailtemplate"));
		return array(
				'emailtemplate'=>$emaildetails
		);
	}
	public function editemaildetailAction()
	{
		
		$templateid = (int) $this->params('id');
		$setid = (int) $this->params('setid');
		
		if (!$templateid) {
			return $this->redirect()->toRoute('emailtemplate');
		}
		
		$emailvariables = $this->getEmailTable()->getEmailVariables();
		
		//echo'<pre>';print_r($emailvariables);die;
		$basicaraay = array();
		$i=0;
		foreach ($emailvariables as $k => $v)
		{
			if($v['type'] == 'basic')
			{
				$basicaraay[$i]['variable']=$v['variable'];
				$basicaraay[$i]['content']=$v['content'];
			//	$basicaraay[]['content']
				$i++;
			}
			
		}	
		
		$otheraray = $this->getEmailTable()->getothervariable($templateid,$setid);
	
		$emailviewarray = $this->getEmailTable()->getTemplateView($templateid);
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$config_variables = $sm->get('config');
		$form = new EmailForm($sm);
	
		$form->bind($emailviewarray);
		$form->get('pk_set_id')->setValue($setid);
		
		 $request = $this->getRequest();
		
		if ($request->isPost())
		{
			
			$maildetails = new EmailValidator();
		
			$form->setValidationGroup('pk_set_id','pk_template_id','template_key','subject','body','type');
				
			//$maildetails->setAdapter($adapt);
			$form->setInputFilter($maildetails->getInputFilter());
			$form->setData($request->getPost());
		
		
			if ($form->isValid())
			{
				
				$maildetails->exchangeArray($form->getData());
			
				$emailviewdetails = $this->getEmailTable()->saveEmailviewDetails($maildetails);
				
				return $this->redirect()->toRoute('emailtemplate',array('action'=>'emaildetails','id'=>$setid));
				
				//return $this->redirect()->toRoute('emailtemplate',array('action'=>'emaildetails','id'=>$id));
			}
		 /* else
			 {
				 $messages = $form->getMessages();
				 print_r($messages); exit();
					
			 }   */
	
		
		}
		$this->layout()->setVariables(array('page_title'=>"Email Details",'breadcrumb'=>"Email Template"));
		
		return array(
				//'emailtemplate'=>$emailviewarray,
				'form' => $form,
				//'emailvariable' =>$emailvariables
				'basicvariable' => $basicaraay,
				'othervariable' => $otheraray
		);
		
	}
	
	public function editAction()
	{
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('emailtemplate');
		}
		
		$getmailset = $this->getEmailTable()->getemailtemplate($id);
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$form = new EmailForm($sm);
		
		$form->bind($getmailset);
		 
		$form->get('name')->setValue($getmailset['set_name']);
		$form->get('pokemonRed')->setValue($getmailset['is_default']);
		$form->get('status')->setValue($getmailset['status']);
		//$form->get('pk_set_id')->setValue($getmailset['pk_set_id']);
		
		$request = $this->getRequest();
		
		if ($request->isPost())
		{
			$emailvalidator = new EmailValidator();
		
			$form->setValidationGroup('name','purpose','status');
		
			//$emailvalidator->setAdapter($adapt);
			$form->setInputFilter($emailvalidator->getInputFilter());
			$form->setData($request->getPost());
		
			if ($form->isValid())
			{
				$emailvalidator->exchangeArray($form->getData());
				$emailset = $this->getEmailTable()->saveEmailset($emailvalidator,$id);
				return $this->redirect()->toRoute('emailtemplate'); 
			}
			/* else
			{
				echo '<pre>ddd';print_r($form->getMessages());exit;
			} */
		}
		
		$this->layout()->setVariables(array('page_title'=>"Set Details",'breadcrumb'=>"SMS Template"));
		return array('form' => $form);
	}
	
	public function emailviewAction()
	{
		$id = (int) $this->params('id');
		
		if (!$id) {
			return $this->redirect()->toRoute('emailtemplate');
		}
		$emailviewarray = $this->getEmailTable()->getTemplateView($id);
		
		$body  = $emailviewarray['body'];
		if($emailviewarray->type=="text")
		{
			$msgbody = strip_tags($body,'<br><br/>');
			
			//$msgbody = str_replace(array("<br />", "<br/>", "<br>","&nbsp"), "\n", $messagebody);
		}
		else 
		{
			$msgbody = $body;
		} 

		$header = file_get_contents(realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__))))))).'/Email_templates/header.phtml' );
		$footer = file_get_contents(realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__))))))).'/Email_templates/footer.phtml' );
	
		$str = <<<HEREDOC
		{$msgbody}
HEREDOC;
	
		$this->layout()->setVariables(array('page_title'=>"Email View",'breadcrumb'=>"Email View","back"=>"emailtemplate"));
		return array(
				'emailtemplate'=>$str
		);
	}
	
	public function ajxEmaillogAction()
	{
	
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$iden = $this->authservice->getIdentity();
		
		$session_setting = new Container("setting");
		$setting = $session_setting->setting;
		
		$utility = Utility::getInstance();
		
		$layout = $this->layout();
		$acl = $layout->acl;
		
		$viewModel = new ViewModel();
		
		$loggedUser = $layout->loggedUser;
		
		$select = new QSelect();
		
		$arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
		$arrColumns = array('0'=>'status','1'=>'created','2'=>'from_email_id','3'=>'to_email_id','4'=>'subject_text');
		
		$order_by = $arrColumns[$arrOrder[0]['column']];
		$order = $arrOrder[0]['dir'];
		 
		$itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
		 
		$arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
		$start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
		$draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		$search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
		 
		$status = $this->params()->fromQuery('status');
		 
		$columns = $this->params()->fromQuery('columns');
		
		$select->where(
		
				new \Zend\Db\Sql\Predicate\PredicateSet(
						array(
								new \Zend\Db\Sql\Predicate\Operator('status', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('created', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('from_email_id', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('to_email_id', 'LIKE', '%'.$search.'%'),
								new \Zend\Db\Sql\Predicate\Operator('subject_text', 'LIKE', '%'.$search.'%'),
							
						),
						// optional; OP_AND is default
						\Zend\Db\Sql\Predicate\PredicateSet::OP_OR
				)
		
		);
		
		$select->order($order_by . ' ' . $order);
		
		$emailqueues = $this->getEmailTable()->getemailqueue($select,$page);

		$emailqueues->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$returnVar = array();
		$returnVar['draw'] = $draw;
		$returnVar['recordsTotal'] = $emailqueues->getTotalItemCount();
		$returnVar['recordsFiltered'] = $emailqueues->getTotalItemCount();
		$returnVar['data'] = array();
		
		foreach($emailqueues as $emailqueue){
		
			$arrTmp = array();
			
			if($emailqueue['status'] =="sent"){$cls="delivered"; $licls="yes"; $state = "Sent";}
			elseif($emailqueue['status'] =="waiting"){$cls="delivered"; $licls="inQue"; $state = "In Queue";}
			elseif($emailqueue['status'] =="error"){$cls="delivered"; $licls="no"; $state = "Not Sent";}
				
			$status = '<ul class="'.$cls.'"><li class="'.$licls.'"><span class="'.$licls.'">'.$state.'</span></li></ul>';
			
			array_push($arrTmp,$status);
			
			array_push($arrTmp,$utility->displayDate($emailqueue['created'],$setting['DATE_FORMAT']));
			
			$from_email_address_input = explode(',', $emailqueue['from_email_id']);
			$from_email_id = $this->getEmailAddressArray($from_email_address_input);
			array_push($arrTmp,implode(', ',$from_email_id));
			
			$to_email_address_input = explode(',', $emailqueue['to_email_id']);
			$to_email_id = $this->getEmailAddressArray($to_email_address_input);
			array_push($arrTmp,implode(', ',$to_email_id));
			
			array_push($arrTmp,$emailqueue['subject_text']);
		 	array_push($arrTmp,'');
			array_push($arrTmp,'');
			$confirm ='';
			$confirmMsg = "Are you sure you want to Resend the mail?";
			$str = '<td><button class="mb0 resend" data-id="'.$emailqueue['email_queue_id'].'" >Resend</button>
					<button class="mb0 dark-greenBg" onClick="location.href=\''.$this->url()->fromRoute('emailtemplate', array('action'=>'viewemailqueuetemplate','id'=>$emailqueue['email_queue_id'])).'\'">View</button></td>';
			
			/* if($confirm == '1')
			{
				echo "resend the mail";
			}
			else 
			{
				echo "do not resend";
			} */
			
			
			array_push($arrTmp,$str);
			array_push($returnVar['data'],$arrTmp);
			}
			
			return new JsonModel($returnVar);
	}
	public function updatestatusAction()
	{
		
		$id = $_POST['id'];
		$status = $_POST['status'];
		
		$result = $this->getEmailTable()->updatestatus($id,$status);
		
		echo $result;die;

	}
	
	
	/**
	 * Get instance of QuickServe\Model\DiscountTable
	 *
	 * @return QuickServe\Model\DiscountTable
	 */
	public function getEmailTable()
	{
		if (!$this->emailTable) {
			$sm = $this->getServiceLocator();
			$this->emailTable = $sm->get('QuickServe\Model\EmailTable');
		}
		return $this->emailTable;
	}
	
	public function getEmailAddressArray($email_address_input) {
		
		$email_address_array = array();
		foreach( $email_address_input as $itr => $email_address ) {
			
			$email_address = stripslashes(trim($email_address));
			
			$name = '';
			$email_id = '';
			$part_array = explode('" &lt;', $email_address);
			
			$name = substr($part_array[0], 1);
			
			$email_id = substr($part_array[1], 0, strrpos($part_array[1], '&gt;') );
			
			
			if( !empty($name) && !empty($email_id) ) {
				$email_address_array[$name] = $email_id;
			}
			elseif( empty($name) && !empty($email_id) ) {
				array_push($email_address_array, $email_id);
			}
			
		}
		
		return $email_address_array;
	}
	public function viewemailqueuetemplateAction()
	{                
		$id = (int) $this->params('id');
	
		if (!$id) {
			return $this->redirect()->toRoute('emailtemplate');
		}
		$emailqueueviewarray = $this->getEmailTable()->getEmailQueueView($id);
		//echo'<pre>';print_r($emailqueueviewarray);die;	
		$header = file_get_contents(realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__))))))).'/Email_templates/header.phtml' );
		$footer = file_get_contents(realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__))))))).'/Email_templates/footer.phtml' );
		//echo'<pre>';print_r(htmlentities($emailview[0]['body']));die;
		
		$file = $emailqueueviewarray->attachment_file_name;
		$filename = substr($file,strpos($file, "/INV")+1);
		
		$str = <<<HEREDOC
		{$emailqueueviewarray->message_content}
		
HEREDOC;
				//echo "<pre>";print_r($str);die;
				$this->layout()->setVariables(array('page_title'=>"Email View",'breadcrumb'=>"Email View"));
				return array(
						'emailtemplate'=>$str,
						'attached_file'=>$filename
				);
	}
	public function resendmailAction()
	{
        $sm = $this->getServiceLocator();        
        $libCommon = QSCommon::getInstance($sm);
        
		$request = $this->getRequest();
			
		if($request->isPost())
		{
			$id = $request->getPost('id');
		}
				
		/* $config=$sm->get('config');
		 $mailer_config = $config['mail']['transport']['options']; */
		
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		 
		 
		$mailer = new \Lib\Email\Email();
		$mailer_config = $setting->getArrayCopy();
		$mailer->setConfiguration($mailer_config);
		$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
		
		$sms_common = $libCommon->getSmsConfig($setting);
		$mailer->setMerchantData($sms_common);
		 
		// get email storage queue
		$mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
		
		$queue = new \Lib\Email\Queue();
		$queue->setStorage($mail_storage);
		$mailer->setQueue($queue);
		
		$maildetails = $this->getEmailTable()->getqueuedata($id);
		
		if( is_array($maildetails) && !empty($maildetails) ) {
			
				$from_email_address_input = explode(',', $maildetails['from_email_id']);
				 
				$from_email_id = !empty($maildetails['from_email_id']) ? $this->getEmailAddressArray($from_email_address_input) : array($mailer_config['SMTP_FROM_NAME'] => $mailer_config['SMTP_FROM_EMAIL']);
				
				$to_email_address_input = explode(',', $maildetails['to_email_id']);
		
				$to_email_id = !empty($maildetails['to_email_id']) ? $this->getEmailAddressArray($to_email_address_input) : array();
		
				//echo'<pre>from = ';print_r($to_email_id);die;
		
				$cc_email_address_input = explode(',', $maildetails['cc_email_id']);
				$cc_email_id = !empty($maildetails['cc_email_id']) ? $this->getEmailAddressArray($cc_email_address_input) : array();
		
				
				$bcc_email_address_input = explode(',', $maildetails['bcc_email_id']);
				$bcc_email_id = !empty($maildetails['bcc_email_id']) ? $this->getEmailAddressArray($bcc_email_address_input) : array();
		
				$email_details['encoding'] = !empty($maildetails['encoding']) ? $maildetails['encoding'] : 'UTF-8';
				$files_array = array();
				if( !empty($maildetails['attachment_file_name']) ) { $files_array = explode(',', $maildetails['attachment_file_name']); }
				
				$mailer->sendmail(
						$from_email_id, $to_email_id,
						$cc_email_id, $bcc_email_id,
						$maildetails['subject_text'], $maildetails['message_content'],
						$maildetails['encoding'], $files_array,$maildetails['content_type'],
						true
				);
				
				$mailer->getQueue()->updateQueue($maildetails['email_queue_id'], array('sent' => 1,'status'=>'sent') );
				
				
		}
		echo 1;exit();
		$view = new ViewModel();
		$view->setTerminal(true);
		return $view;
	}
	
	/**
	 * Download service data by user
	 * @return void
	 */
	public function downloadAction(){
		$params = $this->params()->fromQuery();
		$file = $params['file'];
		
		$file = dirname($_SERVER['DOCUMENT_ROOT'])."/data/invoices/".$file;
		
		if(!file_exists($file)){
			die("file not exists");
		}
		
		header('Content-Description: File Transfer');
		header('Content-Type: application/octet-stream');
		header("Content-Type: application/force-download");
		header('Content-Disposition: attachment; filename=' . urlencode(basename($file)));
		// header('Content-Transfer-Encoding: binary');
		header('Expires: 0');
		header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		header('Pragma: public');
		header('Content-Length: ' . filesize($file));
		ob_clean();
		flush();
		readfile($file);
		exit;
		
	}
	
	public function updatedefaultsetAction()
	{
		$id = $_POST['id'];
		
		$result = $this->getEmailTable()->updateisdefault($id);
		
		echo $result;die;
	}
	
	public function updateemailstatusAction()
	{
		
		$id = $_POST['id'];
		$status = $_POST['status'];
			
		$result = $this->getEmailTable()->updatestatus($id,$status);
		
		echo $result;die;
		
	}
	public function deletelogAction()
	{
		$emaillog = $this->getEmailTable()->deleteEmailLog();
		
		/* ($emaillog) ?$this->flashMessenger()->addSuccessMessage("Log deleted successfully"):$this->flashMessenger()->addErrorMessage("Error updating Customer Group.");
		return $this->redirect()->toRoute('emailtemplate',array('action' => 'emaillog')); */
		return new JsonModel(array('msg'=>'Log deleted successfully'));
	
	}
	public function updatesendtoadminAction()
	{		
		$id = $_POST['id'];
		$value = $_POST['value'];			
		$result = $this->getEmailTable()->updatesendtoadmin($id,$value);		
		echo $result;die;		
	}
}
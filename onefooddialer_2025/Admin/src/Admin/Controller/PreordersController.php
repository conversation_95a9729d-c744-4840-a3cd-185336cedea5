<?php
/**
 * This File manages the preorders of fooddialer system
 * It is responsible to show & cancel the preorders
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: PreordersController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\Utility;

class PreordersController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\OrderTable model
	 *
	 * @var QuickServe\Model\OrderTable $ordertable
	 */
	protected $ordertable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * To show the list of preorders
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		if (! $this->authservice)
		{
     		$this->authservice = $this->getServiceLocator()
     		->get('AuthService');
     	}

     	$iden = $this->authservice->getIdentity();

     	$select = New QSelect();
		$order_by = $this->params()->fromRoute('order_by')?
					$this->params()->fromRoute('order_by'):'pk_order_no';
		$order = $this->params()->fromRoute('order')?
				 $this->params()->fromRoute('order'): QSelect::ORDER_ASCENDING;
		$page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;

		$customer = $this->getOrderTable()->fetchAll($select->order($order_by . ' ' . $order));
		$returnvar = $customer->toArray();
		$itemsPerPage = 10;

		//echo "<pre>"; print_r($returnvar); exit;
		$customer->current();
		$paginator = new Paginator(new paginatorIterator($customer));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$this->layout()->setVariables(array('page_title'=>"Pre Orders",'description'=>"to be delivered",'breadcrumb'=>"Pre Orders"));

		return new ViewModel(array(
				'order_by' => $order_by,
				'order' => $order,
				'page' => $page,
				'paginator' => $returnvar,
				'acl' => $acl,
				'loggedUser' => $loguser,
		));
	}
	
	public function ajxPreorderAction(){
	    
	    if (! $this->authservice)
	    {
	        $this->authservice = $this->getServiceLocator()
	        ->get('AuthService');
	    }
	    
	    $iden = $this->authservice->getIdentity();
	    
	    $session_setting = new Container("setting");
	    $setting = $session_setting->setting;
	    
	    $utility = Utility::getInstance();
	     
	    $cutofftime = $this->getServiceLocator()->get('Config')['cutofftime'];
	    
	    $tblFront = $this->getServiceLocator()->get('Front\Model\FrontTable');
	    
	    $layout = $this->layout();
	    $acl = $layout->acl;
	    
	    $viewModel = new ViewModel();
	    
	    $loggedUser = $layout->loggedUser;
	    
	    $select = new QSelect();
	     
	    $arrOrder = $this->params()->fromQuery('order') ? $this->params()->fromQuery('order') : array('0'=>array('column'=>0,'dir'=>'desc'));
	    $arrColumns = array('0'=>'pk_order_no','1'=>'customer_name','2'=>'phone','3'=>'location','4'=>'promo_code','5'=>'total_amt','6'=>'total_applied_discount','7'=>'line_delivery_charges','8'=>'total_amt','9'=>'order_status','10'=>'order_menu','11'=>'order_days','12'=>'order_days');
	     
	    $order_by = $arrColumns[$arrOrder[0]['column']];
	    $order = $arrOrder[0]['dir'];
	    
	    $itemsPerPage = $this->params()->fromQuery('length') ? $this->params()->fromQuery('length') : 10;
	    
	    $arrSearch = $this->params()->fromQuery('search') ? $this->params()->fromQuery('search') : "";
	    $expires_on = $this->params()->fromQuery('expires_on') ? $this->params()->fromQuery('expires_on') : "";
	    
	    $start = $this->params()->fromQuery('start') ? $this->params()->fromQuery('start') : 0 ;
	    $draw = $this->params()->fromQuery('draw') ? $this->params()->fromQuery('draw') : 1 ;
	    $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
	    $search = ($arrSearch['value'] !="") ? $arrSearch['value'] : "";
	    
	    
	    $columns = $this->params()->fromQuery('columns');
	    $expireDate = array();
	    
	    if(isset($expires_on) && $expires_on !=""){
	    
	    	switch($expires_on){
	    		case "today":
	    
	    			$expireDate[] = date("Y-m-d");
	    			break;
	    
	    		case "tommorrow":
	    
	    			$expireDate[] = date("Y-m-d",strtotime("+1 day"));
	    			break;
	    
	    		case "next2day":
	    			 
	    			$todaysdate = date("Y-m-d");
	    			$endDate = date("Y-m-d",strtotime("+2 day"));
	    
	    			$start = new \DateTime($todaysdate);
	    			$interval = new \DateInterval('P1D');
	    			$end = new \DateTime($endDate);
	    			$end = $end->modify( '+1 day');
	    
	    			$period = new \DatePeriod($start, $interval, $end,
	    					\DatePeriod::EXCLUDE_START_DATE);
	    
	    			// By iterating over the DatePeriod object, all of the
	    			// recurring dates within that period are printed.
	    			// Note that, in this case, start date i.e todays date is not printed.
	    			foreach ($period as $date) {
	    
	    				$expireDate[] =  $date->format('Y-m-d');
	    			}
	    			 
	    			break;
	    		case "thisweek":
	    			$todaysdate = date("Y-m-d");
	    			//$expireDate = date("Y-m-d",strtotime("this week"));
	    
	    			$endDate = date('Y-m-d',strtotime('next sunday', strtotime($todaysdate)));
	    
	    			$start = new \DateTime($todaysdate);
	    			$interval = new \DateInterval('P1D');
	    			$end = new \DateTime($endDate);
	    			$end = $end->modify('+1 day');
	    			$period = new \DatePeriod($start, $interval, $end);
	    			 
	    			foreach ($period as $date) {
	    
	    				$expireDate[] =  $date->format('Y-m-d');
	    			}
	    			 
	    			break;
	    		case "nextweek":
	    
	    			$todaysdate = date("Y-m-d");
	    			$start = date('Y-m-d',strtotime('next monday', strtotime($todaysdate)));
	    			$endDate = date('Y-m-d',strtotime('next sunday', strtotime($start)));
	    			 
	    			$start = new \DateTime($start);
	    			$interval = new \DateInterval('P1D');
	    			$end = new \DateTime($endDate);
	    			$end = $end->modify('+1 day');
	    
	    			$period = new \DatePeriod($start, $interval, $end);
	    				
	    			foreach ($period as $date) {
	    
	    				$expireDate[] =  $date->format('Y-m-d');
	    			}
	    			 
	    			break;
	    
	    		case "nextmonth":
	    
	    			$startDate = date('Y-m-d',strtotime("first day of next month"));
	    			$endDate = date('Y-m-d',strtotime("last day of next month"));
	    			$lastDate = new \DateTime($endDate);
	    
	    			$start = new \DateTime($startDate);
	    			$interval = new \DateInterval('P1D');
	    			$end = $lastDate->modify( '+1 day');
	    
	    			$period = new \DatePeriod($start, $interval, $end);
	    			 
	    			foreach ($period as $date) {
	    
	    				$expireDate[] =  $date->format('Y-m-d');
	    			}
	    			 
	    			break;
	    
	    	}
	    
	    
	    	foreach ($expireDate as $date){
	    		 
	    		$where.=" order_days LIKE '%".$date."' OR";
	    
	    	}
	    	$where = substr($where, 0, -2);
	    	 
	    	 
	    	$select->where($where);
	    }
	    
	    if(isset($search) && $search !=""){
	         
	        $select->where(
	    
	            new \Zend\Db\Sql\Predicate\PredicateSet(
	                array(
	                    new \Zend\Db\Sql\Predicate\Operator('pk_order_no', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('customer_name', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('location', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('phone', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('promo_code', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('amount', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('order_date', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('order_status', 'LIKE', '%'.$search.'%'),
	                	new \Zend\Db\Sql\Predicate\Operator('order_menu', 'LIKE', '%'.$search.'%'),
	                    new \Zend\Db\Sql\Predicate\Operator('last_modified', 'LIKE', '%'.$search.'%'),
	                ),
	                // optional; OP_AND is default
	                \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
	            )
	             
	        );
	         
	    }
	    
	    $select->order($order_by . ' ' . $order);
	    $orders = $this->getOrderTable()->fetchAll($select,$page);
	     
	    $orders->setCurrentPageNumber($page)
	    ->setItemCountPerPage($itemsPerPage)
	    ->setPageRange(7);
	    
	    $returnVar = array();
	    $returnVar['draw'] = $draw;
	    $returnVar['recordsTotal'] = $orders->getTotalItemCount();
	    $returnVar['recordsFiltered'] = $orders->getTotalItemCount();
	    $returnVar['data'] = array();
	    
	  
	    foreach($orders as $order){
	    
	    	$orderPaymentDetails = $tblFront->getOrderBal($order['pk_order_no'],'pre_orders');
	    	$arrDays = explode(",",$order["order_days"]);
	    	$totalDays = count($arrDays);
	    	
	    	$orderAmount = number_format($totalDays * $orderPaymentDetails['order_amount'],2);
	    	$totalDiscount = number_format($totalDays * $orderPaymentDetails['discount'],2);
	    	$totalDelivery = number_format($totalDays * $orderPaymentDetails['delivery'],2);
	    	$totalLockedAmount = number_format($totalDays * $orderPaymentDetails['lockedamt'],2);
	    	
	    	sort($arrDays);

	    	$startDay = $arrDays[0];
	    	
	    	$endDay = $arrDays[$totalDays-1];
	    	
	    //	$startDate = \DateTime::createFromFormat('Y-m-d', $startDay);
	    	//$endDate = \DateTime::createFromFormat('Y-m-d', $endDay);
	   
	    	//echo "<pre>";print_r($orderPaymentDetails);echo "</pre>";die;
	    	
	        //echo $customer->customer_name."<br />";
	        
	    	$pk_order_no="<a href=". $this->url()->fromRoute('preorders', array('action' => 'view', 'id' => $order['pk_order_no'])).">".$order['pk_order_no']."</a>";
	    	//$customer_name="<a href=". $this->url()->fromRoute('order', array('action' => 'view', 'id' => $order['pk_order_no'])).">".$order['customer_name']."</a>";
	    	
	    	$status = $order['order_status']=='Cancel'?'Cancelled':$order['order_status'];
	        $arrTmp = array();
	        array_push($arrTmp,$pk_order_no);
	        array_push($arrTmp,$order['customer_name']);
	        array_push($arrTmp,$order['phone']);
	        array_push($arrTmp,$order['location']);
	        array_push($arrTmp,$order['promo_code']);
	        array_push($arrTmp,$orderAmount);
	        array_push($arrTmp,$totalDiscount);
	        array_push($arrTmp,$totalDelivery);
	        array_push($arrTmp,$totalLockedAmount);
	        array_push($arrTmp,$status);
	        array_push($arrTmp,ucfirst($order['order_menu']));
	        array_push($arrTmp,$utility->displayDate($startDay,$setting['DATE_FORMAT']));
	        array_push($arrTmp,$utility->displayDate($endDay,$setting['DATE_FORMAT']));
	        
	        $str = "";
	        
	        if($acl->isAllowed($loggedUser->rolename,'preorders','cancelpreorder') && $order['order_status']!="Cancel"){
	        	$confirmMsg = "Are you sure do you really want to perform this operation?";
                //$str = '<button class="smBtn redBg has-tip tip-top" onclick="location.href=\''.$this->url()->fromRoute('order', array('action'=>'cancelorder','id' => $order_show['pk_order_no'],'c_id' => $order_show['customer_code'])).'\'" data-tooltip  title="Delete"><i class="fa fa-trash-o"></i></button></td>';
                $str = '<button class="smBtn redBg has-tip tip-top" data-tooltip  onclick="javsacript:if(confirm(\''.$confirmMsg.'\')){location.href=\''.$this->url()->fromRoute('preorders', array('action'=>'cancelpreorder', 'id' =>  $order['pk_order_no'],'c_id'=>$order['customer_code'])).'\'}" title="Cancel Pre Order"><i class="fa fa-ban"></i></button>';
	        }
	        
            array_push($arrTmp,$str);
	        array_push($returnVar['data'],$arrTmp);
	    }
	     
	    return new JsonModel($returnVar);
	}
	
	/**
	 * To view preorder of given preorder id
	 *
	 * @return array
	 */
	public function viewAction()
	{
		$id = (int) $this->params('id');
	//	echo "<pre>";print_r($id);die;
		if (!$id) {
			return $this->redirect()->toRoute('order', array('action' => 'add'));
		}
		$order = $this->getOrderTable()->getOrder($id);


		//echo "<pre>";print_r($order);die;
		//$order= $order->current();
		
		$this->layout()->setVariables(array('page_title'=>"Edit Pre Order",'description'=>"to be delivered",'breadcrumb'=>"Edit Pre Order"));
		
		return array(
				'id' => $id,
				'page_price' => $config_variables['page_price'],
				'orders'=>$order
		);
	}
	/**
	 * To delete the preorder of given preorder id
	 *
	 * @param int id
	 * @return route order
	 */
	public function deleteAction()
	{
		$id = (int) $this->params('id');
		if (!$id)
		{
			return $this->redirect()->toRoute('order');
		}
		$this->getOrderTable()->deleteOrder($id);
		return $this->redirect()->toRoute('order');
	}
	/**
	 * Get instance of QuickServe\Model\PreordersTable
	 *
	 * @return QuickServe\Model\PreordersTable
	 */
	public function getOrderTable()
	{
		if (!$this->ordertable)
		{
			$sm = $this->getServiceLocator();
			$this->ordertable = $sm->get('QuickServe\Model\PreordersTable');
		}
		return $this->ordertable;
	}
	/**
	 * To cancel preorder
	 *
	 * @param int id
	 * @param int c_id
	 * @return route preorder
	 */
	public function cancelpreorderAction(){
		
		$id = (int) $this->params()->fromRoute('id', 0);
		$customer_id = (int) $this->params()->fromRoute('c_id', 0);
		$this->getFrontTable()->cancelmyorder($customer_id,$id);
		
		$message['success']="PreOrder ID : $id is marked as cancelled";
		$this->flashmessenger()->addSuccessMessage("PreOrder ID : $id is marked as cancelled");

		return $this->redirect()->toRoute('preorders');

		//NEED TO WRITE EMAIL SENDING FUNCTIONALITY
	}
	/**
	 * Get instance of Front\Model\FrontTable
	 *
	 * @return Front\Model\FrontTable
	 */
	public function getFrontTable(){
        if (!$this->frontTable) {
            $sm = $this->getServiceLocator();
            $this->frontTable = $sm->get('Front\Model\FrontTable');
        }
        return $this->frontTable;
    }
}
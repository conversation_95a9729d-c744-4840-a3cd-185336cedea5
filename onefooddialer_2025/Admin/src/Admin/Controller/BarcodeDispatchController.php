<?php
/**
 * This file contains the code related to order dispatch
 * Order Dispatch is the very important module of fooddialer system
 * When order is prepaired ,it must be dispatched before it get delivered
 * So once order get prepared,that order get dispatched
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: OrderDispatchController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;
use DOMPDFModule\View\Model\PdfModel;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use Lib\QuickServe\CommonConfig as Qscommon;
use Lib\QuickServe\Order as QSOrder;

use QuickServe\Model\BarcodeDispatchValidator;

use Admin\Form\BarcodeDispatchForm;

class BarcodeDispatchController extends AbstractActionController
{
	
	/**
	 * It has an instance of QuickServe\Model\OrderDispatchTable model
	 *
	 * @var QuickServe\Model\OrderDispatchTable $orderdispatchTable
	 */
	protected $orderdispatchTable;
	/**
	 * It has an instance of QuickServe\Model\OrderDispatchTable model
	 *
	 * @var QuickServe\Model\OrderDispatchTable $orderdispatchTable
	 */
	protected $barcodedispatchTable;
	
	protected $frontTable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * To display the prepared order by locationwise
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		$todays_date=date('Y-m-d');
// 		echo $todays_date; exit();
		//echo "inside barcode"; exit();
		if (! $this->authservice)
		{
			$this->authservice = $this->getServiceLocator()
			->get('AuthService');
		}
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$form = new BarcodeDispatchForm($adapt);
		
		$libOrder = QSOrder::getInstance($sm);

		$request = $this->getRequest();
		
		if ($request->isPost()) {
		
			$validator = new BarcodeDispatchValidator();
			$validator->getInputFilter()->get('barcode')
			->getValidatorChain() 
			->attach(new \Zend\Validator\Db\RecordExists(array(
					'table'     => 'order_barcodes',
					'field'     => 'barcode',
					'adapter'   => $adapt,
					'message'   => "Barcode doesn't exists",
			
			)
			));
			
			$form->setInputFilter($validator->getInputFilter());
				
			$form->setData($request->getPost());
		
			if ($form->isValid()) {
				
				$barcode = $request->getPost('barcode');
				$returnData = $this->getBarcodeDispatchTable()->getOrderIdByBarcode($barcode);	

				if($returnData['order_date']>$todays_date)
				{
					$error_messages = array('msg'=>"Can't dispatch orders of future date");
					return new JsonModel(array(
							'error' => true,
							'form_validation_error' => $error_messages
					));
				}
				//$orderData = $this->getBarcodeDispatchTable()->getTodaysorder($returnData['order_id']); 
				//echo "data<pre>"; print_r($returnData);
				$orderData = $this->getBarcodeDispatchTable()->getTodaysorder($returnData['order_no'],$returnData['order_date']);
				
// 				echo "<pre>"; print_r($orderData);
				
				if(empty($orderData)){
					$error_messages = array('msg'=>"Order already dispatched");
					return new JsonModel(array(
							'error' => true,
							'form_validation_error' => $error_messages
					));

				}else{
					$menu = $orderData[$returnData['order_no']][0]['order_menu'];
					
					$select = new QSelect();
					$select->where(array('order_menu'=>$menu));
					$prepared_orders = $this->getOrderDispatchTable()->fetchAll($select);
					
					$prod_id = array();
					$newArray = array();

					foreach($prepared_orders as $setArray)
					{	if(strval($setArray['date'])==strval($todays_date))
						$newArray[$setArray['fk_product_code']] = $setArray['prepared'] - $setArray['dispatch'];
					}

					$mealTableObj = $sm->get('QuickServe\Model\MealTable');
					$mainlocarray = array();
					$kitchenProducts = array();
					
					foreach ($orderData as $orderId=>$data)
					{
						foreach($data as $prod=>$proddata)
						{
							
							if($proddata['product_type']=='Meal'){
								
								$mealObj = $mealTableObj->getMeal($proddata['product_code']);
								$mealItems = $mealObj->getItems();
							
								foreach ($mealItems as $prod_id=>$desc){
								
									if(!isset($mainlocarray[$prod_id])){
										$mainlocarray[$prod_id] = ( $desc['quantity'] * $proddata['quantity']);
									}else{
											
										$mainlocarray[$prod_id] += ( $desc['quantity'] * $proddata['quantity']);
									}
									
									if(!isset($kitchenProducts[$proddata['fk_kitchen_code']][$proddata['order_menu']][$prod_id])){
										$kitchenProducts[$proddata['fk_kitchen_code']][$proddata['order_menu']][$prod_id] = ( $desc['quantity'] * $proddata['quantity']);
									
									}else{
										$kitchenProducts[$proddata['fk_kitchen_code']][$proddata['order_menu']][$prod_id] += ( $desc['quantity'] * $proddata['quantity']);
									} 
									
								}
							}else{
									
								if(!isset($mainlocarray[$proddata['product_code']])){
					
									$mainlocarray[$proddata['product_code']] = $proddata['quantity'];
					
								}else{
										
									$mainlocarray[$proddata['product_code']] += $proddata['quantity'];
								}
								
								if(!isset($kitchenProducts[$proddata['fk_kitchen_code']][$proddata['order_menu']][$proddata['product_code']])){
									$kitchenProducts[$proddata['fk_kitchen_code']][$proddata['order_menu']][$proddata['product_code']] = $proddata['quantity'];
								
								}else{
									$kitchenProducts[$proddata['fk_kitchen_code']][$proddata['order_menu']][$proddata['product_code']] += $proddata['quantity'];
								} 
							} 
						}
					}
					
					foreach($mainlocarray as $prod_id=>$limit)
					{	
						if(array_key_exists($prod_id, $newArray))
						{
							if($newArray[$prod_id] < $limit)
							{
								$error_messages = array('msg'=>"Order not yet prepared");
								return new JsonModel(array(
										"error"=>true,
										"form_validation_error"=>$error_messages
								)
								);
							}	
						}
					
					}
					
					$orders= array();
					$orders[] = $returnData['order_no'];
					
					$libOrder->updateOrders($orders,$todays_date);
					$libOrder->updateKitchen($kitchenProducts,$todays_date);
					
					return new JsonModel(array(	
							"success"=>true,
							"orderData"=>$orderData
						)
					);
				
			 	}

			}else{
				
				$messages = $form->getMessages();
				$error_messages = array();
				
				foreach ($messages as $e_key => $e_val) {
					foreach ($e_val as $val) {
						$error_messages[] = $val;
					}
				}
				return $messages = new JsonModel(array(
						'error' => true,
						'form_validation_error' => $error_messages,
						
				));
			}
		}
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		$this->layout()->setVariables(array('page_title'=>"Dispatch Orders",'description'=>"Ready for delivery",'breadcrumb'=>"Dispatch Orders By Barcode","back"=>"orderdispatch"));
		
		return new ViewModel(array(
			'acl' => $acl,
			'loggedUser' => $loguser,
			'form'=>$form,
		));
	}
	/**
	 * Get instance of QuickServe\Model\OrderDispatchTable
	 *
	 * @return QuickServe\Model\OrderDispatchTable
	 */
	public function getBarcodeDispatchTable() {
		if (!$this->barcodedispatchTable) {
			$sm = $this->getServiceLocator();
			$this->barcodedispatchTable = $sm->get('QuickServe\Model\BarcodeDispatchTable');
		}
		return $this->barcodedispatchTable;
	}
	
	public function printLabelDoctorktnAction()
	{
		//echo "gfgdfgdf";die;
		$view = new ViewModel();
		$view->setTerminal(true);
		return TRUE;
	}

	/**
	 * Get instance of QuickServe\Model\OrderDispatchTable
	 *
	 * @return QuickServe\Model\OrderDispatchTable
	 */
	public function getOrderDispatchTable() {
		if (!$this->orderdispatchTable) {
			$sm = $this->getServiceLocator();
			$this->orderdispatchTable = $sm->get('QuickServe\Model\OrderDispatchTable');
		}
		return $this->orderdispatchTable;
	}
}
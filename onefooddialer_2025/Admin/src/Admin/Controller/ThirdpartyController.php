<?php
/**
 * This file manages the delivery locations on fooddialer system
 * The activity includes add,update and delete delivery locations
 *
 * PHP versions 5.4
 *
 * Project name Quickserve
 * @version 1.1: ThirdpartyController.php 2015-04-08 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\QuickServe\CommonConfig as Qscommon;

use Admin\Form\ThirdpartyForm;
use QuickServe\Model\ThirdpartyValidator;

class ThirdpartyController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\ThirdpartyTable model
	 *
	 * @var QuickServe\Model\LocationTable $thirdpartyTable
	 */
	protected $thirdpartyTable;
	/**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * This function used to display the list of third party
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
            if (! $this->authservice) {
                $this->authservice = $this->getServiceLocator()->get('AuthService');
            }

            $iden = $this->authservice->getIdentity();

            $select = New QSelect();
            $order_by = $this->params()->fromRoute('order_by')?
                                    $this->params()->fromRoute('order_by'):'third_party_id';
            
            $order = $this->params()->fromRoute('order')?
                             $this->params()->fromRoute('order'): QSelect::ORDER_DESCENDING;
           
            $page = $this->params()->fromRoute('page') ? (int) $this->params()->fromRoute('page') : 1;

            $thirdparty = $this->getThirdPartyTable()->fetchAll($select->order($order_by . ' ' . $order));
            $returnvar = $thirdparty->toArray();
            $itemsPerPage = 2;

            $thirdparty->current();
            $paginator = new Paginator(new paginatorIterator($thirdparty));
            $paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);
            
            $layoutviewModel = $this->layout();
            $acl =$layoutviewModel->acl;
            $loguser = $layoutviewModel->loggedUser;
            
            $this->layout()->setVariables(array('page_title'=>"Third Party",'description'=>"Third Party",'breadcrumb'=>"Third Party"));
            return new ViewModel(array(
                'order_by' => $order_by,
                'order' => $order,
                'page' => $page,
                'paginator' => $returnvar,
                'acl' => $acl,
                'loggedUser' => $loguser,
                'flashMessages'=> $this->flashMessenger()->getMessages()
            ));
	}
	
	/**
	 * To add new third party
	 *
	 * @return \Admin\Form\ThirdpartyForm
	 */
	public function addAction()
	{
            $layoutviewModel = $this->layout();
            $acl =$layoutviewModel->acl;
            $loguser = $layoutviewModel->loggedUser;

            $log_user_id=$loguser->pk_user_code;

            $sm = $this->getServiceLocator();
            $adapt = $sm->get('Write_Adapter');

            $libCommon = Qscommon::getInstance($sm);

            $form = new ThirdpartyForm($sm);
            
            $form->get('submit')->setAttribute('value', 'Add');

            $request = $this->getRequest();
            
            if (!$request->isPost()) { // if not post then set checkbox to 'checked'
                $form->get('create_account')->setValue(1); // added sankalp
            }
             
            if ($request->isPost()) {
              
                $form->setValidationGroup('name','phone','email','thirdparty_type','thirdparty_system','charges_type','commission_type','comission_rate','status');
                
                if($request->getPost('create_account') == true){

                    $form->setValidationGroup('name','phone','email','thirdparty_type','thirdparty_system','charges_type','commission_type','comission_rate','status', 'password', 'confirm_password', 'city', 'location', 'address');

                }
                  
                $thirdparty = new ThirdpartyValidator();
                
                $thirdparty->getInputFilter()->get('email')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                'table'     => 'third_party',
                                'field'     => 'email',
                                'adapter'   => $adapt,
                                'message'   => 'Email already exists',
                    )
                ));
                
                $thirdparty->getInputFilter()->get('email')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                'table'     => 'users',
                                'field'     => 'email_id',
                                'adapter'   => $adapt,
                                'message'   => 'Email already exists',
                    )
                ));
                
                $thirdparty->getInputFilter()->get('phone')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                'table'     => 'third_party',
                                'field'     => 'phone',
                                'adapter'   => $adapt,
                                'message'   => 'Phone number already exists',
                    )
                ));
                
                $thirdparty->getInputFilter()->get('phone')
                ->getValidatorChain()                  // Filters are run second w/ FileInput
                ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                'table'     => 'users',
                                'field'     => 'phone',
                                'adapter'   => $adapt,
                                'message'   => 'Phone number already exists',
                    )
                ));
                
                //$thirdparty->setAdapter($adapt);
                
                $form->setInputFilter($thirdparty->getInputFilter());
                
                $form->setData($request->getPost());
                
                if ($form->isValid()) {
                    
                    $formData = $form->getData(); 
              
                    $sql                = "SELECT role_name FROM roles WHERE pk_role_id = ".$form->getData()['thirdparty_type'];

                    $rol_name           = $adapt->query($sql,Adapter::QUERY_MODE_EXECUTE)->toArray();

                    if($rol_name[0]['role_name'] == 'Third-Party Aggregator'){

                        $formData['is_aggregator'] = 1;   // is_aggregator
                    }
                    
                    $thirdparty->exchangeArray($formData);

                    $data_third_party = $this->getThirdPartyTable()->saveThirdParty($thirdparty,$log_user_id);
				
                    if($request->getPost('create_account') == true){
                    
                        $array['first_name']    = $form->getData()['name'] ;
                        $array['phone']         = $form->getData()['phone'] ;
                        $array['email_id']      = $form->getData()['email'] ;
                        $array['role_id']       = $form->getData()['thirdparty_type'] ;
                        $array['status']       = $form->getData()['status'] ;
                        $array['password']       = $form->getData()['password'] ;
                        $array['third_party_id'] = $data_third_party['third_party_id'] ;

                        $user = new \QuickServe\Model\User();
                        $user->exchangeArray($array);

                        $data_user = $this->getUserTable()->saveUser($user);
                        
                        if($data_user['last_id']){
                            $fk_user_code = $data_user['last_id'];
                            $fk_location_code =  $form->getData()['location'] ;
                            $fk_city_code =  $form->getData()['city'] ;
                            $date = date('Y-m-d');
                            $sql = "INSERT INTO user_locations (fk_user_code, fk_location_code, fk_city_code, created_date) values ('$fk_user_code', '$fk_location_code', '$fk_city_code', '$date')";
                            
                            $result2 = $adapt->query(
                                            $sql, $adapt::QUERY_MODE_EXECUTE
                            );

                        }
                    }
                                
                    $full_name=$loguser->first_name." ".$loguser->last_name;
                    $activity_log_data=array();
                    $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                    $activity_log_data['context_name']= $full_name;
                    $activity_log_data['context_type']= 'user';
                    $activity_log_data['controller']= 'Thirdparty';
                    $activity_log_data['action']= 'add';
                    $activity_log_data['description']= "ThirdParty : New Thirdparty '".$thirdparty->name."' created.";

                    $libCommon->saveActivityLog($activity_log_data);

                    ($data_third_party) ?$this->flashMessenger()->addSuccessMessage("Third Party added successfully"):$this->flashMessenger()->addErrorMessage("Error adding third party.");
                    return $this->redirect()->toRoute('thirdparty');
                }
                else{
//                    $form->get('create_account')->setValue(1); // added sankalp
                }
                
            }
            
            $this->layout()->setVariables(array('page_title'=>"Add Third Party",'breadcrumb'=>"Add Third Party"));
            return array('form' => $form);
	}
	
        public function getUserTable() {
            if (!$this->userTable) {
                $sm = $this->getServiceLocator();
                $this->userTable = $sm->get('QuickServe\Model\UserTable');
            }
            return $this->userTable;
        }
        
	/**
	 * To update the third party of given third party id
	 * @param int id
	 * @return \Admin\Form\ThirdpartyForm
	 */
	public function editAction()
	{
		$id = (int) $this->params('id');
		if (!$id) {
                    return $this->redirect()->toRoute('thirdparty', array('action' => 'add'));
		}
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		$thirdparty = $this->getThirdpartyTable()->getThirdparty($id);
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		$form = new ThirdpartyForm($sm);
		$form->bind($thirdparty);
		$form->get('submit')->setAttribute('value', 'Edit');
		
                $email = $thirdparty->email;
                $phone = $thirdparty->phone;
                $is_aggregator = $thirdparty->is_aggregator;
                
		$request = $this->getRequest();
                
                $status = $thirdparty['status'];
                $third_party_id = $thirdparty['third_party_id'];
                
                $errStr = '';
		if ($request->isPost())
                { 
                    $form->setValidationGroup('third_party_id','name','phone','email','address', 'charges_type','commission_type','comission_rate','status');

                    $thirdparty = new ThirdpartyValidator();

                    $thirdparty->getInputFilter()->get('email')
                    ->getValidatorChain()                  // Filters are run second w/ FileInput
                    ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                    'table'     => 'third_party',
                                    'field'     => 'email',
                                    'adapter'   => $adapt,
                                    'message'   => 'Email already exists',
                                    'exclude' => array(
                                                    'field' => 'email',
                                                    'value' => $email,
                                    )
                        )
                    ));
                    
                    $thirdparty->getInputFilter()->get('phone')
                    ->getValidatorChain()                  // Filters are run second w/ FileInput
                    ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                    'table'     => 'third_party',
                                    'field'     => 'phone',
                                    'adapter'   => $adapt,
                                    'message'   => 'Phone number already exists',
                                    'exclude' => array(
                                                    'field' => 'phone',
                                                    'value' => $phone,
                                    )
                    )
                    ));
                    
                    $thirdparty->getInputFilter()->get('email')
                    ->getValidatorChain()                  // Filters are run second w/ FileInput
                    ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                    'table'     => 'users',
                                    'field'     => 'email_id',
                                    'adapter'   => $adapt,
                                    'message'   => 'Email already exists',
                                    'exclude' => array(
                                                    'field' => 'email_id',
                                                    'value' => $email,
                                    )
                        )
                    ));
                    
                    $thirdparty->getInputFilter()->get('phone')
                    ->getValidatorChain()                  // Filters are run second w/ FileInput
                    ->attach(new \Zend\Validator\Db\NoRecordExists(array(
                                    'table'     => 'users',
                                    'field'     => 'phone',
                                    'adapter'   => $adapt,
                                    'message'   => 'Phone number already exists',
                                    'exclude' => array(
                                                    'field' => 'phone',
                                                    'value' => $phone,
                                    )
                    )
                    ));
                    
                    //$thirdparty->setAdapter($adapt);
                    $form->setInputFilter($thirdparty->getInputFilter());
                    $form->setData($request->getPost());
                    
                    if ($form->isValid()) {
                        
                        $formData = $form->getData();
                        $formData['is_aggregator'] = $is_aggregator;
                        
                        $thirdparty->exchangeArray($formData);
                        
                        if($status != $form->getData()['status']){
                            $third_party_user = $this->getUserTable()->getUser($third_party_id, 'third_party_id');
                            
                            $data['pk_user_code'] = $third_party_user['pk_user_code'];
                            $data['status'] = $form->getData()['status'];
                            $data['email_id'] = $form->getData()['email'];
                            $data['phone'] = $form->getData()['phone'];
                            $data['first_name'] = $form->getData()['name'];

                            $user = new \QuickServe\Model\User();
                            $user->exchangeArray($data);

                            $return = $this->getUserTable()->saveUser($user);
                            
                        }
                        
                        $return_data = $this->getThirdpartyTable()->saveThirdparty($thirdparty);
                        
                        if($return_data){

                            $full_name=$loguser->first_name." ".$loguser->last_name;
                            $activity_log_data=array();
                            $activity_log_data['context_ref_id']=$loguser->pk_user_code;
                            $activity_log_data['context_name']= $full_name;
                            $activity_log_data['context_type']= 'user';
                            $activity_log_data['controller']= 'Thirdparty';
                            $activity_log_data['action']= 'add';
                            $activity_log_data['description']= "ThirdParty : New Thirdparty '".$thirdparty->name."' updated.";

                            //$activity_log_data['description']= "'$discount_name' discount $discount_status by $full_name";
                            $libCommon->saveActivityLog($activity_log_data);

                        }


                        $this->flashMessenger()->addSuccessMessage("Third party updated successfully");
                        return $this->redirect()->toRoute('thirdparty');
                    }else {
                        $error_array = $form->getMessages(); // form->getMessages avaiable only after checking isValid 
                        
                        if( is_array($error_array) && !empty($error_array) ) {
                                foreach($error_array as $ear => $error) {
                                    $errStr = empty($errStr) ? $errStr : $errStr.'<br/>';
                                    if( is_array($error) ) {
                                            $errStr.= implode('<br/>', $error);
                                    }
                                    else {
                                            $errStr.= $error;
                                    }

                                }
                        }
                    }
		}

		$this->layout()->setVariables(array('page_title'=>"Edit Third Party",'breadcrumb'=>"Edit Third Party"));
		
		return array(
				'id' => $id,
				'form' => $form,
                                'errStr'=>$errStr
		);
	}
	
	/**
	 * To delete the third party of given third party id
	 * @param int id
	 * @return route third party
	 */
	public function deleteAction() {
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		

		
		$id = (int) $this->params('id');
		if (!$id) {
			return $this->redirect()->toRoute('thirdparty');
		}
		
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = Qscommon::getInstance($sm);
		
		$select = new QSelect();
		$select->where(array('third_party_id'=>$id));
		$locations = $this->getThirdPartyTable()->fetchAll($select);
		$arrlocation=$locations->toArray();
	
		
		$thirdpartyName=$arrlocation[0]['name'];
		$thirdparty_status=($arrlocation[0]['status'])=='1'?'deactivated':'activated';
		
		
		$data_thirdparty=$this->getThirdpartyTable()->deleteThirdparty($id);
		
		if($data_thirdparty){
			
			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'Thirdparty';
			$activity_log_data['action']= 'delete';
			$activity_log_data['description']= "ThirdParty : ThirdParty $thirdpartyName $thirdparty_status.";
				
			//$activity_log_data['description']= "'$discount_name' discount $discount_status by $full_name";
			$libCommon->saveActivityLog($activity_log_data);
			
		}
		
		($data_thirdparty) ?$this->flashMessenger()->addSuccessMessage("Third party updated successfully"):$this->flashMessenger()->addErrorMessage("Error updating third party.");
		return $this->redirect()->toRoute('thirdparty');
	}
	
	/**
	 * Get instance of QuickServe\Model\ThirdpartyTable
	 *
	 * @return QuickServe\Model\ThirdpartyTable
	 */
	public function getThirdPartyTable()
	{
		
		if (!$this->thirdpartyTable) {
			$sm = $this->getServiceLocator();
			$this->thirdpartyTable = $sm->get('QuickServe\Model\ThirdpartyTable');
		}
		return $this->thirdpartyTable;
	}
}

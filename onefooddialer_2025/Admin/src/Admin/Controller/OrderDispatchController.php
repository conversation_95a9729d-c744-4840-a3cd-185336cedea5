<?php
/**
 * This file contains the code related to order dispatch
 * Order Dispatch is the very important module of fooddialer system
 * When order is prepaired ,it must be dispatched before it get delivered
 * So once order get prepared,that order get dispatched
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: OrderDispatchController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use DOMPDFModule\View\Model\PdfModel;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\QuickServe\CommonConfig as Qscommon;
use Lib\QuickServe\Order as QSOrder;


class OrderDispatchController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\OrderTable model
	 *
	 * @var QuickServe\Model\OrderDispatchTable $ordertable
	 */
	protected $ordertable;
	/**
	 * It has an instance of QuickServe\Model\OrderDispatchTable model
	 *
	 * @var QuickServe\Model\OrderDispatchTable $orderdispatchTable
	 */
	protected $orderdispatchTable;
	/**
	 * It has an instance of QuickServe\Model\KitchenMasterTable model
	 * 
	 * @var QuickServe\Model\KitchenMasterTable $kitchenMasterTable
	 */
	protected $kitchenMasterTable;
	
	protected $activitylogTable;
	
	protected $frontTable;
    protected  $userTable;
    /**
	 * It has an instance of AuthService model
	 *
	 * @var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * To display the prepared order by locationwise
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
        
		if (! $this->authservice){
			$this->authservice = $this->getServiceLocator()->get('AuthService');
		}
        
		$iden = $this->authservice->getIdentity();
        $sm = $this->getServiceLocator();
        
        //migration function		
		$libOrder = QSOrder::getInstance($sm);
		$migration = $libOrder->migrationProcess();
		//migration function
		
		$location_data=$this->getOrderTable()->getLocationData();
		
		$config_variables = $sm->get('config');
		$menuSelected = $this->params()->fromQuery('menu','breakfast');

		$location = $this->params()->fromQuery('location','');
		
		$kitchenSelected = $_SESSION['adminkitchen'];
		
		$purpose = $this->params('purpose');
		//$purpose = $menu = $this->params()->fromQuery("purpose","");
		//echo $purpose; exit();
		$setting_session = new Container('setting');
        
		$menus = $setting_session->setting['MENU_TYPE'];
		$kitchens = $this->getKitchenMasterTable()->fetchAll();
		$kitchens_arr = $kitchens->toArray();
		$default_kitchen = $kitchens_arr[0]['pk_kitchen_code'];
        
        $skipKitchenCheck = $setting_session->setting['GLOBAL_SKIP_KITCHEN'];
        
		$showbarcode = $setting_session->setting['PRINT_LABEL_SHOW_BARCODE'];
//        dd($setting_session->setting['PRINT_LABEL_SHOW_BARCODE']);
		$sess_acceptedMenu = new Container('acceptedMenu');
		
		if($menuSelected ==""){
			$menuSelected = $sess_acceptedMenu->menu['default'];
		}
		
		$today = date("Y-m-d");
		
		$select = new QSelect();
		
		if($kitchenSelected != 'all') {
			$select->where(array('fk_kitchen_code'=>$kitchenSelected));
		}else{
            $select->where->in('fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
        
		$select->where(array('order_menu'=>$menuSelected));
		
		$select->where(array('kitchen.date'=>$today));
		
		$prepared_orders = $this->getOrderDispatchTable()->fetchAll($select);
		
        if($skipKitchenCheck=='no'){
            
            $flag = 0;

            foreach ($prepared_orders as $key => $val)
            {
                if($val['prepared'] > 0 && $val['prepared']!=$val['dispatch'])
                {
                    $flag = 1;
                }
            }
        
        }else{
            $flag = 1;
        }
        
        $controller = 'orderdispatch';
        $action = 'printLabel';
        $date = date('Y-m-d');

        $enablediapatch = 0;

        $activity_log_count = $this->getActivityLogTable()->getPrintActivity($controller,$action,$date,$menuSelected);
		if($activity_log_count >0){
            $enablediapatch = 1;
        }
        
		$select = new QSelect();
		
		if($kitchenSelected != 'all') {
			$select->where(array('fk_kitchen_code'=>$kitchenSelected));
		}else{
            $select->where->in('fk_kitchen_code', array_column($iden->kitchens, 'fk_kitchen_code') );
        }
        
		$select->where(array('order_menu'=>$menuSelected));
		
		if($location!='' && $location!='all'){
			$location = $location;
		}else {
		 	$location = false;
		}
		//echo '<pre>'; print_r($location); echo '</pre>';
		//die;
		$select->group('order_details.ref_order_no');

		$loc_arr = array();
		array_push($loc_arr, $location);
		$order_loc = $this->getOrderDispatchTable()->getOrderLocation($loc_arr,$select);
        
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
	
		$this->layout()->setVariables(array('page_title'=>"Dispatch Orders",'description'=>"Ready for delivery",'breadcrumb'=>"Dispatch Orders"));
		return new ViewModel(array(
			'prepared_orders' => $prepared_orders,
			'orderdata'=>$order_loc,
			'acl' => $acl,
			'menus' => $menus,
			'kitchens' => $kitchens,
			'menuSelected' => $menuSelected,
			'kitchenSelected' => $kitchenSelected,
			'loggedUser' => $loguser,
			'languages' => $config_variables['supported_nonenglish_languages'],
			'purpose' => $purpose,
			'flag' => $flag,
			'enablediapatch' => $enablediapatch,
			'location_data' => $location_data,
			'locationcode' =>$location,
			'skipKitchenCheck'=>$skipKitchenCheck,
            'showbarcode'=>$showbarcode
		));
	}

	/**
	 * Get instance of QuickServe\Model\KitchenMasterTable
	 *
	 * @return QuickServe\Model\KitchenMasterTable
	 */
	public function getKitchenMasterTable() {
		if (!$this->kitchenMasterTable) {
			$sm = $this->getServiceLocator();
			$this->kitchenMasterTable = $sm->get('QuickServe\Model\KitchenMasterTable');
		}
		return $this->kitchenMasterTable;
	}
	/**
	 * Get instance of QuickServe\Model\OrderTable
	 *
	 * @return QuickServe\Model\OrderTable
	 */
	public function getOrderTable()
	{
		if (!$this->ordertable)
		{
			$sm = $this->getServiceLocator();
			$this->ordertable = $sm->get('QuickServe\Model\OrderTable');
		}
		return $this->ordertable;
	}
	/**
	 * Get instance of QuickServe\Model\OrderDispatchTable
	 *
	 * @return QuickServe\Model\OrderDispatchTable
	 */
	public function getOrderDispatchTable() {
		if (!$this->orderdispatchTable) {
			$sm = $this->getServiceLocator();
			$this->orderdispatchTable = $sm->get('QuickServe\Model\OrderDispatchTable');
		}
		return $this->orderdispatchTable;
	}
	
	public function printLabelDoctorktnAction()
	{
		$view = new ViewModel();
		$view->setTerminal(true);
		return TRUE;
	}
	/**
	 * With this action order gets dispatched & sent to delivery person to deliver
	 * All dispatched orders label is printed
	 * This function prints labels of dispatched orders
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function printLabelAction()
	{

		//error_reporting(E_ALL & ~E_NOTICE);
		//ini_set('display_errors', 'On'); 		        
		
		$sm = $this->getServiceLocator();
	        $libCommon = Qscommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
        
		$purpose = $this->params()->fromRoute('purpose');
		
		$location_code = $this->params()->fromRoute('location');
		$location_code1 = $this->params()->fromQuery('location');
		$language_array = $this->params()->fromQuery('language');
		
		$layout = $this->layout();
	    $loggedUser = $layout->loggedUser;
        
        $setting_session = new Container('setting');
		$setting = $setting_session->setting;

        $skipKitchenCheck = $setting['GLOBAL_SKIP_KITCHEN'];
        
		$menu = $this->params()->fromQuery("menu","");
		$kitchen = $this->params()->fromQuery('kitchen','all');

		$sms_common = $libCommon->getSmsConfig($setting);
		$config = $this->getServiceLocator()->get('Config');
		
		$adapt = $sm->get('Write_Adapter');
		
		$this->getOrderDispatchTable()->setServiceLocator($sm);

		$today = date("Y-m-d");
		$select = new QSelect();
        
        if($kitchen !=0){
            $kitchens = array($kitchen);
        }else{
            $kitchens = array_column($loggedUser->kitchens, 'fk_kitchen_code');
        }
        
		$select->where->in('fk_kitchen_code', $kitchens);
		$select->where(array('order_menu'=>$menu));
		$select->where(array('date'=>$today));
		
		$prepared_orders = $this->getOrderDispatchTable()->fetchAll($select);
		
		$select = new QSelect();
		
		if($menu !=""){
			$select->where(array("order_menu" => $menu));
		}
		
		$select->where->in('fk_kitchen_code', $kitchens);
		
		if($skipKitchenCheck=='no'){
            
            $check_data = $this->getOrderDispatchTable()->getOrderLocation($location_code1,$select);

            //echo count($check_data['4']['products']);
            //die;
            $flg = 0;
            foreach ($location_code1 as $key => $val)
            {	

                if(count($check_data[$val]['products']) !=  0)
                {
                    $flg = 1;
                }
            }
            if($flg == 0)
            {
                echo "No new orders";exit;
            }


            $prod_id = array();
            foreach($prepared_orders as $setArray)
            {
                $newArray[$setArray['fk_product_code']] = $setArray['prepared'] - $setArray['dispatch'];
            }

            $mainlocarray = array();
            $kitchenProducts = array();

            foreach ($check_data as $loc=>$data)
            {

                foreach($data['products'] as $prod=>$proddata)
                {
                    foreach ($proddata as $key=>$details){

                        if($key==='total' || $key==='name'){
                            continue;
                        }

                        if(!isset($mainlocarray[$details['detail_product_code']])){
                            $mainlocarray[$details['detail_product_code']] = $details['detail_quantity'];

                        }else{
                            $mainlocarray[$details['detail_product_code']] += $details['detail_quantity'];
                        } 

                        if(!isset($kitchenProducts[$details['fk_kitchen_code']][$details['order_menu']][$details['detail_product_code']])){
                            $kitchenProducts[$details['fk_kitchen_code']][$details['order_menu']][$details['detail_product_code']] = $details['detail_quantity'];

                        }else{
                            $kitchenProducts[$details['fk_kitchen_code']][$details['order_menu']][$details['detail_product_code']] += $details['detail_quantity'];
                        } 

                    }
                }
			}
			

            foreach($mainlocarray as $prod_id=>$limit){

                if(array_key_exists($prod_id, $newArray)){
                    if($newArray[$prod_id] < $limit){
						//echo $newArray[$prod_id]."=>".$limit;
                        echo "Orders yet not prepared";exit();
                    }
                }
            }
		}
		
        
		$printLableOrderBy = $setting['PRINT_LABEL_ORDER_BY'];
		
		//exit();
		$print_data = $this->getOrderDispatchTable()->getTodaysorder($location_code1,$menu, $kitchens,$printLableOrderBy);
		
		//echo "<pre>";print_r($print_data);die;
		
		$print_location = $setting['PRINT_LOCATION'];
		
		if( ($language_array!='all' && $language_array!='') || ($language_array == 'all') ) {
			foreach ($print_data as $order=>$data)
			{               
				$multilingualCodeSupport = $this->getServiceLocator()->get('QuickServe\Model\MultilingualCodeSupportTable');
				$location_language = ( isset($language_array) && !empty($language_array) ) ? $language_array : '';				
				$multilingual_array = $multilingualCodeSupport->fetchMultilingualDetail(array('context_ref_id' => $data[0]['product_code'], 'location_id' => $data[0]['location_code'], 'language_code' => $location_language));				
				if( is_array($multilingual_array) && !empty($multilingual_array) ) {
					$print_data[$order][0]['product_short_code'] = ( isset($multilingual_array[0]['context_code']) && !empty($multilingual_array[0]['context_code']) ) ? $multilingual_array[0]['context_code'] : null;
					$print_data[$order][0]['name'] = ( isset($multilingual_array[0]['context_name']) && !empty($multilingual_array[0]['context_name']) ) ? $multilingual_array[0]['context_name'] : $print_data[$order][0]['name'];
					//DEBUG STATEMENT: $print_data[$order][0]['dabbawala_code_type']='text';
					if( strtolower($print_data[$order][0]['dabbawala_code_type']) == 'text') {
						$print_data[$order][0]['dabbawala_code'] = ( isset($multilingual_array[0]['location_code']) && !empty($multilingual_array[0]['location_code']) ) ? $multilingual_array[0]['location_code'] : $print_data[$order][0]['dabbawala_code'];
					}					
				}			 
				$order_items_array = $this->getOrderTable()->getOrdered_items($data[0]['order_no'],$data[0]['order_date'],$data[0]['product_code']);
								
				$order_items_string = '';
				if( is_array($order_items_array) && !empty($order_items_array) ) {
					$item_array = array();
					
					foreach( $order_items_array as $meal_id=>$items) {						
						$strProductDesc = "";						
						$cnt = 0;						
	    				foreach($items as $product_id=>$item_value){
	    				
	    					if($meal_id != $product_id && $cnt==0){
	    						
	    						$arrMultilingualMeal = $multilingualCodeSupport->fetchMultilingualDetail(array('context_ref_id' => $meal_id, 'language_code' => $location_language));
	    						
	    						if( is_array($arrMultilingualMeal) && !empty($arrMultilingualMeal) ) {
	    							$meal_name = ( isset($arrMultilingualMeal[0]['context_name']) && !empty($arrMultilingualMeal[0]['context_name']) ) ? $arrMultilingualMeal[0]['context_name'] : $item_value['meal_name'];
	    							if( !empty($meal_name)) {	    								
	    								$strProductDesc .= $meal_name.' ('.$item_value['meal_quantity'].')';	    								
	    							}	    							
	    						}else{	    								
	    							$strProductDesc .= $item_value['meal_name'].' ('.$item_value['meal_quantity'].')';
	    						}	    						
	    						if($setting['PRINT_LABEL_SHOW_ITEM_DETAILS']=='yes'){
	    							$strProductDesc .= ' [';
	    						}else{
	    							$strProductDesc .= ',';
	    						}
	    					}
	    					
	    					if($setting['PRINT_LABEL_SHOW_ITEM_DETAILS']=='yes'){
	    					
			    				if( !empty($product_id) && !empty($item_value['quantity']) ) {
			    					
				    				$multilingual_array = $multilingualCodeSupport->fetchMultilingualDetail(array('context_ref_id' => $product_id, 'language_code' => $location_language));
				    				if( is_array($multilingual_array) && !empty($multilingual_array) ) {
				    					$item_name = ( isset($multilingual_array[0]['context_name']) && !empty($multilingual_array[0]['context_name']) ) ? $multilingual_array[0]['context_name'] : $item_value['product_name'];
				    					if( !empty($item_name)) {	
				    						$strProductDesc .= $item_name.' ('.$item_value['quantity'].'),';
				    					}
				    				}
				    				else {
				    					$strProductDesc .= $item_value['product_name'].' ('.$item_value['quantity'].'),';
				    				}
			    				}		    			
	    					}		    				
		    				$cnt++;
		    				
		    				if(count($items) == $cnt){
		    					
		    					$strProductDesc  = rtrim($strProductDesc,",");
		    				
		    					if($setting['PRINT_LABEL_SHOW_ITEM_DETAILS']=='yes'){
		    						$strProductDesc .= " ] ";
		    					}		    					
		    				}		    				 
	    				}	    				
	    			}//end of foreach
					
	    			$order_items_string = $strProductDesc;
					
					if( !empty($order_items_string) ) {
						$print_data[$order][0]['product_description'] = $order_items_string;
					}
				}
			}//end of foreach
		}//end of if
			
			
        $mailer = new \Lib\Email\Email();

        $mailer->setAdapter($sm);

        //get sms configuration
        $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
        //SET sms configuration to mailer
        $mailer->setSMSConfiguration($sms_config);
        //check for mobile no and give it to
        $mailer->setMobileNo($data[0]['phone']);

        //$message_array = $this->getServiceLocator()->get('Config')['sms'];
        //$sms_common = $this->getServiceLocator()->get('Config')['sms_common'];
        $sms_common = $libCommon->getSmsConfig($setting);
        $mailer->setMerchantData($sms_common);
        $time_to_deliver = date("h:i A", time());
        //echo $time_to_deliver;exit;
        
        $sms_array = array(
                'type_of_order' => $this->getOrderDispatchTable()->getTextMessageForMealNames($data),
                'website'	=> $setting['CLIENT_WEB_URL'],
                'time'	=> $time_to_deliver,
        ); 
                
        /*
        if(!empty($message_array) && array_key_exists('order_dispatch', $message_array)){
            $message = $message_array['order_dispatch'];
            $sms_array = array(
                    'type_of_order' => $this->getOrderDispatchTable()->getTextMessageForMealNames($data),
                    'website'	=> $setting['CLIENT_WEB_URL'],
                    'time'	=> $time_to_deliver,
            );
            foreach($sms_array as $var_key => $var_value) {
                $message = str_replace( '#'.$var_key.'#', $var_value, $message );
            }
            $mailer->setSMSMessage($message);
        }
        */
        $mailer->setSMSPriority(\Lib\Email\Email::PRIORITY_SMS_STORE_IN_DATABASE);
        if( $mailer->getSMSPriority() !== \Lib\Email\Email::PRIORITY_SMS_IMMEDIATELY ) {
            $sms_storage = $mailer->setAdapter($sm);
        }

        $message = $libCommon->getSMSTemplateMsg('order_dispatch',$sms_array);

        if($message){
            $mailer->setSMSMessage($message);
            $mailer->sendmessage();
        }       
        if($data[0]['email_address']!=''){
          
            $order_datetime = date('d-m-Y h:i A');
            $email_vars_array = array(
                'cust_name'	=> $data[0]['customer_name'],
                'order_no'	=> $order,
                'type_of_order' => $this->getOrderDispatchTable()->getTextMessageForMealNames($data),
                'time'	=> $time_to_deliver,
                'company_name' => $setting['MERCHANT_COMPANY_NAME'],
            );
            
            $email_data = $libCommon->getEmailTemplateMsg('order_dispatch',$email_vars_array);
            $contenttype = $email_data['type'];
           
            $mailer_config = $setting->getArrayCopy();//$this->ge$this->getActivityLogTable()->saveActivityLog($activity_log_data);tServiceLocator()->get('Config')['mail']['transport']['options'];
            $mailer->setConfiguration($mailer_config);
           
            $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);

            // get email storage queue
            $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);

            $queue = new \Lib\Email\Queue();
            $queue->setStorage($mail_storage);
            $mailer->setQueue($queue);
          
            //SEND EMAIL TO THE USER
            if($email_data['subject']!="" && $email_data['body']!=""){                              
                $mailer->sendmail(array(), array( $data[0]['customer_name'] => $data[0]['email_address'] ), array(), array(),$email_data['subject'],$email_data['body'] ,'UTF-8',array(),$contenttype);		
            }            
        }       
			
		$orders = array_keys($print_data);
		
		if($purpose == 'dispatchabtn'){

            if($skipKitchenCheck=='no'){
                $libOrder->updateKitchen($kitchenProducts,$today);
            }
            
            $libOrder->dispatchedOrder($orders,$today);
			
			return $this->redirect()->toRoute('orderdispatch',array('action' => 'index','purpose' => "success"));
		}
		
    	if(strtolower($setting['PRINT_LABEL_SHOW_BARCODE'])=='yes'){
	   		$print_data = $libOrder->getOrderTable()->saveOrderBarcode($orders,$today,$print_data);
    	}

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$count = isset($print_data)?count($print_data):0;
		$full_name = $loguser->first_name." ".$loguser->last_name;
		$activity_log_data=array();
		$activity_log_data['context_ref_id']=$loguser->pk_user_code;
		$activity_log_data['context_name']= $full_name;
		$activity_log_data['context_type']= 'user';
		$activity_log_data['controller']= 'orderdispatch';
		$activity_log_data['action']= 'printLabel';
		$activity_log_data['description']= "Print : $count label printed for $menu in todays orders .";
		
		//$activity_log_data['description']= "'$discount_name' discount $discount_status by $full_name";
		$this->getActivityLogTable()->saveActivityLog($activity_log_data);
		
		$libPrintLabel = \Lib\QuickServe\PrintLabel::getInstance($setting,$sm);
		//echo "<pre>";print_r($print_data);die;
		$config = $this->getServiceLocator()->get("config");
		
		// Set SMS config and application config data.
		$libPrintLabel->setConfig($config);
		//$libPrintLabel->setShowBarcode('yes');
		$libPrintLabel->setData($print_data);
		 
		try{
			$libPrintLabel->renderLabels();
		}catch(\Exception $e){
			 
			echo $e->getMessage();
		}
		die;
	}
	/**
	 * This function used to generate pdf of dispatched orders & forces to download
	 *
	 * @return \DOMPDFModule\View\Model\PdfModel
	 */
	public function orderdispatchgetAction()
	{
		$tblSetting = $this->getServiceLocator()->get("QuickServe\Model\SettingTable"); 
		
		$locationSetting = $tblSetting->getSetting("PRINT_LOCATION");
		
		$model = new PdfModel();
		$model->setOption('fileName', 'orderdispatch');
		$model->setOption('paperSize', 'A4');
		$model->setOption('paperOrientation', 'portrait');
		$sm = $this->getServiceLocator();
		$config = $sm->get('config');
		$variables = array();
		$variables = $this->getRequest()->getPost()->toArray();
		$variables['root_url'] = $config['root_url'];
		$variables['print_location'] = $locationSetting->value;
		$model->setVariables($variables);
		return $model;
	}
	/**
	 * This function get the CURL response of dispatched orders data
	 *
	 * @param string $url
	 * @param array $options
	 * @return /Zend/Json/view/ViewModel
	 */
	public function getCurlResponse($url,$options=array())
	{
		// create curl resource
		//$url = "http://www.fooddialer.com";
		//echo $url;exit;
		$ch = curl_init();
		// set url
		curl_setopt($ch, CURLOPT_URL, $url);
		//return the transfer as a string
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		if(isset($options['POST']) && $options['POST'] == 1)
		{
			curl_setopt($ch, CURLOPT_POST, 1);
	
			if(isset($options['POSTFIELDS'])) {
				$postfields = http_build_query($options['POSTFIELDS']);
				curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
			}
		}

		if(isset($options['SSL']) && $options['SSL'] == 1)
		{
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 1);
		}
		if(isset($options['PEER']) && $options['PEER'] == 1)
		{
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
		}
		// $output contains the output string
		$output = curl_exec($ch);
		//echo '<pre>';print_r(curl_getinfo($ch));//exit;
		//echo curl_error($ch);
		//echo $output;die;
		// close curl resource to free up system resources
		curl_close($ch);
		return $output;
	}
	/**
	 * This function mainly responsible for fetching dispatched orders data & putting it into pdf file
	 *
	 * @param string $outfile
	 * @param /Zend/Json/view/ViewModel  $option
	 * @param string $filename
	 */
	public function generateInvoicePDF($outfile,$option,$filename)
	{
		$config = $this->getServiceLocator()->get('config');
		$url = $config['root_url']."pdf-order-dispatch";
		$options['POST'] = 1;
		$options['POSTFIELDS'] = array('print_data' => $option);
		$pdfContent = $this->getCurlResponse($url,$options);
		file_put_contents($outfile,$pdfContent);
		$this->downloadOrderDispatch($outfile,$filename);
	}
	/**
	 * This function download the pdf file forcibly
	 *
	 * @param string $fileName
	 * @param string $filemainname
	 */
	public function downloadOrderDispatch($fileName,$filemainname) {
		if(!is_file($fileName)) {
			echo "Error while generating pdf file";exit;
		}

		$fileContents = file_get_contents($fileName);

		$response = $this->getResponse();
		$response->setContent($fileContents);

		$headers = $response->getHeaders();
		$headers->clearHeaders()
		->addHeaderLine('Content-Type', 'application/pdf')
		->addHeaderLine('Content-Disposition', 'attachment; filename="' . $filemainname . '"')
		->addHeaderLine('Content-Length', strlen($fileContents));
		echo $this->response;
	}
	/**
	 * Mark All items as prepared instead of going to Kitchen portal and mark prepared
	 * 
	 * @return String
	 */
	
	public function markAllAction()
	{
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		 
		$libCommon = Qscommon::getInstance($sm);
		
		$menuSelected = $_POST['menu'];
		$kitchenSelected = $_POST['kitchen'];
		$location= $_POST['location'];
		
		$kitchens = array();
		
        if($kitchenSelected !=0){
            $kitchens = array($kitchenSelected);
        }else{
            $kitchens = array_column($loguser->kitchens, 'fk_kitchen_code');
        }
       
		$returndata = $this->getOrderDispatchTable()->updatePreparedAll($menuSelected, $kitchens);
        
		$msg = (isset($returndata['msg']))?$returndata['msg']:'';
		$count = (isset($returndata['count']))?$returndata['count']:'';
		
		if($msg){

			$full_name=$loguser->first_name." ".$loguser->last_name;
			$activity_log_data=array();
			$activity_log_data['context_ref_id']=$loguser->pk_user_code;
			$activity_log_data['context_name']= $full_name;
			$activity_log_data['context_type']= 'user';
			$activity_log_data['controller']= 'orderdispatch';
			$activity_log_data['action']= 'markAll';
			$activity_log_data['description']= "Order : $count products marked prepared.";
		
			//$activity_log_data['description']= "'$discount_name' discount $discount_status by $full_name";
			$libCommon->saveActivityLog($activity_log_data);
			
			
		}
		
		echo $returndata;die;
		
		//return new JsonModel($returndata);die;
	}
	
	/**
	 * Instant order 
	 */
	public function instantOrderAction(){

		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$layoutviewModel = $this->layout();
		$acl = $layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$libCommon = Qscommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
        $setting_session = new Container('setting');
		$skipKitchenCheck = $setting_session->setting['GLOBAL_SKIP_KITCHEN'];
		$menu = $this->params()->fromQuery('menu','instantorder');
		$kitchen = $this->params()->fromQuery('kitchen',$_SESSION['adminkitchen']);
		$location = $this->params()->fromQuery('location',null);

		$mode = $this->params()->fromPost('mode','html'); // all , product
		$latestOrderNo = $this->params()->fromPost('lon',null);

		$today = date("Y-m-d");

		if($kitchen=='all'){
			$kitchen = array_column($loguser->kitchens, 'fk_kitchen_code');
		}
		
		//$orders = $libOrder->getInstantOrders($today,$kitchen,$menu,$location,$skipKitchenCheck);
		$result = $libOrder->getInstantOrders($today,$kitchen,$location,null,false,false);
		$deliveryPersons = $this->getuserTable()->getAllDeliveryPerson();
		
		if($mode=='html'){
			$this->layout()->setVariables(array('page_title'=>"Dispatch Instant Order",'description'=>"Dispatch Instant Order",'breadcrumb'=>"Dispatch Instant Order"));
	    	return new ViewModel(array('slotOrders'=>$result['orders'],'latestOrder'=>$result['latestOrder'],'date'=>$today,'deliveryPersons'=>$deliveryPersons,'skipKitchenCheck'=>$skipKitchenCheck));
		}else{
			return new JsonModel(array('slotOrders'=>$result['orders'],'latestOrder'=>$result['latestOrder'],'date'=>$today,'deliveryPersons'=>$deliveryPersons));
		}	

		/*
		$this->layout()->setVariables(array('page_title'=>"Dispatch Instant Order",'description'=>"Dispatch Instant Order",'breadcrumb'=>"Dispatch Instant Order"));
	    return new ViewModel(array('slotOrders'=>$orders,'date'=>$today,'deliveryPersons'=>$deliveryPersons,'skipKitchenCheck'=>$skipKitchenCheck));
		*/
	}
	
	/**
	 * Print Bills of instant orders
	 */
	public function printBillsAction(){
	    //error_reporting(E_ALL);
	    //ini_set("display_errors","On");
	    
	    $request = $this->getRequest();
	    
	    $params = $this->params()->fromQuery();
        
	    if(!isset($params['ord'])){
	        die("Orders not specified");
	    }
	    
		if(!is_array($params['ord'])){
	        die("Orders data mulfuntioned");
	    }
	    
	    if(count($params['ord'])==0){
	        die("Orders data mulfuntioned");
	    }
	    
	    if(!isset($params['date'])){
	        die("Date not specified");
	    }
	    
	        
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$layoutviewModel = $this->layout();
		$acl = $layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$libCommon = Qscommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
		
		$setting_session = new Container('setting');
		$setting = $setting_session->setting; 
	    
		$orders = $this->params()->fromQuery('ord');
		$date = $this->params()->fromQuery('date');
		
		$this->getOrderDispatchTable()->setServiceLocator($sm);
        
		/// fetch orders data..
		

		$printData = $this->getOrderDispatchTable()->getTodaysorder(null,'instantorder', null,'date',null,$orders);
        
		$bills = $libOrder->getOrderTable()->getOrderBillNos($orders,$date);
		$orderTaxDetails = $libOrder->getOrderTable()->getOrderTaxDetails(null,$bills);
        $orderTaxDetails = $orderTaxDetails->toArray();
		//dd($orderTaxDetails);
		
        //dd($printData);
		 
		foreach($printData as $orderNo=>$data){
		  // dd($data);
		    $printData[$orderNo]['tax_details'] = array();
		    
		    foreach ($orderTaxDetails as $tx){
		        
                $key = $orderNo."#".$date;
		        if($bills[$key] == $tx['bill_no']){
		            array_push($printData[$orderNo]['tax_details'],$tx);
		        }
		    }
		}
		
		
		
		//dd($setting);
		$setting['PRINT_LABEL'] = "orderwise";
		$setting['PRINT_LABEL_SHOW_ITEMS_PRICE'] = "yes";
		$setting['PRINT_LABEL_TEMPLATE'] = "7";
		$libPrintLabel = \Lib\QuickServe\PrintLabel::getInstance($setting,$sm);
		//echo "<pre>";print_r($print_data);die;
		$config = $this->getServiceLocator()->get("config");
		
		// Set SMS config and application config data.
		$libPrintLabel->setConfig($config);
		//$libPrintLabel->setShowBarcode('yes');
		$libPrintLabel->setData($printData);
		
	    try{
			$libPrintLabel->renderLabels();die;
		}catch(\Exception $e){
			 
			echo $e->getMessage();die;
		}
		
	}
	
	/**
	 * Prepared all items and Dispatch 
	 */
	public function dispatchInstantAction(){
	    $request = $this->getRequest();
	    
	    //try{
	    
    	    if($request->isPost()){
    	    
    	        
        	    $params = $request->getPost();
                
        	    if(!isset($params['ord'])){
        	        throw new \Exception("Orders not specified");
        	    }
        	    
        		if(!is_array($params['ord'])){
        	        throw new \Exception("Orders data mulfuntioned");
        	    }
        	    
        	    if(count($params['ord'])==0){
        	        throw new \Exception("Orders data mulfuntioned");
        	    }
        	    
        	    if(!isset($params['date'])){
        	        throw new \Exception("Date not specified");
        	    }
        	        
        		$sm = $this->getServiceLocator();
        		$adapt = $sm->get('Write_Adapter');
        		
        		$layoutviewModel = $this->layout();
        		$acl = $layoutviewModel->acl;
        		$loguser = $layoutviewModel->loggedUser;
        		
        		$libCommon = Qscommon::getInstance($sm);
        		$libOrder = QSOrder::getInstance($sm);
        		
        		$setting_session = new Container('setting');
        		$setting = $setting_session->setting; 
        	    
        		$orders = $params['ord'];
        		$date = $params['date'];
        		
        		$this->getOrderDispatchTable()->setServiceLocator($sm);
        		
        		$arrOrders = $libOrder->getInstantOrders($date,null,null,$orders,true,true);
        		
        		$items = array();
        		
        		foreach($arrOrders as $orderNo=>$tOrders){
        		    
        		    foreach($tOrders as $order){
        		        
            		    foreach($order['order_details'] as $itm){
            		        
            		        if(!isset($items[$itm['fk_kitchen_code']][$itm['menu']][$itm['product_code']])){
            		          $items[$itm['fk_kitchen_code']][$itm['menu']][$itm['product_code']] = $itm['quantity'];
            		        }else{
            		          $items[$itm['fk_kitchen_code']][$itm['menu']][$itm['product_code']] += $itm['quantity'];  
            		        }
            		    }
        		    }
        		}
        		
        		
        		
        		$libOrder->updateKitchen($items,$date,'prepare');
        		$libOrder->dispatchedOrder($orders,$date);
        		
        		return new JsonModel(array("status"=>"success"));
        		
    	    }
    		
    	/*}catch(\Exception $e){
    	        
    	   return new JsonModel(array("status"=>"error","msg"=>$e->getMessage()));    
	    
	    }*/
	    
	}
	
	/**
	 * Print Kot of instant orders
	 */
    
//    if($skinKitchenCheck=='yes'){
	public function printKotAction(){
	   
	    //error_reporting(E_ALL);
	    //ini_set("display_errors","On");
	    
	    $request = $this->getRequest();
	    
	    $params = $this->params()->fromQuery();
        
	    if(!isset($params['ord'])){
	        die("Orders not specified");
	    }
	    
		if(!is_array($params['ord'])){
	        die("Orders data mulfuntioned");
	    }
	    
	    if(count($params['ord'])==0){
	        die("Orders data mulfuntioned");
	    }
	    
	    if(!isset($params['date'])){
	        die("Date not specified");
	    }
	    
	        
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		
		$layoutviewModel = $this->layout();
		$acl = $layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		
		$libCommon = Qscommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
		
		$setting_session = new Container('setting');
		$setting = $setting_session->setting; 
	    
		$orders = $this->params()->fromQuery('ord');
		$date = $this->params()->fromQuery('date');
		
		/// fetch orders data..
      
		$arrData = array();
		
		foreach($orders as $orderNo){
		  $arrData[$orderNo] = $libOrder->getOrderTable()->getOrderDetails($orderNo,null,$date);
		}
        
		$viewModel = new ViewModel(
    	        array(
    	            'data'=>$arrData,
    	            'date'=>$date
    	        )
		 );
		
		$viewModel->setTerminal(true);
		return $viewModel;
		
	}
	
	public function getActivityLogTable()
	{
		if (!$this->activitylogTable)
		{
			$sm = $this->getServiceLocator();
			$this->activitylogTable = $sm->get('QuickServe\Model\ActivityLogTable');
		}
		return $this->activitylogTable;
	}
	
	/**
	 * Get instance of getUserTable
	 * @method getUserTable()
	 * @return QuickServe\Model\UserTable
	 */
	public function getUserTable()
	{
		if(!$this->userTable)
		{
			$sm = $this->getServiceLocator();
			$this->userTable = $sm->get('QuickServe\Model\UserTable');
		}
		return $this->userTable;
	}
	
	
}

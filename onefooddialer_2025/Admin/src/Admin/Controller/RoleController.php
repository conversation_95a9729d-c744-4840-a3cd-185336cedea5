<?php
/**
 * This File manages the taxes on fooddialer system
 * It is used to add ,update delete tax
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: TaxController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Admin\Form\RoleForm;
use QuickServe\Model\Role;

class RoleController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\RoleTable model
	 *
	 * @var QuickServe\Model\RoleTable $roletable
	 */
	protected $roletable;
	
	protected $aclroletable;
	/**
	 * It has an instance of AuthService model
	 *
	 *@var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * To display the list of tax
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
// 		echo "sa";exit;
		
		 if (! $this->authservice)
		{
     		$this->authservice = $this->getServiceLocator()
     		->get('AuthService');
     	}
     	
     	$sm = $this->getServiceLocator();
     	//$adapt = $sm->get('Write_Adapter');

     	$iden = $this->authservice->getIdentity();


     	$select = New QSelect();

		$role = $this->getRoleTable()->fetchAll();
		$returnvar = $role;
		$itemsPerPage = 2;
        
		$role->current();
		$paginator = new Paginator(new paginatorIterator($role));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);

		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		
		$utility = \Lib\Utility::getInstance();
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		$this->layout()->setVariables(array('page_title'=>"Role",'description'=>"Role Info",'breadcrumb'=>"Role"));
		return new ViewModel(array(
				'paginator' => $returnvar,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'flashMessages'=> $this->flashMessenger()->getMessages(),
				'setting'=>$setting,
				'utility'=>$utility,
		)); 
		
	}
	/**
	 * To add new tax
	 *
	 * @return \Admin\Form\TaxForm
	 */
	public function addAction()
	{
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$form = new RoleForm($sm);
		$form->get('submit')->setAttribute('value', 'Add');
		
		$acltpl = $this->getAclTplTable()->fetchAll();

		$request = $this->getRequest();
		if ($request->isPost())
		 {
		
			$role = new Role();
			$role->getInputFilter()->get('role_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'roles',
					'field'     => 'role_name',
					'adapter'   => $adapt,
					'message'   => 'Role name already exists',
				)
			));

			//$role->setAdapter($adapt);
			$form->setInputFilter($role->getInputFilter());
			$form->setData($request->getPost());

			if ($form->isValid())
			{
				//print_r($request->getPost());exit;
				$data = $request->getPost();
				
				$role->exchangeArray($form->getData());
			
				$data_role = $this->getRoleTable()->saveRole($role);
			
				$data_role = $this->getRoleTable()->saveAclTransactions($data,$data_role);
				
				($data_role) ?$this->flashMessenger()->addSuccessMessage("Role added successfully"):$this->flashMessenger()->addErrorMessage("Error adding Role.");
				// Redirect to list of albums
				return $this->redirect()->toRoute('role');
			}
		}
		//$form->get('number_of_pages')->setOptions();
		$this->layout()->setVariables(array('page_title'=>"Add Role",'breadcrumb'=>"Add Role"));
		return array('form' => $form,
					'acltpl' => $acltpl
		);
	}

	/**
	 * To update tax of given tax id
	 *
	 * @param int id
	 * @return \Admin\Form\TaxForm
	 */
	public function editAction()
	{
       
		$id = (int) $this->params('id');
		if (!$id)
		{
			return $this->redirect()->toRoute('role', array('action' => 'add'));
		}
		$role = $this->getRoleTable()->getRole($id);
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$config_variables = $sm->get('config');
		
		$acltpl = $this->getAclTplTable()->fetchAll();

		$acltransaction = $this->getRoleTable()->getTransaction($id);
		
		$form = new RoleForm($sm);
		$form->bind($role);
        $form->get('submit')->setAttribute('value', 'Edit');
		$val = $role->role_name;
		$request = $this->getRequest();
        
		if ($request->isPost())
		{
			$role = new Role();
            
			$role->getInputFilter()->get('role_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'roles',
					'field'     => 'role_name',
					'adapter'   => $adapt,
					'message'   => 'Role name already exists',
					'exclude' => array(
							'field' => 'role_name',
							'value' => $val,
					)
				)
			));
            
			$role->setAdapter($adapt);
			$form->setInputFilter($role->getInputFilter());
           
			$form->setData($request->getPost());
             
			$data = $request->getPost();
		
			if ($form->isValid())
			{
                
				$role->exchangeArray($form->getData());
                
				$data_role=$this->getRoleTable()->saveRole($role);
          
			//echo "<pre>";print_r($data_role);die;
				$data_role = $this->getRoleTable()->saveAclTransactions($data,$data_role);
				 
				$this->flashMessenger()->addSuccessMessage("Role updated successfully");
               
				// Redirect to list of albums
				return $this->redirect()->toRoute('role');
			}
		}

		$this->layout()->setVariables(array('page_title'=>"Edit Role",'breadcrumb'=>"Edit Role"));
		//dd($acltransaction);
		return array(
				'id' => $id,
				'form' => $form,
				'acltpl' => $acltpl,
				'acltransaction' => $acltransaction
		);
	}
	/**
	 * To delete tax of given tax id
	 *
	 * @param int id
	 * @return route tax
	 */
	public function deleteAction()
	{
		$id = (int) $this->params('id');
		if (!$id)
		{
			return $this->redirect()->toRoute('tax');
		}
		$data_tax=$this->getRoleTable()->deleteTax($id);
		($data_tax) ?$this->flashMessenger()->addSuccessMessage("Tax updated successfully"):$this->flashMessenger()->addErrorMessage("Error updating Tax.");
		return $this->redirect()->toRoute('tax');
	}
	/**
	 * Get instance of QuickServe\Model\RoleTable
	 *
	 * @return QuickServe\Model\RoleTable
	 *
	 */
	public function getRoleTable()
	{
		if (!$this->roletable)
		{
			$sm = $this->getServiceLocator();
			$this->roletable = $sm->get('QuickServe\Model\RoleTable');
		}
		return $this->roletable;
	}
	/**
     * 
     * Get instance of QuickServe\Model\getAclTplTable
     * 
     * @return QuickServe\Model\getAclTplTable
     * 
     */
    public function getAclTplTable()
	{
		if (!$this->aclroletable)
		{
			$sm = $this->getServiceLocator();
			$this->aclroletable = $sm->get('QuickServe\Model\AclTplTable');
		}
		return $this->aclroletable;
	}
}
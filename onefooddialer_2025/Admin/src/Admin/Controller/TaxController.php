<?php
/**
 * This File manages the taxes on fooddialer system
 * It is used to add ,update delete tax
 *
 * PHP versions 7.0
 *
 * Project name FoodDialer
 * @version 1.1: TaxController.php 2017-07-10 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 4.0.0
 *
 */

namespace Admin\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use QuickServe\Model\TaxValidator;
use QuickServe\Model\TaxTable;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Lib\Utility;
use Lib\QuickServe\CommonConfig as Qscommon;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Admin\Form\TaxForm;

class TaxController extends AbstractActionController
{
	/**
	 * It has an instance of QuickServe\Model\TaxTable model
	 *
	 * @var QuickServe\Model\TaxTable $taxtable
	 */
	protected $taxtable;
	/**
	 * It has an instance of AuthService model
	 *
	 *@var AuthService $authservice
	 */
	protected $authservice;
	/**
	 * To display the list of tax
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		if (! $this->authservice)
		{
     		$this->authservice = $this->getServiceLocator()
     		->get('AuthService');
     	}

     	$iden = $this->authservice->getIdentity();


     	$select = New QSelect();

		$tax = $this->getTaxTable()->fetchAll();

		$returnvar = $tax->toArray();

		$itemsPerPage = 2;

		$tax->current();
		$paginator = new Paginator(new paginatorIterator($tax));
		$paginator->setCurrentPageNumber($page)->setItemCountPerPage($itemsPerPage)->setPageRange(7);

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		$this->layout()->setVariables(array('page_title'=>"Tax",'description'=>"Complete Tax Info",'breadcrumb'=>"Tax"));
		return new ViewModel(array(
				'paginator' => $returnvar,
				'acl' => $acl,
				'loggedUser' => $loguser,
				'flashMessages'=> $this->flashMessenger()->getMessages()
		));
	}
	/**
	 * To add new tax
	 *
	 * @return \Admin\Form\TaxForm
	 */
	public function addAction()
	{
		
		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;

		$sm = $this->getServiceLocator();	
		
		$adapt = $sm->get('Write_Adapter');
		$libCommon = Qscommon::getInstance($sm);
		$config_variables = $sm->get('config');
		$form = new TaxForm($sm);

		$form->get('submit')->setAttribute('value', 'Add');
		$request = $this->getRequest();
		if ($request->isPost())
		 {
			$tax = new TaxValidator();
			$tax->getInputFilter()->get('tax_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'tax',
					'field'     => 'tax_name',
					'adapter'   => $adapt,
					'message'   => 'Tax name already exists',
			)
			));

			$form->setInputFilter($tax->getInputFilter());
			$form->setData($request->getPost());

			if ($form->isValid())
			{
				$tax->exchangeArray($form->getData());
				$data_tax = $this->getTaxTable()->saveTax($tax);
				($data_tax) ?$this->flashMessenger()->addSuccessMessage("Tax added successfully"):$this->flashMessenger()->addErrorMessage("Error adding Tax.");

				if($data_tax)
				{
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$tax_name=$tax->tax_name;
					$activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'tax';
					$activity_log_data['action']= 'add';
					$activity_log_data['description']= "Tax : New tax $tax_name created.";

					$libCommon->saveActivityLog($activity_log_data);
					
				}

				return $this->redirect()->toRoute('tax');
			}
		}
		$this->layout()->setVariables(array('page_title'=>"Add Tax",'breadcrumb'=>"Add Tax"));
		return array('form' => $form);
	}

	/**
	 * To update tax of given tax id
	 *
	 * @param int id
	 * @return \Admin\Form\TaxForm
	 */
	public function editAction()
	{
		$id = (int) $this->params('id');
		if (!$id)
		{
			return $this->redirect()->toRoute('tax', array('action' => 'add'));
		}

		$layoutviewModel = $this->layout();
		$acl =$layoutviewModel->acl;
		$loguser = $layoutviewModel->loggedUser;
		$libCommon = Qscommon::getInstance($sm);
		$tax = $this->getTaxTable()->getTax($id);
		$sm = $this->getServiceLocator();
		$adapt = $sm->get('Write_Adapter');
		$config_variables = $sm->get('config');
		$form = new TaxForm($sm);
		$form->bind($tax);
		$form->get('submit')->setAttribute('value', 'Edit');

		$request = $this->getRequest();
		if ($request->isPost())
		{
			$tax = new TaxValidator();

			$tax->getInputFilter()->get('tax_name')
			->getValidatorChain()                  // Filters are run second w/ FileInput
			->attach(new \Zend\Validator\Db\NoRecordExists(array(
					'table'     => 'tax',
					'field'     => 'tax_name',
					'adapter'   => $adapt,
					'message'   => 'Tax name already exists',
					'exclude' => array(
							'field' => 'tax_name',
							'value' => $val,
					)
			)
			));

			$form->setInputFilter($tax->getInputFilter());
			$form->setData($request->getPost());
			if ($form->isValid())
			{
				$tax->exchangeArray($form->getData());
				$data_tax=$this->getTaxTable()->saveTax($tax);
				$this->flashMessenger()->addSuccessMessage("Tax updated successfully");
				
				if($data_tax)
				{
					$full_name=$loguser->first_name." ".$loguser->last_name;
					$tax_name=$tax->tax_name;
					$activity_log_data=array();
					$activity_log_data['context_ref_id']=$loguser->pk_user_code;
					$activity_log_data['context_name']= $full_name;
					$activity_log_data['context_type']= 'user';
					$activity_log_data['controller']= 'tax';
					$activity_log_data['action']= 'Edit';
					$activity_log_data['description']= "Tax : tax $tax_name updated.";

					$libCommon->saveActivityLog($activity_log_data);
					
				}

				return $this->redirect()->toRoute('tax');
			}
		}

		$this->layout()->setVariables(array('page_title'=>"Edit Tax",'breadcrumb'=>"Edit Tax"));
		
		return array(
				'id' => $id,
				'form' => $form
		);
	}
	/**
	 * To delete tax of given tax id
	 *
	 * @param int id
	 * @return route tax
	 */
	public function deleteAction()
	{
		$id = (int) $this->params('id');
		if (!$id)
		{
			return $this->redirect()->toRoute('tax');
		}
		$data_tax=$this->getTaxTable()->deleteTax($id);
		($data_tax) ?$this->flashMessenger()->addSuccessMessage("Tax updated successfully"):$this->flashMessenger()->addErrorMessage("Error updating Tax.");
		return $this->redirect()->toRoute('tax');
	}
	/**
	 * Get instance of QuickServe\Model\TaxTable
	 *
	 * @return QuickServe\Model\TaxTable
	 *
	 */
	public function getTaxTable()
	{
		if (!$this->taxtable)
		{
			$sm = $this->getServiceLocator();
			$this->taxtable = $sm->get('QuickServe\Model\TaxTable');
		}
		return $this->taxtable;
	}
}
<?php
/**
 * This file Responsible for dispatching the orders
 * It includes the operations which help in create order at backend
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: OrderTable.php 2014-06-19 $
 * @package Admin/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Model;

use Zend\Db\TableGateway\AbstractTableGateway;
use Zend\Db\Adapter\Adapter;
use Zend\Db\Adapter\Adapter\Platform\Sql92;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Sql\Select;
use Zend\Db\Sql\Sql;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Misscall\Model\PreorderTable as preordertable;
use Admin\Model\ProductTable;
use Zend\Session\Container;
use Zend\Db\Sql\Expression;

use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\CommonConfig as QSCommon;

use Lib\S3;

class OrderTable extends AbstractTableGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='orders';
	
	protected $_service_locator;
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	public function __construct(Adapter $adapter)
	{
		$this->adapter = $adapter;
		$this->resultSetPrototype = new ResultSet();
		//$this->resultSetPrototype->setArrayObjectPrototype(new Content());
		$this->initialize();
	}
	/**
	 *  To get the order list
	 *
	 * @param Select $select
	 * @return array
	 */

	public function fetchAll(Select $select = null,$paged=null,$view=null,$context=null,$joinFlag=true, $deliveryPersonFlag = false) // $deliveryJoinFlag arguement added by sankalp
	{

		if (null === $select)
			$select = new Select();
		
		$select->from($this->table);

		$columns = array('order_no','fk_kitchen_code','promo_code','pk_order_no','customer_code','customer_name','group_name','phone','quantity','amount'=>new Expression("SUM(amount)"),'price'=>new Expression("SUM(amount)"),'tax'=>new Expression("SUM(tax)"),'applied_discount'=>new Expression("SUM(applied_discount)"),'delivery_charges'=>new Expression("SUM(delivery_charges)"),'service_charges'=>new Expression("SUM(service_charges)"),'mealnames'=>new Expression("GROUP_CONCAT( DISTINCT (orders.product_name),'(',orders.quantity,')')"),'net_amount'=>new Expression(" IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )"),'order_status'=>new Expression("IF (GROUP_CONCAT(DISTINCT orders.order_status) ='Cancel',orders.order_status,IF (GROUP_CONCAT(DISTINCT orders.order_status) LIKE '%Cancel%','Partial Cancel',orders.order_status))"),'order_date','order_days'=>new Expression("GROUP_CONCAT(DISTINCT(orders.order_date))"),'ship_address','delivery_status','invoice_status','order_menu','amount_paid','name'=>'product_name','location'=>'location_name','city','product_code','payment_mode', 'delivery_type');
		
		if($context=='history'){
			$select->join('order_details', 'order_details.ref_order_no=orders.order_no AND orders.order_date=order_details.order_date',array('detail_product_code'=>'product_code','detail_quantity'=>'quantity' ,'product_type'));
			$columns['product_description'] = new Expression("GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')')");
		}

		//$select->join('customers', 'customers.pk_customer_code=orders.customer_code',array('dabbawala_code','dabbawala_code_type','location_code','location_name','lunch_loc_code','lunch_loc_name','dinner_loc_code','dinner_loc_name'));
                
        $customerColumns = array( 'location_code','location_name','lunch_loc_code','lunch_loc_name','dinner_loc_code','dinner_loc_name');
                
		$select->join('customers', 'customers.pk_customer_code=orders.customer_code', $customerColumns);
		
		if($joinFlag){ // wrong -> should be group concat
			$select->join(array('ca'=>'customer_address'), 'orders.customer_code=ca.fk_customer_code and orders.order_menu= ca.menu_type',array('dabbawala_code','dabbawala_code_type', 'dabbawala_image'),$select::JOIN_LEFT);//,$select::JOIN_LEFT
		}
                
		if($view=='preorder'){
			//$group = array("orders.order_no","orders.order_date");
			$group = array("orders.order_no");
			$columns['start_date'] = new Expression('MIN(orders.order_date)');
			$columns['end_date'] = new Expression('MAX(orders.order_date)');
			$columns['all_status'] = new Expression("GROUP_CONCAT( DISTINCT (orders.order_status))");
		
		}else{
			$group = array("orders.order_no","orders.order_date");
		}
             
                // added by sankalp => extra column for delivery person
                if($deliveryPersonFlag){
//                    print_r($deliveryPersonCondtion); die();
                    $deliveryPerson = "IF(orders.delivery_person is not NULL, 
                                            CONCAT(users.first_name,' ',users.last_name),
                                            (SELECT  GROUP_CONCAT(DISTINCT(CONCAT(users.first_name,' ',users.last_name)) SEPARATOR '|') 
                                                FROM user_locations
                                                LEFT JOIN users ON users.pk_user_code = user_locations.fk_user_code
                                                WHERE user_locations.fk_location_code = orders.location_code AND users.role_id = 3
                                            )

                                        )";
                
                    $columns['delivery_person'] = new Expression($deliveryPerson);
                    $select->join('users', 'users.pk_user_code = orders.delivery_person',array('first_name','last_name'), $select::JOIN_LEFT);
                    
                }
		 
		$select->columns($columns);
		$select->group($group);
		$select->order('location');
		
               // echo $select->getSqlString();die;

		if($paged) {	
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
			 
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		$resultSet = $this->selectWith($select);
		 
		$resultSet->buffer();
		return $resultSet;
	}

	
	/**
	 * To delete an order of given order id $id
	 * @param int $id
	 * @return boolean
	 */
	public function deleteOrder($id)
	{
		$wherecon = array('status'=>"1");
		$this->update($wherecon,array('pk_order_no' => (int) $id));
		return true;
	}
	/**
	 * To get the order information of given order id $id
	 *
	 * @param int $id
	 * @return /Zend/resultSet
	 */
	public function getOrder($id, $locationFlag = false)
	{
		$sql = new Sql($this->adapter);
		$dbAdapter = $this->adapter;
		$select =  "SELECT orders.pk_order_no, orders.ref_order, orders.promo_code, orders.due_date, orders.last_modified, orders.customer_code, orders.customer_name, orders.group_code,orders.group_name, orders.phone, orders.location_name as location, orders.product_name as name, products.product_type, orders.quantity,orders.amount, orders.applied_discount, orders.order_status, orders.order_date, orders.ship_address, orders.delivery_status, orders.invoice_status, orders.order_menu, orders.fk_kitchen_code,orders.amount_paid,orders.delivery_charges,orders.order_no,"
                . "IF(tax_method='inclusive',( SUM(orders.amount) + SUM(orders.delivery_charges) + SUM(orders.service_charges) - SUM(orders.applied_discount)),( SUM(orders.amount) + SUM(orders.tax) + SUM(orders.delivery_charges) + SUM(orders.service_charges) - SUM(orders.applied_discount) ) ) AS net_amount ";
        
        if($locationFlag){
            $select .= ", delivery_locations.pin AS pincode ";
        }
        
        $select .= "FROM orders "
                . "INNER JOIN products ON products.pk_product_code = orders.product_code ";
        
        if($locationFlag){
           $select .= "INNER JOIN delivery_locations ON delivery_locations.pk_location_code = orders.location_code ";
        }
        
        $select .= "where pk_order_no='$id'";
		
		$results = $dbAdapter->query(
			$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		
		return $results;
	}
        
	public function getOrderWithCustomerDetails($id, $menu_type)
	{
            $select = new Select();

            $select->columns(array('pk_order_no', 'product_description', 'order_menu', 'order_date',
                'net_amount'  => new Expression("IF(tax_method='inclusive',( SUM(amount) + SUM(orders.delivery_charges) - SUM(applied_discount) + SUM(IFNULL(service_charges,0)) ),( SUM(amount) + SUM(tax) + SUM(orders.delivery_charges) - SUM(applied_discount) + SUM(IFNULL(service_charges,0)) ) )" ),
            ));
            $select->from('orders');

            $select->join('kitchen_master', 'kitchen_master.pk_kitchen_code=orders.fk_kitchen_code',array('kitchen_alias') );

            $select->join('customers', 'customers.pk_customer_code=orders.customer_code',array('pk_customer_code','customer_name', 'phone'));
            $select->join('customer_address', 'customer_address.fk_customer_code=customers.pk_customer_code',array('pk_customer_address_code','location_address','location_name', 'location_code'));
            $select->join('delivery_locations', 'delivery_locations.pk_location_code=orders.location_code',array('pincode' => 'pin'));
            $select->where('pk_order_no = '. $id);
            
            $select->where("IF(orders.order_menu = '".$menu_type."', customer_address.menu_type = '".$menu_type."', customer_address.default = 1)");
            
//            echo $select->getSqlString(); die();
            
            $resultSet = $this->selectWith($select);
		 
            $resultSet->buffer();
            
            return $resultSet->toArray();
	}
	
	public function getNextAvailableOrderDate() {
		$sql = new Sql($this->adapter);
		$dbAdapter = $this->adapter;
		$select =  "SELECT order_date from orders WHERE date(order_date) > now() ORDER BY order_date LIMIT 1";
		//echo $select;exit;
		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		//print_r($results);exit;
		return $results;
	}
	
	public function getPreOrder($id)
	{
		$sql = new Sql($this->adapter);
		$dbAdapter = $this->adapter;
		$select =  "select group_concat(product_description) product_description from pre_orders where ref_order='$id'";
		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$result=$results->toArray();
	
		if(isset($result[0]['product_description']))
		{
			$pr_description = $result[0]['product_description'];
		}
		else
		{
			$pr_description = '';
		}
		return $pr_description;
	}

	/**
	 *  To get the order information of given order id $id
	 * @param int $id
	 * @return /Zend/ResultSet
	 */
	public function getviewOrder($id,$field='primary',$date=null,$condition=null)
	{ 
		$sql = new Sql($this->adapter);
		$dbAdapter = $this->adapter;
		
		if($field=='primary'){
			
			$select =  "SELECT orders.pk_order_no,orders.order_no, orders.promo_code, orders.due_date, orders.order_menu, orders.last_modified, orders.customer_name , orders.customer_code, customers.email_address, orders.group_name as group_name, orders.phone, orders.location_name as location, orders.product_name as name, orders.quantity,orders.amount, orders.applied_discount, orders.order_status, orders.order_date, orders.ship_address, orders.delivery_status, orders.invoice_status, products.product_type FROM orders INNER JOIN customers ON customers.pk_customer_code=orders.customer_code INNER JOIN products ON products.pk_product_code = orders.product_code where pk_order_no='$id'";
			
		}elseif($field=='referencegroup'){
			
			$select =  "SELECT orders.pk_order_no,orders.order_no, orders.promo_code, orders.due_date, orders.order_menu, orders.last_modified, orders.customer_name, orders.customer_code, orders.customer_code, customers.email_address, orders.group_name as group_name, orders.phone, orders.location_name as location, GROUP_CONCAT(DISTINCT(orders.product_name)) as name, GROUP_CONCAT(DISTINCT(orders.quantity)) as quantity, GROUP_CONCAT(DISTINCT(products.product_type)) as product_type, orders.amount, orders.applied_discount, orders.order_status, orders.order_date, orders.ship_address, orders.delivery_status, orders.invoice_status, GROUP_CONCAT(DISTINCT(orders.order_date)) as order_days FROM orders INNER JOIN customers ON customers.pk_customer_code=orders.customer_code INNER JOIN products ON products.pk_product_code = orders.product_code where order_no='$id'";
			
		}elseif($field=='reference'){
			
			$select =  "SELECT orders.pk_order_no,orders.order_no, orders.promo_code, orders.due_date, orders.order_menu, orders.last_modified, orders.customer_name, orders.customer_code, customers.email_address, orders.group_name as group_name, orders.phone, orders.location_name as location, orders.product_code, orders.product_name as name, orders.quantity , product_type, orders.amount, orders.applied_discount, orders.order_status, orders.order_date, orders.ship_address, orders.delivery_status, orders.invoice_status, orders.order_date FROM orders INNER JOIN customers ON customers.pk_customer_code=orders.customer_code INNER JOIN products ON products.pk_product_code = orders.product_code where order_no='$id'";
		}
		
		if($date != null){
		
			if(is_array($date)){
				$date = "'".implode("','",$date)."'";
			}else{
				$date = "'".$date."'";
			}
			$select .= " AND order_date IN ($date)";
		}
		if(isset($condition)){
			
			$select .= $condition;
		}
	
		if($field=='referencegroup'){
			$select .= " group by order_no";
		}
	
		$results = $dbAdapter->query(
			$select, $dbAdapter::QUERY_MODE_EXECUTE
		);

		return $results;
	}

	/**
	 * To get the sub orders of given order id $id
	 * @param int $id
	 * @return /Zend/ResultSet
	 */
	/*  public function getsubOrder($id)
	 {
		$sql = new Sql($this->adapter);
		$dbAdapter = $this->adapter;
		$select = "SELECT orders.pk_order_no, orders.promo_code, orders.due_date, orders.last_modified, orders.customer_name, customers.email_address, orders.group_code, orders.group_name, orders.phone, delivery_locations.location, products.name, products.product_type, orders.quantity,orders.amount, orders.applied_discount, orders.order_status, orders.order_date, orders.ship_address, orders.delivery_status, orders.invoice_status FROM orders INNER JOIN delivery_locations ON delivery_locations.pk_location_code=orders.location_code INNER JOIN products ON products.pk_product_code = orders.product_code INNER JOIN customers ON customers.pk_customer_code=orders.customer_code where ref_order='$id'";

		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);

		return $results;
	}  */
	
	/**
	 * To get the order detail of given order id $id
	 * @param int $id
	 * @return /Zend/ResultSet
	 */
	public function getOrderProductDetails($order_no,$str_order_dates=null)
	{
		$sql = new Sql($this->adapter);
		$dbAdapter = $this->adapter;
		$select = "SELECT o.*,p.product_type,p.name as meal_name FROM order_details as o LEFT JOIN products as p ON o.meal_code=p.pk_product_code WHERE ref_order_no='$order_no'";
		
		if(!empty($str_order_dates)){
			$select .= " AND ( o.order_date IN ($str_order_dates) ) ";
		}
		
		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
	
		return $results;
	}
		
	/**
	 * To get the order detail of given order id $id
	 * @param int $id
	 * @return /Zend/ResultSet
	 */
	public function getOrderDetails($order_no,$date=null)
	{
		$dbAdapter = $this->adapter;
		/* $orderDetails = $this->getviewOrder($order_no,$context,$date);
		
		$orderDetails1 = array();
		
		foreach($orderDetails as $order){
			
			if(!isset($orderDetails1[$order['product_code']])){
				$orderDetails1[$order['product_code']]['product_code'] = $order['product_code'];
				$orderDetails1[$order['product_code']]['product_type'] = $order['product_type'];
				$orderDetails1[$order['product_code']]['product_name'] = $order['name'];
				$orderDetails1[$order['product_code']]['product_quantity'] = $order['quantity'];
			}
		}
		
		return $orderDetails1; */
		$select = "SELECT order_details.*,products.name meal_name from order_details join products on(products.pk_product_code=order_details.meal_code) where ref_order_no='$order_no' ";
		
		if($date!=NULL)
		{
			$select.=" AND date(order_date)='$date'";
		}
		//echo $select; exit();
		$results = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		
		$result=$results->toArray();
		return $result;
		
	}
	
	public function getItemName($item_id)
	{
		$dbAdapter = $this->adapter;
		$select = "SELECT name from products where pk_product_code=$item_id";

		$results = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		
		$result=$results->toArray();
		
		return $result[0]['name'];
	}
	
	public function getProducts($product_id)
	{
		$dbAdapter = $this->adapter;
		$select = "SELECT * from products where pk_product_code=$product_id";
		$results = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		return $results->toArray();
	}
	
	public function getItemscount($product_id)
	{
		$dbAdapter = $this->adapter;
		$select = "SELECT * from products where pk_product_code=$product_id";
		$results = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		$result=$results->toArray();
		return $result;
	}
	
	/**
	 * @deprecated
	 * @param unknown $menu
	 * @return Ambigous <multitype:multitype: , number, unknown>
	 */
	public function getOrderDetailsCount($menu)
	{
		$tmr_date=date('Y-m-d',strtotime("+1 days"));
		
		$dbAdapter = $this->adapter;
		$select = "SELECT * from orders where order_status='New' and order_date = '$tmr_date'";
		$resultSet = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
	
		$result = $resultSet->toArray();
		$count = $resultSet->count(); 
		$main_orders=$order_dtls=array();
		foreach($result as $data){
				$main_orders[]=$data;
		} 
		$products_qty_for_kitchen = array();
		
		if( !empty($menu) ) {
			foreach ($menu as $menudata) {
				$products_qty_for_kitchen[$menudata] = array();
			}
		}
		foreach($main_orders as $data)
		{
			if(isset($products_qty_for_kitchen[$data['order_menu']]) && isset($products_qty_for_kitchen[$data['order_menu']][$data['product_code']])){
				$qty = $products_qty_for_kitchen[$data['order_menu']][$data['product_code']]+$data['quantity'];
				$products_qty_for_kitchen[$data['order_menu']][$data['product_code']]=$qty;
			}else{
				if(!isset($products_qty_for_kitchen[$data['order_menu']])) { 
					$products_qty_for_kitchen[$data['order_menu']] = array(); 
				}
				$products_qty_for_kitchen[$data['order_menu']][$data['product_code']]=$data['quantity'];
			}
		}
		
		return $products_qty_for_kitchen;
	}
	
	public function getTodaysorder($location_code,$menu=false,$kitchen_screen=false,$date=null,$order_by=null){
		
		$sm = $this->_service_locator;
		$s3 = $sm->get('S3');
		$hostname = $s3->getHostname();
		
		$session_setting = new Container('setting');
		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();
		$bucketFolder = $settings['S3_BUCKET_URL'];
		
			
		$setting = $session_setting->setting;
		$barcodeObj = new \Lib\Barcode\BarcodeProcess();
		
		if(empty($date)){
			$tmr_date = $this->getNextAvailableOrderDate()->toArray()[0];
			$date = $tmr_date['order_date'];
		}
	
		$this->table = "orders";
		$sel_order = new Select();
		$sel_order->columns(array('fk_kitchen_code','pk_order_no','order_no','order_date','customer_code','phone','city','city_name','name'=>'product_name','product_code','customer_name','ship_address','quantity','location_code','location_name','order_menu','food_type', 'delivery_type', 'description'=>'product_description','amount'=>new Expression("SUM(amount)"),'tax'=>new Expression("SUM(tax)"),'applied_discount'=>new Expression("SUM(applied_discount)"),'delivery_charges'=>new Expression("SUM(orders.delivery_charges)"),'service_charges'=>new Expression("SUM(service_charges)"),'net_amount'=>new Expression(" IF(tax_method='inclusive',( SUM(amount) + SUM(orders.delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(orders.delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )")));
		$sel_order->join('customers','customers.pk_customer_code = orders.customer_code',array('email_address','dabbawala_code','dabbawala_image','dabbawala_code_type','food_preference'),$sel_order::JOIN_LEFT);
		
        $sel_order->from($this->table);
        
        if($order_by != null && $order_by == 'pincode'){
            $sel_order->join('customer_address', 'customer_address.fk_customer_code = customers.pk_customer_code');
            $sel_order->join('delivery_locations','delivery_locations.pk_location_code = customer_address.location_code',array('pin'),$sel_order::JOIN_LEFT);
        }
        
		$sel_order->where->in('orders.location_code',$location_code);
		$sel_order->where->like('orders.order_date', $date);
		$sel_order->where(array(
			'orders.order_status' => 'New',
		));
		if($menu)
		{
			$sel_order->where(array('orders.order_menu' =>$menu));
		}
        
		if(!is_array($kitchen_screen)){
			$sel_order->where(array("orders.fk_kitchen_code" => $kitchen_screen));
        }else{
            $sel_order->where->in("orders.fk_kitchen_code", $kitchen_screen);
        }
		
        $sel_order->group(array('orders.order_no','orders.order_date','orders.product_code'));
		
		if($order_by != null){
			$arrOrder = array();
			if($order_by=='customer'){
				$arrOrder = array("orders.customer_code ASC", "orders.product_type DESC");
			}elseif($order_by=='location'){
				$arrOrder = array("orders.location_code ASC" , "orders.product_type DESC");
            }else if($order_by == 'pincode'){
                $arrOrder = array("delivery_locations.pin ASC" , "orders.product_type DESC");
            }else{
				$arrOrder = array("orders.product_type DESC");
			}
		
			$sel_order->order($arrOrder);
		
		}else{
			$sel_order->order(array('order_details.product_type DESC'));
		}
//		dd($sel_order->getSqlString());
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		$rs1 = $resultSet->toArray();
		//return $resultSet;
		$mainArr = array();
		$barcodeArr = array();
		$customers = array();
		
		$libCustomer = QSCustomer::getInstance($this->_service_locator);
		
		$tblUser = $this->_service_locator->get('QuickServe\Model\UserTable');

		//$spname = array();
		
		$new_select = new Select();
		//$new_select->join('order_details', 'order_details.ref_order_no=orders.order_no AND orders.order_date=order_details.order_date AND orders.product_code = order_details.meal_code',array('planned_product_qty'=>new Expression("GROUP_CONCAT(order_details.quantity)"),'planned_product_code'=>new Expression("GROUP_CONCAT(order_details.product_code)"),'planned_product_names'=>new Expression("GROUP_CONCAT(order_details.product_name)"),'detail_product_code'=>'product_code','detail_quantity'=>'quantity' ,'product_type','product_description'=>new Expression("CONCAT(orders.product_name,' [ ',GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')'),' ] ')"),"meal_description"=>new Expression("CONCAT(orders.product_name,'(',orders.quantity,') ')"),"item_description"=>new Expression("GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')')")));
		$new_select->columns(array('order_no'=>ref_order_no,'planned_product_qty'=>new Expression("GROUP_CONCAT(order_details.quantity)"),'planned_product_code'=>new Expression("GROUP_CONCAT(order_details.product_code)"),'planned_product_names'=>new Expression("GROUP_CONCAT(order_details.product_name)")));
		$new_select->from('order_details');
		$new_select->where(array('order_date'=>$date));
		$new_select->group('order_details.ref_order_no');
		//echo $new_select->getSqlString();die;
		$new_resultSet = $this->selectWith($new_select);
		$new_resultSet->buffer();
		$new_rs = $new_resultSet->toArray();
		
		//dd($new_rs);
		
		foreach ($rs1 as $data)
		{
			foreach ($new_rs as $order_items) {
				if($data['order_no'] == $order_items['order_no']) {
					$data['planned_product_qty'] = $order_items['planned_product_qty'];
					$data['planned_product_code'] = $order_items['planned_product_code'];
					$data['planned_product_names'] = $order_items['planned_product_names'];
				}
				//echo "<pre>";print_r($data); echo "</pre>";
			}
			//dd($data);
			
			$meal_name = $data['name']."(".$data['quantity'].")";
			$data['net_amount'] = $data['net_amount']/$data['quantity'];
			
			$arrQty = explode(",",$data['planned_product_qty']);
			$arrCode = explode(",",$data['planned_product_code']);
			$arrNames = explode(",",$data['planned_product_names']);
			
			$plannedCodes = array_combine($arrCode,$arrQty);
			$plannedProductNames = array_combine($arrCode,$arrNames);
			
			//dd($plannedCodes);
			
			$spname1 = "";
			
			foreach ($arrCode as $product_code) {
				//$spname1 = "";
   				$select = new Select();
				$select->from('product_planner');
				$select->columns(array('date','menu','fk_kitchen_code','generic_product_code','specific_product_code','specific_product_name'));
				$select->join('products','products.pk_product_code=product_planner.generic_product_code',array('generic_product_name'=>'name'),$select::JOIN_LEFT);
				$select->where(array('generic_product_code'=>$product_code,'menu'=>$data['order_menu'],'date'=>$data['order_date'],'fk_kitchen_code'=>$data['fk_kitchen_code'],'isdefault'=>'yes'));
				
				$rs = $this->selectWith($select);
				$rs->buffer();
				$rsArray = $rs->toArray();		
				//echo "<pre>"; print_r($rsArray); echo "</pre>";
			 	if($rs->count()>0) {
					foreach ($rs as $spItem){
						if( $product_code == $spItem['generic_product_code'] ){
							$spname1 .= $spItem['specific_product_name']."";
							$qty = $plannedCodes[$spItem['generic_product_code']]/$data['quantity'];
							$spname1 .= "({$qty}), ";
						}
					}
				}
		 		else {
						$qty = $plannedCodes[$product_code]/$data['quantity'];
						$generic_name = $plannedProductNames[$product_code];
						$spname1 .= $generic_name."({$qty}), ";	
				} 
			}
			
			$spname1 = rtrim($spname1,", ");
			$data['product_description'] = $meal_name." [".$spname1."]";
			$data['meal_description'] = $meal_name;
			$data['item_description'] = $spname1;
			
			// get address of customer.
			$addresses = $libCustomer->getCustomerAddress($data['customer_code']);
			
			//echo "<pre>";print_r($addresses);die;
			
			$customers[$data['customer_code']] = $addresses['addresses'];
			
			if(isset($customers[$data['customer_code']][$data['order_menu']])){
				$data['dabbawala_code'] = $customers[$data['customer_code']][$data['order_menu']]['dabbawala_code'];
				$data['dabbawala_code_type'] = $customers[$data['customer_code']][$data['order_menu']]['dabbawala_code_type'];
				$data['dabbawala_image'] = $GLOBALS['http_request_scheme'].$hostname."/".$bucketFolder."/dabbawala/".$customers[$data['customer_code']][$data['order_menu']]['dabbawala_image'];
				
				$deliveryPersonName = "";
				
				$deliveryPersonId = $customers[$data['customer_code']][$data['order_menu']]['delivery_person_id'];
					
				if(!empty($deliveryPersonId)){
				
					$deliveryPerson = $tblUser->getUser($deliveryPersonId,'id');
					$deliveryPersonName = $deliveryPerson->first_name." ".$deliveryPerson->last_name;
				
				}else{
					
					$deliveryPersons = $tblUser->getDPbyLocation($customers[$data['customer_code']][$data['order_menu']]['location_code']);
					if(!empty($deliveryPersons)){
						$deliveryPersonName = $deliveryPersons[0]['first_name']." ".$deliveryPersons[0]['last_name'];
					}
				}
					
				$data['delivery_person'] = $deliveryPersonName;
				
			}else{
				
				$data['dabbawala_code'] = $addresses['default']['dabbawala_code'];
				$data['dabbawala_code_type'] = $addresses['default']['dabbawala_code_type'];
				$data['dabbawala_image'] = $GLOBALS['http_request_scheme'].$hostname."/".$bucketFolder."/dabbawala/".$addresses['default']['dabbawala_image'];
				
				$deliveryPersonName = "";
				
				$deliveryPersonId = $addresses['default']['delivery_person_id'];
					
				if(!empty($deliveryPersonId)){
				
					$deliveryPerson = $tblUser->getUser($deliveryPersonId,'id');
					$deliveryPersonName = $deliveryPerson->first_name." ".$deliveryPerson->last_name;
				
				}else{
					$deliveryPersons = $tblUser->getDPbyLocation($addresses['default']['location_code']);
					if(!empty($deliveryPersons)){
						$deliveryPersonName = $deliveryPersons[0]['first_name']." ".$deliveryPersons[0]['last_name'];
					}
				}
					
				$data['delivery_person'] = $deliveryPersonName;
			}
			
			// Find delivery person...
			
			$mainArr[$data['order_no']][] = $data;

		}
		
		//dd($data);
		//die;
		
		$printData = array();
		if(strtolower($setting['PRINT_LABEL_SHOW_BARCODE'])=='yes'){
				
			foreach ($mainArr as $orderNo=>$main){
					
				$barcode = $barcodeObj->generateBarcode();
				$barcodeArr[] = $barcode;
				foreach ($main as $details){
					$details['barcode'] = $barcode;
					$printData[$orderNo][] = $details;
				}
					
			}
		}else{
			$printData = $mainArr;
			$data['barcode'] = null;
		}
		
		//dd($mainArr);
	
		return array('printData'=>$printData,'barcodeArr'=>$barcodeArr);
	}
	
	/**
	 * To get today's sub order of given order id $id
	 *
	 * @param int $id
	 * @return array
	 */
	public function getTodaysSubOrder($id)
	{
		$this->table = "orders";
		$sel_order = new Select();
		$sel_order->columns(array('quantity','product_code'));
		$sel_order->join('products',"products.pk_product_code = orders.product_code",array('name','product_type'),$sel_order::JOIN_LEFT);
		$sel_order->from($this->table);
		$sel_order->where(array('order_no'=>$id));
		$sel_order->order(array('products.product_type DESC'));
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		return $resultSet->toArray();
	}
	
	public function old_saveOrderBarcode($orderBarcodeArr){
		$tmr_date=date('Y-m-d',strtotime("+1 days"));
		$dbAdapter = $this->adapter;
		$platform = $dbAdapter->getPlatform();
		$query = 'INSERT INTO ' . $platform->quoteIdentifier('order_barcodes') . ' (`barcode`, `preorder_id`,`order_date`) VALUES ';
		$queryVals = array();
		foreach ($orderBarcodeArr as $preorder_id=>$barcode) {
			
				$values =  $barcode.','.$preorder_id.',"'.$tmr_date.'"';
				$queryVals[] =  '(' . $values . ')';
			
		}
		$stmt = $dbAdapter->query($query . implode(',', $queryVals),$dbAdapter::QUERY_MODE_EXECUTE);
		return true;
	}
	
	public function saveOrderBarcode($orderBarcodeArr){

		$tmr_date=date('Y-m-d',strtotime("+1 days"));
		// check if barcode entery exist for tomorrows date for particular order
		foreach ($orderBarcodeArr as $order_no=>$barcode){
			$result = 	$this->getBarcodeByOrderNo($order_no,$tmr_date);
			if(!empty($result)){
				$sql = "UPDATE `order_barcodes` SET `barcode`=$barcode WHERE `pk_barcode_id` = $result->pk_barcode_id";
				$this->Adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
				unset($orderBarcodeArr[$order_no]);
			}
		}
			
		if(!empty($orderBarcodeArr)){
			$dbAdapter = $this->adapter;
			$platform = $dbAdapter->getPlatform();
			$query = 'INSERT INTO ' . $platform->quoteIdentifier('order_barcodes') . ' (`barcode`,`order_no`,`order_date`) VALUES ';
			$queryVals = array();
			foreach ($orderBarcodeArr as $order_no=>$barcode) {
				$values =  $barcode.',"'.$order_no.'","'.$tmr_date.'"';
				$queryVals[] =  '(' . $values . ')';
			}
			$stmt = $dbAdapter->query($query . implode(',', $queryVals),$dbAdapter::QUERY_MODE_EXECUTE);
		}
		return true;
	}
	
	public function getBarcodeByOrderNo($order_no,$order_date){
	
		$this->table = 'order_barcodes';
		$select = new Select();
		$select->columns(array('pk_barcode_id','barcode','order_no','order_date'));
		$select->from($this->table);
		$select->where(array('order_no' => $order_no,'order_date'=>$order_date));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->current();
	}
	
	public function getKitchenKitchenScreen()
	{	
		//$kitchen_array = array('all' => 'All');
		$sql = new Sql($this->adapter);
		$select = $sql->select();
		$select->from('kitchen_master');
		$statement = $sql->prepareStatementForSqlObject($select);
		$results = $statement->execute();
		foreach ($results as $res) {
			$kitchen_array[$res['pk_kitchen_code']] = $res['kitchen_name'].( !empty($res['kitchen_name']) ? ' ('.$res['location'].')' : '' );
		}
		return $kitchen_array;
	}
	
	public function getLocationData()
	{
		$dbAdapter = $this->adapter;
		$select = "SELECT * from delivery_locations where status='1'";
		$resultSet = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		return $resultSet->toArray();
	}

	public function getOrdered_items($order_no,$order_date=null,$meal_code=null) {
		if(empty($order_no)) { return array(); }
		$order_items_array = array();
		$sql = new Sql($this->adapter);
		$select = $sql->select();
		$select->from('order_details');
		$select->join('orders',"orders.product_code=order_details.meal_code",array("meal_name"=>"product_name","meal_quantity"=>"quantity"));
		$select->where('ref_order_no="'.$order_no.'"');
		if($order_date!=null)
		{
			$select->where('orders.order_date="'.$order_date.'"');
		}
		if($meal_code!=null)
		{
			$select->where('order_details.meal_code="'.$meal_code.'"');
		}
		//echo $select->getSqlString(); 
		$statement = $sql->prepareStatementForSqlObject($select);
		$results = $statement->execute();
		
		
		if(!empty($results)) {
			foreach ($results as $res) {
				$order_items_array[$res['meal_code']][$res['product_code']] = $res;
			}// end of foreach
		}
		
		return $order_items_array;
	}
	
	public function old_getPreviousOrder($id){
		$dbAdapter = $this->adapter;
		$select = "select * from orders where order_date > NOW() and customer_code = '".$id."'";
		$resultSet = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		return $resultSet->toArray();
	}
	
	public function getPreviousOrder($id,$menu){
		$dbAdapter = $this->adapter;
		$today_date = date('Y-m-d');
		$select = "select * from orders where order_date > '".$today_date."' and customer_code = '".$id."' and order_status !='cancel' and order_menu = '".$menu."'";
		//echo $select;exit;
		$resultSet = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		return $resultSet->toArray();
	}
	
	public function getCustomerOrder($customer, $menu, $date , $restrict_to_date=false) {
		if( empty($menu) || empty($date) ) { return array(); }
		$order_array = array();
		$sql = new Sql($this->adapter);
		$select = $sql->select();
		$select->from('orders');
		$select->columns(array('order_date', 'order_menu', 'amount'));
		$select->where('customer_code="'.$customer.'"');
		$select->where('order_menu="'.$menu.'"');
	
		if($restrict_to_date){
			$select->where('order_date = "'.$date.'"');
		}else{
			$select->where('order_date>="'.$date.'"');
		}
	
		$select->where('order_status = "New"');
		$select->order('order_date ASC');
		$statement = $sql->prepareStatementForSqlObject($select);
		$results = $statement->execute();
		if(!empty($results)) {
			foreach ($results as $res) {
				array_push( $order_array, array('menu' => $res['order_menu'], 'order_date' => $res['order_date'], 'amount' => $res['amount']) );
			}// end of foreach
		}
		return $order_array;
	}
	
	public function getMealDates($meal_id,$menu){
		$dbAdapter = $this->adapter;
		$select = "select DISTINCT(calendar_date) from meal_calendar where fk_product_code = '".$meal_id."' and menu = '".$menu."'";
		$resultSet = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		return $resultSet->toArray();
	}
	
	
	public function setServiceLocator($sm){
		
		$this->_service_locator = $sm;
	}
        
    public function printPackaging($select, $dateFilter){

        $select->from($this->table);
           
        $select->columns(array('product_name', 'quantity' => new Expression('SUM(quantity)') ));
        $select->where(array("order_date" => $dateFilter, 'order_status' => 'New', 'delivery_status' => 'Pending'));
             
        $select->group('product_name');
                
//      echo $select->getSqlString(); die();
        $resultSet = $this->selectWith($select);

        $resultSet->buffer();
        return $resultSet;
    }
}


?>
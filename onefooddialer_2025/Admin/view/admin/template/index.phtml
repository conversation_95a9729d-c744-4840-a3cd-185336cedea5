<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.effects.core.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.effects.explode.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
<style>
#showcolor span {
	background: url("images/colorpicker/select2.png") no-repeat scroll -4px -4px #000000;
    display: inline-block;
    height: 28px;
   vertical-align:middle;
    position: relative;
    top: 0;
    width: 28px;
	margin-right:4px;
	color:#999;
	font-weight:bolder;
	text-align:center;
	font-size:12px;
	cursor:default;
	
}
</style>
 <div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="image"><span>Template</span></h2>
          </div>
          <!--contenttitle-->
          <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('template_crud', array('action'=>'add'));?>" class="btn btn_add"><span>Add Record</span></a></div>
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            </colgroup>
            <thead>
              <tr>
                <td class="head0">Template Code</td>
                <td class="head1">Name</td>
                <td class="head0">Nature of Business</td>
                <td class="head1">Colors</td>
                <td class="head0">Supported Device</td>
                <td class="head1">Layout</td>
                <td class="head0">Live Demo</td>
                <td class="head1">Thumb</td>
               <!-- <td class="head0">Preview Upload</td>-->
                <td class="head1">Price Plan</td>
                <!--<td class="head0">Last Modified</td>-->
                <td class="head1">Status</td>
                <td class="head0 center">Action</td>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <td class="head0">Template Code</td>
                <td class="head1">Name</td>
                <td class="head0">Nature of Business</td>
                <td class="head1">Colors</td>
                <td class="head0">Supported Device</td>
                <td class="head1">Layout</td>
                <td class="head0">Live Demo</td>
                <td class="head1">Thumb</td>
                <!--<td class="head0">Preview Upload</td>-->
                <td class="head1">Price Plan</td>
               <!-- <td class="head0">Last Modified</td>-->
                <td class="head1">Status</td>
                <td class="head0 center">Action</td>
              </tr>
            </tfoot>
            <tbody>
				<?php foreach ($paginator as $template) : // <-- change here! ?>
            	  <tr>
				     <td><?php echo $this->escapeHtml($template['template_code']);?></td>
				     <td><?php echo $this->escapeHtml($template['template_name']);?></td>
				     <td><?php echo $this->escapeHtml($template['business_name']);?></td>
				     <td id="showcolor"><?php $colors = explode(',', $template['colors']); foreach ($colors as $color) { echo '<span style="background-color:'.$color.'"></span>';}?></td>
				     <td><?php $devices = json_decode($template['supported_device'],true); echo implode(',', $devices);?></td>
				     <td><?php echo $this->escapeHtml($template['layout']);?></td>
				     <td><?php echo $this->escapeHtml($template['live_demo_url']);?></td>
				     <td><img src="<?php echo str_replace("public/","",$path['small']); ?><?php echo $this->escapeHtml($template['thumb_small']); ?>" /></td>
					 <td><?php echo $this->escapeHtml($template['price_name']);?></td>
				      <td><?php echo ($this->escapeHtml($template['template_status']))?'Active':'<span class="red">Inactive</span>';?></td>
				    <td class="center">
				        <a href="<?php echo $this->url('template_crud', array('action'=>'edit', 'id' => $template['pk_template_id']));?>" class="btn btn5 btn_pencil5"></a>
         				  <?php $textadd = ($template['template_status'])? 'Suspend' :'Activate'; ?>
        				<a href="<?php echo $this->url('template_crud',array('action'=>'delete', 'id' => $template['pk_template_id']));?>"  onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this logo plan ?')" class="btn btn5 btn_trash5"></a>
				     </td>
				 </tr>
            	<?php endforeach; ?>
             
            </tbody>
          </table>
          <br />
          <br />
        </div>

<?php
    /*echo $this->paginationControl(
            $paginator, 'Sliding', 'paginator-slide', array('order_by' => $order_by, 'order' => $order)
    );*/
    ?>
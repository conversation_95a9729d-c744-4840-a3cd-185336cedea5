<script src="/admin/js/plugins/jquery-1.9.1.js"></script>
<script type="text/javascript" src="/admin/js/plugins/colorpicker.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.jgrowl.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.alerts.js"></script>
<style type="text/css">
#addcolor {
	background: none repeat scroll 0 0 #EAEAEA;
    border: 0 none;
    box-shadow: none;
    color: #000000;
    cursor: pointer;
    font-weight: bold;
    margin: 0 0 0 10px;
    padding: 6px 10px;
    width: auto;
	text-decoration:none;
	transition: color,background 0.5s;
}
#labeloverride label{
width:auto !important;
}
#colorpickerholder7 span {
	background: url("images/colorpicker/select2.png") no-repeat scroll -4px -4px #000000;
    display: inline-block;
    height: 28px;
   vertical-align:middle;
    position: relative;
    top: 0;
    width: 28px;
}
#showcolor span {
	background: url("images/colorpicker/select2.png") no-repeat scroll -4px -4px #000000;
    display: inline-block;
    height: 28px;
   vertical-align:middle;
    position: relative;
    top: 0;
    width: 28px;
	margin-right:10px;
	color:#999;
	font-weight:bolder;
	text-align:center;
	font-size:12px;
	cursor:pointer;
	
}
#colorpicker7 {
	width:40% !important;
}
#showcolor { display:block; }


</style>
<script type="text/javascript" src="/admin/js/custom/elements.js"></script>
<link rel="stylesheet" href="/admin/css/jquery-ui.css">
<script src="/admin/js/plugins/jquery-ui.js"></script>
<script type="text/javascript" src="/admin/js/custom/form.js"></script>
<script>
function showcolors() {
	var colors = jQuery('#colorpicker7').val();
	var color = colors.split(",");
	jQuery('#showcolor').empty();
	for (var i=0; i<color.length; i++)
	{
		jQuery('#showcolor').append('<span id="'+color[i]+'" class="deleteshowcolor" style="background-color:'+color[i]+'">X</span>')
	}
}
jQuery(document).ready(function(){
	jQuery( "#datepicker" ).datepicker();
	jQuery('#colorpickerholder7 #addcolor').attr('title', '#000000');
	jQuery('#colorpickerholder7').ColorPicker({
		flat: true,
		onChange: function (hsb, hex, rgb) 
		{
			jQuery('#colorpickerholder7 span').css('backgroundColor', '#' + hex);
			jQuery('#colorpickerholder7 #addcolor').attr('title', '#' + hex);
			//jQuery('#colorpicker7').val('#'+hex);
			
		}
		
	}); 
	jQuery('#addcolor').on("click",function(){
		var val = jQuery(this).attr('title');
	
			var prev_val = jQuery('#colorpicker7').val();
			if(prev_val == '') {
				jQuery('#colorpicker7').val(val);
			}
			else {
			jQuery('#colorpicker7').val(prev_val+','+val);
			}
			showcolors();
		
		
		return false;
	});
	jQuery('#showcolor').on('click','.deleteshowcolor',function(){
		var color_check = jQuery(this).attr('id');
		if(confirm("Do you really want to delete this color "+color_check+" ? ") == true)
		{
			var colors = jQuery('#colorpicker7').val();
			var color = colors.split(",");
			jQuery('#showcolor').empty();
			var maincolor = [];
			for (var i=0; i<color.length; i++)
			{
					if(color[i] != color_check)
					{
						maincolor.push(color[i]);
					}
			}
			var newcolor = maincolor.join(",");
			jQuery('#colorpicker7').val(newcolor);
			showcolors();
		}
		return false;
	});
}); 

</script>
<?php 
$form = $this->form;
$form->setAttribute('action', $this->url('template_crud', array('action' => 'add')));
$form->setAttribute('class','stdform');
$form->prepare();
?>
 <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Template Edit</span></h2>
          </div>
          <!--contenttitle--> 
          
          <br />
         <?php echo $this->form()->openTag($form) ;?>
			<p>
              <label><?php echo $this->formLabel($form->get('price_plan')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('price_plan'));
					echo $this->formElementErrors($form->get('price_plan'));
			  ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('template_code')); ?></label>
              <span class="field">
             <?php
			   echo $this->formHidden($form->get('pk_template_id'));
			 echo $this->formElement($form->get('template_code'));
			echo $this->formElementErrors($form->get('template_code'));
			 ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('template_name')); ?></label>
              <span class="field">
             <?php
			 echo $this->formElement($form->get('template_name'));
			echo $this->formElementErrors($form->get('template_name'));
			?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('nature_of_business')); ?></label>
              <span class="field">
              <?php echo $this->formElement($form->get('nature_of_business'));
				echo $this->formElementErrors($form->get('nature_of_business'));
				?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('colors')); ?></label>
              <span class="field">
              <span id="showcolor" ><b>No Colors Selected</b></span>
              <?php echo $this->formElement($form->get('colors')); ?>
			<span id="colorpickerholder7" class="colorpickerholder7"> <span ></span><a id="addcolor" >ADD</a> </span> </span><!--field-->
			<span id="showtmpclr"></span>
			
			<?php echo $this->formElementErrors($form->get('colors')); ?></p>
            <p>
              <label><?php echo $this->formLabel($form->get('supported_device')); ?></label>
              <span class="formwrapper" id="labeloverride">
              <?php echo $this->formElement($form->get('supported_device'));
					echo $this->formElementErrors($form->get('supported_device')); ?> 
				</span> 
			</p><br/>
            <p>
              <label><?php echo $this->formLabel($form->get('layout')); ?></label>
              <span class="formwrapper">
				<?php echo $this->formElement($form->get('layout'));
				echo $this->formElementErrors($form->get('layout'));
				?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('live_demo_url')); ?></label>
              <span class="formwrapper">
              <?php echo $this->formElement($form->get('live_demo_url'));
				echo $this->formElementErrors($form->get('live_demo_url'));
				?>
              </span></p>
            <!--<p>
              <label>Preview Upload</label>
              <span class="formwrapper">
              <input type="radio" name="radiofield" />
              Yes &nbsp;&nbsp;
              <input type="radio" name="radiofield" />
              NO &nbsp;&nbsp; </span></p>
            <p>-->
              <label><?php echo $this->formLabel($form->get('thumb_small')); ?></label>
              <span class="formwrapper">
              <?php echo $this->formElement($form->get('thumb_small'));
				echo $this->formElementErrors($form->get('thumb_small'));
				?>
              </span></p>
              
             <p>
              <label><?php echo $this->formLabel($form->get('thumb_medium')); ?></label>
              <span class="formwrapper">
             <?php echo $this->formElement($form->get('thumb_medium'));
					echo $this->formElementErrors($form->get('thumb_medium')); ?>
              </span></p>
            <p>
              <label><?php echo $this->formLabel($form->get('thumb_large')); ?></label>
              <span class="formwrapper">
              <?php echo $this->formElement($form->get('thumb_large'));
					echo $this->formElementErrors($form->get('thumb_large')); 
					echo $this->formElement($form->get('csrf'));?>
              </span></p>
            
           
            <p class="stdformbutton">
             <?php echo $this->formInput($form->get('submit')); ?>
             <?php echo $this->formSubmit($form->get('cancel')); ?>
            </p>
            <p>
             
              <span class="field">
             <?php echo $this->formElement($form->get('backurl'));
				 ?>
              </span> </p>
            
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form) ;?>
          <br clear="all" />
          <br />
        </div>
        <!--content--> 
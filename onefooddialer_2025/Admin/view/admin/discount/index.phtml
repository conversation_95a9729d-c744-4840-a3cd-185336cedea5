<?php 
	
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	
	
?>
     
      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Discount</h4>  
            <ul class="toolOption">
            	<li>
            	<?php
                if($acl->isAllowed($loggedUser->rolename,'discount','add')){  ?>
                	<div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('discount', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Discount</button>
                    </div>
                <?php } ?>
                </li>
             
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                            <th>Discount Name</th>
                            <th>Discount For</th>
                            <th>Quantity</th>
                            <th>Group</th>
                            <th>Discount Type</th>
                            <th>Discount Rate <i class="fa fa-rupee"></i></th>
                            <th>Discount Expiry Date</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    <tbody>
                      <?php foreach ($paginator as $discount) {  ?>
                        <tr>
                            <td><?php echo $this->escapeHtml($discount['discount_name']);?></td>
                            <td><?php echo ($this->escapeHtml($discount['discount_for']))=="Qty"? 'Quantity':'Group';?></td>
                            <td><?php echo $this->escapeHtml($discount['quantity']);?></td>
                            <td><?php echo $this->escapeHtml($discount['group_name']);?></td>
                            <td><?php echo ($this->escapeHtml($discount['discount_type']))=="0"? 'Fixed':'Percentage';?></td>
                            <td><?php echo $this->escapeHtml($discount['discount_rate']);?></td>
                            <td><?php echo $utility->displayDate($discount['till_date'],$setting['DATE_FORMAT']);?></td>
                            <td><span class="<?php echo ($this->escapeHtml($discount['group_status'])) == "1"?'active':'inactive';?>"><?php echo ($this->escapeHtml($discount['group_status'])) == "1"?'Active':'Inactive';?></span></td>
                            <td>
                            <?php
                			if($acl->isAllowed($loggedUser->rolename,'discount','edit')){  ?>
				        	<a href="<?php echo $this->url('discount', array('action'=>'edit', 'id' => $discount['pk_discount_code']));?>" class="btn btn5 btn_pencil5">
				         	<button class="smBtn blueBg has-tip tip-top"   data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
				       		 </a>
	         				  <?php $textadd = ($discount['group_status'])== "1"? 'Deactive' :'Activate'; ?>
	         				<?php } ?>
                            
                            <?php
	                		if($acl->isAllowed($loggedUser->rolename,'discount','delete')){  ?>
		        			<a href="<?php echo $this->url('discount',array('action'=>'delete', 'id' => $discount['pk_discount_code']));?>"  onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this discount ?')" class="btn btn5 btn_trash5">
		        			 <button class="smBtn redBg has-tip tip-top" data-tooltip  title="Delete"><i class="fa fa-trash-o"></i></button></td>
	        				</a>
	        			<?php } ?>
                           
                           
                        </tr>
                       <?php } ?>
                      
                    </tbody>
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
    
          
<script type="text/javascript">
$(document).ready(function() {

	myPageTable.init();

});
</script> 

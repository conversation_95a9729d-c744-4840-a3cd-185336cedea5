<?php
$form = $this->form;
$form->setAttribute('action', $this->url('discount', array('action' => 'edit', 'id'=>$this->id)));
$form->setAttribute('class','stdform');
$form->prepare();
?>
      <!-- END PAGE HEADER-->
      
      <div id="content">
        <div class="large-6 columns">
                    <?php echo $this->form()->openTag($form);?>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('discount_name')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php	
               		echo $this->formHidden($form->get('pk_discount_code'));
             		echo $this->formElement($form->get('discount_name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						->setMessageCloseString('</small>')
						->render($form->get('discount_name'));
			  ?>
              </div>
            </div>
             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('discount_for')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                 <?php 
	                echo $this->formElement($form->get('discount_for'));
					echo $this->formElementErrors($form->get('discount_for'));
				?>
              </div>
            </div>
             <div class="row qty">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('quantity')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
	                echo $this->formElement($form->get('quantity'));
					echo $this->formElementErrors($form->get('quantity'));
				?>
              </div>
            </div>
            
              <div class="row prod">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('product_id')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
	                echo $this->formElement($form->get('product_id'));
					echo $this->formElementErrors($form->get('product_id'));
				?>
              </div>
            </div>
            
             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('group_code')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
	                echo $this->formElement($form->get('group_code'));
					echo $this->formElementErrors($form->get('group_code'));
				?>
              </div>
            </div>
             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('discount_type')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
	                echo $this->formElement($form->get('discount_type'));
					echo $this->formElementErrors($form->get('discount_type'));
				?>
              </div>
            </div>
           
             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('discount_rate')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
	                echo $this->formElement($form->get('discount_rate'));
					echo $this->formElementErrors($form->get('discount_rate'));
				?>
              </div>
            </div>
            
             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('till_date')); ?></label>
              </div>
              
              <div class="large-8 small-8 medium-8 columns">
             	<?php 
                 	echo $this->formElement($form->get('till_date'));
					echo $this->formElementErrors($form->get('till_date'));
				?>
           	  </div>
            </div>
           
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
	                echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'));
					echo $this->formElement($form->get('csrf')); 
				?>
              </div>
            </div>
            <?php
             echo $this->formElement($form->get('backurl'));
					 ?>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8 small-8 medium-8 columns">
                <button	type="submit" id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id="cancelbutton" class="button	left tiny left5	redBg" >Cancel &nbsp;<i class="fa	fa-ban"></i></button>
              </div>
            </div>
           <?php echo $this->form()->closeTag($form);?>
        </div>
      </div>
    </div>
    
    <!-- END PAGE CONTAINER--> 
    
   <script type="text/javascript">



$(document).ready(function() {


	var now = new Date();
 	var curyear=now.getFullYear();
 	var month=now.getMonth() + 1;
 	var date=now.getDate();
 	var CurrentMonth = month + '/' + date + '/' + curyear;

	$("#till_date").datepicker({minDate:CurrentMonth});

	$(document).on('click',"#till_date",function(){
		$( "#till_date" ).datepicker({minDate:CurrentMonth});
	});
	
	 var selectedOption = $("#discount_for").val();
	 if(selectedOption =="Group")
		{
			$(".qty").hide();
			$(".prod").hide();
		}
		else
		{
			$(".qty").show();
			$(".prod").show();
		}
	$(document).on("change","#discount_for",function(){

		var menu = $(this).val();
		
		if(menu =="Group")
		{
			$(".qty").hide();
			$(".prod").hide();
		}
		else
		{
			$(".qty").show();
			$(".prod").show();
		}	
	});
});

$(document).on("click","#submitbutton",function(){
	var selectedoption = $('#discount_type').find(":selected").text();
	
	if(selectedoption == "Fixed")
	{
		if($("#discount_rate").val() <=0 && $("#discount_rate").val() !="")
		{
			alert("Please enter Discount Rate more than 0");
			return false;
		}
	}
	else if(selectedoption == "Percent")
	{
		if($("#discount_rate").val() > 100)
		{
			alert("Please enter Discount Rate less than 100 %");
			return false;
		}
	}

});
					 
</script> 

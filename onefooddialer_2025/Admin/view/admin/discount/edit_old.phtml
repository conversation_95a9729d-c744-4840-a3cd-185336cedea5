<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/form.js"></script>
<style>
.disradio label{
width:auto;
}
</style>

<script>
jQuery(document).ready(function(){
jQuery( "#datepicker" ).datepicker();
jQuery( "#datepicker1" ).datepicker();
});
</script>

<?php
$form = $this->form;
$form->setAttribute('action', $this->url('discount', array('action' => 'edit', 'id'=>$this->id)));
$form->setAttribute('class','stdform');
$form->prepare();
?>
    <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Edit Discount</span></h2>
          </div>
          <!--contenttitle-->
          <br />
          <form class="stdform" action="" method="post">
            <p>
              <label><?php echo $this->formLabel($form->get('discount_name')); ?></label>
              <span class="field">
              <?php echo $this->formhidden($form->get('pk_discount_code'));
             		echo $this->formElement($form->get('discount_name'));
					echo $this->formElementErrors($form->get('discount_name')); ?>
              </span>
            </p>
            <p>
              <label><?php echo $this->formLabel($form->get('discount_for')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('discount_for'));
				echo $this->formElementErrors($form->get('discount_for'));
				?>
              </span> </p>
			  <p>
	              <label><?php echo $this->formLabel($form->get('quantity')); ?></label>
	              <span class="field">
	            	 <?php echo $this->formElement($form->get('quantity'));
					echo $this->formElementErrors($form->get('quantity'));
					?>
	               </span>
               </p>

              <p>
              <label><?php echo $this->formLabel($form->get('product_id')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('product_id'));
				echo $this->formElementErrors($form->get('product_id'),array('class' => 'red'));
				?>
              </span> </p>

	           <p>
              <label><?php echo $this->formLabel($form->get('group_code')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('group_code'));
				echo $this->formElementErrors($form->get('group_code'),array('class' => 'red'));
				?>
              </span> </p>
               <p>
	              <label><?php echo $this->formLabel($form->get('discount_type')); ?></label>
	              <span class="field">
	            	 <?php echo $this->formElement($form->get('discount_type'));
						echo $this->formElementErrors($form->get('discount_type'));
					?>
	              </span>
               </p>
 			   <p>
	              <label><?php echo $this->formLabel($form->get('discount_rate')); ?></label>
	              <span class="field">
	              <?php echo $this->formElement($form->get('discount_rate'));
					echo $this->formElementErrors($form->get('discount_rate'));
					?>
	              </span>
              </p>
 			  <p>
              <label><?php echo $this->formLabel($form->get('till_date')); ?></label>
              <span class="field">
             	<?php echo $this->formElement($form->get('till_date'));
				echo $this->formElementErrors($form->get('till_date'));
				?>
              </span>
              </p>
              <p>
              <label><?php echo $this->formLabel($form->get('status')); ?></label>
              <span class="field">
               <?php echo $this->formElement($form->get('status'));
				echo $this->formElementErrors($form->get('status'));
				echo $this->formElement($form->get('csrf')); ?>
              </span>
              </p>
	          <p class="stdformbutton">
					<?php echo $this->formSubmit($form->get('submit')); ?>
					<?php echo $this->formSubmit($form->get('cancel')); ?>
	          </p>
	          <p>
	              <span class="field">
	            	 <?php echo $this->formElement($form->get('backurl')); ?>
             	  </span>
             </p>
          </form>
          <br clear="all" />
          <br />
        </div>

<script type="text/javascript">
$(document).ready(function(){
//$('#group_code').prop('disabled', true);
if($('#discount_for').val() == "Qty")
{
	 $('#quantity').prop('disabled', false);
	 $('#product_id').prop('disabled', false);
     $('#group_code').prop('disabled', true);
}else{
	 $('#quantity').prop('disabled', true);
	 $('#product_id').prop('disabled', true);
     $('#group_code').prop('disabled', false);
}

$("#discount_for").change(function () {
	 if ($(this).find('option:selected').val() == "Qty") {
	        $('#quantity').prop('disabled', false);
	        $('#product_id').prop('disabled', false);
	        $('#group_code').prop('disabled', true);
	    } else {
	        $('#quantity').prop('disabled', true)
	        $('#product_id').prop('disabled', true);
	        $('#group_code').prop('disabled', false);
	    }
});
});
</script>
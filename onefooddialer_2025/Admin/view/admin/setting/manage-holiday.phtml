<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                      <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addweek" class="common-orange-btn-on-hover"> Add Weekoff &nbsp;&nbsp;</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="addholiday" class="common-orange-btn-on-hover">Add Holidays</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
	<style>
		.calendar .date-picker {
			width: 100%
		}
		h3.holi {
		  text-align: center;
		  color: rgb(252, 110, 81);
		}

	</style>

						<!-- END PAGE HEADER-->
<?php 
$setweek = (isset($setweekoff['holiday_date']) && !empty($setweekoff['holiday_date']))?$setweekoff['holiday_date']:'';
$setWeeekDay = (isset($setweekoff['holiday_description']) && !empty($setweekoff['holiday_description']))?$setweekoff['holiday_description']:'';

$uniquedays = explode(",", $setWeeekDay);

?>
						<div id="content">
							<div class="large-8 small-12 medium-12 columns">
							
							
								   <?php
								if ($this->FlashMessenger()->hasSuccessMessages()){
									foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
							?>
								<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
								
								<div  data-alert="" class="alert-box success round">
					 			 <?php echo $msg; ?>
					  				<a href="#" class="close">&times;</a>
								</div>
								
							<?php
									}
								}else if($this->FlashMessenger()->hasErrorMessages()){
									foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
								<div data-alert class="alert-box alert round">
									   <?php echo $msg; ?>
									  <a href="#" class="close">&times;</a>
								</div>
							<?php 	}
								}
							?>	
									
							
							
								<form method="POST" action="/setting/manage-holiday">
									<fieldset>
										<legend class="text-center">
											Weekoff
										</legend>
										
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<label class="inline right">Weekoff</label>
											</div>
											<div class="large-8 small-8 medium-8 columns">

												
												<!-- name="category&#x5B;&#x5D;" style="height&#x3A;75px" multiple="multiple" id="menu" -->
												<!-- <select class="prepaid">
												<option value=""  name="pokemon" class="postpaidradio" data-close=".prepaidrow">Saturday-Sunday</option>
												<option value="" name="pokemon" class="postpaidradio" data-close=".prepaidrow">Only Sunday</option>
												<option value="" name="pokemon" class="postpaidradio" data-close=".prepaidrow">No Weekoff</option>
												<option value="" name="pokemon"  class="prepaidradio" data-open=".prepaidrow">Choose Days</option>
												
											</select> -->
											<select size="1" name="weekOff" id="usgsfeed" class="selectweek">
												<option value="" class="prepaidradio">Please select option</option>
												<?php foreach ($weekoffs as $key_w=>$val_w){?>
													<?php if($key_w == $setweek){?>
														<option value="<?php echo $key_w;?>" class="prepaidradio" selected ><?php echo $val_w; ?></option>
													<?php }else{?>
														<option value="<?php echo $key_w;?>" class="prepaidradio" ><?php echo $val_w; ?></option>
													<?php }?>
												<?php }?>
											
												
											</select>

											</div>
										</div>
										<?php $display_day=($setweek == "chooseDay")?'block':'none' ?>
										<div class="row prepaidrow " style="display:<?php echo $display_day;?>">
											 <div class="large-4 small-4 medium-4 columns">
												<label class="inline right">ChooseDay</label>
											</div>
											<div class="large-8 small-8 medium-8 columns">
												
												<select name='unique[]' multiple="multiple" placeholder="select day" class="SlectBox day unidays">
											
											     	<?php foreach ($unique as $key_u=>$val_u){?>
											     			<?php if($setweek == "chooseDay" && in_array($key_u, $uniquedays)){?>
											     				<option value="<?php echo $key_u;?>" selected><?php echo $val_u; ?></option>
											     			<?php }else{?>
											     				<option value="<?php echo $key_u;?>"><?php echo $val_u; ?></option>
											     			<?php }?>
											     			
													<?php }?>
										    	</select>
											</div>
											
										</div>
										

									</fieldset>

									
									<fieldset class="mt15 holidays">
										<legend class="text-center">
											Holidays
										</legend>
										
										 <div class="row">
										 	<div class="large-6 small-4 medium-4 columns">
												<label class="inline right"> </label>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<label class="inline right">Select Year</label>
											</div>
											<div class="large-2 small-4 medium-4 columns prepaid">

												
												<select class="sel_year" name="select_year">
												
													<option value="" >Choose Year</option>
													<?php for($i=$currentYear;$i<2025;$i++){?>
													<option value='<?php echo $i;?>'><?php echo $i;?></option>
													<?php }?>
													
												</select>

											</div>
										</div>
										<div class="multi-field-wrapper" id="divmealsetting" name="divmealsetting">
										<div class="multi-fields holidaylist">
										
										
										
										
										
										
<!-- 											<div class="multi-field"> -->
<!-- 												<div class="row"> -->
<!-- 													<div class="large-4 small-4 medium-4 columns"> -->
<!-- 														<label class="inline right">Holiday Name <span class="red">*</span> </label> -->
<!-- 													</div> -->
<!-- 													<div class="large-3 small-3 medium-3 columns"> -->
<!-- 															<input type="text" name="" class="smallinput" value="Enter Holiday Name"> -->
<!-- 														</div> -->
<!-- 													<div class="productadd"> -->
														
<!-- 														<div class="large-4 small-4 medium-4 columns"> -->
<!-- 															<div class="large-4 small-4 medium-4 columns"> -->
<!-- 																<label class="inline right">Date <span class="red">*</span> -->
<!-- 															</div> -->
<!-- 															<div class="large-8 small-8 medium-8 columns"> -->
																<!-- <input type="text" name="product_quantity&#x5B;&#x5D;" class="smallinput" value=""> -->
																<!-- <input type="text" data-date/> -->
<!-- 																<div class="dateText"> -->
														
<!-- 														<input type="text" name="maxDate" id="" class="left&#x20;filterSelect calender minDate" value=""><!-- minDate -->
<!-- 													</div> -->
<!-- 															</div> -->
<!-- 														</div> -->
<!-- 														<button class="add-field smBtn has-tip "  title="Add more" type="button"  > -->
<!-- 															<i class="fa fa-plus"></i> -->
<!-- 														</button> -->

<!-- 													</div> -->
<!-- 												</div> -->
<!-- 											</div> -->
											
											
											
											
											
											
											<h3 class="holi">Please Select Year</h3>
											
											
											

										</div>
									</div>
			            		
		            		   </fieldset>

									
									<div class="large-12 columns mt15 pr0">
										<div class="right">
											<button class="greenBg savebtn" type="submit">
												<i class="fa fa-save"></i>&nbsp;Save
											</button>
											<button class="redBg" type="button" onclick="window.location.href='/dashboard'">
												<i class="fa fa-ban"></i> &nbsp;Cancel
											</button>
										</div>
									</div>
								</form>

							</div>

<!-- 						</div> -->
<!-- 					</div> -->

					<!-- END PAGE CONTAINER-->

				<!-- END PAGE -->
			</div>
			<!-- END CONTAINER -->
			<div class="clearfix mt15"></div>
			<!-- BEGIN FOOTER -->
			<span id="toTop" class="go-top"><i class="fa fa-angle-up"></i></span>

			<!-- wrapper End -->

			<!-- wrapper End -->
			
			<script>
				jQuery(document).ready(function($) {
					$("#minDate").datepicker({
						autoSize : true
					});
					$(".minDate").datepicker({
						autoSize : true
					});
					$("#maxDate").datepicker({
						autoSize : true
					});
				});
			</script>
			<script>
				$(document).ready(function() {
					window.asd = $('.SlectBox').SumoSelect({ csvDispCount: 3 });
// 					$(".settings").addClass("active");
// 					$(".settings ul li:nth-of-type(4)").addClass("active");
					
					$(".mealtype").hide();
					$(".allow input").click(function() {
						$(".mealtype").toggle();
					});

					$(".prepaidradio").click(function() {
						var closediv = $(this).attr('data-open');
						$(closediv).show();
					});

					$(".postpaidradio").click(function() {
						var closediv = $(this).attr('data-close');
						$(closediv).hide();
					});

					$(".onlinecheck").click(function() {
						var closediv = $(this).attr('data-open');
						$(closediv).show();
					});

					$(".cashcheck,.neftcheck,.chequecheck").click(function() {
						var closediv = $(this).attr('data-close');
						$(closediv).hide();
					});

					$(".fdradio").click(function() {
						var closediv = $(this).attr('data-open');
						$(closediv).show();
					});
					$(".iciciradio,.payuradio,.ebsradio,.aveneyradio").click(function() {
						var closediv = $(this).attr('data-close');
						$(closediv).hide();
					});	
					
					var wrapper = $('.multi-fields');


				$(document).on('change',".sel_year",function(e){
						var sel_year = $(this).val();
						if(sel_year){
							$.ajax({
									type:"POST",
									url:"/setting/ajaxgetholidays",
									async:false,
									data:{currentYear:sel_year},
									success:function(data){
											if(data.status=="success"){
													$(".holidaylist").html(data.view);
													$(".dateselect").datepicker({
														minDate:0,
														autoSize : true
													});
														
												}
										
										},
								});
						}
						else{
								$(".holidaylist").html("<h3 class='holi'>Please Select Year</h3>");
							}
						
					});	


				$(document).on('change','.selectweek',function(){
						var option = $(this).val();

						if(option == "chooseDay"){
								$(".prepaidrow").css("display","block");
							}else{
								$(".prepaidrow").css("display","none");

								$(".unidays option").each(function(){
// 										$(this).removeAttr("selected");
// 										$(this).removeClass("selected" );
									});
								
								
								
								}

						
						
					});

				$(document).on('click','.savebtn',function(){
						var error = false;
						var errormsg='';
						var selectweek = $('.selectweek').val();
						if(selectweek=="chooseDay"){
								var uni = $('.unidays').val();
								if(!uni){
										alert("Please select days for choose days");
										return false;
									}
							}


						$(".smallinput").each(function(){
	
								if(!$(this).val()){
										errormsg = 'Holiday Name';
										error = true;
									}

							});

						$(".dateselect").each(function(){
							
							if(!$(this).val()){
									if(error){
											errormsg += ' and ';
										}
									errormsg += 'Holiday Date';
									error = true;
									
								}

						});

						if(error){
								alert("Please fill empty "+errormsg+" field");
								return false;
							}

						
					});
				

				$(document).on('click', ".add-field", function(e) {
					

					$(this).removeClass("add-field").html('<i class="fa fa-trash-o"></i>');
					$(this).addClass("remove-field");
					// Foundation.libs.tooltip.getTip($('.tooltip')).remove();
					 $(this).attr("title", "Remove");
					// $(document).foundation('tooltip', 'reflow');
					var thsele = this.outerHTML;
					
					var btns = $(this).parent().find("button");

					$(this).parent().append(thsele);
					
					btns.each(function() {
						$(this).remove();
					});

					var ele = $('.multi-field:first-child').clone();


// 					var element = "<div class='multi-field'>
// 							    	+"<div class='row'>
// 						    		+"<div class='large-4 small-4 medium-4 columns'>
// 						    		+"<label class='inline right'>Holiday Name<span class='red'>*</span> </label>
// 						    		+"</div>
// 						    		+"<div class='large-3 small-3 medium-3 columns'>
// 						    		+"<input type='text' name='holiday[".$currentYear."]['holiday_description']' class='smallinput' value=''>
// 						    		+"</div>
// 						    		+"<div class='productadd'>
						   
// 						    		+"<div class='large-4 small-4 medium-4 columns'>
// 						    		+"<div class='large-4 small-4 medium-4 columns'>
// 						    		+"<label class='inline right'>Date <span class='red'>*</span>
// 						    		+"</div>
// 						    		+"<div class='large-8 small-8 medium-8 columns'>
						   
// 						    		+"<div class='dateText'>
						   
// 						    		+"<input type='text' name='holiday[".$currentYear."][holiday_date]' id='' class='left&#x20;filterSelect calender minDate dateselect' value=''>
// 										+"									</div>
// 						    			+"	</div>
// 						    				+"</div>
// 						    				+"<button class='smBtn has-tip remove-field' title='Remove' type='button'><i class='fa fa-trash-o'></i></button>
// 						    				+"<button class='add-field smBtn has-tip'  title='Add more' type='button'  >
// 						    				+"<i class='fa fa-plus'></i>
// 											+"</button>
						   
// 						    		+"</div>
// 						    		+"</div>
// 						    		+"</div>";

					ele.find("button").remove();	
					
					//ele.prependTo($wrapper).find('input').val('').focus();
// 					ele.appendTo(wrapper).find('input').val('').focus();
					
// 					ele.appendTo(wrapper).find('input').val('');
					
					var rand_num1 = Math.floor((Math.random() * 10) + 1);

					var rand_num = randomString(rand_num1);

					ele.find(".dateselect").attr('id',rand_num);

					ele.find(".productadd").append('<button class="smBtn has-tip tip-top remove-field" aria-describedby="tooltip-i6hptux91" data-selector="tooltip-i6hptux91" type="button" data-tooltip="Remove" title="Remove"><i class="fa fa-trash-o"></i></button><button title="Add more" type="button" class="smBtn has-tip tip-top add-field" data-tooltip="" data-selector="tooltip-i6htcnc50" aria-describedby="tooltip-i6htcnc50" title=""><i class="fa fa-plus"></i></button>');

// 					ele.find('input').val('');

					ele.find('input').attr('value','');

					ele.find(".dateselect").removeClass("hasDatepicker");
					
					ele.appendTo(wrapper);

// 					var new_html = ele.html();

// 					$(new_html).appendTo(".holidaylist");
					

					/*ele.find(".dateselect").bind( "click", function() {
						$(this).datepicker({
							minDate:0,
							autoSize : true
						});
					
					});*/


					
					
			
					

					//$("#"+rand_num).datepicker();


				

					$("#"+rand_num).datepicker({
							minDate:0,
							autoSize : true
						});
						
			
					


					//ele.find(".productadd").append('<button title="add more" type="button" class="smBtn has-tip tip-top add-field" data-tooltip="" data-selector="tooltip-i6htcnc50" aria-describedby="tooltip-i6htcnc50" title=""><i class="fa fa-plus"></i></button><div class="clearfix"></div>');
					//ele.find(".productadd").append('<button class="smBtn has-tip tip-top remove-field" aria-describedby="tooltip-i6hptux91" data-selector="tooltip-i6hptux91" type="button" data-tooltip="" title=""><i class="fa fa-trash-o"></i></button>');
					
					//ele.appendTo($wrapper).find('input').val('').focus();

				});

				$(document).on('click', '.multi-field .remove-field', function() {

					if ($('.multi-field').length > 1) {

						var btns = $(this).parent().find("button");

						//return false;

						if (btns.length == '2') {

							var addBtn = btns[1].outerHTML;

							$(this).parent().parent().parent().remove();

							var ele = $('.multi-field:last-child');

							ele.find(".productadd").append(addBtn);

							// Check again if only one row is remaining the allow only plus button. and delete remove button.
							if ($('.multi-field').length == 1) {

								var btns = $('.multi-field').find(".productadd").find("button");

								if (btns.length == '2') {
									$(btns[0]).remove();
								}
							}

						} else {
							$(this).parent().parent().parent().remove();

							// Check again if only one row is remaining the allow only plus button. and delete remove button.
							if ($('.multi-field').length == 1) {

								var btns = $('.multi-field').find(".productadd").find("button");

								if (btns.length == '2') {
									$(btns[0]).remove();
								}
							}
						}

					}
				});

				});
			</script>
			<script>

			function randomString(len, charSet) {
			    charSet = charSet || 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz123456789';
			    var randomString = '';
			    for (var i = 0; i < len; i++) {
			    	var randomPoz = Math.floor(Math.random() * charSet.length);
			    	randomString += charSet.substring(randomPoz,randomPoz+1);
			    }
			    return randomString;
			}
			
				function usgsChanged(el) {
			    window["display_" + el.options[el.selectedIndex].value]();
			}
			
			function display_sat_sun() {
			    // alert("display_sat_sun");
			     $(".prepaidrow").hide();
			}
			
			function display_only_sun() {
			    // alert("display_only_sun");
			     $(".prepaidrow").hide();
			}
			    
			function display_no_off() {
			   // var closediv = $(this).attr('data-close');
									// $(closediv).hide();
				$(".prepaidrow").hide();
			}
			    
			  
			function display_day() {
			   // var closediv = $(this).attr('data-open');
									// $(closediv).show();
				$(".prepaidrow").show();
			}
			        
			       
			</script>

		<script type="text/javascript">
		  $(document).on('click',"#addweek",function(e){
		      e.preventDefault();
		      $('#usgsfeed').attr("data-step", "1");
		      $('#usgsfeed').attr("data-intro", "Click here to specify weekoff of kitchen");
		      $('#usgsfeed').attr("data-position", "right");
		      introJs().start();
		      $('#usgsfeed').removeAttr("data-step");
		      $('#usgsfeed').removeAttr("data-intro");

		  });
		  $(document).on('click',"#addholiday",function(e){
		      e.preventDefault();
		    //  alert($('.displayTable').find('tbody tr:first td:eq(5) button:first').length);
		      $('.holidays').attr("data-step", "1");
		      $('.holidays').attr("data-intro", "Add holidays for fooddialer<br/>1.Select year<br/>2.Specify holidays on prior.<br/>3.Save it");
		      $('.holidays').attr("data-position", "right");
		      introJs().start();
		      $('.holidays').removeAttr("data-step");
		      $('.holidays').removeAttr("data-intro");
		  });

		</script> 
<?php 
$utility = \Lib\Utility::getInstance();

?>
<script type="text/javascript" src="/spectrum/spectrum.js"></script>

<link rel="stylesheet" type="text/css" href="/spectrum/spectrum.css">
<!--<link rel="stylesheet" type="text/css" href="/spectrum/docs/docs.css">-->


<style>
	.radiocls label.right {
		display: inline-block;
	};
        .full-spectrum .sp-palette {
            max-width: 200px;
        }

</style>
<?php
$form = $this->form;
$form->setAttribute('action', $this->url('setting', array('action' => 'application-setting')));
$form->prepare();
?>						
    <div id="content">
        <div class="large-10 small-12 medium-12 columns">
        <?php
                if ($this->FlashMessenger()->hasSuccessMessages()){
                    foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
            ?>
                <!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->

                <div  data-alert="" class="alert-box success round">
                 <?php echo $msg; ?>
                    <a href="#" class="close">&times;</a>
                </div>

            <?php
                    }
                }
                elseif ($this->FlashMessenger()->hasInfoMessages()){
                    foreach ($this->FlashMessenger()->getInfoMessages() as $msg){
            ?>
                <!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->

                <div  data-alert="" class="alert-box info round">
                 <?php echo $msg; ?>
                    <a href="#" class="close">&times;</a>
                </div>

            <?php
                    }
                }elseif($this->FlashMessenger()->hasErrorMessages()){
                    foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
                <div data-alert class="alert-box alert round">
                       <?php echo $msg; ?>
                      <a href="#" class="close">&times;</a>
                </div>
            <?php 	}
                }
            ?>	

             <?php echo $this->form()->openTag($form);?>
                <fieldset>
                    <legend class="text-center">
                        General Settings
                    </legend>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('CATALOGUE_STATUS')); ?> 
                        </div> 
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php 
                                    echo $this->formHidden($form->get('id'));
                                    echo $this->formElement($form->get('CATALOGUE_STATUS'));
                                    echo $this->formElementErrors()
                                        ->setMessageOpenFormat('<small class="error">')
                                        ->setMessageCloseString('</small>')
                                        ->render($form->get('CATALOGUE_STATUS'));
                            ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_CUSTOMER_PAYMENT_MODE')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns prepaid">
                            <?php echo $this->formelement($form->get('GLOBAL_CUSTOMER_PAYMENT_MODE'));?>
							<?php echo $this->formElementErrors($form->get('GLOBAL_CUSTOMER_PAYMENT_MODE'));?>
						</div>
                    </div>
                    <div class="row mt10">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('FOOD_TYPE')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <div class="large-3 small-3 medium-3 columns" style="padding-left: 0">
                                <?php echo $this->formelement($form->get('FOOD_TYPE'));?>
                                <?php echo $this->formElementErrors($form->get('FOOD_TYPE'));?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt10">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_MIN_ORDER_PRICE')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <div class="large-3 small-3 medium-3 columns" style="padding-left: 0">
                                <?php echo $this->formelement($form->get('GLOBAL_MIN_ORDER_PRICE'));?>
                                <?php echo $this->formElementErrors($form->get('GLOBAL_MIN_ORDER_PRICE'));?>
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_MAX_ORDER_PRICE')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <div class="large-3 small-3 medium-3 columns" style="padding-left: 0">
                                <?php echo $this->formelement($form->get('GLOBAL_MAX_ORDER_PRICE'));?>
                                <?php echo $this->formElementErrors($form->get('GLOBAL_MAX_ORDER_PRICE'));?>
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('ADMIN_WEB_URL')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('ADMIN_WEB_URL'));?>
                            <?php echo $this->formElementErrors($form->get('ADMIN_WEB_URL'));?>
                        </div>
                    </div>

                    <div class="row ">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('CLIENT_WEB_URL')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('CLIENT_WEB_URL'));?>
                            <?php echo $this->formElementErrors($form->get('CLIENT_WEB_URL'));?>
                        </div>
                    </div>

                    <div class="row ">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_COMPANY_NAME')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('MERCHANT_COMPANY_NAME'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_COMPANY_NAME'));?>
                        </div>
                    </div>
                    <div class="row"> 
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('CATALOGUE_MOBILE_APP_VERSION')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('CATALOGUE_MOBILE_APP_VERSION'));?>
                            <?php echo $this->formElementErrors($form->get('CATALOGUE_MOBILE_APP_VERSION'));?>
                        </div>
                    </div>

                    <div class="row"> 
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('RESTAURANT_MOBILE_APP_VERSION')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('RESTAURANT_MOBILE_APP_VERSION'));?>
                            <?php echo $this->formElementErrors($form->get('RESTAURANT_MOBILE_APP_VERSION'));?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('FORCE_CUSTOMER_TO_USE_PASSWORD')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('FORCE_CUSTOMER_TO_USE_PASSWORD'));?>
                            <?php echo $this->formElementErrors($form->get('FORCE_CUSTOMER_TO_USE_PASSWORD'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('DATE_FORMAT')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <div class="large-3 small-3 medium-3 columns" style="padding-left: 0">
                            <?php echo $this->formelement($form->get('DATE_FORMAT'));?>
                            <?php echo $this->formElementErrors($form->get('DATE_FORMAT'));?>
                            </div><label class="disabled">(10-03-2015, 10-03-15)</label>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <label class="inline right">Menu Section<span class="red">*</span></label>
                        </div>
                        <div class="large-8 small-8 medium-8 columns prepaid">
                            <label class="inline left mr5" style="line-height: 0.9em;">                                
                                <div class="checker">
                                    <input type="checkbox" name="top" class="topmenu" data-close=".onlinerow" value="top" checked="checked">
                                </div>                                
                               Top
                            </label>
                            <label class="inline left mr5" style="line-height: 0.9em;">
                                <div class="checker">                                    
                                    <input type="checkbox" name="bottom" class="bottommenu" data-close=".onlinerow" value="bottom">                                    
                                </div>
                                Bottom
                            </label>
                            <label class="inline left mr5" style="line-height: 0.9em;">
                                <div class="checker">
                                    <input type="checkbox" name="left" class="leftmenu" data-close=".onlinerow" value="left">
                                </div>
                                Left Side
                            </label>                                                       
                        </div>
                    </div>
                    <div class="row ">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_APP_STORE_PAGE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_APP_STORE_PAGE'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_APP_STORE_PAGE'));?>
                        </div>
                    
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_PLAY_STORE_PAGE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_PLAY_STORE_PAGE'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_PLAY_STORE_PAGE'));?>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_CATALOG_BY_CATEGORY')); ?> 
                        </div> 
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php 
                                    echo $this->formHidden($form->get('id'));
                                    echo $this->formElement($form->get('GLOBAL_CATALOG_BY_CATEGORY'));
                                    echo $this->formElementErrors()
                                        ->setMessageOpenFormat('<small class="error">')
                                        ->setMessageCloseString('</small>')
                                        ->render($form->get('GLOBAL_CATALOG_BY_CATEGORY'));
                            ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION')); ?> 
                        </div> 
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php 
                                    echo $this->formHidden($form->get('id'));
                                    echo $this->formElement($form->get('GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION'));
                                    echo $this->formElementErrors()
                                        ->setMessageOpenFormat('<small class="error">')
                                        ->setMessageCloseString('</small>')
                                        ->render($form->get('GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION'));
                            ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('SHOW_PRODUCT_AND_MEAL_CALENDAR')); ?> 
                        </div> 
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php 
                                    echo $this->formHidden($form->get('id'));
                                    echo $this->formElement($form->get('SHOW_PRODUCT_AND_MEAL_CALENDAR'));
                                    echo $this->formElementErrors()
                                        ->setMessageOpenFormat('<small class="error">')
                                        ->setMessageCloseString('</small>')
                                        ->render($form->get('SHOW_PRODUCT_AND_MEAL_CALENDAR'));
                            ?>
                        </div>
                    </div>
                    <?php
                        //if($utility->checkSubscription('custom_sender_id','allowed')){
                    ?>
                    <div class="row ">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_SENDER_ID')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('MERCHANT_SENDER_ID'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_SENDER_ID'));?>
                        </div>
                    </div>
                    <?php
                       // }
                    ?>
                    <div class="row ">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_WORKING_HOURS')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('MERCHANT_WORKING_HOURS'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_WORKING_HOURS'));?>
                        </div>
                    </div>
                    <div class="row ">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('SIGNATURE_COMPANY_NAME')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('SIGNATURE_COMPANY_NAME'));?>
                            <?php echo $this->formElementErrors($form->get('SIGNATURE_COMPANY_NAME'));?>
                        </div>
                    </div>
                    <div class="row ">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_GST_NO')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('MERCHANT_GST_NO'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_GST_NO'));?>
                        </div>
                    </div>
                    <div class="large-4 small-4 medium-4 columns">
                        &nbsp;
                    </div>
                </fieldset>

                <fieldset class="mt15 settings">
                    <legend class="text-center">
                        Bank Settings
                    </legend>

                    <div class="row bankdetails">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_BANK_ACCOUNT_NAME')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('MERCHANT_BANK_ACCOUNT_NAME'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_BANK_ACCOUNT_NAME'));?>
                        </div>
                    </div>

                    <div class="row bankdetails">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_BANK_ACCOUNT_NO')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('MERCHANT_BANK_ACCOUNT_NO'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_BANK_ACCOUNT_NO'));?>
                        </div>
                    </div>

                    <div class="row bankdetails">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_BANK_NAME')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('MERCHANT_BANK_NAME'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_BANK_NAME'));?>
                        </div>
                    </div>

                    <div class="row bankdetails">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_BANK_IFSC_CODE')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('MERCHANT_BANK_IFSC_CODE'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_BANK_IFSC_CODE'));?>
                        </div>
                    </div>
                    <div class="row  bankdetails">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_BANK_BRANCH_ADDRESS')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('MERCHANT_BANK_BRANCH_ADDRESS'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_BANK_BRANCH_ADDRESS'));?>
                        </div>
                    </div>

                </fieldset>

                <fieldset class="mt15">
                    <legend class="text-center">
                        Reminder Settings
                    </legend>
                    <div class="row">
            <div class="large-4 small-4 medium-4 columns">
                <?php echo $this->formLabel($form->get('ORDER_EXPIRY_SMS_DAYS_BEFORE')); ?> 
            </div>
            <div class="large-8  small-8 medium-8 columns">
                <div class="large-3 small-3 medium-3 columns" style="padding-left: 0">
                    <?php echo $this->formelement($form->get('ORDER_EXPIRY_SMS_DAYS_BEFORE'));?>
                    <?php echo $this->formElementErrors($form->get('ORDER_EXPIRY_SMS_DAYS_BEFORE'));?>
                </div>
            </div>
            </div>
            <div class="row">
                    <div class="large-4 small-4 medium-4 columns">
                        <?php echo $this->formLabel($form->get('ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND')); ?>
                    </div>
                    <div class="large-8  small-8 medium-8 columns">
                        <div class="large-3 small-3 medium-3 columns" style="padding-left: 0">
                            <?php echo $this->formelement($form->get('ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND'));?>
                            <?php echo $this->formElementErrors($form->get('ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND'));?>
                        </div>
                    </div>
           </div>

                </fieldset> 
                <fieldset class="mt15">
                    <legend class="text-center">
                        Dispatch Label Settings
                    </legend>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_TEMPLATE')); ?>
                        </div>
                        <div class="large-8  small-8 medium-8 columns">
                            <div class="large-3 small-3 medium-3 columns" style="padding-left: 0">
                                <?php echo $this->formelement($form->get('PRINT_LABEL_TEMPLATE'));?>
                                <?php echo $this->formElementErrors($form->get('PRINT_LABEL_TEMPLATE'));?>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                                <?php echo $this->formelement($form->get('PRINT_LABEL'));?>
                                <?php echo $this->formElementErrors($form->get('PRINT_LABEL'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_CUSTOMER_PHONE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_CUSTOMER_PHONE'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_CUSTOMER_PHONE'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_DIBBAWALA_CODE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_DIBBAWALA_CODE'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_DIBBAWALA_CODE'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_ITEM_DETAILS')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_ITEM_DETAILS'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_ITEM_DETAILS'));?>
                        </div>
                    </div>

                   <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_BARCODE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_BARCODE'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_BARCODE'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_MERCHANT_PHONE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_MERCHANT_PHONE'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_MERCHANT_PHONE'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_MERCHANT_WEBSITE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_MERCHANT_WEBSITE'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_MERCHANT_WEBSITE'));?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_DELIVERY_PERSON')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_DELIVERY_PERSON'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_DELIVERY_PERSON'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_TEXT_COLOR')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <div class="large-3 small-3 medium-3 columns" style="padding-left: 0">
                                <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_TEXT_COLOR'));?>
                                <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_TEXT_COLOR'));?>
                            </div><label class="disabled">(eg: REG:#3423, VEG:#342344, DIAT:#sfs534)</label>


                                                                            <!--<input type='text' id="full"/>-->

                        </div>
                    </div>

                                                            <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_NONVEG_DAY_COLOR')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <div class="large-3 small-3 medium-3 columns" style="padding-left: 0">
                                <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_NONVEG_DAY_COLOR'));?>
                                <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_NONVEG_DAY_COLOR'));?>
                            </div><label class="disabled">(eg: THU:#ff0000)</label>

                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE'));?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_PRICE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_PRICE'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_PRICE'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_SHOW_DELIVERY_TYPE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_SHOW_DELIVERY_TYPE'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_SHOW_DELIVERY_TYPE'));?>
                        </div>
                    </div>     
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PRINT_LABEL_ORDER_BY')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('PRINT_LABEL_ORDER_BY'));?>
                            <?php echo $this->formElementErrors($form->get('PRINT_LABEL_ORDER_BY'));?>
                        </div>
                    </div>                                     

              </fieldset>  
              <fieldset class="mt15 settings">
                    <legend class="text-center">
                        Social Media Settings
                    </legend>
                   <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_SOCIAL_MEDIA_FACEBOOK')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_SOCIAL_MEDIA_FACEBOOK'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_SOCIAL_MEDIA_FACEBOOK'));?>
                        </div>
                    </div>
                  <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_SOCIAL_MEDIA_INSTAGRAM')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_SOCIAL_MEDIA_INSTAGRAM'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_SOCIAL_MEDIA_INSTAGRAM'));?>
                        </div>
                    </div>
                  <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_SOCIAL_MEDIA_TWITTER')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_SOCIAL_MEDIA_TWITTER'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_SOCIAL_MEDIA_TWITTER'));?>
                        </div>
                    </div>
                  <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS'));?>
                        </div>
                    </div>
                  <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GOOGLE_TRACKING_ID')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GOOGLE_TRACKING_ID'));?>
                            <?php echo $this->formElementErrors($form->get('GOOGLE_TRACKING_ID'));?>
                        </div>
                    </div>
                </fieldset>  

                 <fieldset class="mt15 settings">
                    <legend class="text-center">
                       Contact Us Settings
                    </legend>
                   <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_POSTAL_ADDRESS')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('MERCHANT_POSTAL_ADDRESS'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_POSTAL_ADDRESS'));?>
                        </div>
                    </div>
                  <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MERCHANT_SUPPORT_EMAIL')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('MERCHANT_SUPPORT_EMAIL'));?>
                            <?php echo $this->formElementErrors($form->get('MERCHANT_SUPPORT_EMAIL'));?>
                        </div>
                    </div>
                  <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_WEBSITE_PHONE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_WEBSITE_PHONE'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_WEBSITE_PHONE'));?>
                        </div>
                    </div>

                  <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('CONTACTUS_GOOGLE_LATITUDE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('CONTACTUS_GOOGLE_LATITUDE'));?>
                            <?php echo $this->formElementErrors($form->get('CONTACTUS_GOOGLE_LATITUDE'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('CONTACTUS_GOOGLE_LONGITUDE')); ?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('CONTACTUS_GOOGLE_LONGITUDE'));?>
                            <?php echo $this->formElementErrors($form->get('CONTACTUS_GOOGLE_LONGITUDE'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns"></div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                                <a href="http://www.mapcoordinates.net" target="_blank" class="">Click here</a><span class="" style=" text-transform: none" > to find your latitude and longitude please</span>
                        </div>
                    </div>                    
                </fieldset> 

                <div class="large-12 columns mt15 pr0">
                    <div class="right">
                        <button type="submit" class="greenBg" id="submitbutton">
                            <i class="fa fa-save"></i>&nbsp;Save
                        </button>
                        <button class="redBg" type="submit" id="cancelbutton"><i class="fa fa-ban"></i> &nbsp;Cancel</button>
                    </div>
                </div>
                 <?php 
                    echo $this->formElement($form->get('backurl'));
                 ?>
            <?php
                echo $this->formElement($form->get('csrf'));
                echo $this->form()->closeTag($form);
            ?>        

        </div>

    </div>
</div>
	
			<script>
				jQuery(document).ready(function($) {
					$("#minDate").datepicker({
						autoSize : true
					});
					$("#maxDate").datepicker({
						autoSize : true
					});
				});
			</script>
<!--            <script>
                $(document).ready(function(){
                    $("#IF_THEME_ENABLE").hide();
                  
                    $("#IS_THEME_ENABLE").click(function(){
                       $("#IF_THEME_ENABLE").show();
                      

                    });
                     
                      $("#IS_THEME_ENABLE").click(function(){
                       $("#IF_THEME_ENABLE").hide();
                     

                    });      
                });
                    
            </script>-->
			<script>
                showGateway = function(ele,isSel){
					
					if(isSel){
    					if( $(ele).val() == 'neft') {
    					    $("div.bankdetails").toggleClass('hide');
    					}
                    }
                }
				$(document).ready(function() {
					$(".settings").addClass("active");
					$(".settings ul li:nth-of-type(2)").addClass("active");
					
					$(".mealtype").hide();
					$(".allow input").click(function() {
						$(".mealtype").toggle();
					});

					$(".prepaidradio").click(function() {
						var closediv = $(this).attr('data-open');
						$(closediv).show();
					});

					$(".postpaidradio").click(function() {
						var closediv = $(this).attr('data-close');
						$(closediv).hide();
					});

					$(".onlinecheck").click(function() {
						var closediv = $(this).attr('data-open');
						$(closediv).show();
					});

					$(".cashcheck,.neftcheck,.chequecheck").click(function() {
						var closediv = $(this).attr('data-close');
						$(closediv).hide();
					});

					$(".fdradio").click(function() {
						var closediv = $(this).attr('data-open');
						$(closediv).show();
					});
					$(".payuradio,.ebsradio,.aveneyradio").click(function() {
						var closediv = $(this).attr('data-close');
						$(closediv).hide();
					});
                    $(".topmenu,.bottommenu,.leftmenu").click(function(){
                        
                    })
                    $("#app_store,#play_store").click(function(){
                        
                    })
                    $("#facebook,#instagram,#google_plus,#twitter").click(function(){
                        //alert(123);
                    })
                    $("input:checkbox.gateway").click(function() {
						
    					showGateway(this,true);
							
					});

					$("input:checkbox.gateway").each(function(){
						showGateway(this,$(this).is(":checked"));
					});

				});
                                
                                
 $(".basic").spectrum({
    color: "#f00",
    change: function(color) {
        $("#basic-log").text("change called: " + color.toHexString());
    }
});

$("#full").spectrum({
    color: "#ECC",
    showInput: true,
    className: "full-spectrum",
    showInitial: true,
    showPalette: true,
    showSelectionPalette: true,
    maxSelectionSize: 10,
    preferredFormat: "hex",
    localStorageKey: "spectrum.demo",
    move: function (color) {
        
    },
    show: function () {
    
    },
    beforeShow: function () {
    
    },
    hide: function () {
    
    },
    change: function() {
        
    },
    palette: [
        ["rgb(0, 0, 0)", "rgb(67, 67, 67)", "rgb(102, 102, 102)",
        "rgb(204, 204, 204)", "rgb(217, 217, 217)","rgb(255, 255, 255)"],
        ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
        "rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(153, 0, 255)", "rgb(255, 0, 255)"], 
        ["rgb(230, 184, 175)", "rgb(244, 204, 204)", "rgb(252, 229, 205)", "rgb(255, 242, 204)", "rgb(217, 234, 211)", 
        "rgb(208, 224, 227)", "rgb(201, 218, 248)", "rgb(207, 226, 243)", "rgb(217, 210, 233)", "rgb(234, 209, 220)", 
        "rgb(221, 126, 107)", "rgb(234, 153, 153)", "rgb(249, 203, 156)", "rgb(255, 229, 153)", "rgb(182, 215, 168)", 
        "rgb(162, 196, 201)", "rgb(164, 194, 244)", "rgb(159, 197, 232)", "rgb(180, 167, 214)", "rgb(213, 166, 189)", 
        "rgb(204, 65, 37)", "rgb(224, 102, 102)", "rgb(246, 178, 107)", "rgb(255, 217, 102)", "rgb(147, 196, 125)", 
        "rgb(118, 165, 175)", "rgb(109, 158, 235)", "rgb(111, 168, 220)", "rgb(142, 124, 195)", "rgb(194, 123, 160)",
        "rgb(166, 28, 0)", "rgb(204, 0, 0)", "rgb(230, 145, 56)", "rgb(241, 194, 50)", "rgb(106, 168, 79)",
        "rgb(69, 129, 142)", "rgb(60, 120, 216)", "rgb(61, 133, 198)", "rgb(103, 78, 167)", "rgb(166, 77, 121)",
        "rgb(91, 15, 0)", "rgb(102, 0, 0)", "rgb(120, 63, 4)", "rgb(127, 96, 0)", "rgb(39, 78, 19)", 
        "rgb(12, 52, 61)", "rgb(28, 69, 135)", "rgb(7, 55, 99)", "rgb(32, 18, 77)", "rgb(76, 17, 48)"]
    ]
});

			</script>

		</body>
</html>


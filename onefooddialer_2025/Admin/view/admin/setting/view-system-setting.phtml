				<?php $utility = \Lib\Utility::getInstance();?>
                    <div id="content">
					
						<div class="row">
							<div class="large-8 medium-12 columns">
							
								      <?php
							if ($this->FlashMessenger()->hasSuccessMessages()){
								foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
						?>
							<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
							
							<div  data-alert="" class="alert-box success round">
				 			 <?php echo $msg; ?>
				  				<a href="#" class="close">&times;</a>
							</div>
							
						<?php
								}
							}else if($this->FlashMessenger()->hasErrorMessages()){
								foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
							<div data-alert class="alert-box alert round">
								   <?php echo $msg; ?>
								  <a href="#" class="close">&times;</a>
							</div>
						<?php 	}
							}
						?>	
							
								<form>
									
								<div class="block sysSetting">
									<fieldset>
									<legend >
											General Settings
									</legend>
										<div class="block-content">
											<ul class="list list-timeline pull-t">
												<li>
													<div class="list-timeline-time">
														Website Maintenance
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['WEBSITE_MAINTENANCE']))?ucwords($setting_data['WEBSITE_MAINTENANCE']):""?>
														</p>
													</div>
												</li>
											
												<li>
													<div class="list-timeline-time">
														Timezone
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo (isset($setting_data['TIME_ZONE']))?$setting_data['TIME_ZONE']:"";?>
														</p>
	
													</div>
												</li>
	
												<li>
													<div class="list-timeline-time">
														 Menu type
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
														<?php
															if(isset($setting_data['MENU_TYPE']))
															{
																$last = count($setting_data['MENU_TYPE']);
																foreach($setting_data['MENU_TYPE'] as $key=>$val)
																{
																	if($last==$key+1)
																	{
																		echo ucwords($val);
																	}
																	else 
																	{
																		echo " ".ucwords($val).", ";
																	}
																}
															}
														?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Apply tax
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo (isset($setting_data['GLOBAL_APPLY_TAX']))?ucwords($setting_data['GLOBAL_APPLY_TAX']):""?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Tax method
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_TAX_METHOD']))?ucwords($setting_data['GLOBAL_TAX_METHOD']):""?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Auto Dispatch
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['ENABLE_AUTO_DISPATCH']))?ucwords($setting_data['ENABLE_AUTO_DISPATCH']):""?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Enable Recurring Order
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ENABLE_RECURRING_ORDER']))?ucwords($setting_data['GLOBAL_ENABLE_RECURRING_ORDER']):""?>
														</p>
	
													</div>
												</li>

												<li>
													<div class="list-timeline-time">
														Auto Delivery
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['ENABLE_AUTO_DELIVERY']))?ucwords($setting_data['ENABLE_AUTO_DELIVERY']):""?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Apply delivery charges
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_APPLY_DELIVERY_CHARGES']))?ucwords($setting_data['GLOBAL_APPLY_DELIVERY_CHARGES']):""?>
														</p>
	
													</div>
												</li>
	
												<li>
													<div class="list-timeline-time">
														Delivery charges calculations
	
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['APPLY_DELIVERY_CHARGES']))?ucwords($setting_data['APPLY_DELIVERY_CHARGES']):""?>
														</p>
	
													</div>
												</li>
												
												<li>
													<div class="list-timeline-time">
														Allow SMS quota to exceed
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ALLOW_SMS_QUOTA_EXCEED']))?ucwords($setting_data['GLOBAL_ALLOW_SMS_QUOTA_EXCEED']):""?>
														</p>
													</div>
												</li>
												<?php
                                                    if($utility->checkSubscription('phone_verification_otp','allowed')){
                                                ?>
												<li>
													<div class="list-timeline-time">
														Customer phone verification method
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['PHONE_VERIFICATION_METHOD']))?ucwords($setting_data['PHONE_VERIFICATION_METHOD']):""?>
														</p>
	
													</div>
												</li>
                                                    <?php
                                                    
                                                    }
                                                    ?>
                                                
												<li>
													<div class="list-timeline-time">
														Show product meal calender
	
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php  if(isset($setting_data['SHOW_PRODUCT_AND_MEAL_CALENDAR'])){
																
																if($setting_data['SHOW_PRODUCT_AND_MEAL_CALENDAR']=='0'){
																	$showmealcalender = 'No';
																}
																else 
																{
																	$showmealcalender = 'Yes';
																}
																
																
															} /* ?ifucwords($setting_data['SHOW_PRODUCT_AND_MEAL_CALENDAR']):"" */
															echo $showmealcalender;
																
															?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Show catalog view
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_SHOW_CATALOG_VIEW']))?ucwords($setting_data['GLOBAL_SHOW_CATALOG_VIEW']):""?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Catalog cart plan
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_CATALOG_CART_PLAN']))?ucwords($setting_data['GLOBAL_CATALOG_CART_PLAN']):""?>
														</p>
	
													</div>
												</li>
												
												<li>
													<div class="list-timeline-time">
														S3 Bucket URL
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['S3_BUCKET_URL']))?ucwords($setting_data['S3_BUCKET_URL']):""?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Allow Meal Swap
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ALLOW_MEAL_SWAP']))?ucwords($setting_data['GLOBAL_ALLOW_MEAL_SWAP']):""?>
														</p>
	
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Show to customer meal swap
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ALLOW_ADMIN_MEAL_SWAP']))?ucwords($setting_data['GLOBAL_ALLOW_ADMIN_MEAL_SWAP']):""?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Country (locale)
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo (isset($setting_data['GLOBAL_LOCALE'])) ? $setting_data['GLOBAL_COUNTRY'].' ('.$setting_data['GLOBAL_LOCALE'].')':""?>
														</p>
	
													</div>
												</li>	
												
												<li>
													<div class="list-timeline-time">
														Currency
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_CURRENCY']))?ucwords($setting_data['GLOBAL_CURRENCY']):""?>
														</p>
	
													</div>
												</li>
												
												<li>
													<div class="list-timeline-time">
														Enable Menu Planner
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ALLOW_MENU_PLANNER']))?ucwords($setting_data['GLOBAL_ALLOW_MENU_PLANNER']):""?>
														</p>
	
													</div>
												</li>																							
												
												<li>
													<div class="list-timeline-time">
														Publish Planned Menu
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_PUBLISH_MENU_PLANNER']))?ucwords($setting_data['GLOBAL_PUBLISH_MENU_PLANNER']):""?>
														</p>
	
													</div>
												</li>																							
												
												<li>
													<div class="list-timeline-time">
														Allow Customer To Set Item Preference
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ALLOW_MEAL_ITEM_SWAP']))?ucwords($setting_data['GLOBAL_ALLOW_MEAL_ITEM_SWAP']):""?>
														</p>
	
													</div>
												</li>																							
												
                                                <li>
													<div class="list-timeline-time">
														Global Delivery Type
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_DELIVERY_TYPE']))?($setting_data['GLOBAL_DELIVERY_TYPE']):""?>
														</p>
	
													</div>
												</li>	
                                                
                                                <li>
													<div class="list-timeline-time">
														Allow Partial Payment
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ALLOW_PARTIAL_PAYMENT']))?($setting_data['GLOBAL_ALLOW_PARTIAL_PAYMENT']):""?>
														</p>
	
													</div>
												</li>	
                                                <li>
													<div class="list-timeline-time">
														Skip KOT
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo(isset($setting_data['GLOBAL_SKIP_KITCHEN']))?($setting_data['GLOBAL_SKIP_KITCHEN']):""?>
														</p>
	
													</div>
												</li>	
												<li>
													<div class="list-timeline-time">
														Allow Instant Order
													</div>	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ALLOW_INSTANT_ORDER']))?($setting_data['GLOBAL_ALLOW_INSTANT_ORDER']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Enable Instant Order Image
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ENABLE_INSTANT_ORDER_IMAGE']))?($setting_data['GLOBAL_ENABLE_INSTANT_ORDER_IMAGE']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Enable Website
													</div>
													<div class="list-timeline-content">	
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ENABLE_WEBSITE']))?($setting_data['GLOBAL_ENABLE_WEBSITE']):""?>
														</p>
													</div>
												</li>											
                                                <li>
													<div class="list-timeline-time">
														Enable Meal Plan
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ENABLE_MEAL_PLANS']))?($setting_data['GLOBAL_ENABLE_MEAL_PLANS']):""?>
														</p>
	
													</div>
												</li> 
                                                <li>
													<div class="list-timeline-time">
														Enable Timeslot Manager
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_ALLOW_TIMESLOT']))?($setting_data['GLOBAL_ALLOW_TIMESLOT']):""?>
														</p>
	
													</div>
												</li> 												                                               
											</ul>
										</div>
									</fieldset>
									<fieldset>
									<legend >
											SMTP Settings
									</legend>
										<div class="block-content">
											<ul class="list list-timeline pull-t">
												<li>
													<div class="list-timeline-time">
														From name
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['SMTP_FROM_NAME']))?ucwords($setting_data['SMTP_FROM_NAME']):""?>
														</p>
	
													</div>
												</li>
	
												<li>
													<div class="list-timeline-time">
														From email
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['SMTP_FROM_EMAIL']))?ucwords($setting_data['SMTP_FROM_EMAIL']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														SMTP hosts
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['SMTP_HOST']))?ucwords($setting_data['SMTP_HOST']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														SMTP ports
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['SMTP_PORT']))?ucwords($setting_data['SMTP_PORT']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														SMTP username
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['SMTP_USERNAME']))?ucwords($setting_data['SMTP_USERNAME']):""?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														SMTP password
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['SMTP_PASSWORD']))?ucwords($setting_data['SMTP_PASSWORD']):""?>
														</p>
													</div>
												</li>
											</ul>
										</div>
									</fieldset>
									
									<fieldset>
									<legend >
											Payment Gateway Settings
									</legend>
										<div class="block-content">
											<ul class="list list-timeline pull-t">
												<li>
													<div class="list-timeline-time">
														Global payment environment
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GLOBAL_PAYMENT_ENV']))? ($setting_data['GLOBAL_PAYMENT_ENV']):""?>
														</p>
													</div>
												</li>
                                                                                                <li>
                                                                                                    
													<div class="list-timeline-time">
														Online payment gateway
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['ONLINE_PAYMENT_GATEWAY']))?($setting_data['ONLINE_PAYMENT_GATEWAY']):""?>
														</p>
	
													</div>
												</li>
                                                <?php if(in_array('payu', $payment_gateway)){?>
												<li>
													<div class="list-timeline-time">
														PayU gateway merchant id
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYU_MERCHANT_ID']))?($setting_data['GATEWAY_PAYU_MERCHANT_ID']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														PayU gateway merchant key
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYU_MERCHANT_KEY']))? ($setting_data['GATEWAY_PAYU_MERCHANT_KEY']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														PayU gateway merchant salt
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYU_MERCHANT_SALT']))? ($setting_data['GATEWAY_PAYU_MERCHANT_SALT']):""?>
														</p>
													</div>
												</li>                                                                            
                                                <?php }?>
                                                <?php if(in_array('instamojo', $payment_gateway)){?>
                                                <li>
													<div class="list-timeline-time">
														Instamojo gateway merchant key
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_INSTAMOJO_MERCHANT_KEY']))? ($setting_data['GATEWAY_INSTAMOJO_MERCHANT_KEY']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Instamojo gateway merchant token
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_INSTAMOJO_MERCHANT_TOKEN']))? ($setting_data['GATEWAY_INSTAMOJO_MERCHANT_TOKEN']):""?>
														</p>
													</div>
												</li>
                                                <?php }?>
                                                <?php if(in_array('paytm', $payment_gateway)){?>
                                                <li>
													<div class="list-timeline-time">
														Paytm gateway merchant id
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYTM_MERCHANT_MID']))? ($setting_data['GATEWAY_PAYTM_MERCHANT_MID']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Paytm gateway merchant key
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYTM_MERCHANT_KEY']))? ($setting_data['GATEWAY_PAYTM_MERCHANT_KEY']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Paytm gateway merchant industry id
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYTM_MERCHANT_INDUSTRY']))? ($setting_data['GATEWAY_PAYTM_MERCHANT_INDUSTRY']):""?>
														</p>
													</div>
												</li>	
												<li>
													<div class="list-timeline-time">
														Paytm gateway channel id
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYTM_MERCHANT_CHANNEL']))? ($setting_data['GATEWAY_PAYTM_MERCHANT_CHANNEL']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Paytm gateway merchant website
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYTM_MERCHANT_WEBSITE']))? ($setting_data['GATEWAY_PAYTM_MERCHANT_WEBSITE']):""?>
														</p>
													</div>
												</li>											
                                                <?php }?>
                                                <!-- Payeezy Details Starts-->
                                                <?php if(in_array('payeezy', $payment_gateway)) { ?>
                                                <li>
													<div class="list-timeline-time">
														Payeezy Login ID
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['PAYEEZY_HCO_LOGIN']))? ($setting_data['PAYEEZY_HCO_LOGIN']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Payeezy Transaction Key
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['PAYEEZY_HCO_TRANSACTION_KEY']))? ($setting_data['PAYEEZY_HCO_TRANSACTION_KEY']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Payeezy Gateway ID
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYEEZY_ID']))? ($setting_data['GATEWAY_PAYEEZY_ID']):""?>
														</p>
													</div>
												</li>	
												<li>
													<div class="list-timeline-time">
														Payeezy Gateway Key
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYEEZY_KEY']))? ($setting_data['GATEWAY_PAYEEZY_KEY']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Payeezy Gateway Secret
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYEEZY_SECRET']))? ($setting_data['GATEWAY_PAYEEZY_SECRET']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Payeezy Gateway Hmac Key
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYEEZY_HMAC_KEY']))? ($setting_data['GATEWAY_PAYEEZY_HMAC_KEY']):""?>
														</p>
													</div>
												</li>											

                                                <?php }?>
                                                <!-- Payeezy Details Ends-->
                                                
                                                <!-- Paypal Details Start-->
                                                <?php if(in_array('paypal', $payment_gateway)) { ?>
                                                <li>
													<div class="list-timeline-time">
														Paypal Login ID
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYPAL_USER']))? ($setting_data['GATEWAY_PAYPAL_USER']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Paypal Secret
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYPAL_SECRET']))? ($setting_data['GATEWAY_PAYPAL_SECRET']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Paypal Signature
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_PAYPAL_SIGNATURE']))? ($setting_data['GATEWAY_PAYPAL_SIGNATURE']):""?>
														</p>
													</div>
												</li>																								
                                                 <?php }?>
                                                <!-- Paypal Details Ends-->	
                                                
                                                <!-- Converge Details start-->
                                                <?php if(in_array('converge', $payment_gateway)) { ?>
                                                <li>
													<div class="list-timeline-time">
														Converge Merchant Id
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_CONVERGE_MERCHANT_ID']))? ($setting_data['GATEWAY_CONVERGE_MERCHANT_ID']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Converge User Id
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_CONVERGE_USER_ID']))? ($setting_data['GATEWAY_CONVERGE_USER_ID']):""?>
														</p>
													</div>
												</li>
                                                <li>
													<div class="list-timeline-time">
														Converge Pin
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_CONVERGE_PIN']))? ($setting_data['GATEWAY_CONVERGE_PIN']):""?>
														</p>
													</div>
												</li>																								
                                                 <?php }?>
                                                <!-- Converge Details Ends-->
                                                
												<li>
													<div class="list-timeline-time">
														Apply gateway transaction charges
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['APPLY_GATEWAY_TRANSACTION_CHARGES']))?ucwords($setting_data['APPLY_GATEWAY_TRANSACTION_CHARGES']):""?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Gateway transaction charges amount 
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['GATEWAY_TRANSACTION_CHARGES_AMOUNT']))?ucwords($setting_data['GATEWAY_TRANSACTION_CHARGES_AMOUNT']):""?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Third party charges
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo  (isset($setting_data['THIRD_PARTY_CHARGES']))?ucwords($setting_data['THIRD_PARTY_CHARGES']):""?>
														</p>
													</div>
												</li>
												
											</ul>
										</div>
									</fieldset>
									
								</div>
								
									
								<?php 
									if($log_user_type=='admin')
									{
								?>
								<div class="large-12 columns mt15 pr0">
									<div class="right">
										<a href="/setting/system-setting" class="button"><i class="fa fa-edit"></i>&nbsp;Edit</a>
									</div>
								</div>
								<?php 
									}
								?>
								</form>
							</div>

						</div>

					</div>
			
			<script type="text/javascript">
				$(document).ready(function() {
					$(".customer").addClass("active");
                    

					showDabbawala = function(type) {

						$("#dabbawala_code_text").hide();
						$("#dabbawala_code_image").hide();

						$("#dabbawala_code_" + type).show();

					}

					$('#datepicker').datepicker();
					$("#datepicker").datepicker({
						dateFormat : "yy/mm/dd"
					}).datepicker("setDate", new Date());

					$('#sameadd').change(function() {
						//alert("dddd");
						if ($(this).is(':checked')) {
							$('#lunch_add').val($('#customer_Address').val());
							$('#dinner_add').val($('#customer_Address').val());

						} else {
							$('#lunch_add').val('');
							$('#dinner_add').val('');
						}
					});

					$("input:radio[name='dabbawala_code_type']").on('click', function() {

						showDabbawala($(this).val());

					});

					showDabbawala($("input:radio[name='dabbawala_code_type']:checked").val());

				});

			</script>

	</body>
</html>
<?php
	$form = $this->form;
	$form->setAttribute('action', $this->url('setting', array('action' => 'index')));
	$form->setAttribute('class','stdform');
	$form->prepare();

	$seleted_tax = $form->get('GLOBAL_APPLY_TAX')->getValue();
	
	$tax_provision = $form->get('GLOBAL_APPLY_TAX');
	$tax_provision_opt = $tax_provision->getOptions();
	
?>

					<div id="content">
						<div class="row">
							<div class="large-8 medium-12 columns">
								<form>
									
								<div class="block sysSetting">
									<fieldset>
									<legend >
											General Settings
									</legend>
										<div class="block-content">
											<ul class="list list-timeline pull-t">
												<li>
													<div class="list-timeline-time">
														<?php echo $this->formLabel($form->get('TIME_ZONE')); ?> 
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo $this->formelement($form->get('TIME_ZONE'));?>
															<?php echo $this->formElementErrors($form->get('TIME_ZONE'));?>
														</p>
													</div>
												</li>
	
												<li>
													<div class="list-timeline-time">
															<?php echo $this->formLabel($form->get('MENU_TYPE')); ?>
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															<?php echo $this->formelement($form->get('MENU_TYPE'));?>
															<?php echo $this->formElementErrors($form->get('MENU_TYPE'));?>
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
															<?php echo $this->formLabel($form->get('GLOBAL_APPLY_TAX'));?>
															<?php echo $this->formElementErrors($form->get('GLOBAL_APPLY_TAX'));?>
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
														<?php
							                				foreach($tax_provision_opt['value_options'] as $key=>$val){
							            				?>
															<input  type="radio" id="<?php echo $key;?>" name="GLOBAL_APPLY_TAX"  value="<?php echo $key;?>" <?php echo $checked=($seleted_tax==$key)?'checked':''?>>
															<label class="pull-left" for="<?php echo $key;?>"><?php echo $val;?></label>
														<?php 
							                				}
							                				echo $this->formElementErrors($form->get('GLOBAL_APPLY_TAX'));
														?>
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Tax method
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Service tax
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Apply delivery charges
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Yes
														</p>
	
													</div>
												</li>
	
												<li>
													<div class="list-timeline-time">
														Delivery charges calculations
	
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Mealwise
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Allow SMS quota to exceed
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Yes
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Customer phone verification method
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															OTP
														</p>
	
													</div>
												</li>
	
												<li>
													<div class="list-timeline-time">
														Show product meal calender
	
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															No
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Show catalog view
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Tiffin
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Catalog cart plan
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Period
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Locale
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															en_US
														</p>
	
													</div>
												</li>												
												<li>
													<div class="list-timeline-time">
														Currency
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															USD
														</p>
	
													</div>
												</li>												
												
											</ul>
										</div>
									</fieldset>
									
									
									<fieldset>
									<legend >
											SMTP Settings
									</legend>
										<div class="block-content">
											<ul class="list list-timeline pull-t">
												<li>
													<div class="list-timeline-time">
														From name
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															Name
														</p>
	
													</div>
												</li>
	
												<li>
													<div class="list-timeline-time">
														From email
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Email
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														SMTP hosts
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Hosts
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														SMTP ports
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															Ports
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														SMTP username
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Username
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														SMTP password
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															Password
														</p>
													</div>
												</li>
											</ul>
										</div>
									</fieldset>
									
									<fieldset>
									<legend >
											Payment Gateway Settings
									</legend>
										<div class="block-content">
											<ul class="list list-timeline pull-t">
												<li>
													<div class="list-timeline-time">
														Online payment gateway
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															ICICI
														</p>
	
													</div>
												</li>
	
												<li>
													<div class="list-timeline-time">
														Payment gateway merchant Id
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Merchant Id
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Payment gateway merchant Salt
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Payment gateway
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														ICICI key
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															ICICI key
														</p>
													</div>
												</li>												
												<li>
													<div class="list-timeline-time">
														Apply gateway transaction charges
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															Yes
														</p>
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Gateway transaction charges amount 
													</div>
	
													<div class="list-timeline-content">
														<p class="font-w600">
															Amount 
														</p>
	
													</div>
												</li>
												<li>
													<div class="list-timeline-time">
														Third party charges
													</div>
													<div class="list-timeline-content">
														<p class="font-w600">
															Inclusive
														</p>
													</div>
												</li>
											</ul>
										</div>
									</fieldset>
									
								</div>
								
									
								
								<div class="large-12 columns mt15 pr0">
									<div class="right">
										<a href="systemsettings_edit.php" class="button"><i	class="fa fa-edit"></i>&nbsp;Edit</a>

									</div>
								</div>
								</form>
							</div>

						</div>

					</div>




			<script type="text/javascript">
				$(document).ready(function() {
					$(".customer").addClass("active");

					showDabbawala = function(type) {

						$("#dabbawala_code_text").hide();
						$("#dabbawala_code_image").hide();

						$("#dabbawala_code_" + type).show();

					}

					$('#datepicker').datepicker();
					$("#datepicker").datepicker({
						dateFormat : "yy/mm/dd"
					}).datepicker("setDate", new Date());

					$('#sameadd').change(function() {
						//alert("dddd");
						if ($(this).is(':checked')) {
							$('#lunch_add').val($('#customer_Address').val());
							$('#dinner_add').val($('#customer_Address').val());

						} else {
							$('#lunch_add').val('');
							$('#dinner_add').val('');
						}
					});

					$("input:radio[name='dabbawala_code_type']").on('click', function() {

						showDabbawala($(this).val());

					});

					showDabbawala($("input:radio[name='dabbawala_code_type']:checked").val());

				});

			</script>
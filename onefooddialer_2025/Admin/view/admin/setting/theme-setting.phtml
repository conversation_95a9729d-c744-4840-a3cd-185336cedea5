<style>
	.radiocls label.right {
		display: inline-block;
	}
</style>				
<?php /*
						<div id="content">
							<div class="large-10 small-12 medium-12 columns">
												<?php
									if ($this->FlashMessenger()->hasSuccessMessages()){
										foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
								?>
									<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
									
									<div  data-alert="" class="alert-box success round">
						 			 <?php echo $msg; ?>
						  				<a href="#" class="close">&times;</a>
									</div>
									
								<?php
										}
									}
									elseif ($this->FlashMessenger()->hasInfoMessages()){
										foreach ($this->FlashMessenger()->getInfoMessages() as $msg){
								?>
									<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
									
									<div  data-alert="" class="alert-box info round">
						 			 <?php echo $msg; ?>
						  				<a href="#" class="close">&times;</a>
									</div>
									
								<?php
										}
									}elseif($this->FlashMessenger()->hasErrorMessages()){
										foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
									<div data-alert class="alert-box alert round">
										   <?php echo $msg; ?>
										  <a href="#" class="close">&times;</a>
									</div>
								<?php 	}
									}
								?>	
								 <?php //echo $this->form()->openTag($form);?>
									
						<?php 
 
 * 
 */?>
				<div class="container-fluid clearfix">
					<!-- END PAGE HEADER-->
					<div id="dashboard">
						<div class="row">

							<p class="theme-qun">Select Your Theme: </p>
							<div class="small-6 large-3 columns ft-left">
								<div class="theme-img">
									<span class="theme-checker"><input type="checkbox" class="location inline inline_check_box" name="themecheck" checked=""></span>
									<img src="../images/theme1.png">
								</div>
							</div>
							<div class="small-6 large-3 columns ft-left">
								<div class="theme-img">
									<span class="theme-checker"><input type="checkbox" class="location inline inline_check_box" name="themecheck"></span>
									<img src="../images/theme2.png">
								</div>
							</div>
						</div>
                            <div class="row">

							<p class="theme-qun">Select Theme Skin: </p>
							<div class="small-12 large-12 columns ">
								
								<div class="color-palette">
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color bluejeans-light_bg"></div> 
                                        </label>
									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color aqua-light_bg"></div> </label>
									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color mint-light_bg"></div> </label>
									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color grass-light_bg"></div> </label>
									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color sunflower-light_bg"></div> </label>
									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio" checked="">
											<div class="control__indicator_input_color bittersweet-light_bg"></div> </label>
									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color grapefruit-light_bg"></div> </label>
									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color lavender-light_bg"></div> </label>
									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color pink-light_bg"></div> </label>
									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color darkgray-light_bg"></div> </label>
									</div>
								</div>
							</div>
						</div>

    
                        <div class="row">
							<p class="theme-qun">Select Header Color: </p>

							<div class="small-12 large-12 columns ">
								
								<div class="color-palette">
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">

											<div class="control__indicator_input_color bluejeans-light_bg"></div> 
                                        </label>

											<div class="control__indicator_input_color bluejeans-light_bg"></div> </label>

									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color aqua-light_bg"></div> </label>
									</div>

									
								</div>
							</div>
						</div>
                        <div class="row">
							<p class="theme-qun">Select Your Footer Color: </p>

						</div>
							</div>
						</div>    
                        <div class="row">
							<p class="theme-qun">Select Footer Color: </p>

							<div class="small-12 large-12 columns ">
								
								<div class="color-palette">
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">

											<div class="control__indicator_input_color bluejeans-light_bg"></div> 

											<div class="control__indicator_input_color bluejeans-light_bg"></div> </label>

									</div>
									<div class="disinbl">
										<label class=" control-color">
											<input name="color" type="radio" class="color-palette-radio">
											<div class="control__indicator_input_color aqua-light_bg"></div> </label>
									</div>

								</div>
							</div>
						</div>      
                        <div class="row">
							<p class="theme-qun">Select Background Image: </p>
							<div class="small-6 large-3 columns ft-left">
								<div class="theme-img">
									<span class="theme-checker"><div class="checker"><span class="checked"><input type="checkbox" class="location inline inline_check_box" name="themecheck" checked=""></span></div></span>
									<img src="../images/theme1.png">
								</div>
							</div>
							<div class="small-6 large-3 columns ft-left">
								<div class="theme-img">
									<span class="theme-checker"><div class="checker"><span><input type="checkbox" class="location inline inline_check_box" name="themecheck"></span></div></span>
									<img src="../images/theme2.png">
								</div>
							</div>
						</div>     

						<br>
						<div class="row">
							<div class="large-12 columns ">
								<div class="theme-btns">
									<a href="#" target="_blank" class="theme-pre-button">Preview</a>
									<a href id="submitbutton" class="theme-button">Apply Changes</a>
								</div>
							</div>
						</div>

			</div>
			<!-- END PAGE CONTAINER-->
		</div>
									<?php /*
									<?php 
					                 	echo $this->formElement($form->get('backurl'));
									?>
									 
								<?php 
									echo $this->formElement($form->get('csrf'));
									echo $this->form()->closeTag($form);
								?>
                                    </div>


						</div>
                                     * 
                                     */?>
				

<?php
$form = $this->form;
$form->setAttribute ( 'action', $this->url ( 'product_category', array (
		'action' => 'edit',
		'id' => $this->id 
) ) );
$form->setAttribute ( 'class', 'stdform' );
$form->prepare ();
?>
<style>
	  
	.sortList_container{
		overflow-y:auto;
		overflow-x:hidden;
		max-height:400px;
	}
	
</style>

<div id="content">
<div class="large-6 small-12 medium-8 columns">
	<form>
	<?php 
	if(isset($productsFromCategory) && count($productsFromCategory)>0){?>
		<div class="row">
			<div class="large-9 small-12 medium-9 large-offset-3 medium-offset-3 columns">
                            <button id="updateOrder" class="button tiny left dark-greenBg" type="button">
                                    Update Sequence <i class="fa fa-save"></i>
                            </button>
                            <button id="resetOrder" class="button tiny left dark-greenBg" style="float:right !important;" type="btn">
                                    Reset Sequence <i class="fa fa-save"></i>
                            </button>
			</div>
		</div>
		<div class="row sortList_container">
			<div
                            class="large-9 small-12 medium-9 large-offset-3 medium-offset-3 columns">
                            <ul id="sortable" class="ui-sortable">
                                <?php
                                    $loop = 1;
                                    foreach ( $productsFromCategory as $key => $val ) {
                                ?>
                            <li class="ui-state-default"
                            id="order<?php echo $val['pk_product_code'];?>"
                            pk_product_code="<?php echo $loop.'_'.$val['pk_product_code'];?>"><span
                            class="ui-icon ui-icon-arrowthick-2-n-s"></span><?php echo $val['name'];?></li>
	             <?php
                                    $loop ++;
                            }
                            ?>
                            </ul>
			</div>
		</div>
	<?php }else{?>
		<div  data-alert="" class="alert-box info round">
                    There are no products in this category
                        <a href="#" class="close">&times;</a>
                </div>
	<?php }?>
	</form>
</div>
</div>

<script type="text/javascript">
$(document).ready(function(){
	 $( "#sortable" ).sortable();
	 var cache = $("#sortable").html();
	 $("#updateOrder").on('click',function(){
            console.log($("#sortable").sortable("toArray"));
            $.ajax({
                url:"/product-category/update-product-sequence",
                type: "POST",
                data: {arr:$("#sortable").sortable("toArray")},
                success:function(response)
                {
                       alert("Sequence menu has been updated.");
                }
            });
        });
	$("#resetOrder").on('click',function(){
            $("#sortable").html(cache).sortable("refresh");
	});
});
</script>
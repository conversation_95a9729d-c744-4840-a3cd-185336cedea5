
      
      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Group</h4>  
            <ul class="toolOption">
            	<li>
            	 <?php
                if($acl->isAllowed($loggedUser->rolename,'custgroup','add')){  ?>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('custgroup', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Group</button>
                    </div>
                 <?php } ?>
                </li>
             
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                            <th>Group Name</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    <tbody>
                    
                     <?php foreach ($paginator as $group) {  ?>
                        <tr>
                            <td><?php echo $this->escapeHtml($group['group_name']);?></td>
                            <td><?php echo $this->escapeHtml($group['location']);?></td>
                            <td ><span class="<?php echo $this->escapeHtml($group['group_status']) == "1"? 'active':'inactive';?>"><?php echo $this->escapeHtml($group['group_status']) == "1"? 'Active':'Inactive';?></span></td>
                            <td>
                             <?php
                			if($acl->isAllowed($loggedUser->rolename,'custgroup','edit')){  ?>
                           	<a  href="<?php echo $this->url('custgroup', array('action'=>'edit', 'id' => $group['group_code']));?>">
                            <button class="smBtn blueBg has-tip tip-top"  data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
                            <?php $textadd = ($group['group_status'])== "1"? 'Deactive' :'Activate'; ?>
                            </a>
                            <?php } ?>
                            <?php 
                			if($acl->isAllowed($loggedUser->rolename,'custgroup','delete')){  ?>
                            <a href="<?php echo $this->url('custgroup',array('action'=>'delete', 'id' => $group['group_code']));?>"  onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this group ?')">
                            <button class="smBtn redBg has-tip tip-top" data-tooltip  title="Delete"><i class="fa fa-trash-o"></i></button>
                            </a>
                            <?php } ?>
                            </td>
                            
                        </tr>
               		<?php } ?> 
                       
                        
                    </tbody>
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER-->
    
    
      
<script type="text/javascript">
$(document).ready(function() {

	myPageTable.init();

});
</script> 

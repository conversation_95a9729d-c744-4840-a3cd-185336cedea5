<div id="content">
	<div class="large-12 columns">
		<div class="portlet box yellow">
			<div class="portlet-title">
				<h4 class="white"><i class="fa fa-table"></i>  <?php echo $setname;?> Templates</h4>
				<ul class="toolOption">
					<li>
						<div class="addRecord">
						
							<button class="btn" onClick="location.href='<?php echo $this->url('smstemplate', array('action'=>'smslog'));?>'"><i class="fa fa-eye"></i> &nbsp; SMS Log</button>
							
						</div>
					</li>
					
					<li>
						<div class="addRecord">
							<button class="btn" onClick="location.href='<?php echo $this->url('smstemplate');?>'">
								<i class="fa fa-eye"></i>&nbsp;View SMS Template
 							</button> 
 						</div>
					
					</li>
<!-- 					<li> -->
<!-- 						<div class="addRecord"> -->
<!-- 							<button class="btn" onClick="location.href='<?php //echo $this->url('smstemplate', array('action'=>'addsmsset'));?>'"> -->
<!-- 								<i class="fa fa-plus"></i> &nbsp;Add SMS Template Set -->
<!-- 							</button> -->
<!-- 						</div> -->
<!-- 					</li> -->
				</ul>
			</div>
			<div class="portlet-body">
				<table id="customer" class="display displayTable defaulttemplate">
					<thead>
						<tr>
							<th width="5%">Sr. No</th>
							<th width="15%">Purpose</th>
							<th width="45%">Message</th>
							<th width="15%">Status</th>
							<th width="10%">Sending</th>
							<th width="10%">Action</th>
						</tr>
					</thead>
					<tbody>
						<?php $cnt = 1; foreach ($smstemplate as $key=>$val){?>
						<tr>
							<td class=""><?php echo $cnt;?></td>
							<td><?php echo str_replace('_', " ", $val['template_key']); ?></td>
							<td><?php echo $val['sms_content'];?></td>
							<td>
								<?php if($val['is_approved'] == 'yes'){$cls="approved"; $value = "Approved";}else{$cls="unapproved"; $value = "Unapproved";}?>
									<span class="<?php echo $cls; ?>"><?php echo $value;?></span>
							</td>
							<td>
								<?php 
								$templateid = $val['sms_template_id'];
								$fkid = $val['fk_set_id'];
								
								if($val['is_active'] == 'yes'){
										$cls = "yes"; $spncls = "fa-check"; $val="1";  }
									else{
										$cls = "no"; $spncls = "fa-times"; $val="0";  }?>
										
								<span id="<?php echo $templateid;?>" data-fkid="<?php echo $fkid;?>" value="<?php echo $val;?>"  class="cls <?php echo $cls;?>"><i class="fa <?php echo $spncls;?>" style="display: block;  cursor: pointer"></i></span>
							</td>
							<td><button class="smBtn blueBg has-tip tip-top"  onClick="location.href='<?php echo $this->url('smstemplate', array('action'=>'editsetdetails','id'=>$templateid,'setid'=>$id));?>'" data-tooltip title="Edit" data-text-swap="Wait.."><i class="fa fa-edit"></i></button></td>
						</tr>
						<?php $cnt++; } ?>	
					</tbody>
				</table>
			</div>
		</div>
		<div class="clearBoth20"></div>
	</div>
</div>
					

<script>
	$(document).ready(function() {
		myPageTable.init();
		$(document).on("click",".cls",function(){
			
			var id = $(this).attr('id');
			var val = $(this).attr('value');
			var fkid = $(this).data("fkid");

			if(confirm("Do you really want to change the status?")){
				$.ajax({
					url:"/smstemplate/updatesetstatus",
					type: "POST",
					data: {id:id,val:val,fkid:fkid},
					success:function(result)
					{
						//console.log(result);
						if(result == 1)
						{
							window.location.reload();
						}
					}
				});
				
			}
		});
});

</script>

		

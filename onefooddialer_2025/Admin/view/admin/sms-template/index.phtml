<div id="content">
    <div class="large-12 columns">
         <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4 class=""><i class="fa fa-table"></i>SMS Sets List</h4>  
            <ul class="toolOption">
            	<li>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('smstemplate', array('action'=>'smslog'));?>'"><i class="fa fa-eye"></i> &nbsp; SMS Log</button>
                    </div>
                </li>
            	<li>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('smstemplate', array('action'=>'addsmsset'));?>'"><i class="fa fa-plus"></i> &nbsp;Add SMS Template Set</button>
                    </div>
                </li>
                <!-- <li>
                    <div class="print">
                        <button class="btn dropdown" data-dropdown="dropPrint"><i class="fa fa-print"></i>&nbsp;Print/Export</button>
                        <ul id="dropPrint" data-dropdown-content class="f-dropdown exportPrint">
                        	<li data-tooltip class="has-tip tip-top" title="Print"><a href="#"><i class="fa fa-print"></i></a></li>
                          	<li data-tooltip class="has-tip tip-top" title="Export PDF"><a href="#"><i class="fa fa-file-pdf-o"></i></a></li>
                          	<li data-tooltip class="has-tip tip-top" title="Export EXCEL"><a href="#"><i class="fa fa-file-excel-o"></i></a></li>
                        </ul>
                    </div>
                </li> -->
                
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable defaulttemplate">
                    <thead>
                        <tr>
                            <th>Set Name</th>
                            <th>Description</th>
                            <th>Character Limit</th>
                            <th>Make it Default</th>
                             <th>Edit</th>
                       
                        </tr>
                    </thead>
 						<tbody>
 						
                      <?php //echo'<pre>';print_r($paginator) ;
                      foreach ($paginator as $emailset) {  ?>
                        <tr>
                            <td><?php $id=$emailset['pk_set_id'];?><a onClick="location.href='<?php echo $this->url('smstemplate', array('action'=>'setdetails','id'=>$id));?>'">
									<?php echo $this->escapeHtml($emailset['name']);?><input type="hidden" name="setid" id="setid" value="<?php echo $this->escapeHtml($emailset['pk_set_id']);?>"></a></td>
                            <td><?php echo $this->escapeHtml($emailset['description']);?></td>
                            <td><?php echo $this->escapeHtml($emailset['character_limit']);?></td>
                            <td><input class="isdefault" id="<?php echo $this->escapeHtml($emailset['pk_set_id']);?>" type="radio" name="default" <?php if($emailset['is_default'] == 1){echo 'checked';} ?> ></td>
                            <td>
                             <?php   if($acl->isAllowed($loggedUser->rolename,'smstemplate','edit')) { ?> 
                            <a href="<?php echo $this->url('smstemplate', array('action'=>'edit', 'id' => $emailset['pk_set_id']));?>" class="btn btn5 btn_pencil5">
				         			<button class="smBtn blueBg has-tip tip-top"   data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
				       		 </a>
				       		<?php }?> 
				       		 
				       		 </td>
                        </tr>
                       <?php } ?>
                      
                    </tbody>
 						
 				</table>            
               
          	</div>
          	
        </div>
        <div class="clearBoth20"></div>
      </div>
      </div>
  </div>  
<script type="text/javascript">

$(document).ready(function(e) {

	
var previd = $('input[name=default]:checked').attr('id');

	// on click
	
	/* $('body').delegate('.lcs_check', 'lcs-statuschange', function() {
		var status = ($(this).is(':checked')) ? 'checked' : 'unchecked';
	});*/
	
	
	$(document).on('click','.isdefault',function(){

			var id = $(this).parent().find('input').attr("id");

			if(confirm("Do you really want to make this set default ?")){
				
				$.ajax({
					 url:"/smstemplate/updatedefaultset",
					 type: "POST",
					 data: {id:id},
					 success:function(result)
					 {
						//console.log(Obj.attr('class'));
						console.log(result);
						/*  if(result != 1)
						 {
							 window.location.reload();
								
						 } */
						
					 }
					 
				 });
				previd = id;
			}
			else
			{
			    $('input:radio[id='+id+']').parent().removeClass('checked');
				$('input:radio[id='+previd+']').parent().addClass('checked');
				// window.location.reload();
			}
		});
		

});
</script>


<?php
$form = $this->form;
$form->setAttribute('action', $this->url('user_crud', array('action' => 'editProfile', 'id'=>$this->id)));
$form->setAttribute('class','stdform');
$form->prepare();
?>
      
      <!-- END PAGE HEADER-->
      
      <div id="content">
        <div class="large-6 columns">
         <?php echo $this->form()->openTag($form);?>
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('first_name')); ?></label>
              </div>
              <div class="large-8 columns">
               <?php 
               	     	echo $this->formHidden($form->get('pk_user_code'));
						echo $this->formElement($form->get('first_name'));
						echo $this->formElementErrors()
							->setMessageOpenFormat('<small class="error">')
							->setMessageCloseString('</small>')
							->render($form->get('first_name'));
				 ?>
              </div>
            </div>
             <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('last_name')); ?></label>
              </div>
              <div class="large-8 columns">
                <?php 
                	echo $this->formElement($form->get('last_name'));
					echo $this->formElementErrors($form->get('last_name'));
				 ?>
              </div>
            </div>
             <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('phone')); ?></label>
              </div>
              <div class="large-8 columns">
               <?php
               	 	echo $this->formElement($form->get('phone'));
					echo $this->formElementErrors($form->get('phone')); 
				?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('email_id')); ?></label>
              </div>
              <div class="large-8 columns">
               <?php 
               		echo $this->formElement($form->get('email_id'));
					echo $this->formElementErrors($form->get('email_id')); 
				?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('old_password'));?></label>
              </div>
              <div class="large-8 columns">
               <?php 
               		echo $this->formElement($form->get('old_password'));
					echo $this->formElementErrors($form->get('old_password')); 
					if(!$this->valid_flag)
					{
						echo $this->errMsg;
					}
				?>
            </div>
            </div>
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('new_password'));?></label>
              </div>
              <div class="large-8 columns">
                <?php 
                	echo $this->formElement($form->get('new_password'));
					echo $this->formElementErrors($form->get('new_password')); 
				?>
              </div>
            </div>
      
       <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('confirm_password'));?></label>
              </div>
              <div class="large-8 columns">
                <?php 
                	echo $this->formElement($form->get('confirm_password'));
					echo $this->formElementErrors($form->get('confirm_password')); 
					echo $this->formElement($form->get('csrf'));
				?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 columns">&nbsp;</div>
              <div class="large-8 columns">
                <button	type="submit" id = 'submitbutton' class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id ='cancelbutton' class="button	left tiny left5	redBg" >Cancel &nbsp;<i class="fa	fa-ban"></i></button>
              </div>
            </div>
          		<?php 
                 	echo $this->formElement($form->get('backurl'));
				 ?>
				 
              	<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>       
        </div>
      </div>
  
    
    <!-- END PAGE CONTAINER--> 


<script>
$(document).ready(function() {


/*var config = {
  '.chosen-select'           : {},
  '.chosen-select-deselect'  : {allow_single_deselect:true},
  '.chosen-select-no-single' : {disable_search_threshold:10},
  '.chosen-select-no-results': {no_results_text:'Oops, nothing found!'},
  '.chosen-select-width'     : {width:"95%"}
}
for (var selector in config) {
  $(selector).chosen(config[selector]);
}
*/
});

</script>
  

</body>
</html>
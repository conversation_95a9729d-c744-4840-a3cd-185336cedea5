<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addUser" class="common-orange-btn-on-hover"> Add User </a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="editUser" class="common-orange-btn-on-hover"> Edit User</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="deactUser" class="common-orange-btn-on-hover">Deactivate User</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
<script type = "text/javascript">
var user_id = '';
var city_id = '';
</script>
<!-- popup-->
			<div id="myModal" class="reveal-modal custPopup" data-reveal>
				<div  class="table-parent"  style="height:auto;overflow:visible;">
					<!-- style="height:215px;overflow:hidden;" -->

					<form>
						<fieldset class="pad0 mb0">
							<legend>
								Location
							</legend>
							<div class="row">
										<div class="large-4 small-4 medium-4 columns">
											<label class="inline right">City<span class="red">*</span></label>
										</div>
										<div class="large-8 small-8 medium-8 columns">
											<select	 class="city_location">
												<?php foreach($cities as $key=>$val){ ?>
														<option value = "<?php echo $val['pk_city_id']?>"><?php echo $val['city'];?></option>
												<?php }?>
											</select>
										</div>
									</div>
							<div class="row">
								<div class="large-4 small-4 medium-4 columns">
									<label class="inline right">Delivery Location</label>
								</div>
								<div class="large-8 small-8 medium-8 columns">
									<select id="foobar" data-placeholder="Choose Area..." class="chosen-select" multiple >

									</select>
								</div>
							</div>

						</fieldset>
						<!-- <div class="clearfix mb15"></div> -->
						<div class="large-12 columns pl0 pr0 pad0 mb0">
							<div class="row">
								<div class="large-4 small-4 medium-4 columns">
									&nbsp;
								</div>
								<div class="large-8 small-8 medium-8 columns">
									<div class="right">
										<button	type="button" class="button	left tiny left5	dark-greenBg btn_theme saveBtn">
											<i	class="fa fa-save"></i>&nbsp;Save
										</button>
										<button	type="button" class="button	left tiny left5	redBg cancelPopup">
											<i	class="fa fa-ban"></i>&nbsp;Cancel <!-- &nbsp;<i class="fa	fa-ban"></i> -->
										</button>
									</div>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
			<!-- popup end-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        
            <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i> Users</h4>  
            <ul class="toolOption">
            	<li>
            	 <?php
               		 if($acl->isAllowed($loggedUser->rolename,'user_crud','add')){  ?>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('user_crud', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add User</button>
                 </div>
                 <?php } ?>
                </li>
                
                
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                        	<th>Sr. No.</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <!--<th>Contact Number</th>-->
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                   
                </table>          
          	</div>
        </div>
        <div class="clearBoth20"></div>
       </div>
    </div>
    <!-- END PAGE CONTAINER--> 
  
<script type="text/javascript">
$(document).ready(function() {

	$('#customer').dataTable( {
        "processing": true,
        "serverSide": true,
        "ajax": "/users/ajx-user",
//        "ajax": "/users/get-prosim-users",
        "order": [[ 0, "desc" ]],
        "aoColumnDefs": [
        	                {
        	                 bSortable: false,
        	                aTargets: [ -1 ,-2]
        	                }
        	              ],
    });

	var multiselect = function() {
		var config = {
			'.chosen-select' : {},
			'.chosen-select-deselect' : {
				allow_single_deselect : true
			},
			'.chosen-select-no-single' : {
				disable_search_threshold : 10
			},
			'.chosen-select-no-results' : {
				no_results_text : 'Oops, nothing found!'
			},
			'.chosen-select-width' : {
				width : "95%"
			}
		}
		for (var selector in config) {
			$(selector).chosen(config[selector]);
		}
	}

		$(document).on('opened.fndtn.reveal', '#myModal', function() {
			multiselect();
			$(".chosen-select").val('').trigger("chosen:updated");
			$.ajax({
		        url: "/users/getSelectedDeliveryLocation",
		        method: 'POST',
		        data:{user_id:user_id,city_selected_id:city_id},
		        dataType:"json",
		        async: false,
		        success : function(data){
			       console.log(data);
			       $(".chosen-select").val(data);
				   $(".chosen-select").trigger("chosen:updated");
			    },
			});
			
		});
		$(document).on('click', '#myModalLocation', function() {
			user_id = $(this).attr('data-id');
			$('#myModal').foundation('reveal', 'open');
		});
		

		$(document).on('change','.city_location',function(){
			city_id = $(this).val();
			$.ajax({
		        url: "/users/getDeliveryLocation",
		        method: 'POST',
		        data:{city_id:city_id},
		        dataType:"json",
		        async: false,
		        success : function(data){
			       var strHtml = '';
			       $.each(data,function(i,e){
						strHtml +='<option value="'+e.pk_location_code+'">'+e.location+'</option>';
				   });
				   $("#foobar").html(strHtml);
				   $.ajax({
				        url: "/users/getSelectedDeliveryLocation",
				        method: 'POST',
				        data:{user_id:user_id,city_selected_id:city_id},
				        dataType:"json",
				        async: false,
				        success : function(data){
					       console.log(data);
					       $(".chosen-select").val(data);
						   $(".chosen-select").trigger("chosen:updated");
					    },
					});
				   $(".chosen-select").trigger("chosen:updated");
			    },
			});
		});
		$(document).on('click','.saveBtn',function(){
			/*if($(".chosen-select").val()==null){
				alert("Please select delivery location before saving");
				return false;
			}*/
			$.ajax({
		        url: "/users/saveDeliveryLocation",
		        method: 'POST',
		        data:{delivery_locations:$(".chosen-select").val(),user_id:user_id,fk_city_code:city_id},
		        dataType:"json",
		        async: false,
		        success : function(data){
		        	$('#myModal').foundation('reveal', 'close');
			    },
			});
		});
		$('.city_location').trigger('change');
		$(document).on('click','.cancelPopup',function(){
			$('#myModal').foundation('reveal', 'close');
		});
});
</script>

<script type="text/javascript">
	$(document).on('click',"#addUser",function(e){
	    e.preventDefault();
	    $('.portlet-title').find('.addRecord').attr("data-step", "1");
	    $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add user");
	    $('.portlet-title').find('.addRecord').attr("data-position", "left");
	     introJs().start();
	    $('.portlet-title').find('.addRecord').removeAttr("data-step");
	    $('.portlet-title').find('.addRecord').removeAttr("data-intro");

	});

	$(document).on('click',"#editUser",function(e){
	    e.preventDefault();
	    var userid = $('.displayTable').find('tbody tr:first td:first').text();
	    $('.displayTable').find('tbody tr:first td:eq(6) button:first').attr("data-step", "1");
	    $('.displayTable').find('tbody tr:first td:eq(6) button:first').attr("data-intro", "Edit user details");
	    introJs().start();
	    $('.displayTable').find('tbody tr:first td:eq(6) button:first').removeAttr("data-step");
	    $('.displayTable').find('tbody tr:first td:eq(6) button:first').removeAttr("data-intro");
	});

		$(document).on('click',"#deactUser",function(e){
	    e.preventDefault();
	    //var userid = $('.displayTable').find('tbody tr:first td:first').text();
	    $('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').attr("data-step", "1");
	    $('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').attr("data-intro", "Deactivate user details");
	   	$('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').attr("data-position", "left");
	    introJs().start();
	    $('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').removeAttr("data-step");
	    $('.displayTable').find('tbody tr:first td:eq(6) button:eq(1)').removeAttr("data-intro");
	});
</script>  
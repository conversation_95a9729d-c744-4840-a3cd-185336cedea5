
<?php
$form = $this->form;
$form->setAttribute('action', $this->url('user_crud', array('action' => 'edit', 'id' => $this->id)));
$form->setAttribute('class', 'stdform');
$form->prepare();
?>
<style>
    ul, ol, dl {
        font-size: inherit;
    }
</style>

<!-- END PAGE HEADER-->

<div id="content">
    <?php echo $this->form()->openTag($form); ?>
    <div class="large-6 columns">
        <fieldset>
            <legend>
                USER INFO
            </legend>
 <!--           
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <label class="inline right"><?php echo $this->formLabel($form->get('first_name')); ?></label>
                </div>
                <div class="large-8 small-8 medium-8 columns">
                    <?php
                    echo $this->formHidden($form->get('pk_user_code'));
                    echo $this->formElement($form->get('first_name'));
                    echo $this->formElementErrors()
                        ->setMessageOpenFormat('<small class="error">')
                        ->setMessageCloseString('</small>')
                        ->render($form->get('first_name'));
                    ?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <label class="inline right"><?php echo $this->formLabel($form->get('last_name')); ?></label>
                </div>
                <div class="large-8 small-8 medium-8 columns">
                    <?php
                    echo $this->formElement($form->get('last_name'));
                    echo $this->formElementErrors($form->get('last_name'));
                    ?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <label class="inline right"><?php echo $this->formLabel($form->get('phone')); ?></label>
                </div>
                <div class="large-8 small-8 medium-8 columns">
                    <?php
                    echo $this->formElement($form->get('phone'));
                    echo $this->formElementErrors($form->get('phone'));
                    ?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <label class="inline right"><?php echo $this->formLabel($form->get('email_id')); ?></label>
                </div>
                <div class="large-8 small-8 medium-8 columns">
                    <?php
                    echo $this->formElement($form->get('email_id'));
                    echo $this->formElementErrors($form->get('email_id'));
                    ?>
                </div>
            </div>
            
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <label class="inline right">Password</label>
                </div>
                <div class="large-8 small-8 medium-8 columns">
                    <?php
                    echo $this->formElement($form->get('password'));
                    echo $this->formElementErrors($form->get('password'));
                    ?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <label class="inline right"><?php echo $this->formLabel($form->get('password_verify')); ?></label>
                </div>
                <div class="large-8 small-8 medium-8 columns">
                    <?php
                    echo $this->formElement($form->get('password_verify'));
                    echo $this->formElementErrors($form->get('password_verify'));
                    ?>
                </div>
            </div>

            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right">
                    <?php echo $this->formLabel($form->get('gender')); ?>
                </label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php
                	echo $this->formElement($form->get('gender'));
					echo $this->formElementErrors($form->get('gender'));
				?>
              </div>
            </div>

-->
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <label class="inline right"><?php echo $this->formLabel($form->get('role_id')); ?></label>
                </div>
                <div class="large-8 small-8 medium-8 columns">
                    <?php
                    echo $this->formElement($form->get('role_id'));
                    echo $this->formElementErrors($form->get('role_id'));
                    echo $this->formElement($form->get('csrf'));
                    ?>
                    <?php // if ($user['role_type'] == 'system') { ?>
                        <input type="hidden" name="role_id" value="<?php  echo $form->get('role_id')->getValue();  ?>" />
                    <?php // } ?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <label class="inline right"><?php echo $this->formLabel($form->get('fk_kitchen_code')); ?></label>
                </div>
                <div class="large-8 small-8 medium-8 columns">
                    <?php
                    echo $this->formElement($form->get('fk_kitchen_code'));
                    echo $this->formElementErrors($form->get('fk_kitchen_code'));
                    ?>
                </div>
            </div>

            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
                </div>
                <div class="large-8 small-8 medium-8 columns">
                    <?php
                    echo $this->formElement($form->get('status'));
                    echo $this->formElementErrors($form->get('status'));
                    ?>
                </div>
            </div>

        </fieldset>
        <div class="large-12 columns pl0 pr0">
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
                <div class="large-8 small-8 medium-8 columns">
                    <button	type="submit" id = 'submitbutton' class="button	left tiny left5	dark-greenBg">Save &nbsp;<i class="fa fa-save"></i></button>
                    <button	type="submit" id ='cancelbutton' class="button	left tiny left5	redBg" >Cancel &nbsp;<i class="fa fa-ban"></i></button>
                </div>
            </div>
        </div>
        <?php
        echo $this->formElement($form->get('backurl'));
        ?>

        <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form); ?>       
    </div>
</div>
</div>

<!-- END PAGE CONTAINER--> 

<?php
//$value =  $form->get('delivery_location_code')->getValue();
?>

<script>

    function getAllLocations1(city) {
        $.ajax({
            url: "/menu/delivery",
            type: "POST",
            dataType: 'json',
            async: false,
            data: {city: city},
            success: function (data) {
                locations = data.data;
                var strHtml = '<option value=""></option>';
                $('ul.chzn-results').empty();
                var art = [];
                $.each(locations, function (key, value) {
                    console.log("loc_code == " + value.pk_location_code + " " + " value == " + value.location);
                    strHtml += "<option value='" + value.pk_location_code + "'>" + value.location + "</option>";
                    art[value.pk_location_code] = value.location;
                    //$('ul.chzn-results').append('<li class="active-result">' + value.location + '</li>');
                });
                $("#delivery_location_code").html(strHtml);
                $('#delivery_location_code').val(<?php echo $selected_locations; ?>);
                $("#delivery_location_code").chosen().change(function () {
                    //alert($(this).val());

                    var d_location = $(this).val();

                    console.log("del_loc " + d_location);
                    $('#default_location_code').empty();

                    $.each(d_location, function (index, value) {
                        if (value != '') {
                            $('#default_location_code').append("<option value='" + value + "'>" + art[value] + "</option>");
                        }
                    });
                    $('#default_location_code').val(<?php echo $selected_default_location_code; ?>);
                });

                $("#delivery_location_code").trigger("chosen:updated");
                $("#delivery_location_code").trigger('change');
            },
            error: function () {
                //yummy_tiffins.generateToken();
            }
        });
    }

    $(document).ready(function () {
        console.log(<?php //echo "<pre>";print_r($selected_kitchen); ?>);
        $(".chosen-select").val(<?php echo $selected_kitchen; ?>);
        $(".chosen-select").trigger("chosen:updated");
        var city = '';
        var locations = {};

        var type = $('#role_id').val();
        var role = $("#role_id option[value=" + type + "]").text();

        $('#city').on('change', function () {
            //alert("s"+$(this).val());
            var city = $(this).val();
            if (city != '') {
                locations = getAllLocations1(city);
            }
            
        });

        $('#city').trigger('change');


//////////////////////////////////////////////////////////////////////////////////////////////

        var config = {
            '.chosen-select': {},
            '.chosen-select-deselect': {
                allow_single_deselect: true
            },
            '.chosen-select-no-single': {
                disable_search_threshold: 10
            },
            '.chosen-select-no-results': {
                no_results_text: 'Oops, nothing found!'
            },
            '.chosen-select-width': {
                width: "95%"
            }
        }
        for (var selector in config) {
            $(selector).chosen(config[selector]);
        }

        $('#role_id').change(function () {
            
            $('input[type="hidden"][name="role_id"]').val(this.value);

         });
    });

</script>


</body>
</html>
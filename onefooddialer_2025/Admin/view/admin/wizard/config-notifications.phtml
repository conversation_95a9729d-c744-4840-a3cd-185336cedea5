<section class="wizard-info">
    <div class="row">
       <div class="col-sm-12">
             <ul class="progress-indicator">
                <li class="completed">
                   <span class="bubble"><span class="step-count">1</span></span> Your portal setup <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">2</span></span> Your payment details <br>
                </li>
                <li class="inprogress">
                   <span class="bubble"><span class="step-count">3</span></span> Basic configuration <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">4</span></span> Setting up your first meal <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">5</span></span> Location Setting <br>
                </li>
             </ul>
            </div>
    </div>
    <div class="step-2-1">
       <div class="row">
         <div class="col-sm-12">
             <h4 class="info-about-table">Here you can configure the notifications(sms) for your customers.</h4>
         </div>
       </div>
       <div class="mt-10 form-box">
        <form  id="form-container" name="form-container">

          <div class="row">
             <div class="col-sm-12">
             <div class="clearfix form-head">
                   <div class="pull-left">
                      <h4 class="txt-color title">Review SMS notifications</h4>
                   </div>
                   <div class="pull-right">
                      <p class="txt-color title">Step 3 of 3</p>
                   </div>
                </div>
                <div class="col-sm-6">
                   <div class="form-group">
                      <p class="qus"><i class="fa fa-check-circle circle-active"></i> Notify your customer on a new registration?</p>
                      <div class="div-flex">
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio" name="new_registration" value="yes" <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['new_registration'] == 'yes')? 'checked' : ''; ?> > <span class="check"></span> Yes
                            </label>
                         </div>
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                                <input type="radio" name="new_registration"  value="no" <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['new_registration'] == 'no')? 'checked' : ''; ?> > <span class="check"></span> No
                            </label>
                         </div>
                       </div>
                      <span id="new_registration_err text-error"></span>
                   </div>
                   <hr>
                   <div class="form-group">
                       <p class="qus"><i class="fa fa-check-circle circle-active"></i> Notify your customer on order booking?</p>
                       <div class="div-flex">
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio" name="order_booking"  value="yes"  <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['order_booking'] == 'yes')? 'checked' : ''; ?> > <span class="check"></span> Yes
                            </label>
                         </div>
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio" name="order_booking"  value="no"  <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['order_booking'] == 'no')? 'checked' : ''; ?> > <span class="check"></span> No
                            </label>
                         </div>
                       </div>
                       <span id="order_booking_err text-error"></span>
                   </div>
                </div>

                <div class="col-sm-6">
                   <div class="form-group">
                      <p class="qus"><i class="fa fa-check-circle circle-active"></i> Notify user on order cancellation?</p>
                      <div class="div-flex">
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio" name="order_cancellation" value="yes" <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['order_cancellation'] == 'yes')? 'checked' : ''; ?> > <span class="check"></span> Yes
                            </label>
                         </div>
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio"  name="order_cancellation" value="no" <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['order_cancellation'] !== 'yes')? 'checked' : ''; ?> > <span class="check"></span> No
                            </label>
                         </div>
                       </div>
                      <span id="order_cancellation_err text-error"></span>
                   </div>
                   <hr>
                   <div class="form-group">
                       <p class="qus"><i class="fa fa-check-circle circle-active"></i> Notify customer on successful payment?</p>
                       <div class="div-flex">
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio"  name="order_confirmation" value="yes" <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['order_confirmation'] == 'yes')? 'checked' : ''; ?> > <span class="check"></span> Yes
                            </label>
                         </div>
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio"  name="order_confirmation" value="no" <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['order_confirmation'] == 'no')? 'checked' : ''; ?> > <span class="check"></span> No
                            </label>
                         </div>
                       </div>
                       <span id="order_confirmation_err text-error"></span>
                   </div>
                   <hr>
                   <div class="form-group">
                       <p class="qus"><i class="fa fa-check-circle circle-active"></i> Notify user on renewal of their meal subscription?</p>
                       <div class="div-flex">
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio" name="pre_order_renewal" value="yes" <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['pre_order_renewal'] == 'yes')? 'checked' : ''; ?> > <span class="check"></span> Yes
                            </label>
                         </div>
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio" name="pre_order_renewal" value="no" <?php echo (array_key_exists('notifications', $_SESSION['wizard']) && $_SESSION['wizard']['notifications']['pre_order_renewal'] == 'no')? 'checked' : ''; ?> > <span class="check"></span> No
                            </label>
                         </div>
                       </div>
                       <span id="pre_order_renewal_err text-error"></span>
                   </div>
                </div>
             </div>
          </div> 
        </form>
       </div>
    </div>
 </section>
<section class="wizard-info">
    <div class="row">
       <div class="col-sm-12">
             <ul class="progress-indicator">
                <li class="completed">
                   <span class="bubble"><span class="step-count">1</span></span> Your portal setup <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">2</span></span> Your payment details <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">3</span></span> Basic configuration <br>
                </li>
                <li class="inprogress">
                   <span class="bubble"><span class="step-count">4</span></span> Setting up your first meal <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">5</span></span> Location Setting <br>
                </li>
             </ul>
        </div>
    </div>
    <div class="step-2-1">
       <div class="row">
         <div class="col-sm-12">
             <h4 class="info-about-table">Here, you may wish to choose a setup type. The typical setup will allow you to initiate a setup with a dummy product whereas the advanced setup will guide you through a quick demo of admin portal.</h4>
         </div>
       </div>
       <div class="mt-10 form-box">
          <div class="row">
             <div class="col-sm-12">
             <div class="clearfix form-head">
                   <div class="pull-left">
                      <h4 class="txt-color title">Catalogue Setup</h4>
                   </div>
                   <div class="pull-right">
                      <p class="txt-color title">Step 1 of 4</p>
                   </div>
                </div>
                <div class="col-sm-12">
                  <div class="input-control radio default-style fulllabel mb-10" data-role="input-control">
                      <label class="pull-left mr10">
                         <input type="radio" id="typical" name="bank" value="typical" checked=""> <span class="check"></span> Typical (recommended)
                         <br>
                         <span class="small-font ml-30">Setup your catalogue with one default product. You may choose to edit it later from admin portal.</span>
                      </label>
                   </div>
                </div>
                <div class="col-sm-12">
                   <div class="input-control radio default-style fulllabel mb-10" data-role="input-control">
                      <label class="pull-left mr10">
                         <input type="radio" id="advanced" name="bank" value="advance"> <span class="check"></span> Advanced
                         <br>
                         <span class="small-font ml-30">You will have to add your first product, your menu timings, food type, a subscription plan, etc.</span>
                      </label>
                   </div>
                </div>
             </div>
          </div> 
       </div>
    </div>
 </section>
<script>
$(document).ready(function() {
   
   $(document).on('click','.next-btn',function(){
       if(document.getElementById("typical").checked == true)
            window.location.href = '/wizard/location';
       else if(document.getElementById("advanced").checked == true)
            window.location.href = '/wizard/catalog-firstmeal';     
        
   });

});
</script>
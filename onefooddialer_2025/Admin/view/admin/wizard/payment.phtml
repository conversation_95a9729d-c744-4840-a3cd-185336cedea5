<section class="wizard-info">
    <div class="row">
       <div class="col-sm-12">
             <ul class="progress-indicator">
                <li class="completed">
                   <span class="bubble"><span class="step-count">1</span></span> Your portal setup <br>
                </li>
                <li class="inprogress">
                   <span class="bubble"><span class="step-count">2</span></span> Your payment details <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">3</span></span> Basic configuration <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">4</span></span> Setting up your first meal <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">5</span></span> Location Setting <br>
                </li>
             </ul>
        </div>
    </div>
    <div class="step-2-1">
       <div class="row">
         <div class="col-sm-12">
             <h4 class="info-about-table">Set your payment mode and provide the necessary details. <br>
             <span class="x-font-size">(You can always choose wallet and cash-on-delivery option to get started and add other options from your admin portal)</span></h4>
         </div>
       </div>
       <div class="mt-10 form-box">
        <form  id="form-container" name="form-container">
          <div class="row">
             <div class="col-sm-12">
             <div class="clearfix form-head">
                   <div class="pull-left">
                      <h4 class="txt-color title">Payment Methods</h4>
                   </div>
                   <div class="pull-right">
                      <p class="txt-color title">Step 2 of 2</p>
                   </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                      <p class="qus"><i class="fa fa-check-circle circle-active"></i> Which payment methods do you prefer?</p>

                     <?php foreach($payment_mode as $payment){ ?>
                      <div class="input-control checkbox mb0">
                         <label>
                            <input type="checkbox" name="GLOBAL_CUSTOMER_PAYMENT_MODE" class="third" value="<?php echo $payment?>"  <?php echo (in_array($payment,$settings['GLOBAL_CUSTOMER_PAYMENT_MODE'])) ? 'checked' : '' ?> >
                            <span class="check"></span>  <?php echo ucfirst($payment);?></label>
                            <span class ="error text-error" id="GLOBAL_CUSTOMER_PAYMENT_MODE_err" ></span>
                      </div>
                      <?php } ?>

                        <div class="form-group online-field <?php echo (in_array('online',$settings['GLOBAL_CUSTOMER_PAYMENT_MODE'])) ? 'show' : 'hide' ?>" >
                            <fieldset>
                                <legend>
                                   Online Settings:
                                </legend>

                                <p class="qus"><i class="fa fa-check-circle circle-active"></i> Which payment gateways do you prefer?</p>
                                <?php foreach(array_keys($payment_gateway) as $gateway ){ ?>
                                <div class="input-control checkbox mb0">
                                    <label>
                                        <input type="checkbox" class="third" name ="ONLINE_PAYMENT_GATEWAY" value="<?php echo $gateway?>" <?php echo ( $is_online_enabled &&( strpos($settings['ONLINE_PAYMENT_GATEWAY'], $gateway ) !== false) ) ? 'checked' : ''; ?> >
                                        <span class="check"></span><?php echo ucfirst($gateway);?>
                                    </label>
                                </div>
                                <?php } ?>
                                <span class ="error text-error" id="ONLINE_PAYMENT_GATEWAY_err" ></span>
                                
                                <?php foreach($payment_gateway as $gateway => $params ){ ?>
                                    <div class="form-group <?php echo $gateway?>-field <?php echo ( $is_online_enabled &&( strpos($settings['ONLINE_PAYMENT_GATEWAY'], $gateway ) !== false) ) ? 'show' : 'hide' ?>">
                                        <fieldset>
                                            <legend>
                                               <?php echo ucfirst($gateway);?>:
                                            </legend>
                                             <?php foreach($params as $param){?>
                                            <div class="form-group float-label-control">
                                               <input type="text" class="form-control" name="<?php echo $param; ?>" value="<?php echo $settings[$param];?>">
                                               <label for=""><?php echo str_replace('_', ' ', str_replace('GATEWAY_' ,'', $param) ); ?></label>
                                               <span class ="error mt-5  text-error text-error" id="<?php echo $param; ?>_err"></span>
                                            </div>
                                            <?php } ?>
                                        </fieldset>
                                    </div>
                                <?php } ?>
                            </fieldset>
                        </div>

                   </div>
                   <hr>
                   <div class="form-group online-field <?php echo (in_array('online',$settings['GLOBAL_CUSTOMER_PAYMENT_MODE'])) ? 'show' : 'hide' ?>">
                       <p class="qus"><i class="fa fa-check-circle circle-active"></i> Do you apply any transactional charges?</p>
                       <div class="div-flex">
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio" id="" name="APPLY_GATEWAY_TRANSACTION_CHARGES" value="yes" <?php echo ($settings['APPLY_GATEWAY_TRANSACTION_CHARGES'] == 'yes') ? 'checked' : '';  ?> > <span class="check"></span> Yes
                               <span class ="error mt-5  text-error" id="APPLY_GATEWAY_TRANSACTION_CHARGES_err" ></span>
                            </label>
                         </div>
                         <div class="input-control radio default-style mb-10" data-role="input-control">
                            <label class="pull-left mr10">
                               <input type="radio" id="" name="APPLY_GATEWAY_TRANSACTION_CHARGES" value="no" <?php echo ($settings['APPLY_GATEWAY_TRANSACTION_CHARGES'] == 'no' || trim($settings['APPLY_GATEWAY_TRANSACTION_CHARGES']) == '') ? 'checked' : ''; ?>  > <span class="check"></span> No
                               <span class ="error mt-5  text-error" id="APPLY_GATEWAY_TRANSACTION_CHARGES_err" ></span>
                            </label>
                         </div>
                       </div>
                       <hr>
                   </div>
                   <div class="form-group transaction-field">
                      <p class="qus"><i class="fa fa-check-circle circle-active"></i> If yes, how much do you charge per transaction(in %)?</p>
                      <div class="form-group float-label-control">
                         <input type="text" class="form-control empty" name="GATEWAY_TRANSACTION_CHARGES_AMOUNT" value="<?php echo $settings['GATEWAY_TRANSACTION_CHARGES_AMOUNT']?>">
                         <label for="">Charge per transaction(in %) </label>
                         <span class ="error mt-5  text-error" id="GATEWAY_TRANSACTION_CHARGES_AMOUNT_err" ></span>
                      </div>
                   </div>
                </div>
             </div>
             <!-- <div class="col-sm-12 cancel-div">
                <a href="wizard_3.html" class="cancel-btn">Cancel &#187;</a>    
             </div> -->
          </div> 
        </form>    
       </div>
    </div>
 </section>
 <script >
$(document).ready(function() {
    
    var payment_mode = <?php echo json_encode($payment_mode) ?>;
    
    var payment_gateway = <?php echo json_encode($payment_gateway) ?>;
    var gateway_transaction = $("input[name=APPLY_GATEWAY_TRANSACTION_CHARGES]:checked").val();
    if (gateway_transaction == "no") {
      $(".transaction-field").hide();
    }
    else{
      $(".transaction-field").show();
    }


    $('input[name="GLOBAL_CUSTOMER_PAYMENT_MODE"]').click(function() {
        
        if(this.value == 'online'){
            if($(this).is(':checked')) {
                $("."+this.value+"-field").removeClass('hide').addClass('show');
            }else{
                $("."+this.value+"-field").removeClass('show').addClass('hide');
            }
        }
        
    });
            
    $('input[name="ONLINE_PAYMENT_GATEWAY"]').click(function() {
            
        if($(this).is(':checked')) {
            $("."+this.value+"-field").removeClass('hide').addClass('show');
        }else{
            $("."+this.value+"-field").removeClass('show').addClass('hide');
        }
    });
});

$('input[type="checkbox"]').click(function() {
            if ($(this).attr("value") == "online") {
                    $(".online-field").toggle();
            }
    });

    $('input[type="checkbox"]').click(function() {
            if ($(this).attr("value") == "payu") {
                    $(".payu-field").toggle();
            }
            if ($(this).attr("value") == "instamojo") {
                    $(".instamojo-field").toggle();
            }
            if ($(this).attr("value") == "paytm") {
                    $(".paytm-field").toggle();
            }
    });

    $('input[name="APPLY_GATEWAY_TRANSACTION_CHARGES"]').click(function() {
      if ($(this).attr("value") == "no") {
              $(".transaction-field").hide();
            }
            else{
              $(".transaction-field").show();
            }
    });

</script>
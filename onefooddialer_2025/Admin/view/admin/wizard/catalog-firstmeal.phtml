 <section class="wizard-info">
    <div class="row">
       <div class="col-sm-12">
             <ul class="progress-indicator">
                <li class="completed">
                   <span class="bubble"><span class="step-count">1</span></span> Your portal setup <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">2</span></span> Your payment details <br>
                </li>
                <li class="completed">
                   <span class="bubble"><span class="step-count">3</span></span> Basic configuration <br>
                </li>
                <li class="inprogress">
                   <span class="bubble"><span class="step-count">4</span></span> Setting up your first meal <br>
                </li>
                <li class="">
                   <span class="bubble"><span class="step-count">5</span></span> Location Setting <br>
                </li>
             </ul>
        </div>
    </div>
    <div class="step-2-1">
       <div class="row">
         <div class="col-sm-12">
             <h4 class="info-about-table">Step 2: Add your first meal to catalog <br>
             <span class="x-font-size">Note : More meal and food item you can add later through admin portal </span></h4>
         </div>
       </div>        
        <div class="mt-10 form-box">
            <form  id="form-container" name="form-container">
               <div class="row">
                  <div class="col-sm-12">
                  <div class="clearfix form-head">
                        <div class="pull-left">
                           <h4 class="txt-color title">Catalog Setup</h4>
                        </div>
                        <div class="pull-right">
                           <p class="txt-color title">Step 2 of 4</p>
                        </div>
                     </div>
                     <div class="col-sm-6">
                        <div class="form-group">
                           <p class="qus"><i class="fa fa-check-circle circle-active"></i> What would you like to name your meal ?</p>
                           <div class="form-group float-label-control">
                               <input type="text" class="form-control empty" name="meal_name" value="<?php echo $meal->name;?>">
                              <label for="">Meal</label>
                              <span class='error' id="meal_name_err" style="color:#FC6E51"></span>
                           </div>
                        </div>
                        <div class="form-group">
                           <p class="qus"><i class="fa fa-check-circle circle-active"></i> Add a price to your meal</p>
                           <div class="form-group float-label-control">
                              <input type="text" class="form-control empty" name="meal_price" value="<?php echo $meal->unit_price;?>">
                              <label for="">Price </label>
                               <span class='error' id="meal_price_err" style="color:#FC6E51"></span>
                           </div>
                        </div>
                        <div class="form-group">
                            <p class="qus"><i class="fa fa-check-circle circle-active"></i> Is this food veg or non-veg?</p>
                            <div class="div-flex">
                              <div class="input-control radio default-style mb-10" data-role="input-control">
                                 <label class="pull-left mr10">
                                     <input type="radio" id="" name="veg" value="veg" <?php echo ( in_array('veg',$settings['FOOD_TYPE']) ) ? 'checked' : '' ?> > <span class="check"></span> Veg
                                 </label>
                              </div>
                              <div class="input-control radio default-style mb-10" data-role="input-control">
                                 <label class="pull-left mr10">
                                    <input type="radio" id="" name="veg" value="nonveg" <?php echo ( in_array('nonveg',$settings['FOOD_TYPE']) ) ? 'checked' : '' ?>  > <span class="check"></span> Non-Veg
                                 </label>
                              </div>
                                <span class='error' id="veg_err" style="color:#FC6E51"></span>
                            </div>
                        </div><hr>
                        <div class="form-group">
                           <p class="qus"><i class="fa fa-check-circle circle-active"></i> Choose your subscription plan</p>
                           <div class="form-group float-label-control" id="select-field">
                             <label for="">Select subscription plan</label>                                       
                             <select class="form-control" id="meal_plans" name="plans">
                                 <?php foreach($plans as $key =>$plan){ 
                                     
                                     ?>
                                 <option value="<?php echo $plan->pk_plan_code; ?>" <?php echo ($plan->pk_plan_code == $_SESSION['wizard']['plans'])  ? 'selected' : ''; ?> ><?php echo $plan->plan_name?></option>                                     
                                 <?php } ?>

                             </select>
                             <span class='error' id="plans_err" style="color:#FC6E51"></span>
                           </div>
                        </div> 
                     </div>

                     <div class="col-sm-6">                    
                        <div class="form-group multi-fields">
                            <p class="qus"><i class="fa fa-check-circle circle-active"></i> Add a couple of products to your meal</p>                              
                            <?php 
                               $i = 1;
                               $cnt = count((array)$mealItems);                               
                               foreach($mealItems as $idx => $mealItem){                                  
                            ?>
                            <div class="col-sm-12 multi-field products">   
                                <div class="col-sm-5">
                                    <div class="form-group float-label-control">
                                       <label for="">Product</label>
                                         <select name="product" class="form-control" id="country-list">                                           
                                             <?php foreach($products as $product_id => $product){?>
                                             <option  value="<?php echo $product_id; ?>" <?php echo ($mealItem['id'] == $product_id) ? 'selected' : ''; ?> ><?php echo $product; ?></option>                                     
                                             <?php } ?>
                                         </select>
                                    </div>
                                </div>
                                <div class="col-sm-5">
                                     <div class="form-group float-label-control">
                                         <input type="text" class="form-control empty" name="quantity" value="<?php echo $mealItem['quantity']?>" data-itmId="<?php echo $mealItem['id']?>" >
                                       <label for="">Quantity</label>
                                     </div>
                                </div>
                                <div class="col-sm-2 productadd">
                                    <?php 
                                   if($i==$cnt){
                                    if($cnt > 1){
                                    ?>
                                    <button title="remove" type="button" class="smBtn has-tip tip-top remove-field" data-tooltip="" data-selector="tooltip-i6htcnc50" aria-describedby="tooltip-i6htcnc50" title="">
                                      <i class="fa fa-trash-o"></i>
                                    </button>
                                    <?php } ?>
                                    <button class="add-field smBtn has-tip tip-top" data-tooltip="" title="add more" type="button" data-selector="tooltip-i6hptux91" aria-describedby="tooltip-i6hptux91" title="">
                                      <i class="fa fa-plus"></i>
                                    </button>

                                   <?php 
                                   }else{
                                   ?>
                                   <button title="remove" type="button" class="smBtn has-tip tip-top remove-field" data-tooltip="" data-selector="tooltip-i6htcnc50" aria-describedby="tooltip-i6htcnc50" title="">
                                     <i class="fa fa-trash-o"></i>
                                   </button>
                                   <?php } ?>                                       
                                </div>    
                            </div>                                    
                            <?php 
                            $i++; 
                              } 
                            ?>                                                         
                        </div>
                        <div class="form-group">
                            <p class="qus"><i class="fa fa-check-circle circle-active"></i> When would you like to serve this meal?</p>
                            <div class="input-control checkbox mb0">                              
                            <?php
                                foreach($menu_type as $menu_id =>$menu_value){?>
                                    <label>
                                        <input id="menu_<?php echo $menu_value  ?>" type="checkbox" class="third" name="menu_type" value="<?php echo $menu_value; ?>" <?php echo (in_array($menu_value,$meal->category)) ? 'checked':'' ;?>>
                                        <span for="menu_<?php echo $menu_value  ?>" class="check"></span> <?php echo $menu_value; ?>
                                    </label>
                                <span class='error' id="menu_type_err" style="color:#FC6E51"></span>
                            <?php  }    ?> 
                            </div>                             
                        </div>
                     </div>
                  </div>
                </div> 
            </form>
        </div>
    </div>
 </section>
<script>

$(document).ready(function(){
   
   var wrapper = $('.multi-fields'); 
    var products = Array();

  //alert($('select.products').html());
  var foodtype = '<?php echo json_encode($settings['FOOD_TYPE']);?>';
   var veg = 'veg';
   var nonveg = 'nonveg';
  
  if(foodtype == '["veg"]'){  
    $("input[type=radio][value=" + nonveg + "]").prop("disabled",true);
  }
  else if(foodtype == '["nonveg"]'){
    $("input[type=radio][value=" + veg +"]").prop("disabled",true);
  }
  else{
    $("input[type=radio][value=" + nonveg +"]").prop("disabled",false);
    $("input[type=radio][value=" + veg +"]").prop("disabled",false);
  }

   $(document).on('click',".add-field",function(e) {
		
    	$(this).removeClass("add-field").html('<i class="fa fa-trash-o"></i>');
    	$(this).addClass("remove-field");
    	$(this).attr("title","");

    	var thsele = this.outerHTML;
    	
    	var btns = $(this).parent().find("button");

    	$(this).parent().append(thsele);

    	btns.each(function(){
			$(this).remove();
	    });
    	
        var ele = $('.multi-field:first').clone(true);
        
	    ele.find("button").remove();	  
	    ele.appendTo(wrapper).find('input').val('').focus();	    
	    ele.find(".productadd").append('<button class="smBtn has-tip tip-top remove-field" aria-describedby="tooltip-i6hptux91" data-selector="tooltip-i6hptux91" type="button" data-tooltip="" title=""><i class="fa fa-trash-o"></i></button><button title="add more" type="button" class="smBtn has-tip tip-top add-field" data-tooltip="" data-selector="tooltip-i6htcnc50" aria-describedby="tooltip-i6htcnc50" title=""><i class="fa fa-plus"></i></button>');	   	    	    
    });
    
    $(document).on('click','.multi-field .remove-field',function() {
	    
        if ($('.multi-field').length > 1)
        {        	
        	var btns = $(this).parent().find("button");
        	
        	if(btns.length=='2'){

        		var addBtn = btns[1].outerHTML;

        		$(this).parent().parent().remove();
        		
        		var ele = $('.multi-field:last');

        		ele.find(".productadd").append(addBtn);		
        		
                if ($('.multi-field').length == 1){

                    var btns = $('.multi-field').find(".productadd").find("button");

                    if(btns.length=='2'){
                        $(btns[0]).remove();
                    }
                }	        	
        	}else{
        		$(this).parent().parent().remove();
        	
        		 if ($('.multi-field').length == 1){
	        		 
        			 var btns = $('.multi-field').find(".productadd").find("button");

        			 if(btns.length=='2'){
        				 $(btns[0]).remove();
        			 }
        		 }
        	}            
        }
    });         
});
</script>
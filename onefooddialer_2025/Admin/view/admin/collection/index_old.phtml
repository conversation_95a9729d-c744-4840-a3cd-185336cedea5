  <script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>

  <div class="maincontentinner">
        <div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="widgets"><span>Payment Collection</span></h2>
          </div>
          <!--contenttitle-->
          <div class="dataTables_add" id="dyntable_filter"></div>
          <table class="stdtable" id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            </colgroup>
            <thead>
              <tr>
                <th class="head1">Invoice No.</th>
                <th class="head0">Name</th>
                <th class="head1">Address</th>
              	<th class="head0">Total Amount</th>
                <th class="head1">Amount Due</th>
                <th class="head0">Payment Mode</th>
                <th class="head1">Action</th>
              </tr>
            </thead>
            <tfoot>
               <tr>
                <th class="head1">Invoice No.</th>
                <th class="head0">Name</th>
                <th class="head1">Address</th>
              	<th class="head0">Total Amount</th>
                <th class="head1">Amount Due</th>
             	<th class="head0">Payment Mode</th>
                <th class="head1">Action</th>
              </tr>
            </tfoot>
            <tbody>
              <?php foreach ($paginator as $collection) : ?>
              <tr>
                <td><?php echo $this->escapeHtml($collection->invoice_no);?></td>
              	<td><?php echo $this->escapeHtml($collection->cust_name); ?></td>
                <td><?php echo $this->escapeHtml($collection->customer_Address);?></td>
				<td id="<?php echo "total".$collection->invoice_id;?>" ><?php echo $this->escapeHtml($collection->invoice_amount);?></td>
                <td>
                <input type="text" class="collect-input" disabled value="<?php echo $collection->amount_due;?>" id="<?php echo "due".$collection->invoice_id;?>"/>
			  </td>
                <td><select id="<?php echo "mode_of_payment".$collection->invoice_id;?>"class="collect-input">
                		<option value="cash">Cash</option>
                        <option value="cc">Credit card</option>
                        <option value="cheque">Cheque</option>
                	</select>
                </td>
                <td>
                	<?php
						if($collection->status){
							$clsname="paginate_button_disabled ";
          					$sts="Paid";
						}
						else
						{
							$clsname="paginate_button pay";
							$sts="Pay";
						} ?>
                	<span class="<?php echo  $clsname;?>" id="<?php echo $collection->invoice_id;?>" ><?php echo $sts;?></span>

               			 <input type="text" class="collect-input" style="width:60px !important;" data-mobile="<?php echo $collection->phone; ?>" data-fromdate="<?php echo date('M',strtotime($collection->from_date)); ?>" data-todate="<?php echo date('M, Y',strtotime($collection->to_date)); ?>" data-custname="<?php echo $collection->cust_name; ?>" id="<?php echo "pay".$collection->invoice_id;?>" />
					<span class="loader_show<?php echo $collection->invoice_id; ?>" style="display:none;float:right"">
						<img src="/admin/images/ajax-loader.gif" />
					</span>
                	<!-- <a class="btn btn5 btn_pencil5" href="order_summary_details_edit.html" style=""></a>  -->
                </td>
              </tr>
  			<?php endforeach; ?>
            </tbody>
          </table>
        </div>
        <!--content-->

      </div>
      <!--maincontentinner-->

<script type="text/javascript">

	$(document).ready(function(){

		function payCollection(invoice_id)
		{
			$('.loader_show'+invoice_id).show();
			var total=$("#total"+invoice_id).html();
			var pay_amount=$("#pay"+invoice_id).val();
			var cust_name=$("#pay"+invoice_id).data('custname');
			var fromdate=$("#pay"+invoice_id).data('fromdate');
			var todate=$("#pay"+invoice_id).data('todate');
			var phone = $("#pay"+invoice_id).data('mobile');
			var due_amount=$("#due"+invoice_id).val();
			var mode_of_payment= $("#mode_of_payment"+invoice_id).val();
			if(parseFloat(pay_amount) > parseFloat(due_amount))
			{
				$('.loader_show'+invoice_id).hide();
				alert("Invalid amount");
				return false;
			}
			$.ajax({
				 url:'<?php echo $this->basePath()."/collection/pay" ?>',
				 type: "POST",
				 data : {"invoice_id":invoice_id,"cust_name":cust_name,"phone":phone,"from":fromdate,"to":todate,"pay_amount":pay_amount,"total":total,"due_amount":due_amount,"mode_of_payment":mode_of_payment},

				 success:function(result)
				 {
					//alert(result);
					$('.loader_show'+invoice_id).hide();
					var res = jQuery.parseJSON(result);
					if(res)
					{
							var due_amnt=due_amount-pay_amount;
						 	$("#due"+invoice_id).val(due_amnt);

						if(due_amnt==0)
						{
						 	 $("#" + invoice_id).removeClass("paginate_button");
						 	 $("#" + invoice_id).removeClass("pay");
						  	 $("#" + invoice_id).addClass("paginate_button_disabled");
						 	 $("#" + invoice_id).html("Paid");
						}
					}
				 }
			});
		}
		$(".pay").live('click', function(){

			var invoice_id=$(this).attr("id");
			var pay_amount=$("#pay"+invoice_id).val();
	    	if(pay_amount=="")
	    	{
		    	alert("Please enter payment amount");
	    	}
	    	else
	    	{

				var checkstr =  confirm('Are you sure you want to pay?');
		   		 if(checkstr == true){
			    	payCollection(invoice_id);
		   		 }
		    }

		});
	});
 </script>

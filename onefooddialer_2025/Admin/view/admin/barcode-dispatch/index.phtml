<?php
		   //   $form->setAttribute('action', $this->url('barcodedispatch', array('action' => 'index')));
		      $form->setAttribute('class', 'stdform');
		      $form->prepare();
		    
?>
      <!-- <PERSON>ND PAGE HEADER-->
      
      <div id="content">
        <div class="large-6 columns">
           <?php  echo $this->form()->openTag($form);?>
        		<div class="row">
            		<div class="large-3 columns">
                    	<label class="inline right">Enter Barcode<span class="red">*</span></label>
                  	</div>
                  	<div class="large-7 columns">                
                       <?php   echo $this->formElement($form->get('barcode')); ?>
	      		  		<?php   
	      		  			echo $this->formElementErrors()
							->setMessageOpenFormat('<small class="error">')
							->setMessageCloseString('</small>')
							->render($form->get('barcode')); ?>
					<div class="dn" id="err">
             	
             		</div>
					        
                  	</div>
                  	     <div class="large-2 columns">     
                    	 <?php echo $this->formElement($form->get('submit')); ?>
                  	</div>
              	</div>
              	
       	<?php  echo $this->form()->closeTag(); ?>
   
        </div>
        <div class="large-12 columns"> 
       
       <div class="portlet box yellow">
       <div class="portlet-title">
       	<h4>
			<i class="fa fa-table"></i>
			Dispatched Orders
		</h4>
       </div>
       <div class="portlet-body">
	       <table class="pull-center" style="width: 100%;">
	      <thead>
	        <tr>
	          <th>Order No.</th>
	          <th>Order Date</th>
	          <th>Barcode Number</th>
	          <th>Customer Name</th>
	          <th>Order Menu</th>
	          <th>Product</th>
	          <th>Location</th>
	          <th>Delivery Status</th>
	        </tr>
	      </thead>
	      <tbody id="OrderData">
	      </tbody>
	    </table>
	    </div>
        </div>
       </div>
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
    
    <script type="text/javascript">

    $(document).ready(function(){

    	  $( "#barcode" ).focus();
    	$(":input").keypress(function(event){
    	    if (event.which == '10' || event.which == '13') {
    	        event.preventDefault();
    	        $("#submit").trigger("click");
    	    }
    	});

       	$("#submit").click(function(){
       	  	$('#err').empty();
       		var datastring = $("#frontlogin").serialize();
       		var barcode = $("#barcode").val();
			$.ajax({
				 type: "POST",
				 url:"<?php echo $this->url('barcodedispatch',array('action' => 'index')); ?>",
				 data : datastring,
				 dataType: "json",
				 beforeSend : function(){

					 $("#submit").attr('value','Processing..');
					 $("#submit").prop("disabled", true);
				
				 },
				 success:function(data)
				 {	
					  if(data.error){
						
		                	  var error_str="";
	                 		  $.each(data.form_validation_error, function(){

	                 			error_str+=this +"<br/>";

	                 		  });
	                 		  $('#err').html("<small class='error'>"+error_str+"</small>");  //console.log(this);
	                		  $('#err').show();
	                		  $( "#barcode" ).val('');
	                		  $( "#barcode" ).focus();
					  }else if(data.success){
						 var orderData = data.orderData;
						
						 var str ='';
						 var products = '';
						  $.each(orderData, function(key, name){
							  $.each(name, function(key1, dataOrder){

								  products = products + dataOrder.name+'('+dataOrder.quantity+')'+',';
								  //var dabbawalaCode = (dataOrder.dabbawala_code ==null || dataOrder.dabbawala_code =='')?'-':dataOrder.dabbawala_code;
								   
								  str = '<tr><td>'+dataOrder.pk_order_no+'</td><td>'+dataOrder.order_date+'</td><td>'+barcode+'</td><td>'+dataOrder.customer_name+'</td><td>'+dataOrder.order_menu+'</td><td>'+products+'</td><td>'+dataOrder.ship_address+'</td><td>Dispatched</td></tr>';	
							  });
						  });
						  //console.log(str);
						
						   $("#OrderData").prepend(str);
						  $( "#barcode" ).val('');
						  $( "#barcode" ).focus();
					  }
				 },
				 complete: function(){
					   $("#submit").attr('value','Dispatch');
					   $("#submit").prop("disabled", false);
			      }
				
			});
       	}); 
       });
	</script>

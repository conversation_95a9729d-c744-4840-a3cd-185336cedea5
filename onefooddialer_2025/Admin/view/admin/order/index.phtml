   
   <div id="myModal1" class="reveal-modal" data-reveal>
   
   </div>
   <div id="content" class="clearfix">
        <div class="large-12 columns">
       <?php // <span data-tooltip aria-haspopup="true" class="has-tip" title=" <div> Tooltips are awesome, </div> <div> you should totally use them! </div>" >extended information</span> ?>
        <?php //echo "<pre>"; print_r($new_menu_array); die; ?>
  <?php $status = $this->flashMessenger()->getMessages(); ?>

   <?php if( isset($status[0]) && array_key_exists('success',$status[0])){?>

        <div  data-alert="" class="alert-box success round">
        <?php echo $status[0]['success']; ?>
                <a href="#" class="close">&times;</a>
        </div>

   <?php }elseif( isset($status[0]) && array_key_exists('error',$status[0])){?>

        <div data-alert class="alert-box alert round">
                  <?php echo $status[0]['error'] ?>
                  <a href="#" class="close">&times;</a>
        </div>

   <?php } ?>
		
        <input type="hidden" name="view" id="view" value="<?php echo $view;?>" />
        <input type="hidden" name="condition" id="condition" value="<?php echo $condition;?>"> 
        <input type="hidden" name="selectkitchen" id="selectkitchen" value="<?php echo $_SESSION['adminkitchen'];?>">
        
        <?php 
          if($view!='preorder'){
            
        ?>
        
    <div class="filter">
        <form class="advance_search" id="filterFrm" style="display:block" name="filterFrm"  method="post">
            <div class="row">
                <div class="medium-12 columns">
                    <div class="type left">

                    <!-- <label style="margin:0px" class="left inline" for="right-label">Menu Type &nbsp;:&nbsp;</label> -->
                        <select name="delivery_status" id="delivery_status" class="left filterSelect">
                            <option value="">--Delivery Status--</option>
                                 <option <?php if($delivery_status=='pending'){ echo "selected"; } else { echo ""; } ?> value="pending">Pending</option>
                                 <option <?php if($delivery_status=='delivered'){ echo "selected"; } else { echo ""; } ?> value="delivered">Delivered</option>
                                 <option <?php if($delivery_status=='dispatched'){ echo "selected"; } else { echo ""; } ?> value="dispatched">Dispatched</option>
                                 <option <?php if($delivery_status=='undelivered'){ echo "selected"; } else { echo ""; } ?> value="undelivered">UnDelivered</option>
                                 <option <?php if($delivery_status=='rejected'){ echo "selected"; } else { echo ""; } ?> value="rejected">Rejected</option>	
                        </select>

                        <select name="menu" id="menu" class="left filterSelect">
                            <option value="">--Menu Type--</option>
                                <?php 
                                foreach($this->menus as $menu){
                                //$selected = ($this->menuSelected==$menu) ? "selected" : "";
                                ?>
                                <option <?php //echo $selected;?> value="<?php echo $menu;?>"><?php echo ucfirst($menu);?></option>
                                <?php 
                                }
                                ?>
                        </select>
							        
			        				
                        <?php //if($which_order == "Next Day's"){?>
                            <!-- <label style="margin:0px" class="left inline" for="right-label">Delivery Location &nbsp;:&nbsp;</label> -->
                            <select name="location" id="location" class="left filterSelect">
                                <option value="all">--Delivery Location--</option>
                                <option value="all">All</option>
                                <?php 
                                foreach($location_data as $locationkey=>$locationval){
                                ?>
                                <option value="<?php echo $locationval['pk_location_code'];?>"><?php echo $locationval['location'];?></option>
                                <?php 
                                }
                                ?>
                           </select>
                        <?php // }?>
                        <?php if($view != "cancel"){?>
                        <select class="left filterSelect" name="deliveryperson" id="deliveryperson" >
                            <option value="">--Delivery Person--</option>
                            <option value="all">All</option>													
                            <?php foreach($this->deliverypersons as $key=>$val){ ?>
                            <option  value="<?php echo $val['pk_user_code'];?>"><?php echo ucfirst($val['first_name']." ".$val['last_name']);?></option>
                            <?php } ?>

                        </select>
                            <?php 
                                }
                            ?>
                                                                    
                            <!--added sankalp 14 july pickup/delivery -->
                            <?php $hideClass = (array_key_exists('GLOBAL_DELIVERY_TYPE', $setting) && (strpos($setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ) ? 'show': 'hide'?>
                            <select class="left filterSelect <?php echo $hideClass?>" name="delivery_type" id="delivery_type" >
                                <option value="">--Delivery Type--</option>
                                <option value="all">All</option>													
                                <option value="pickup">Pickup</option>
                                <option value="delivery">Delivery</option>
                            </select>
                            <div class="clearfix"></div>
                            <div class="mt10">                            
                            <?php

                            if($view == "today"){
                            ?>
                                <label class="advance_lable" for="filter2">Date</label>
                                <input id="dateExportParam" class="left filterSelect hasDatepicker" type="text" disabled="disabled" value="<?php echo date("d M Y");?>" name="maxDate" size="10">
                            <?php 
                            }
                            if($view == "nextday"){
                            ?>
                            <label class="advance_lable" for="filter2">Date</label>
                                <input id="dateExportParam" class="left filterSelect" type="text" value="<?php echo $nextdate;?>" name="dateExportParam" size="10">
                                    <?php } 
                                    if($view != "cancel"){
                                    ?>
                                    <!-- <label style="margin:0px" class="left inline" for="right-label">Status &nbsp;:&nbsp;</label>
                                    <select name="order_status" id="order_status" class="left filterSelect">
                                     <option value="all">Select Status</option>
                                     <option value="New">New</option>
                                     <option value="Complete">Completed</option>
                                     <option value="Cancel">Cancel</option>
                                        </select>  -->
                                   <?php }

                                   if($view=='' || $view == "cancel"){
                                   ?> 


                                    <label class="left inline" for="minDate"> From:  </label>
                                        <input class="left filterSelect" name ="minDate" id="minDate" readonly="readonly" type="text"   />
                                        <!-- <input id="minDate" class="left filterSelect calender" type="date" value="" step="1" readonly="readonly" name="neft_date" style="display: block;"> -->
                                   <label class="left inline" for="maxDate" style="margin-left:0"> To:  </label>
                                    <input class="left filterSelect" name ="maxDate" id="maxDate" readonly="readonly" type="text"   />
                                        <!-- <input id="maxDate" class="left filterSelect calender" type="date" value="" step="1" readonly="readonly" name="neft_date" style="display: block;"> -->
                                    <?php } ?>
                                    <button id="kitchensbtbtn" name="kitchensbtbtn" style="font-size:10px;" class="button left left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
                            </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
		    
    <div class="clearBoth10"></div>
    <?php }
    ?>


    <?php 

    if($view=='preorder'){
    ?>
    <div class="hidden-xs">
        <div class="service-slide color_white clearfix">
            <div class="service_content pull-left theme_back_color">
                <div class="slide_click service_click pull-left theme_back_color">
                    <div class="service-inner slide-inner">
                       <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                    </div>
                </div>
                <ul class="pull-left services ">
                    <li>
                        <a id="editqty" class="common-orange-btn-on-hover">Modify meal Qty&nbsp;&nbsp;</a>
                    </li>
                    <li class="devider"></li>
                    <li>
                    <a id="editdates" class="common-orange-btn-on-hover">Edit Order Dates</a>
                    </li>
                    <li class="devider"></li>
                    <li>
                        <a id="preexport" class="common-orange-btn-on-hover"> Export Data </a>
                    </li>
                </ul>
            </div>
        </div>
    </div> 
    <div class="filter">
        <form class="advance_search" id="filterFrm" name="filterFrm" style="display:block">
            <div class="row">
                <div class="medium-12 columns">
                    <div class="type left">

                        <select name="delivery_status" id="delivery_status" class="left filterSelect">
                        <option value="">--Delivery Status--</option>
                            <option value="Pending">Pending</option>
                            <option value="Delivered">Delivered</option>
                            <option value="Dispatched">Dispatched</option>
                            <option value="UnDelivered">UnDelivered</option>
                            <option value="Rejected">Rejected</option>	
                        </select>

                        <select name="menu" id="menu" class="left filterSelect">
                            <option value="">--Menu Type--</option>
                                <?php 
                                foreach($this->menus as $menu){
                                        //$selected = ($this->menuSelected==$menu) ? "selected" : "";
                                ?>
                                        <option <?php //echo $selected;?> value="<?php echo $menu;?>"><?php echo ucfirst($menu);?></option>
                                <?php 
                                }
                                ?>
                        </select>

                         <select name="location" id="location" class="left filterSelect">
                            <option value="all">--Delivery Location--</option>
                            <option value="all">All</option>
                            <?php 
                            foreach($location_data as $locationkey=>$locationval){
                            ?>
                                <option value="<?php echo $locationval['pk_location_code'];?>"><?php echo $locationval['location'];?></option>
                            <?php 
                            }
                            ?>
                        </select>
                        <select class="left filterSelect" name="deliveryperson" id="deliveryperson" >
                            <option value="">--Delivery Person--</option>
                            <option value="all">All</option>													
                            <?php foreach($this->deliverypersons as $key=>$val){ ?>
                            <option  value="<?php echo $val['pk_user_code'];?>"><?php echo ucfirst($val['first_name']." ".$val['last_name']);?></option>
                            <?php } ?>

                        </select>
                        <?php $hideClass = (array_key_exists('GLOBAL_DELIVERY_TYPE', $setting) && (strpos($setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ) ? 'show': 'hide'?>
                        <select class="left filterSelect <?php echo $hideClass?>" name="delivery_type" id="delivery_type" >
                            <option value="">--Delivery Type--</option>
                            <option value="all">All</option>													
                            <option value="pickup">Pickup</option>
                            <option value="delivery">Delivery</option>
                        </select>
                        <div class="clearfix"></div>
                        <div class="mt10">
                            <label class="left inline" for="minDate"> From:&nbsp;</label> 
                            <input class="left filterSelect" name ="minDate" id="minDate" readonly="readonly" type="text"   />

                            <label class="left inline" for="maxDate" style="margin-left:0"> To:&nbsp;  </label> 
                            <input class="left filterSelect" name ="maxDate" id="maxDate" readonly="readonly" type="text"   />

                            <button id="submitButton" class="button left tiny left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
			        
    <div class="clearBoth10"></div>
    <?php 
     }
    ?>
    <div class="portlet box yellow">
        <div class="portlet-title">
        <h4><i class="fa fa-table"></i><?php echo $which_order?> Orders</h4>

        <?php if($which_order == "All" || $which_order == "Cancelled" || $which_order == "Preorders"){?>
            <ul class="toolOption">
                <li>
                  <div class="print">
                    <button class="btn columnModal export"  data-id="orders" data-dropdown=""><i class="fa fa-print"></i>&nbsp;Export</button>
                        <ul id="" data-dropdown-content="" class="f-dropdown">
                            <li data-tooltip="" class="has-tip tip-top" data-exporttype="pdf" data-selector="tooltip-illu2kcd0" title="Export PDF"><a href="javascript:void(0);" id="exportPDF"><i class="fa fa-file-pdf-o"></i></a></li>
                            <li data-tooltip="" class="has-tip tip-top" data-exporttype="xls" data-selector="tooltip-illu2kcd1" title="Export XLS"><a href="javascript:void(0);" id="exportXLS"><i class="fa fa-file-pdf-o"></i></a></li>
                        </ul>
                  </div>

                    <div id="myModal" class="reveal-modal custPopup" data-reveal=""></div>
                </li>
            </ul>
        <?php }?>
        <?php if($which_order == "Today's"){?>
        <div class="hidden-xs">
            <div class="service-slide color_white clearfix">
                <div class="service_content pull-left theme_back_color">
                    <div class="slide_click service_click pull-left theme_back_color">
                        <div class="service-inner slide-inner">
                            <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                        </div>
                    </div>
                    <ul class="pull-left services ">
                       
                        <li>
                            <a id="printOrder" class="common-orange-btn-on-hover">Print order</a>
                        </li>
                        <li class="devider"></li>
                        <li>
                        <a id="todayprintPack" class="common-orange-btn-on-hover">Print Packaging</a>
                        </li>
                        <li class="devider"></li>
                        <li>
                           <a id="todaykot" class="btn-kot common-orange-btn-on-hover">KOT</a>
                        </li>
                        <li class="devider"></li>
                        <li>
                            <a id="todayexportData" class="common-orange-btn-on-hover"> Export Data </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div> 
        <ul class="toolOption">
            <li>
                <div class="print" style="float:none">
                    <?php 
                    if( isset($languages) && is_array($languages) && !empty($languages) ) {
                    ?><select id="select_language" name="select_language" style="width:160px;display:inline;">
                        <option value="">Select a language</option>
                    <?php
                        foreach( $languages as $code => $language ) {
                    ?>
                        <option value="<?php echo $code; ?>"><?php echo $language; ?></option>
                    <?php
                        }//end of foreach
                    ?></select>
                    <?php
                    }
                    ?>
                </div>
            </li>
        <li>
           <div class="print" style="float:none">
               <button id="print_button" target="_blank" class="btn printOrders" data-dropdown=""><i class="fa fa-print"></i>&nbsp;Print Order</button>
               <button id="" target="_blank" class="btn print_packaging todaypack" data-dropdown=""><i class="fa fa-print"></i>&nbsp;Print Packaging</button>
                <!--<button class="btn" data-dropdown="dropKOT"><i class="fa fa-print"></i>&nbsp;Print KOT</button>&nbsp;--> 
               <button class="btn-kot btn dropdown todaykotlist" data-dropdown="dropKOT" >&nbsp;KOT</button>&nbsp;
               <ul id="dropKOT" data-dropdown-content class="f-dropdown">
                   <li id="print_kot_list"  data-action="print" data-tense="today" target="_blank" data-tooltip class="printKot has-tip tip-top" title="Print KOT"><a href="javascript:void(0);">&nbsp;<i class="fa fa-print"></i></a></li>
                   <li id="export_kot_list" data-action="export" data-tense="today" target="_blank" data-tooltip class="printKot has-tip tip-top " title="Export KOT"><a href="javascript:void(0);">&nbsp;<i class="fa fa-file-excel-o"></i></a></li>
               </ul>
               <!-- <button id="print_kot_list" target="_blank" class="btn " data-dropdown=""><i class="fa fa-file-excel-o"></i>&nbsp;Export KOT</button>&nbsp; -->
               <button class="btn columnModal todayexport"  data-id="orders" data-dropdown="" ><i class="fa fa-print"></i>&nbsp;Export</button>
               <ul id="" data-dropdown-content class="f-dropdown">
                   <li data-tooltip class="has-tip tip-top "  data-id="orders" data-exporttype="pdf" title="Export PDF"><a href="javascript:void(0);"  id="exportPDF"><i class="fa fa-file-pdf-o" ></i></a></li>
                   <li data-tooltip class="has-tip tip-top "  data-id="orders" data-exporttype="xls" title="Export XLS"><a href="javascript:void(0);"  id="exportXLS"><i class="fa fa-file-pdf-o" ></i></a></li>
               </ul>				  	
           </div>

           <div id="myModal" class="reveal-modal custPopup"  data-reveal></div>

        </li>

        </ul>

        <?php }
        elseif($which_order == "Next Day's"){?>
            <div class="hidden-xs">
                <div class="service-slide color_white clearfix">
                    <div class="service_content pull-left theme_back_color">
                        <div class="slide_click service_click pull-left theme_back_color">
                            <div class="service-inner slide-inner">
                                <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                            </div>
                        </div>
                        <ul class="pull-left services ">
                            <li>
                                <a id="printLabels" class="common-orange-btn-on-hover">Print Labels</a>
                            </li>
                            <li class="devider"></li>
                            <li>
                                 <a id="nextKot" class="btn-kot common-orange-btn-on-hover">Print KOT</a>
                            </li>
                            <li class="devider"></li>
                            <li>
                               <a id="nextprintPack" class="common-orange-btn-on-hover">Print Packaging&nbsp;&nbsp;&nbsp;&nbsp;</a>
                            </li>
                            <li class="devider"></li>
                            <li>
                                <a id="nextexportData" class="common-orange-btn-on-hover"> Export Data </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div> 
            <ul class="toolOption">
                <li>
                    <div class="print" style="float:none">
                    <?php
                    if( isset($languages) && is_array($languages) && !empty($languages) ) {
                    ?><select id="select_language" name="select_language" class="left filterSelect">
                        <option value="">Select a language</option>
                    <?php
                        foreach( $languages as $code => $language ) {
                    ?>
                        <option value="<?php echo $code; ?>"><?php echo $language; ?></option>
                    <?php
                        }//end of foreach
                    ?></select>
                    <?php
                    }   
                    ?>
                    </div>
                </li>
                <li>
                    <div class="print" style="float:none">
                        <button id="print_nextdayLabel" target="_blank" class="btn nextprintLabels" data-dropdown=""><i class="fa fa-print"></i>&nbsp;Print Labels</button>
                         <button id="print_nextday" target="_blank" class="btn btn-kot " data-dropdown=""><i class="fa fa-print"></i>&nbsp;Print KOT</button> 
                        <!--<button class="btn dropdown nextKots" data-dropdown="dropKOT" >&nbsp;KOT</button>&nbsp;-->
                        <ul id="dropKOT" data-dropdown-content class="f-dropdown">
                            <li id="print_kot_list"  dmyMoata-action="print" data-tense="next" target="_blank" data-tooltip class="btn-kot printKot has-tip tip-top" title="Print KOT"><a href="javascript:void(0);">&nbsp;<i class="fa fa-print"></i></a></li>
                            <li id="export_kot_list" data-action="export" data-tense="next" target="_blank" data-tooltip class="printKot has-tip tip-top " title="Export KOT"><a href="javascript:void(0);">&nbsp;<i class="fa fa-file-excel-o"></i></a></li>
                        </ul>

                        <button id="" target="_blank" class="btn print_packaging nextprintPacks" data-dropdown=""><i class="fa fa-print"></i>&nbsp;Print Packaging</button>
                        <button class="btn columnModal nextdayexport" data-id="orders" data-dropdown="" ><i class="fa fa-print"></i>&nbsp;Export</button>
                        <ul data-dropdown-content class="f-dropdown">
                            <li data-tooltip class="has-tip tip-top" data-exporttype="pdf" title="Export PDF"><a href="javascript:void(0);"  id="exportPDF"><i class="fa fa-file-pdf-o" ></i></a></li>
                            <li data-tooltip class="has-tip tip-top" data-exporttype="xls" title="Export XLS"><a href="javascript:void(0);"  id="exportXLS"><i class="fa fa-file-pdf-o" ></i></a></li>
                        </ul>	
                    </div>
                    <div id="myModal" class="reveal-modal custPopup"  data-reveal></div>
                </li>
            </ul>
            <?php }?>
        </div>
        <div class="portlet-body sales_data_table">
       <div class="filter">
            <div>
                    <a class="advance_search_click"> Hide Advance Search </a>
            </div>
        </div>

            <table id="customer" class="display displayTable" style="width: 100%">
                <thead>
                    <tr>
                        <th></th>
                        <?php if($view!="preorder"){?>
                        
                        <th>Bill No.</th>
                        <?php }else{ ?>
                        <th>Order No.</th>
                        <?php } ?>
                        <th>Customer</th>
                        <th>Location</th>
                        <th>Amount <!-- <i class="fa fa-rupee"></i> --></th>
                        <th>Meal</th>                      
                        <th>Payment</th>
                        <?php if($view!="preorder"){?>
                        <th>Status</th>
                        <?php } ?>
                        <th>Menu</th>
                        <?php 
                        if($view=='preorder'){
                        ?>
                        <th>Start</th>
                        <th>End</th>
                        <?php 
                        }else{
                        ?>
                        <th>Delivery</th>
                        <?php 
                        }
                        ?>
                        <?php if($view!="preorder"){?>
                       <th>Date</th>
                        <?php } ?>   
                        <?php if($view=="today"){?>
                        <th>Delivery Person</th>  
                        <?php } ?>   
                        <?php if($view=="today" || $view=="preorder" || $view=="nextday"){?>
                        <th class="head1 no_sort" width="10%">Action</th>
                        <?php } ?>
                    </tr>
                </thead>
            </table>
        </div>
    </div>

    <div class="clearBoth20"></div>
            
    </div>
</div>
    <!-- END PAGE CONTAINER-->
    
<script type="text/javascript">

function loadModel(ordno,cid,date,ordstatus,menu){	
    
	$('#myModal1').foundation('reveal', 'open', {
	    url: "/order/loadshiftmodel/menu/"+menu+"/date/"+date+"/ordno/"+ordno+"/custid/"+cid+"/ordstatus/"+ordstatus,
	});
	
}
	
 $(document).on("click",".columnModal",function(){
 			
        var table = $(this).data('id');
//        var exporttype = $(this).data('exporttype');
        var exporttype = 'pdf'; // hardcoded. value to be set in MODAL view.

        var delivery_person = "";  var location = ""; var delivery_type = "";
        location = $('#location').val();
        menu = ($('#menu').val()) ?$('#menu').val(): "all" ;
        var which_view = ''; 
        which_view = $('#view').val();
        var datastring = "service=order&menu="+menu+"&location_code="+location+"&";

        if($("#deliveryperson").length != 0) {
            delivery_person = $('#deliveryperson').val();
            datastring += "delivery_person="+delivery_person+"&";
        }
          
        if($("#delivery_type").length != 0) {
            delivery_type = $('#delivery_type').val();
            datastring += "delivery_type="+delivery_type+"&";
        }

        if($("#delivery_status").length != 0) {
        	delivery_status = $('#delivery_status option:selected').val();
            datastring += "delivery_status="+delivery_status+"&";
        }
          
        
       // console.log("date: "+which_view);
        
        if(which_view == "today" || which_view == "nextday"  ){
        
            var dateParam       = ($('#dateExportParam').val()) ?$('#dateExportParam').val(): "all" ;
            datastring          += "dateFilterParam="+dateParam+"&";
        }else{
            if(which_view == "cancel"){
                datastring  += "filter_order_options=Cancel&";
            } 
            var minDate = $("input[name=minDate]").val();
            var maxDate = $("input[name=maxDate]").val();

            datastring  += "filter_check=2&minDate="+minDate+"&maxDate="+maxDate+"&";

        }
        
        if(which_view == "preorder"){
            var pre_order_days = $("#expires_on").val();
            datastring  += "pre-order-days="+pre_order_days;
        } 

        $('#myModal').foundation('reveal', 'open', {
            url: '/report/exportData',
            data: {table: table,form: datastring,exporttype:exporttype,order_type:which_view}
        });
        return false;
});

$(document).on('click','#export',function(e){
       e.preventDefault();
       if(document.querySelectorAll('input[type="checkbox"]:checked').length==0){
               alert('Please Select fields to be exported');
               return false;
       }
       else
       {	
               var values =[]; 
               var type = $("input[name=export_type]:checked").val();
               values = $('.checkbox:checked.checkbox').map(function () {
                         return this.id;
               }).get();
               if(type=='pdf' && values.length>8){
                       alert("You can select maximum 8 columns");
                       return false;
               }
                
           var fromTable = $("#table").val();
               $("#selected_columns").val(values.join());
               $("#exportForm").attr("target", "_blank");
//               $("#exportForm").submit();
               $('#exportForm').attr('action', "/report/exportData").submit();
               return false;
       }
});

function getConfirmation(status,orderno,custid,ordermenu,orderdate,kitchen,view) {

        var msg = "Are you sure to perform this operation?";
        var url = "/order/cancelorder";
        var titlemsg = (view == 'today' ? 'Cancel Today\'s Order' : 'Cancel Next Day\'s Order' );
        var cancelDays = [orderdate];

        if(status == 1) {
            var dialog = $('<p>'+msg+'</p>').dialog({

                title: titlemsg,
                buttons: {
                    "Cancel and stop recurring order": function() {

                        $.ajax({
                            url     : url,
                            type    :'POST',
                            data    : {id:orderno,c_id:custid,menu:ordermenu,cancelDays:cancelDays,fk_kitchen_code:kitchen,recurring_status:0},
                            dataType: 'json',
                            async: false,            
                            beforeSend : function() {
                                //$('#'+ordno).append('processing..');
                            },
                            success : function(response) {
                                console.log(response);
                                if(response.status=="success") {
                                    window.location.reload();
                                }
                            },
                            error: function(xhr, status, errorThrown) {
                                console.log('Error: '+xhr.status+' '+xhr.responseText);
                            }
                        });                        
                    },
                    "Cancel and continue recurring order": function() {

                        $.ajax({
                            url     : url,
                            type    :'POST',
                            data    : {id:orderno,c_id:custid,menu:ordermenu,cancelDays:cancelDays,fk_kitchen_code:kitchen,recurring_status:1},
                            dataType: 'json',
                            async: false,            
                            beforeSend : function() {
                                //$('#'+ordno).append('processing..');
                            },
                            success : function(response) {
                                console.log(response);
                                if(response.status=="success") {
                                    window.location.reload();
                                }
                            },
                            error: function(xhr, status, errorThrown) {
                                console.log('Error: '+xhr.status+' '+xhr.responseText);
                            }
                        });
                    }
                }            
            });            
        }
        else {

            var dialog = $('<p>'+msg+'</p>').dialog({
                title: titlemsg,
                buttons: {
                    "Ok": function() {

                        $.ajax({
                            url     : url,
                            type    :'POST',
                            data    : {id:orderno,c_id:custid,menu:ordermenu,cancelDays:cancelDays,fk_kitchen_code:kitchen,recurring_status:0},
                            dataType: 'json',
                            async: false,            
                            beforeSend : function() {
                                //$('#'+ordno).append('processing..');
                            },
                            success : function(response) {
                                console.log(response);
                                if(response.status=="success") {
                                    window.location.reload();
                                }
                            },
                            error: function(xhr, status, errorThrown) {
                                console.log('Error: '+xhr.status+' '+xhr.responseText);
                            }
                        });                        
                    },
                    "Cancel":  function() {
                        dialog.dialog('close');
                    }
                }
            }); 
        }
   

/*    if(status == 1) {
        if(confirm('Cancel today\'s order and stop recurring')) {
            alert('gotcha 1');
            return false;
        }
        else {
            if(confirm('Cancel today\'s order and continue recurring')) {
                alert('gotcha 2');
                return false;                
            }
        }
    } */
/*   var retVal = confirm("Do you want to continue ?"+status);
   if( retVal == true ){
      document.write ("User wants to continue!");
      return false;
   }
   else{
      document.write ("User does not want to continue!");
      return false;
   }
*/}   
          
$(document).ready(function() {

	//myPageTable.init();
	//$('#minDate').datepicker();
    /*
    $(document).on('click', '.btnCancel', function(e) {

        e.preventDefault();
        var dialog = $('<p>Are you sure?</p>').dialog({
            title: "Cancel Today's Order",
            buttons: {
                "Cancel and Stop Recurring": function() {alert('you chose yes');},
                "Cancel and Continue Recurring": function() {alert('you chose no');},
                "Cancel":  function() {
                    dialog.dialog('close');
                }
            }
        });        
        return false;
    });
    */

    $(document).on('click', '.recurringorder', function() {

        var ordno = $(this).data('orderno');
        var url = "/order/cancel-recurring-order"; 

        $.ajax({
            url     : url,
            type    :'POST',
            data    : {orderid:ordno},
            dataType: 'json',
            async: false,            
            beforeSend : function() {
                $('#'+ordno).append('processing..');
            },
            success : function(response) {
                console.log(response);
                if(response.status=="success") {
                    window.location.reload();
                }
            },
            error: function(xhr, status, errorThrown) {
                console.log('Error: '+xhr.status+' '+xhr.responseText);
            }
        });
    });
	
	$("#minDate").datepicker({ dateFormat: "yy-mm-dd" });
	$("#maxDate").datepicker({ dateFormat: "yy-mm-dd" });
    
    var holidays = [<?php echo $holidays; ?>];
	
    var weekOff1='<?php echo $weekOff; ?>';
    
    var week_off1=weekOff1.split(",");
    
    var skipKitchenCheck = '<?php echo $skipKitchenCheck; ?>';
    
    var weekoffs1= new Array();

    $.each(week_off1,function(key1,value1){
            weekoffs1.push(parseInt(value1));
    });
    
    $("#dateExportParam").datepicker({ 
        
        minDate: 1,
        dateFormat: "yy-mm-dd",
        beforeShowDay: function(date){
            show = true;
            if(jQuery.inArray(date.getDay(), weekoffs1) != -1){ show = false; }//No Weekends
            for (var i = 0; i < holidays.length; i++) {
                if (new Date(holidays[i]).toString() == date.toString()) {show = false;}//No Holidays
            }
            var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
            return display;
        },
    });
	
    var aoColumns = [];
    $('#customer thead th').each( function () {
        if ( $(this).hasClass('no_sort')) {
            aoColumns.push( { "bSortable": false } );
        } else {
            aoColumns.push( { "asSorting": [ "desc" ] } );
        }
    } );
	
     if(skipKitchenCheck=='yes'){
        $(".btn-kot").css('display','none');
    }
    var UnsortableArray = [ -1 ];

    var orderTable = $('#customer').dataTable( {
        
    	"aaSorting": [[ 0, "desc" ]],
        
        "processing": true,
        "serverSide": true,
       // "bDestroy" :true,
    	//"stateSave": true,
        "scrollX": true,
        "orderable":false,
       // "scrollY": "200px",
        "scrollCollapse": true,
        //"aoColumns":aoColumns,
        //"aoColumnDefs": [ { "bSortable": false, "aTargets": [-1] }],
        "ajax": {
            "url":"/order/ajx-order",
            "data": function (d,loadScroll){
                d.view = $('#view').val();
                d.condition = $('#condition').val();
                d.menu = $('#menu').val();
                d.expires_on = $('#expires_on').val();
                d.kitchenscreen = $('#selectkitchen').val();
                d.location_code = $('#location').val();
                //d.orderstatus = $("#order_status").val();
                d.minDate = $('#minDate').val();
                d.maxDate = $('#maxDate').val();
                d.deliveryperson = $('#deliveryperson').val();
                d.date_filter = $('#dateExportParam').val();
                d.delivery_status = $('#delivery_status').val();
            },
            
        },
	      "aoColumnDefs": [
     	      {
     	          bSortable: false,
     	          aTargets: UnsortableArray
     	      }
	     ],
	     "fnDrawCallback": function( oSettings ) {
	    	 $('#customer tbody tr').each( function() {
	    		 var sTitle;
	             var nTds = $('td', this);

	             $(nTds).each( function(key,value) {
						if(key==4){
							//console.log(this);
							var net_str = $(this).find("span").data("title");
							console.log(net_str);
							//html = $.parseHTML( net_str );
							//net_str = "test<br \>test";
							this.setAttribute( 'class', 'top');
				           	this.setAttribute( 'title', net_str );
						}
	             });
	             
	    	 });

	    	orderTable.$('tr').tooltip( {
	    	    "delay": 0,
	    	    "track": true,
	    	    "fade": 250,
	    	    html:true
	    	});
	    	 
	    	 
	     }
	     
      });

   

    $("#kitchensbtbtn").click(function(){
    	orderTable.api().ajax.reload();
    	$(".upperdiv").hide();
    	if($("#menu").val()==''){
    	    $(".upperdiv").show();
        }else{
    		$("#upperdiv_"+$("#menu").val()).show();
        }
    });
    //orderTable.fnSort( [ [0,'desc']] );

	 $('#print_button').click(function(){

		 var loc ='';
		 var menu = $('#menu').val()
		 
		 var language=($("#select_language").val()=='')?'all':$("#select_language").val();
		 var location=$("#location").val();
		 var kitchen = $("#kitchen").val();
		 var deliveryperson=($("#deliveryperson").val()=='')?'all':$("#deliveryperson").val();
		 var delivery_type=($("#delivery_type").val()=='')?'all':$("#delivery_type").val();
		
         //alert(location);
         //return false;   
		 if(menu!='')
		 {	
		 	loc = "/order/printorder/menu/"+menu+"/language/"+language+"/location/"+location+"/deliveryperson/"+deliveryperson+"/delivery_type/"+delivery_type+"/kitchen/"+kitchen;
		 }
		 else
		 {

			 loc = "/order/printorder/language/"+language+"/location/"+location+"/kitchen/"+kitchen;
		 }
		 window.open(loc, '_blank');
		  
	});
    
    
    
        
    
	$('.print_packaging').click(function(){

                var loc ='';
                var menu = $('#menu').val();
                var selectkitchen = $('#selectkitchen').val();
                var language=($("#select_language").val()=='')?'all':$("#select_language").val();
                
                var date = $('#dateExportParam').val();

                //alert(date);

                //return false;
                
                if(menu!='')
                {
                        loc = "/order/printpackaging?menu="+menu+"&screen="+selectkitchen+"&lang="+language+"&date="+date;	
                }
                else
                {
                        loc = "/order/printpackaging?screen="+selectkitchen+"&lang="+language+"&date="+date;
                }
                window.open(encodeURI(loc), '_blank');
		  
	});

	 $('.printKot').click(function(){
		
		 var loc ='';
		 var menu = $('#menu').val();
		 var selectkitchen = $('#selectkitchen').val();
		 var language = ($("#select_language").val()=='')?'all':$("#select_language").val();
		 var subaction = $(this).data("action");
		 var tense = $(this).data("tense");
		 var lnk = "printkotlist";	
		 var print_date = $("#dateExportParam").val();

		 loc = "/order/"+lnk+"?screen="+selectkitchen+"&lang="+language+"&s="+subaction+"&date="+print_date;
        if(menu!='')
		 {
			 loc += "&menu="+menu;	
		 }
		  
		 window.open(loc, '_blank');
		  
	});


	 $('#print_nextday').click(function(){
		 var menu = $('#menu').val();
		 var loc ='';
		 var location=$("#location").val();
		 var print_date = $("#dateExportParam").val();
         if(menu!='')
         {
              if(location!='')
              {
                  //alert(location);
                  loc = "/order/printnextdayorder/menu/"+menu+"/location/"+location;
              }
              else
              {
                  loc = "/order/printnextdayorder/menu/"+menu;
              }
          }
          else
          {
              if(location!='')
              {
                  loc = "/order/printnextdayorder/location/"+location;
              }
              else
              {

                  loc = "/order/printnextdayorder";
              }
          }

		  loc = loc+"?date="+print_date
                      
        
		  window.open(loc, '_blank'); 
	});

		

    $("#submitButton").on('click',function(){
    	orderTable.api().ajax.reload();
    });	

    $('#print_nextdayLabel').click(function(){

    	 var menu = '';
    	 var loc =''; 
		 var text = $('#customer tr td').text();
		 var language=($("#select_language").val()=='')?'all':$("#select_language").val();
		 var location=$("#location").val();
		 var print_date = $("#dateExportParam").val();
                 var delivery_type = ($("#delivery_type").val() == '')? 'all': $("#delivery_type").val();
                 
		 var kitchen_screen = $("#selectkitchen").val();
        if(kitchen_screen == "")
		 {
			 kitchen_screen="all";
		 }
 		 if(text=='No data available in table'){
			alert("No Next days orders");
				return false;
		 } 
		 else{
			menu = $('#menu').val();
			 	
			if(menu!='')
			{
				if(location!='')
				{
					loc = "/order/printnextdaylabel/menu/"+menu+"/language/"+language+"/location/"+location+"/delivery_type/"+delivery_type+"/kitchen/"+kitchen_screen+"?date="+print_date;
				}
				else
				{
					loc = "/order/printnextdaylabel/menu/"+menu+"/language/"+language+"/delivery_type/"+delivery_type+"/kitchen/"+kitchen_screen+"?date="+print_date;
				}
			}
			else
			{
				if(location!='')
				{
					loc = "/order/printnextdaylabel/language/"+language+"/location/"+location+"/delivery_type/"+delivery_type+"/kitchen/"+kitchen_screen+"?date="+print_date;
				}
				else
				{
					
					loc = "/order/printnextdaylabel/language/"+language+"/delivery_type/"+delivery_type+"/kitchen/"+kitchen_screen+"?date="+print_date;
				}
			}
			window.open(loc, '_blank');
		 } 
	}); 

});

        /*added by pradeep 08march17 deliveryPerson */
$(document).on("change",".deliveryBoy",function(){

		var orderNo = $(this).data('orderno');
		var date = $(this).data('date');
		var deliveryPerson = $(this).val();

		var data = {date:date,orderno:orderNo,dp:deliveryPerson};
		
		$.ajax({
			 url:"/order/update-order",
			 type: "POST",
			 data : data,
			 dataType:'json',
			 beforeSend : function(){
			 },
			 success:function(result)
			 {
				console.log(result);

			 }
		});

	});

// sankalp - added 1st april. 
 $(document).on("click","#thirdparty-book",function(){
     
     var id                 = $(this).data('id');
     var thirdparty_id      = $(this).data('tpid');
     var thirdparty         = $(this).data('thirdparty');
     
     if(thirdparty_id){
         alert('The order has already been placed');
     }else{
        $.ajax({
           url: "/order/manual-delivery",
           method: 'POST',
           data:{id:id, thirdparty: thirdparty},
           dataType:"json",
           async: false,
           success : function(result){

               if(result.code == 201){
                   
                    var orderNo = $(event.target).closest('tr').find('td:nth-child(2)').text();
     
                    alert('Order '+orderNo+' will be picked up on '+result.date+' at: '+result.time);
                    
               }else{
                   alert(result.status); 
               }
               location.reload();
           }        
       });
    }
 }); 
 
  $(document).on("click","#thirdparty-cancel",function(event){
     
     var id             = $(this).data('id');
     var thirdparty     = $(this).data('thirdparty');
     
//     var orderNo = $(event.target).closest('tr').find('td:nth-child(2)').text();
     
    $.ajax({
        url: "/order/cancel-delivery",
        method: 'POST',
        data:{id:id, thirdparty: thirdparty},
        dataType:"json",
        async: false,
        success : function(result){
           
            if(result.code == 200){
                alert('This order has been successfully cancelled.');
            }else{
//                alert(result.status);
                alert('This order has already been cancelled.');
            }
            
//            $('#getStatusModal').foundation('reveal', 'open');
//            $('#getStatusModal .lead').text(orderNo);
//            $('#bodyContent').html('');
            
        }   
        
    });
    
 }); 
 
  $(document).on("click","#thirdparty-getStatus",function(){
     
     var id             = $(this).data('id');
     var order_id       = $(this).data('order_id'); 
     var thirdparty     = $(this).data('thirdparty');
     
     $.ajax({
        url: "/order/get-delivery-status",
        method: 'POST',
        data:{id:id, thirdparty: thirdparty, order_id : order_id},
        dataType:"json",
        async: false,
        success : function(result){
            if(result.code == 200){
                var orderNo = $(event.target).closest('tr').find('td:nth-child(2)').text();
                 
                
                $('#modalTitle').text('').text(capitalizeFirstLetter(thirdparty)+': Pickup Status');
                $('#bodyContent').html('');
                
                $.each(result.status, function(key, val){
                    if(key == 'status'){ val = '<strong>'+val+'</strong>';}
                    $('#bodyContent').append('<tr><td>'+capitalizeFirstLetter(key.replace(/_/g ,' '))+'</td><td>'+val+'</td></tr>');
                })

                $('#getStatusModal').foundation('reveal', 'open');
                $('#getStatusModal .lead').text(orderNo);
            }else{
                alert('something went wrong. STATUS CODE: '+result.code);
            }
        }        
    });
 }); 
 
function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}


//todays tour
 $(document).on('click',"#printOrder",function(e){
    e.preventDefault();
    $('.printOrders').attr("data-step", "1");
    $('.printOrders').attr("data-intro", "Today's detailed order list.");
    introJs().start();
    $('.printOrders').removeAttr("data-step");
    $('.printOrders').removeAttr("data-intro");
});
 $(document).on('click',"#todayprintPack",function(e){
    e.preventDefault();
    $('.todaypack').attr("data-step", "1");
    $('.todaypack').attr("data-intro", "Total meal count to be delivered for today.");
    introJs().start();
    $('.todaypack').removeAttr("data-step");
    $('.todaypack').removeAttr("data-intro");
});
$(document).on('click',"#todaykot",function(e){
    e.preventDefault();
    $('.todaykotlist').attr("data-step", "1");
    $('.todaykotlist').attr("data-intro", "1. Total item/product count to be prepared in kitchen for today. <br/> 2. You can track how many products have been prepared. <br/> 3. It helps in packaging for a meal.");
     $('.todaykotlist').attr("data-position", "left");
     
    introJs().start();
    $('.todaykotlist').removeAttr("data-step");
    $('.todaykotlist').removeAttr("data-intro");
    $('.todaykotlist').removeAttr("data-position");
});
$(document).on('click',"#todayexportData",function(e){
    e.preventDefault();
    $('.todayexport').attr("data-step", "1");
    $('.todayexport').attr("data-intro", "click on Export button and select checkbox to download in the pdf/excel file.");
    $('.todayexport').attr("data-position", "left");
    introJs().start();
    $('.todayexport').removeAttr("data-step");
    $('.todayexport').removeAttr("data-intro");
    $('.todayexport').removeAttr("data-position");
});

// Next days tour

$(document).on('click',"#printLabels",function(e){
    e.preventDefault();
    $('.nextprintLabels').attr("data-step", "1");
    $('.nextprintLabels').attr("data-intro", "1.Print labels for next or future order dates <br/>2.Print label menuwise.");
    introJs().start();
    $('.nextprintLabels').removeAttr("data-step");
    $('.nextprintLabels').removeAttr("data-intro");
});
 $(document).on('click',"#nextKot",function(e){
    e.preventDefault();
    $('.nextKots').attr("data-step", "1");
    $('.nextKots').attr("data-intro", "1. Total products count to be delivered for future order dates. <br/> 2. You can track how many products to be delivered");
    introJs().start();
    $('.nextKots').removeAttr("data-step");
    $('.nextKots').removeAttr("data-intro");
});
$(document).on('click',"#nextprintPack",function(e){
    e.preventDefault();
    $('.nextprintPacks').attr("data-step", "1");
    $('.nextprintPacks').attr("data-intro", "1. Total meals count to be prepared for future order dates. <br/> 2.Track how many meals to be delivered.<br/> 3. It helps for packaging for a meal.");
     $('.nextprintPacks').attr("data-position", "left");
    introJs().start();
    $('.nextprintPacks').removeAttr("data-step");
    $('.nextprintPacks').removeAttr("data-intro");
    $('.nextprintPacks').removeAttr("data-position");
});
$(document).on('click',"#nextexportData",function(e){
    e.preventDefault();
    $('.nextdayexport').attr("data-step", "1");
    $('.nextdayexport').attr("data-intro", "click on Export button and select checkbox to download in the pdf/excel file.");
    $('.nextdayexport').attr("data-position", "left");
    introJs().start();
    $('.nextdayexport').removeAttr("data-step");
    $('.nextdayexport').removeAttr("data-intro");
    $('.nextdayexport').removeAttr("data-position");
});

// Preorders tour

$(document).on('click',"#editqty",function(e){
    e.preventDefault();
    var orderNo =  $('#customer').find('tbody tr:first td:eq(1) a:first').text();    
    $('#customer').find('tbody tr:first td:eq(1) a:first').attr("data-step", "1");
    $('#customer').find('tbody tr:first td:eq(1) a:first').attr("data-intro", "Click on order number");
    introJs().setOption('doneLabel', 'Next page').start().oncomplete(function() {
        window.location.href = '/order/detail/'+orderNo+'/view/preorder?action=qty&multipage=true';
    });    
    $('#customer').find('tbody tr:first td:eq(1) a:first').removeAttr("data-step");
    $('#customer').find('tbody tr:first td:eq(1) a:first').removeAttr("data-intro");
});

$(document).on('click',"#editdates",function(e){
    e.preventDefault();
    var orderNo =  $('#customer').find('tbody tr:first td:eq(1) a:first').text();
    $('#customer').find('tbody tr:first td:eq(1) a:first').attr("data-step", "1");
    $('#customer').find('tbody tr:first td:eq(1) a:first').attr("data-intro", "Click on order number");
    introJs().setOption('doneLabel', 'Next page').start().oncomplete(function() {
        window.location.href = '/order/detail/'+orderNo+'/view/preorder?action=dates&multipage=true';
    });    
    $('#customer').find('tbody tr:first td:eq(1) a:first').removeAttr("data-step");
    $('#customer').find('tbody tr:first td:eq(1) a:first').removeAttr("data-intro");
});
$(document).on('click',"#preexport",function(e){
    e.preventDefault();
    $('.export').attr("data-step", "1");
    $('.export').attr("data-intro", "click on Export button and select checkbox to download in the pdf/excel file.");
    $('.export').attr("data-position", "left");
    introJs().start();
    $('.export').removeAttr("data-step");
    $('.export').removeAttr("data-intro");
    $('.export').removeAttr("data-position");
});


</script>


 <!-- Modal -->
<div id="getStatusModal" class="reveal-modal custPopup" data-reveal aria-labelledby="modalTitle" aria-hidden="true" role="dialog">
  <h3 id="modalTitle"></h3>
  <a class="close-reveal-modal" aria-label="Close">&#215;</a>
  <div id="modalContent">
      <table class="large-12 small-12 medium-12 columns mt15" style="margin-bottom: 0">
        <tbody>
            <tr>
                <th width="75%"> Order Number: </th>
                <th width="25%" class="lead"></th>
            </tr>

            <tr>
                <td colspan="2">
                    <div style=" overflow: hidden; width: 100%; outline: none;" class="table-parent" tabindex="5000">
                        <table class="large-12 small-12 medium-12 columns mt15" style="margin-bottom: 0; width:100%">
                            <tbody id='bodyContent'>
                                
                            </tbody> 
                        </table>
                    </div>
                </td>
            </tr>

        </tbody>
      </table>
  </div>
</div>
 

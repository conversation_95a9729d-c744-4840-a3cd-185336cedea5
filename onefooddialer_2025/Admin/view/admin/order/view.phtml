<?php 
	
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	
?>

      <div id="content" class="clearfix">
        <div class="large-12 columns">

        <div class="portlet box green">
        	<div class="portlet-title">
            <h4 class="white"><i class="fa fa-table"></i>Customer Information</h4>

        </div>
        	<div class="portlet-body">
        	<table id="customerInfo" class="display">

                    <tbody>
                        <tr>
                            <td style="width:12%">Order No</td>
                            <td style="width:1%">:</td>
                            <td style="width:89%"><?php echo $orders[0]['pk_order_no']; ?></td>
                        </tr>
                        <tr>
                            <td>Order Date</td>
                            <td>:</td>
                            <td><?php echo $this->escapeHtml($utility->displayDate($orders[0]['order_date'],$setting['DATE_FORMAT']));?></td>
                           
                        </tr>
                        <tr>
                            <td>Customer Name</td>
                            <td>:</td>
                            <td><b><?php echo $orders[0]['customer_name']; ?></b></td>
                        </tr>
                        <tr>
                            <td>Contact Number</td>
                            <td>:</td>
                            <td><b><?php echo $orders[0]['phone']; ?></b></td>
                        </tr>
                        <tr>
                            <td>Email Id</td>
                            <td>:</td>
                            <td><?php echo $orders[0]['email_address']; ?></td>
                        </tr>
                        <tr>
                            <td>Group</td>
                            <td>:</td>
                            <td><?php if(isset($orders[0]['group_name']) && $orders[0]['group_name']!="0"){echo $orders[0]['group_name'];}else{ echo "N/A"; }; ?></td>
                        </tr>
                        
                        <tr>
                            <td>Order Dates</td>
                            <td>:</td>
                           
                            <td>
                            <?php 
								$days_str='';
								$days = explode(',', $orders[0]['order_days']);
								//echo "<pre>";print_r($days);die;
								
								$day_array = array();
								
								foreach($days as $day)
								{
									//$day_array[] = date('d-M Y',strtotime("January 1st +".($day)." days"));
									
									echo '<span class="blueBg white padding5">'.$day.',</span> &nbsp;';
									
								}
							
						 ?>
						 <?php 
							if(count($days)>1):
						 ?>
						 	<a data-id="<?php echo $orders[0]['order_days'];?>" data-orderid= <?php echo $orders[0]['pk_order_no']; ?> data-reveal-id="myModal" class="editOrder has-tip" data-tooltip title="Edit Your Order"><i class="fa fa-pencil-square-o"></i></a>
						 <?php endif;?>
                         </td>
                            
                        </tr>

                         <tr> 
                            <td>Ship to</td>
                            <td>:</td>
                            <td><?php echo $orders[0]['ship_address']; ?></td>
                        </tr>

                    </tbody>
                </table>
          	</div>
        </div>

        <div class="clearBoth10"></div>

        <div class="portlet box yellow">
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Order Details</h4>

        </div>
        	<div class="portlet-body">
        	<table id="productInfo" class="display" width="100%">
                    <thead>
                        <tr>
                            <th>Sr No.</th>
                            <th>Type</th>
                            <th>Product</th>
                            <th>Quantity</th>
                        </tr>
                    </thead>

                    <tbody>
		             <?php   $i=0; foreach ($orders as $order){ ?>
		              <tr>
		                <td><?php echo $i= $i+1; ?> </td>
		                <td><?php //echo ucfirst($order['order_menu']);
								echo ucfirst($order['product_type']);
							?>
						</td>
		                <td><?php echo $order['name'];?></td>
		                <td><?php echo $order['quantity'];?></td>
		              </tr>
					<?php }?>
					<?php foreach ($orders1 as $order){ ?>
		              <tr>
		                <td><?php echo $i= $i+1; ?> </td>
		                <td><?php echo $order['product_type'];?></td>
		                <td><?php echo $order['name'];?></td>
		                <td><?php echo $order['quantity'];?></td>
		              </tr>
					<?php }?>

                    </tbody>
                </table>
          	</div>
        </div>

        <div class="clearBoth10"></div>

      </div>
    </div>
    <!-- END PAGE CONTAINER-->

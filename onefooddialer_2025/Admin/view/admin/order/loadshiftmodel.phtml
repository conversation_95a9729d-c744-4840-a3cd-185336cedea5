
 	<form name="frmsendnotification" id="frmsendnotification">
 	<h1 style="margin-bottom: 0">Select date to shift order </h1>
 	<div class="row mt10">
 		<div class="mb5">
 			<label> Preferred Days: <?php echo $preferredDays;?></label>
 		</div>
		<label style="display: inline;">Select date:<span class="red">*</span></label>
		<input type="hidden" name="ordDate" id="orddate"  value="<?php echo $ordDate;?>" >
		<input type="text" name="newDate" id="newdate" style="display: inline; width: 24%;">
		<input type="hidden" name="ordno" id="ordnum"  value="<?php echo $ordNo; ?>">
		<input type="hidden" name="custid" id="custid" value="<?php echo $custid; ?>" >
        <input type="hidden" name="ordstatus" id="ordstatus" value="<?php echo $ordstatus; ?>" >
        <input type="hidden" name="ordmenu" id="ordmenu" value="<?php echo $menu; ?>" >
	</div>
	<div class="right">
	
		<button id="saveords" type="button"> <i class="fa fa-file-text-o"></i> Save </button>
	</div>
	</form>
	  <a class="close-reveal-modal">&#215;</a>
	  
	
<script type="text/javascript">
var holidays = [<?php echo $holiday;?>];
var weekoffs= new Array();
var holiday_description = '<?php echo $weekOff; ?>';
var arrWeekoffs = holiday_description.split(",");

$.each(arrWeekoffs,function(key1,value1){
    weekoffs.push(parseInt(value1));
});

$("#newdate").datepicker({
	minDate:new Date("<?php echo $lDate; ?>"),

    beforeShowDay: function(date){
        show = true;
        for (var i = 0; i < holidays.length; i++) {
            if (new Date(holidays[i]).toString() == date.toString()) {show = false;}//No Holidays
        }

        $.each(weekoffs,function(index,value){
                if(date.getDay() == value){show =false;}
            });

        var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
        return display;
    },
    weekoff:weekoffs,

});

$(document).ready(function() {
	
	$(document).on('click',"#saveords",function(){
			        
        var cancelDays = $("#orddate").val();
        var newdate = $('#newdate').val();
        var ordstatus = $('#ordstatus').val();
        
        if(newdate){

            $(this).attr('disabled',true);

            var ordno = $('#ordnum').val();
            var orddate = $("#orddate").val();
            var custid = $('#custid').val();           
            var menu = $('#ordmenu').val();
            if(ordstatus == 'Rejected' || ordstatus == 'UnDelivered'){
                
                if(newdate=="" || cancelDays==null || cancelDays=="undefined"){
                    alert("Please Select Date");
                    return false;
                }
                
                $.ajax({
                    type:"POST",
                    url:"/order/loadshiftmodel",
                    data:{ordno:ordno,orddate:orddate,custid:custid,newdate:newdate},

                    beforeSend:function(){
                        $(this).hide();
                    },
                    success:function(data){
                        if(data.status){
                            alert(data.msg);
                            location.reload();
                        }else{
                            alert(data.msg);
                            location.reload();
                        }
                    },
                    complete:function(){
                        $(this).show();
                    }
                });
            }
        }           
        var ordstatus = $('#ordstatus').val();
        var date_selected = $('#newdate').val();

        if(ordstatus == 'New'){
            
            if(date_selected=="" || cancelDays==null || cancelDays=="undefined"){
                alert("Please Select Date");
                return false;
            }
            
            $.ajax({                    
                url:"/order/shiftcancelorder/"+ordno+"/"+custid+"/menu/"+menu,
                type: "POST",
                async: false,
                data : {'cancelDays[]':cancelDays,'ordno':ordno,'date_selected':date_selected},
                beforeSend:function(){ 
                    $(this).hide();
                },
                success:function(data){
                    if(data.status=="success"){
                       alert('Order shifted successfully.');                                                   
                       window.location.reload();
                    }
                    else{
                       alert("The selected order is already prepared so can not be cancelled or reordered");
                       location.reload();
                   }						
                },
                complete:function(){
                    $(this).show();
                }
            });
        }
        return false;	
    });
});
</script>
<style>
.reveal-modal {
  margin-left: -20% !important;
  width: 43% !important;
}
.ui-datepicker{
z-index:10000 !important;
}
</style>
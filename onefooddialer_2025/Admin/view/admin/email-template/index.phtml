<div id="content">
    <div class="large-12 columns">
         <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4 class=""><i class="fa fa-table"></i> Email Templates List</h4>  
            <ul class="toolOption">
            	<li>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('emailtemplate', array('action'=>'emaillog'));?>'"><i class="fa fa-eye"></i> &nbsp; Email Log</button>
                    </div>
                </li>
            	<li>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('emailtemplate', array('action'=>'templateadd'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Email Template Set</button>
                    </div>
                </li>
                <!-- <li>
                    <div class="print">
                        <button class="btn dropdown" data-dropdown="dropPrint"><i class="fa fa-print"></i>&nbsp;Print/Export</button>
                        <ul id="dropPrint" data-dropdown-content class="f-dropdown exportPrint">
                        	<li data-tooltip class="has-tip tip-top" title="Print"><a href="#"><i class="fa fa-print"></i></a></li>
                          	<li data-tooltip class="has-tip tip-top" title="Export PDF"><a href="#"><i class="fa fa-file-pdf-o"></i></a></li>
                          	<li data-tooltip class="has-tip tip-top" title="Export EXCEL"><a href="#"><i class="fa fa-file-excel-o"></i></a></li>
                        </ul>
                    </div>
                </li> -->
                
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable defaulttemplate">
                    <thead>
                        <tr>
                            <th>Email Set Name</th>
                            <th>Purpose</th>
                            <th>Default template</th>
                            <!-- <th>Actions</th> -->
                            <th>Edit</th>
                       
                        </tr>
                    </thead>
 						<tbody>
 						
                      <?php 
                      		foreach ($paginator as $emailset) {  ?>
                        <tr>
                            <td><?php $id=$emailset['pk_set_id'];?><a onClick="location.href='<?php echo $this->url('emailtemplate', array('action'=>'emaildetails','id'=>$id));?>'">
									<?php echo $this->escapeHtml($emailset['set_name']);?><input type="hidden" name="setid" id="setid" value="<?php echo $this->escapeHtml($emailset['pk_set_id']);?>"></a></td>
                            <td><?php echo $this->escapeHtml($emailset['purpose']);?></td>
                            <td><input class="isdefault" id="<?php echo $this->escapeHtml($emailset['pk_set_id']);?>" type="radio" name="default" <?php if($emailset['is_default'] == 1){echo 'checked';} ?> ></td>
                            <td>
                             <?php   if($acl->isAllowed($loggedUser->rolename,'emailtemplate','edit')) { ?> 
	                            <a href="<?php echo $this->url('emailtemplate', array('action'=>'edit', 'id' => $emailset['pk_set_id']));?>" class="btn btn5 btn_pencil5">
					         			<button class="smBtn blueBg has-tip tip-top"   data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
					       		</a>
				       		<?php }?>
				       		</td>
                            <!-- <td><input id="<?php //echo $this->escapeHtml($emailset['pk_set_id']);?>" type="checkbox" <?php //if($emailset['status'] == 1){ echo 'checked';} ?>  name="check-1" class="lcs_check" /></td> -->
                        </tr>
                       <?php } ?>
                      
                    </tbody>
 				</table>            
            </div>
         </div>
        <div class="clearBoth20"></div>
      </div>
   </div>
</div>  
<script type="text/javascript">

$(document).ready(function(e) {

	
var previd = $('input[name=default]:checked').attr('id');
	$('input[type=checkbox]').lc_switch();
	
	// on click
	
	/* $('body').delegate('.lcs_check', 'lcs-statuschange', function() {
		var status = ($(this).is(':checked')) ? 'checked' : 'unchecked';
	});
	 */	
	$(document).delegate('.lcs_switch:not(.lcs_disabled)', 'click tap', function(e) {
		
		var status = ($(this).parent().find('input').is(':checked')) ? 'checked' : 'unchecked';

		var Obj = $(this);
		
		var id = $(this).parent().find('input').attr("id");

		if(confirm("Do you really want to change status ?")){
			
			$.ajax({
				 url:"/emailtemplate/updatestatus",
				 type: "POST",
				 data: { status:status,id:id },
				 beforeSend : function(){
					
				 },
				 success:function(result)
				 {
					//console.log(Obj.attr('class'));
					
					 if(result == 1)
					 {
						 if( Obj.hasClass('lcs_on') ) {
							 
								if( !Obj.hasClass('lcs_radio_switch') ) { // not for radio
									Obj.lcs_off();
								}
							} else {
								Obj.lcs_on();	
						}
							
					 }
					
				 }
				 
			 });
		}
    });
	$(document).on('click','.isdefault',function(){

			var id = $(this).parent().find('input').attr("id");

			if(confirm("Mark selected email template as default ?")){
				
				$.ajax({
					 url:"/emailtemplate/updatedefaultset",
					 type: "POST",
					 data: {id:id },
					 success:function(result)
					 {
						//console.log(Obj.attr('class'));
						console.log(result);
						 if(result != 1)
						 {
							 window.location.reload();
								
						 }
						
					 }
					 
				 });
				previd = id;
			}
			else
			{
			    $('input:radio[id='+id+']').parent().removeClass('checked');
				$('input:radio[id='+previd+']').parent().addClass('checked');
				// window.location.reload();
			}
		});
		

});
</script>

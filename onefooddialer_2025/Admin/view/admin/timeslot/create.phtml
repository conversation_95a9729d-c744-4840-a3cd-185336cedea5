<?php
$form = $this->form;
$form->setAttribute('action', $this->url('timeslot', array('action' => 'addslots')));
$form->prepare();
?>
<div id="content">
	<?php echo $this->form()->openTag($form);?>
	<div class="large-10 columns">
		<fieldset>
			<legend>
                TIMESLOT INFO 
            </legend>
            <div class="row">
            	<div class="portlet-body" id="slots"></div>
            </div>
		</fieldset>
        <div class="large-12 columns pl0 pr0">
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
                <div class="large-8  small-8 medium-8 columns">
                    <button	type="submit" id="submitbutton" class="button	left tiny left5	dark-greenBg">Create Slots &nbsp;<i	class="fa fa-save"></i></button>
                    <button	type="button" id="cancelbutton" class="button	left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
                </div>
            </div>
        </div>		
	</div>
	<?php echo $this->formElement($form->get('backurl'));?>
	<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
</div>
<script type="text/javascript">

	$(document).ready(function() {

		var slots = <?php echo json_encode($slots); ?>;

		var displaySlots = '<table class="display displayTable">';
	        displaySlots += '<thead><tr><th>Start Time</th><th>End Time</th><th>Day</th><th>Menu</th><th>Kitchen</th><th>Action</th></tr></thead>';
	        displaySlots += '<tbody id="slot">';

	    Object.keys(slots).forEach(function(key) {	
	    	displaySlots += '<tr><td>'+slots[key]['starttime']+'</td><td>'+slots[key]['endtime']+'</td><td>'+slots[key]['day']+'</td><td>'+slots[key]['menu_type']+'</td><td>'+slots[key]['kitchen']+'</td><td><a class="delete_slot" data-slot="'+key+'" href="">X</a></td></tr>';
	    });

	    displaySlots += '</tbody></table>';

	    $("#slots").append(displaySlots);                

		$(".delete_slot").on('click', function() {
			var slot = $(this).data('slot');
			//delete slots[slot];
			slots.splice( slot, 1);
			$(this).closest('tr').remove();
			return false;
		});

		$("#submitbutton").on('click', function() {
			var url = "/timeslot/addslots";

            $.ajax({
                url     : url,
                type    :'POST',
                data    : {slots:slots},
                dataType: 'json',
                async: false,            
                beforeSend : function() {
                    //$('#'+ordno).append('processing..');
                },
                success : function(response) {
                    if(response.status=="success") {
                        document.location.href = '/timeslot',true;
                    }
                },
                error: function(xhr, status, errorThrown) {
                    console.log('Error: '+xhr.status+' '+xhr.responseText);
                }
            });			
			return false;
		});
		
	});
</script>
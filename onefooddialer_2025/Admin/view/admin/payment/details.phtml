 
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
  <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Payment Details</span></h2>
          </div>
          <!--contenttitle-->
          <?php //echo '<pre>';print_r($content);exit; ?>
          <form class="stdform txtFieldContent" action="" method="post">
            <div class="subHeader">
              <p>Order Info</p>
            </div>
            <p>
              <label>Order No :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content['order_no']); ?></label>
              </span> </p>
            <div class="subHeader">
              <p>Personal Details</p>
            </div>
            <p>
              <label>Customer Name :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content->customer_name); ?></label>
              </span> </p>
            <p>
              <label>Business Name :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content->business_name); ?></label>
              </span> </p>
            <p>
              <label>Country :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content->country); ?></label>
              </span> </p>
            <p>
              <label>City :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content->city); ?></label>
              </span> </p>
            <div class="subHeader">
              <p>Billing Details</p>
            </div>
            <p>
              <label>Billing Amount :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content->billing_amount); ?></label>
              </span> </p>
            <p>
              <label>Currency :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content->currency); ?></label>
              </span> </p>
            <p>
              <label>Tax free :</label>
              <span class="txtField">
              <label><?php echo isset($content->tx_fee)?$this->escapeHtml($content->tx_fee):0; ?></label>
              </span> </p>
            <p>
              <label>Discount :</label>
              <span class="txtField">
              <label><?php echo isset($content->discount)?$this->escapeHtml($content->discount):0; ?></label>
              </span> </p>
            <p>
              <label>Payment Method :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content->payment_method); ?></label>
              </span> </p>
            <p>
              <label>Promo Code :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content->promo_code); ?></label>
              </span> </p>
            <p>
              <label>Payment Gateway :</label>
              <span class="txtField">
              <label><?php echo $this->escapeHtml($content->payment_getaway); ?></label>
              </span> </p>
            <p>
              <label>Status :</label>
              <span class="txtField">
              <label><?php echo  $this->escapeHtml($content->status); ?></label>
              </span> </p>
            <div class="subHeader">
              <p>Cheque Info</p>
            </div>
            <p>
              <label>Cheque Info :</label>
              <span class="txtField">
              <label><?php echo isset($check_info->name)?$this->escapeHtml($check_info->name):''; ?></label>
              </span> </p>
            <p>
              <label>Cheque No :</label>
              <span class="txtField">
              <label><?php echo isset($check_info->cheque_no)?$this->escapeHtml($check_info->cheque_no):''; ?></label>
              </span> </p>
            <p>
              <label>Received Date :</label>
              <span class="txtField">
              <label><?php echo isset($check_info->received_on)?$this->escapeHtml($check_info->received_on):''; ?></label>
              </span> </p>
            <p>
              <label>Cheque Status :</label>
              <span class="txtField">
              <label><?php echo isset($check_info->status)?$this->escapeHtml($check_info->status):''; ?></label>
              </span> </p>
            <p>
              <label>Bank Info :</label>
              <span class="txtField">
              <label><?php echo isset($check_info->bank_name)?$this->escapeHtml($check_info->bank_name):''; ?></label>
              </span> </p>
          </form>
          <br clear="all" />
          <br />
        </div>
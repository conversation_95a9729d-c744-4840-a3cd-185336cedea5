<?php $utility = \Lib\Utility::getInstance();?>
	
<!-- BEGIN CONTAINER -->
<div class="page-container clearfix"> 
  
    <!-- BEGIN PAGE CONTAINER-->
    <div class="container-fluid"> 
      <!-- BEGIN PAGE HEADER-->
      
  
      <!-- END PAGE HEADER-->
      <div>
      <form name="orderconf" id="orderconf">
	      	<table style="width:100%;align:centre" id="confirmord" >
	      		<tr><th style="align:centre;width:100%" colspan="2"><h3 class="page-title text-center tiffin_name"> Lock Amount Details </h3></th></tr>
						<tr>
							<th>Amount</th><input type="hidden" name="customerwalletid" id="customerwalletid" value="<?php echo $customerwalletdetails['customer_wallet_id'];?>" />
							<td><input type="text" name="edit_lock_amt" class="amtcls" id="edit_lock_amt" value="<?php echo $customerwalletdetails['wallet_amount'];?>"/></td>
							<input type="hidden" name="hddnvaleditlockamt" id="hddnvaleditlockamt" value="<?php echo $customerwalletdetails['wallet_amount'];?>" />
							<input type="hidden" name=hdnavailbal" id="hdnavailbal" value="<?php echo $customerwalletdetails['available_balance'];?>"/>
						</tr>
						<tr>
							<th>Narration</th>
							<td><textarea name="edit_lockdiv" id="edit_lockdiv"><?php echo $customerwalletdetails['description'];?></textarea></td>
						</tr>
						<tr>
							<td>
								<div class="loaderflg">
									<button name="btnsubmit" id="btnsubmit" style="align: center;" class="button dark-greenBg" value="Update">Update</button>
									<button name="btncancel" id="btncancel" style="align: center;" class="button dark-redBg" value="cancel">Cancel</button>
								</div>
							</td>
						</tr>
	      	 </table>
      	</form>
      </div>
      
       <div class="clearfix"></div>
     	
      </div>

 	  <a class="close-reveal-modal">&#215;</a>
 	  
</div>

<script type="text/javascript">
$(document).foundation();
$(document).ready(function()
{
	$('a.custom-close-reveal-modal').click(function(){

		$('#myModal').foundation('reveal', 'close');

	});
	
	$("#edit_lock_amt").blur(function(){
		var custwalletamt = $("#edit_lock_amt").val();
		$("#edit_lockdiv").val("Lock amount Rs."+custwalletamt+" deposited by Admin Manager");
	});

	$("#btnsubmit").on("click",function(){

		if($("#edit_lock_amt").val()=="")
		{
			alert("Please enter amount");
			return false;
		}
		else if(isNaN($("#edit_lock_amt").val()))
		{
			alert("Please enter valid amount");
			$("#edit_lock_amt").val();
			$("#edit_lockdiv").val('');
			return false;
		}
		else if($("#edit_lock_amt").val()!=''){
			var totalamount=parseFloat($("#hdnavailbal").val())+parseFloat($("#hddnvaleditlockamt").val());
			if(totalamount <  parseFloat($("#edit_lock_amt").val()))
			{
				alert("Please enter lock amount less than or equal to Usable Balance");
				$("#edit_lock_amt").val('');
				$("#edit_lockdiv").val("Lock amount deposited by Admin Manager");
				return false;
			}
			else if(validatedecimal($("#edit_lock_amt").val(),"lock")==false)
			{
				$("#edit_lock_amt").val('');
				$("#edit_lockdiv").val("Lock amount deposited by Admin Manager");
				return false;
			}
			else if(validatezeros($("#edit_lock_amt").val())==false)
			{
				$("#edit_lock_amt").val('');
				$("#edit_lockdiv").val("Lock amount deposited by Admin Manager");
				return false;
			}
			if($("#edit_lockdiv").val()=='')
			{
				$("#edit_lockdiv").val("Lock amount Rs."+$("#edit_lock_amt").val()+" deposited by Admin Manager");
			}
		}

		var customerwalletid=$("#customerwalletid").val();
		var custwalletamt = $("#edit_lock_amt").val();
		var custwalletdesc = $("#edit_lockdiv").val();
		
		var formdata = $(this).serialize();
		var url = "/customer/editcustomerwallet"; 
				$.ajax({
					url 	: url,
					type 	:'POST',
					data 	: {customerwalletid:customerwalletid,custwalletamt:custwalletamt,custwalletdesc:custwalletdesc},
					async	: false,
					success	: function(response)
					{
						if(response=="success")
						{
							alert("Locked amount updated Successfully");
							$(".close-reveal-modal").trigger("click");
							window.location.reload();
						}
					}
				});
	});

	$(document).on('keyup', '.amtcls', function(e) {
	    var val=$(this).val();
	    val = val.replace(/[^0-9\.]/g,'');
	        if(val.split('.').length>2) 
	            val =val.replace(/\.+$/,"");
	        $(this).val(val);
	});

});


	$("#btncancel").click(function(e){
		$('#myModal').foundation('reveal', 'close');
		e.preventDefault();
	});




</script>

<script type="text/javascript">
function validatedecimal(amount,type)
{
	 if ((amount.indexOf('.') != -1) && (amount.substring(amount.indexOf('.')).length > 3)) {
			alert("Please  enter " + type + " amount till 2 decimal places");
			return false;
	    }
}
function validatezeros(amt)
{
	if(isNaN(amt/0)) {
		alert("Please enter amount greater than zero");
		return false;
	}
}
</script>
 <!-- popup-->

 	<form name="frmsendnotification" id="frmsendnotification">
 	<h1 style="margin-bottom: 0">Send SMS to selected <span class="num"><?php echo $count; ?></span> Customers</h1>
	<table class="large-12 small-12 medium-12 columns mt15">
		<tr>
			<th width="80%"> Message </th>
			<th width="20%"> Select  </th>
		</tr>
		<?php foreach ($sms_templates as $template){
			if( $template['template_key'] == 'firstmeal_notification')
			continue;
			$template_msg = preg_replace('/\#[a-zA-Z][a-zA-Z0-9_-]*\#/', '<input type="text" class="popuptext checkbox'.$template['sms_template_id'].'" name="var_'.$template['sms_template_id'].'[]">', $template['sms_content']);
		?>
		<tr>
			<td><?php echo $template_msg;?></td>
			<td><input type="checkbox" class='checkbox noticheck' name="<?php echo 'chk_'.$template['sms_template_id'].'[]'?>" id="<?php echo $template['sms_template_id'];?>"/></td>
		</tr>
		<?php }?>
	</table>
	<div class="right">
		<div id="inner-tab">
		</div>
		<button class="sendAll" id="send_sms_only" name="send_sms_only" type="button"> <i class="fa fa-file-text-o"></i> Send SMS </button>
	</div>
	<div class=" clearfix common-row">
		<div class="large-6 columns">
			<form method="post" name="newcustomer" action="/backorder/new-customer" class="stdform" enctype="multipart/form-data" id="newcustomer">
				<fieldset>
					<legend>
						To cancel order please select below:
					</legend>
					<div class="row">
						<div class="large-4 columns">
							<label class="inline right">Select Date<span class="red">*</span></label>
						</div>
						<div class="large-8 columns">
							<!-- <input type="text" id="cancelDate" class="inputDisabled" required="" placeholder="Date" > -->
							<input type="text" id="cancelDateFrw" class="inputDisabled" placeholder="Date"/>
						</div>
					</div>
					<div class="row">
						<div class="large-4 small-4 medium-4 columns">
							<label class="inline right">Select Order<span class="red">*</span></label>
						</div>
						<div class="large-8 small-8 medium-8 columns">
							<div class="large-4 left">
								<label for="cancell">
									<div class="radio" id="uniform-status_1">
										<span class="checked">
											<input name="order_cancel" checked type="radio" id="cancell" value="cancel">
										</span>
									</div> <span class="custom radio"></span> Cancel  </label>
							</div>
							<div class="large-8 left">
								<label for="cancel_forward">
									<div class="radio" id="uniform-status_2">
										<span>
											<input name="order_cancel" type="radio" id="cancel_forward" value="forward">
										</span>
									</div> <span class="custom radio"></span> Cancel & carry forward </label>
							</div>
						</div>
					</div>
				</fieldset>
			</form>
		</div>
	</div>
	<div class="clearBoth5"></div>	
	
	<input type="hidden" name="menu" id="menu" value="<?php echo $filter['menu'];?>" >
	<input type="hidden" name="location" id="location" value="<?php echo $filter['location'];?>" >
	<input type="hidden" name="deliverypers" id="deliverypers" value="<?php echo $filter['deliverypers'];?>" >
	<input type="hidden" name="menu" id="menu" value="<?php if(isset($filter['menu']) && !empty($filter['menu']))echo $filter['menu']; else "all"; ?>" >
	<input type="hidden" name="location" id="location" value="<?php if(isset($filter['location']) && !empty($filter['location']))echo $filter['location']; else "all"; // echo $filter['location']; ?>" >
	<input type="hidden" name="deliverypers" id="deliverypers" value="<?php if(isset($filter['deliverypers']) && !empty($filter['deliverypers']))echo $filter['deliverypers']; else "all"; // echo $filter['deliverypers']; ?>" >

	<input type="hidden" name="order_date" class="ord_date" id="order_date" value="<?php echo $filter['order_date'];?>" >
	<div class="right">
		<div id="inner-tab">
		</div>
		<!-- <button id="sendAll" id="send_sms_cancel_order" name="send_sms_cancel_order" type="button"> <i class="fa fa-file-text-o"></i> Send  SMS</button> -->
		<button class="sendAll" id="send_sms_cancel_order" name="send_sms_cancel_order" type="button"> <i class="fa fa-file-text-o"></i> Send SMS And Cancel Order</button>
	</div>
	</form>
	  <a class="close-reveal-modal">&#215;</a>
	  
<script>

$(document).foundation();

	$('a.custom-close-reveal-modal').click(function(){
	  $('#myModal1').foundation('reveal', 'close');
	
	});

	$(document).ready(function() {

                $("#send_sms_only").hide();
            	$('.sendAll').click(function(){

            		var menu = $("#menu").val();
            		var location = $("#location").val();
            		var deliverypers = $("#deliverypers").val();
            		var order_date = $(".ord_date").val();
            		
            		var odate;

            		var operation = 'send';

            		if(order_date){
            			odate = new Date($(".ord_date").val());
            		}else{
            			odate = new Date();  
            		}

            		var action = $(this).attr("id");

            		if(action == 'send_sms_cancel_order'){
                		
            			odate = $('#cancelDateFrw').val();
            			
						var subaction = $("input[name='order_cancel']:checked").val();

						if($.trim(odate) == ""){
            				alert('Please select cancellation date');
                			return false;
            			}

						if(subaction=='cancel'){

							operation = "ocancel";
							
						}else if(subaction=='forward'){

							operation = "carryFrw";
						}
						
            		}

            		if($('.noticheck:checked').size()==0){
            			
            			alert('Please Select messsage to be send');
            			return false;
            			
            		}else{
            			
            			var smsArr = [];
            			var valid = true;	
            			/**
            				Iterate all checked checkboxes
            			**/
            			$('.checkbox').each(function(){

            				if($(this).is(":checked")){
            					
            					postArray = [];
            					var templateId = $(this).attr('id');
            					var album_text = [];
            					var name = 'var_'+templateId+'[]';

            					/**
            						iterate all input with same name 
            					**/
            					 $("input[name='"+name+"']").each(function() {
            					    var value = $(this).val();
            					  	if(value==''){
            					  		valid = false;
            					  	}
            					  	
            					  	if(valid == true)
            						{
            					  		 postArray.push(value);
            					  	}	
            					  
            					});
            					/**
            						if none of the template variable is blank then only push in smsArr
            					**/
            					if(valid == true)
            					{	
            						smsArr.push({str : postArray,templateId:templateId});
            					}
            			
            				} 
            				
            				
            			});


            			/*
            			 if any of the selected template variable is blank
            			*/
            			
            			if(valid == false)
            			 {
            			  	  alert("Please fill all values");
            			 }
            			else
            			{
            				$.ajax({
            					 url:"<?php echo $this->url('customer',array('action' => 'sendNotification')); ?>",
            					 type: "POST",
//            					 async: false,
            					 
            					data : {templates:smsArr,menu:menu,location:location,deliverypers:deliverypers,order_date:odate,operations:operation,date_selected:odate},
            					
                                beforeSend:function(){
//                                    $("#sendAll").attr("disabled","disabled");
                                    
									$("#send_sms_cancel_order").hide();
                                    $("#send_sms_cancel_order").attr("disabled","disabled");
								},
								success:function(data)
            					{
            						if(data.success)
            						{	  
                						alert(data.msg);
                						$("#inner-tab").hide();
            							window.location.href ='/customer';
            						}else{
            							alert(data.msg);
            							//window.location.href ='/customer';
            						}
            					},
								complete:function(){
									$("#send_sms_cancel_order").show();
								}
            				});
            			}	

            			return false;  
            				
            			
            			}

            	});

            	$(":radio, :checkbox").uniform();

            	odate = $('#order_date').val();
            
                $("#cancelDate").datepicker({minDate:0});
                
                $(document).on('click',"#cancelDate",function(){
                    $( "#cancelDate" ).datepicker({minDate:0});
                });
                               
                $("#cancelDateFrw").datepicker({minDate:0});

                $(document).on('click',"#cancelDateFrw",function(){
                    $( "#cancelDateFrw" ).datepicker({minDate:0});
                });

                var single_date;

            	if(odate){
                	
            		var val_arr = odate.split('/');
            		console.log(val_arr); 
					var single_date  = new Date();
					single_date.setMonth(val_arr[0]-1);
					single_date.setDate(val_arr[1]);
					single_date.setYear(val_arr[2]);
					
            		//$( "#cancelDate").datepicker('setDate', single_date);
            		$( "#cancelDateFrw").datepicker('setDate', single_date);
            		//$('.ord_date').val(odate);
            		
            	}else{

            		var otime = <?php echo time()*1000;?>;
            		odate = new Date(otime);
            		var single_date  = (odate.getMonth() + parseInt(1))+"/"+odate.getDate()+"/"+odate.getFullYear();
            		var month = (odate.getMonth() + parseInt(1));
            		var day = (odate.getDate());
					var month = ( month < 10 ) ? '0'+month : month;
					var day = ( day < 10 ) ? '0'+day : day;
					single_date = month+"/"+day+"/"+odate.getFullYear();
					
            		$('.checkbox11').val(single_date+' ');
            		$('.ord_date').val(single_date);
            	}


                $('#cDateFrw').click(function(){
                    
                    selecteddate = $('#cancelDateFrw').val();

                    if(selecteddate){
                        
						$('.checkbox11').val(selecteddate+'  and order is forwarded');
						$('.ord_date').val(selecteddate);
							
                    }else{
	                	alert("Please select date");
	                	return false;
                    }
                });

                $('#ocancel').click(function(){
                    
                    selecteddate = $('#cancelDate').val();
                    if(selecteddate){
						$('.checkbox11').val(selecteddate+'  and order is cancelled');
						$('.ord_date').val(selecteddate);
							
                   }else{
	                	alert("Please select date");
	                	return false;
                    }
                });

                $("#cancelDateFrw").on('change',function(){
                    
                	var selecteddate = $(this).val();

                	var cancelOrForward = $("input:radio[name='order_cancel']:checked").val();

                	if(cancelOrForward=='forward'){
					
                		$('.checkbox11').val(selecteddate+'  and order is forwarded');

                	}else{
                		$('.checkbox11').val(selecteddate+'  and order is cancelled');
                	}
                	
                });

                $("input:radio[name='order_cancel']").on('click',function(){
					
					var selecteddate = $("#cancelDateFrw").val();

					if($.trim(selecteddate) != ''){

						var cancelOrForward = $(this).val();

						$('.ord_date').val(selecteddate);
						
						if(cancelOrForward=='forward'){
							
	                		$('.checkbox11').val(selecteddate+'  and order is forwarded');
	
	                	}else{
	                		$('.checkbox11').val(selecteddate+'  and order is cancelled');
	                	}

					}
					
                });
                   
    });
</script>
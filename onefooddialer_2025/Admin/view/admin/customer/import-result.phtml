<div id="content">
<span> <a href="/customer/import" class="right dark-grey" style="padding:0px 26px 0px 0px;"><i class="fa fa-reply"></i></a> </span>
	<div class="large-12 small-12 medium-12 columns">

	
		<div class="row">
			<div class="large-4 small-4 medium-4 columns">
				<label class="inline right">Total rows imported :</label>
			</div>
			<div class="large-8 small-8 medium-8 columns">
				<label class="inline"><strong><?php echo $totalRows;?></strong></label>
			</div>
		</div>
		<div class="row">
			<div class="large-4 small-4 medium-4 columns">
				<label class="inline right">Total valid rows success :</label>
			</div>
			<div class="large-8 small-8 medium-8 columns">
				<?php 
					if($rowCountSuccess > 0){
						$classSuccess = "green";		
					}else{
						$classSuccess = "";
					}
				?>
				<label class="inline <?php echo $classSuccess;?>" ><strong>
					<?php
						echo $rowCountSuccess;
					?></strong></label>
			</div>
		</div>
		<div class="row">
			<div class="large-4 small-4 medium-4 columns">
				<label class="inline right">Total invalid rows:</label>
			</div>
			<div class="large-8 small-8 medium-8 columns">
				<?php 
					$invalidRows = ($totalRows - $rowCountSuccess);
					if($invalidRows > 0){
						$class = "red";		
					}else{
						$class = "";
					}
				?>
				<label class="inline <?php echo $class;?>">
					<strong><?php echo ($totalRows - $rowCountSuccess);?></strong></label>
			</div>
		</div>
		
		<?php 
		if(count($importErrors) > 0){
		?>
		<div class="row">
			<div class="large-12 small-12 medium-12 columns">
				<div class="portlet box grey">
					<div class="portlet-title">
						<h4 class="white"><i class="fa fa-table"></i>Import failure for below rows : </h4>  
					</div>	
					<div class="portlet-body" style="display: block; overflow: auto; height: 400px;">
						<table id="customer" class="display displayTable">
							
							<thead>
								<tr>
									<th class="text-center">Row No.</th>
									<th>Error</th>
								</tr>
							</thead>
							
							<tbody>
								<?php 
								foreach($importErrors as $rownum=>$rowmsg){
								?>
								<tr>
									<td class="text-center"><?php echo $rownum;?></td>
									<td><?php echo $rowmsg;?></td>
								</tr>
								<?php 
								}
								?>
							</tbody>
							
						</table>
					</div>
				</div>
			</div>
		</div>
			<div class="clearBoth20"></div>
		<?php 
		}
		?>	
			
	</div>

</div>
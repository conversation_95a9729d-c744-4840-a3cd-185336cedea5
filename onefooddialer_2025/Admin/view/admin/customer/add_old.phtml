<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/form.js"></script>
<style>
.disradio label{
width:auto;
}
</style>
<script>
jQuery(document).ready(function(){
jQuery( "#datepicker" ).datepicker();
jQuery( "#datepicker1" ).datepicker();
});
</script>
<?php
$form = $this->form;
$form->setAttribute('action', $this->url('customer', array('action' => 'add')));
$form->setAttribute('class','stdform');
$form->prepare();
?>
    <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>New Customer</span></h2>
          </div>
         	 <!--contenttitle-->
          <br/>
          <form class="stdform" action="" method="post">
            <p>
              <label><?php echo $this->formLabel($form->get('customer_name')); ?></label>
              <span class="field">
             <?php  echo $this->formHidden($form->get('pk_customer_code'));
					echo $this->formElement($form->get('customer_name'));
					echo $this->formElementErrors($form->get('customer_name'),array('class' => 'red')); ?>
              </span> </p>
 			 <p>
              <label><?php echo $this->formLabel($form->get('customer_Address')); ?></label>
              <span class="field">
             <?php  echo $this->formElement($form->get('customer_Address'));
					echo $this->formElementErrors($form->get('customer_Address'),array('class' => 'red')); ?>
              </span> </p>
               <p>
              <label><?php echo $this->formLabel($form->get('company_name')); ?></label>
              <span class="field">
             <?php  echo $this->formElement($form->get('company_name'));
					echo $this->formElementErrors($form->get('company_name'),array('class' => 'red')); ?>
              </span> </p>
			  <p>
              <label><?php echo $this->formLabel($form->get('phone')); ?></label>
              <span class="field">
             <?php  echo $this->formElement($form->get('phone'));
					echo $this->formElementErrors($form->get('phone'),array('class' => 'red')); ?>
              </span> </p>

               <p>
              <label><?php echo $this->formLabel($form->get('email_address')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('email_address'));
				echo $this->formElementErrors($form->get('email_address'),array('class' => 'red'));
				?>
              </span> </p>

               <p>
              <label><?php echo $this->formLabel($form->get('location_code')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('location_code'));
				echo $this->formElementErrors($form->get('location_code'),array('class' => 'red'));	?>
              </span> </p>

                 <p>
              <label><?php echo $this->formLabel($form->get('group_code')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('group_code'));
				echo $this->formElementErrors($form->get('group_code'),array('class' => 'red'));	?>
              </span> </p>

               <p>
              <label><?php echo $this->formLabel($form->get('registered_on')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('registered_on'));
				echo $this->formElementErrors($form->get('registered_on'),array('class' => 'red'));	?>
              </span> </p>

                <p>
              <label><?php echo $this->formLabel($form->get('status')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('status'));
				echo $this->formElementErrors($form->get('status'),array('class' => 'red'));
				echo $this->formElement($form->get('csrf')); ?>
              </span> </p>

            <p class="stdformbutton">
				<?php echo $this->formSubmit($form->get('submit')); ?>
				<?php echo $this->formSubmit($form->get('cancel')); ?>
            </p>

            <p>
              <span class="field">
            	 <?php echo $this->formElement($form->get('backurl'));
				 ?>
              </span> </p>
          </form>
          <br clear="all" />
          <br />
        </div>
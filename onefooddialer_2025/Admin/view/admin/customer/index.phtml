<?php 
$utility = \Lib\Utility::getInstance();
	
?>
<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                     <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addCustomer" class="common-orange-btn-on-hover"> Add Customer </a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="sendSms" class="common-orange-btn-on-hover">Cancel bulk orders</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="editCust" class="common-orange-btn-on-hover"> Edit Customer</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="walletAD" class="common-orange-btn-on-hover">Wallet Add/Debit</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="placeOrder" class="common-orange-btn-on-hover"> Place order </a>
                </li>
            </ul>
        </div>
    </div>
</div>  
      <div id="content" class="clearfix">
   
        <div class="large-12 columns">
       	
            <div class="filter">
                <form class="advance_search" id="filterFrm" style="display:block" name="filterFrm" action="/customer" method="post"/>
                <form class="advance_search" id="filterFrm" name="filterFrm">
                    <div class="row">
                        <div class="medium-12 columns">
                            <div class="type left">
                                <select name="menu" id="menu" class="left filterSelect">
                                    <option value="">Select Menu Type</option>
                                    <option value="all">All</option>
                                    <?php 
                                        foreach($this->menus as $menu){
                                    ?>
                                    <option value="<?php echo $menu;?>"><?php echo ucfirst($menu);?></option>
                                    <?php 
                                    }
                                    ?>
                                </select>
                                      
                                    <select class="left filterSelect" name="location" id="location">
                                            <option value="">Select Delivery Location</option>
                                            <option value="all">All</option>
                                    <?php 
                                            foreach($this->locations as $key=>$val)
                                            {
                                    ?>
                                                <option value="<?php echo $val['pk_location_code'];?>"><?php echo $val['location'];?></option>
                                    <?php 
                                            }
                                    ?>
                                    </select>
	                                      
                                        <select class="left filterSelect" name="deliverypers" id="deliverypers">
                                            <option value="">Select Delivery Person</option>
                                            <option value="0">All</option>
                                            <?php 
                                            foreach($this->deliverypersons as $key=>$val)
                                            {
                                            ?>
                                                <option value="<?php echo $val['pk_user_code'];?>"><?php echo $val['first_name']." ".$val['last_name'];?></option>
                                            <?php 
                                            }
                                            ?>
                                        </select>
	                                      
                                            <select class="left filterSelect" name="statuschange" id="statuschange">
                                                <option value="">Select Status</option>
                                                <option value="all">All</option>
                                                <option value="1">Active</option>
                                                <option value="0">Inactive</option>

                                            </select>
                                                <input id="order_date" style="width: 200px;" class="left" type="text" value="" placeholder="Select Order Date"  name="order_date">
										 
                                                    <button id="selectedcust" name="selectedcust" class="button left tiny left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
                                                </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
		        
        <div class="clearBoth10"></div>
       <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box info round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>		
 
 		 <div class="filter">
              <?php $filter = $this->filter; ?>
			  <?php $filter->setAttribute('action', $this->url('customer', array('action' => 'exportData'))); ?>
			  <?php //$filter->setAttribute('class','stdform'); ?>
			  <?php $filter->prepare(); ?>
			  <?php echo $this->form()->openTag($filter); ?>
				<input type="hidden" name="subaction" id="subaction" value="" />
				<input type="hidden" name="service" id="service" value="customer" />
				<input type="hidden" name="exportUrl" id="exportUrl" value="customers" />
            <?php echo $this->form()->closeTag($filter) ?>

            <form action="/report/orders" name="searchForm" id="searchForm" method="post" >
				<input type="hidden" name="subaction1" id="subaction1" value="" />
				<input type="hidden" name="service1" id="service1" value="customer" />
            </form>

          </div>
 
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Customers</h4>  
            <ul class="toolOption">
            	<li>
            	<?php if($acl->isAllowed($loggedUser->rolename,'customer','add')){  ?>
                <div class="addRecord" id="addCust" >
                	<button class="btn" onclick="location.href='<?php echo $this->url('customer', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Customer</button>
                 </div>
                <?php } ?>
                </li>
                 <?php 
                    if($utility->checksubscription('custom_sender_id','allowed')){                       
                 ?>
                    <li>
                    <div class="addRecord" id="sendSms" >
                        <a href="<?php echo $this->url('customer',array('action' => 'sendNotification'))?>"  id="sendnotification"><button class="btn greenBg"><i class="fa fa-chevron-circle-right"  ></i>  &nbsp;Send SMS</button></a>
                    </div>
                       <div id="myModal1" class="reveal-modal" data-reveal>
                    </li>
                <?php
                    }
                ?>
            	
                <li>
                  <div class="addRecord print" >
	                    <button class="btn  columnModal" data-id="customers" data-exporttype="xls" data-dropdown="dropPrint" ><i class="fa fa-share"></i>&nbsp;Export</button>
<!-- 	                   	 <ul id="dropPrint" data-dropdown-content class="f-dropdown"> -->
	                      <!-- <li style="display:none;" data-tooltip class="has-tip tip-top columnModal"  data-id="customers" data-exporttype="pdf" title="Export PDF"><a href="javascript:void(0);"  id="exportPDF"><i class="fa fa-file-pdf-o" ></i></a></li> -->
	                     <!--  <li data-tooltip class="has-tip tip-top columnModal"  data-id="customers" data-exporttype="xls" title="Export XLS"><a href="javascript:void(0);"  id="exportXLS"><i class="fa fa-file-excel-o" ></i></a></li> -->
<!-- 	                    </ul> -->
                  </div>
                  <div id="myModal" class="reveal-modal custPopup"  data-reveal>
                </li>
                <li>
                    <div class="addRecord">
                        <a class="import" href="/customer/import">
                            <i class="fa fa-reply"></i>&nbsp; Import
                        </a>
                    </div>
                </li>
            </ul>
        </div>        
        <div class="portlet-body sales_data_table">

                <div class="filter">
                    <div>
                        <a class="advance_search_click">Hide advance Search </a>
                    </div>
                </div>

                <table id="customer" class="display displayTable" width="100%">
                    <thead>
                        <tr>
                            <th>Customer Code</th>
                            <th>Customer Name</th>
                            <!-- <th>Address</th> -->
                            <th>Phone</th>
                            <th>Email</th>
                            <!-- <th>Location</th> -->
                            <!-- <th>Group</th> -->
                            <th>Registered On</th>
                           <!--  <th>Register From</th> -->
                            <th>Status</th>
                            <th id="custaction">Action</th>
                        </tr>
                    </thead>
    
                   
                </table>          
            </div>
        </div>
        
        <div class="clearBoth20"></div>
        
      </div>
    </div>
    <?php 
        $client_redirection = ($client_redirection == 'yes' ? '/customers/clientredirect' : '/customers/createsession');
    ?>
  	<form id="frontlogin5" class="stdform" name="frontlogin" method="post" action="<?php echo $client_redirection; ?>" target="_blank"> 
        <input type="hidden" value="" id="phone" name="phone">
        <input type="hidden" value="1" id="custom" name="custom">	
        <input type="hidden" value="" id="name" name="name">
        <input type="hidden" value="<?php echo $client_web_url; ?>" id="client_web_url" name="client_web_url">	
        <input type="hidden" value="admin" id="loggedin" name="loggedin">	
    </form>
  	<script type="text/javascript">

// some hardcoded value was being passed to phone. Couldnt find the bug. Patched.
function getUser(phone,loggedin,name, obj){
    
//        console.log($(obj).attr('data-phone')); debugger;
				
//	$("#phone").val(phone);
        $("#phone").val($(obj).attr('data-phone'));
        $("#loggedin").val(loggedin);
        $("#name").val(name); 
        $("#frontlogin5").submit();
       
}
//                function getUser(phone,loggedin,name, mailverified){
//			if(mailverified == 'no')
//			{
//				alert("Customer not verified");
//				return false;
//			}
//
//			else
//			{		
//				$("#phone").val(phone);
//				$("#loggedin").val(loggedin);
//				$("#name").val(name); 
//				$("#frontlogin5").submit();
//			}
//		}
  	</script>
    <script type="text/javascript">
    $(document).ready(function() {
        $("#send_sms_cancel_order").hide();
        <?php /*
        	var customerbackend = "<?php echo (isset($_SESSION['flag']))?$_SESSION['flag']:'';?>";
        	var chkphone = "<?php echo (isset($_SESSION['chkphone']))?$_SESSION['chkphone']:''; ?>";
			var chkrolename = "<?php echo (isset($_SESSION['Zend_Auth']->storage->rolename))? $_SESSION['Zend_Auth']->storage->rolename:''; ?>";
			var customer_name = "<?php echo (isset($_SESSION['Zend_Auth']->storage->firstname))? $_SESSION['Zend_Auth']->storage->firstname:''; ?>.' '.<?php echo  (isset($_SESSION['Zend_Auth']->storage->lastname))?$_SESSION['Zend_Auth']->storage->lastname:'';?>";
			var last_part = window.location.href.split("/").pop();
			
        	if(customerbackend=="new-customer-backend" && last_part!='customer')
        	{
				$(this).attr('target','_blank');
				url = "/customers/createsession?phone="+chkphone+"&role="+chkrolename+"&name="+customer_name;
				window.open(url, '_blank');
        	}
        	
         		if(customerbackend=="new-customer-backend")
        	{
				$(this).attr('target','_blank');
				url = "/customers/createsession?phone="+chkphone+"&role="+chkrolename+"&name="+customer_name;
				//alert(url);
				window.open(url, '_blank');
        	} 
        	*/ ?>
        	
        	/*suraj changes end*/

    	    var now = new Date();
    	 	var curyear=now.getFullYear();
    	 	var month=now.getMonth() + 1;
    	 	var date=now.getDate();
    	 	var CurrentMonth = month + '/' + date + '/' + curyear;

    		$("#order_date").datepicker({minDate:CurrentMonth});

    		$(document).on('click',"#order_date",function(){
    			$( "#order_date" ).datepicker({minDate:CurrentMonth});
    		});

    		//myPageTable.init();

    	var customerTable = $('#customer').dataTable( {
            "processing": true,
            "serverSide": true,
            "ajax": {
            	"url":"/customer/ajx-customer",
            	"data": function ( d ) {
                    d.menu = $("#menu option:selected").val();
                    d.location = $("#location option:selected").val();
					d.deliverypers = $("#deliverypers option:selected").val();
					d.order_date =  $("#order_date").val();
					d.status = $("#statuschange").val();

             	}	
			},
			"aaSorting": [[0,'desc']],
            "aoColumnDefs": [
            	                {
            	                   bSortable: false,
            	                   aTargets: [ -1 ]
            	                }
            ],
        });

    	//customerTable.fnSort( [ [0,'desc']] );

    	$(document).on("click","#sendnotification",function(){

//     		var menu = $("#menu option:selected").val();
//             var location = $("#location option:selected").val();
// 			var deliverypers = $("#deliverypers option:selected").val();
// 			var order_date =  $("#order_date").val();

    		var menu = (($("#menu option:selected").val()) ? $("#menu option:selected").val() : "all");
            var location = (($("#location option:selected").val()) ? $("#location option:selected").val() : "all"); //$("#location option:selected").val();
			var deliverypers = (($("#deliverypers option:selected").val()) ? $("#deliverypers option:selected").val() : "all"); // $("#deliverypers option:selected").val();
			var order_date =  $("#order_date").val();

// 			alert(deliverypers+"->>>>>"+menu+"->>>>>"+location+"->>>"+deliverypers+"->>>>"+order_date); return false;
			
		    var parts = order_date.split('/');
		    var dmyDate = parts[2] + '-' + parts[0] + '-' + parts[1];

			var urlpath='';
			if(order_date !="")
			{
				 urlpath =  '/customer/sendNotification/menu/'+menu+'/location/'+location+'/deliverypers/'+deliverypers+'/order_date/'+dmyDate;
			}
			else
			{
				 urlpath =  '/customer/sendNotification/menu/'+menu+'/location/'+location+'/deliverypers/'+deliverypers;
			}

			$('#myModal1').foundation('reveal', 'open', {
			    url: urlpath,
			});
			return false;
		});


        $(document).on('click','#selectedcust',function(){

			customerTable.api().ajax.reload();
   	
        });

        $(document).on("click",".columnModal",function(){
    		var table = $(this).data('id');
    		var exporttype = $(this).data('exporttype');
    		var datastring0 = $("#filter_form").serialize();
                var datastring1 = $("#filterFrm").serialize();
                var datastring = datastring0+'&'+datastring1;
    		console.log(datastring);
    		$('#myModal').foundation('reveal', 'open', {
    		    url: '/customer/exportData',
    		    data: {table: table,form:datastring,exporttype:exporttype}
    		});
    		return false;
    	}); 

        $(document).on('click','#export',function(e){
    		e.preventDefault();
    		if(document.querySelectorAll('input[type="checkbox"]:checked').length==0){
    			alert('Please select field to export.');
    			return false;
    		}
    		else
    		{	
    			var values =[]; 
    			var type = $("input[name=export_type]:checked").val();
    			values = $('.checkbox:checked.checkbox').map(function () {
    				  return this.id;
    			}).get();
    			if(type=='pdf' && values.length>8){
    				alert("You can select maximum 8 columns");
    				return false;
    			}
    		    var fromTable = $("#table").val();
    			$("#selected_columns").val(values.join());
    			$("#exportForm").attr("target", "_blank");
    			$("#exportForm").submit();
    			return false;
    		}
    	});
    	$("#exportReportList").on('click',function(e){
  		  e.preventDefault();
  		  $("#subaction").val("export");
  		  $("#filter_form").attr("action","/customer/export-pdf-save");
  		  $("#filter_form").attr("target","_blank");
  		  $("#filter_form").submit();
  	  });
  });
    </script>

  <script type="text/javascript">
       
    $(document).on('click',"#addCustomer",function(e){
        e.preventDefault();
        $('.portlet-title').find('#addCust').attr("data-step", "1");
        $('.portlet-title').find('#addCust').attr("data-intro", "Click here to add customer.");
        introJs().start();
        $('.portlet-title').find('#addCust').removeAttr("data-step");
        $('.portlet-title').find('#addCust').removeAttr("data-intro");
       
    });

    $(document).on('click',"#sendSms",function(e){
        e.preventDefault();
        //$('.advance_search_click').click();
        $('.portlet-title').find('#sendSms').attr("data-step", "1");
        $('.portlet-title').find('#sendSms').attr("data-intro", "1.Search the customers based on filters.<br/> 2. Select sms to cancel/carry forward order.<br/> 3.Click on Send SMS button.");
        introJs().start();
        $('.portlet-title').find('#sendSms').removeAttr("data-step");
        $('.portlet-title').find('#sendSms').removeAttr("data-intro");
    });

    $(document).on('click',"#editCust",function(e){
        e.preventDefault();
        var custid = $('.displayTable').find('tbody tr:first td:first').text();
        $('.displayTable').find('tbody tr:first td:eq(6) button:first').attr("data-step", "1");
        $('.displayTable').find('tbody tr:first td:eq(6) button:first').attr("data-intro", "Edit customer details");
        introJs().start();
        $('.displayTable').find('tbody tr:first td:eq(6) button:first').removeAttr("data-step");
        $('.displayTable').find('tbody tr:first td:eq(6) button:first').removeAttr("data-intro");
    });
   
    $(document).on('click',"#walletAD",function(e){
        e.preventDefault();
        var custid = $('.displayTable').find('tbody tr:first td:first').text();
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(2)').attr("data-step", "1");
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(2)').attr("data-intro", "1. Add amount to wallet <br/> 2.To deduct amount manually.");
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(2)').attr("data-position", "left");
        introJs().start();
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(2)').removeAttr("data-step");
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(2)').removeAttr("data-intro");
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(2)').removeAttr("data-position");
    });
  
    $(document).on('click',"#placeOrder",function(e){
        e.preventDefault();
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(3)').attr("data-step", "1");
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(3)').attr("data-intro", "Place an order behalf of customer<br/>* Choose meal<br/>* Choose Plan<br/>* Select Payment mode<br/>* Place an order");
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(3)').attr("data-position", "left");
        introJs().start();
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(3)').removeAttr("data-step");
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(3)').removeAttr("data-intro");
        $('.displayTable').find('tbody tr:first td:eq(6) button:eq(3)').removeAttr("data-position");
    });

</script>

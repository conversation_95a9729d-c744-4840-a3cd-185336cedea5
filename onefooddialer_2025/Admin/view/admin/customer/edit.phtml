 <?php
  //echo "asdas<pre>"; print_r($customer); exit();
  $utility = \Lib\Utility::getInstance();
 ?>
 <style>
	.statuscls label{float:left; width:50%}
</style>

      <div id="content">
        	<div class="large-6 columns">
        	<?php 
	        	if(!empty($error_message))
				{
					/* foreach($error_message as $errkey=>$errval)
					{
						echo '<div class="alert-box alert">'.$errval.'</div>';
					} */
				}
			?>
          	<?php echo $this->form()->openTag($form);?>

		<fieldset>
        	<legend>Customer Info</legend>
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('customer_name')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">
                  
					   <?php  
		                    echo $this->formHidden($form->get('pk_customer_code'));
							echo $this->formElement($form->get('customer_name'));
							
							echo $this->formElementErrors()

							->setMessageOpenFormat('<small class="error">')
							->setMessageCloseString('</small>')
							->render($form->get('customer_name'));
							
						?> 
                  	</div>
              	</div>
              	
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('company_name')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">
                     <?php
                     	echo $this->formElement($form->get('company_name'));
						echo $this->formElementErrors($form->get('company_name'),array('class' => 'red'));
					 ?>
                  	</div>
              	</div>
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('phone')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">
	                 <?php
	                     echo $this->formElement($form->get('phone'));
						 echo $this->formElementErrors($form->get('phone'),array('class' => 'red'));
					 ?>
                  	</div>
              	</div>
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('email_address')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">
                      <?php
                      	echo $this->formElement($form->get('email_address'));
						echo $this->formElementErrors($form->get('email_address'),array('class' => 'red'));
					  ?>
                  	</div>
              	</div>
                
              	
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('city')); 'selected' ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
                        echo $this->formElement($form->get('city'));
						            echo $this->formElementErrors($form->get('city'));	
					            ?>             
                  	</div>
              	</div>
              	
              	
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('registered_on')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">
                      <?php
                      	echo $this->formElement($form->get('registered_on'));
                      echo $this->formElementErrors($form->get('registered_on'),array('class' => 'red'));
                      ?>
                  	</div>
              	</div>
              	
              	<?php if(strtolower($thirdparty) =="yes"){ ;?>
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('thirdparty')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
		                     echo $this->formElement($form->get('thirdparty'));
							           echo $this->formElementErrors($form->get('thirdparty'));
						          ?>         
                  	</div>
              	</div>
              	<?php }?>
              	
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('food_preference')); ?></span></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                        <?php 
                        	echo $this->formElement($form->get('food_preference'));
				 			            echo $this->formElementErrors($form->get('food_preference'));
                        ?>        
                  	</div>
                  
              	</div>
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></span></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns statuscls">
                     <?php
		                  echo $this->formElement($form->get('status'));
						          echo $this->formElementErrors($form->get('status'),array('class' => 'red'));
						          echo $this->formElement($form->get('csrf'));
					           ?>
                  	</div>
              	</div>

                <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                      <label class="inline right"><?php echo $this->formLabel($form->get('isguest')); ?></span></label>
                    </div>
                    <div class="large-8 small-8 medium-8 columns statuscls">
                     <?php
                      echo $this->formElement($form->get('isguest'));
                      echo $this->formElementErrors($form->get('isguest'),array('class' => 'red'));
                      echo $this->formElement($form->get('csrf'));
                     ?>
                    </div>
                </div>                
              	
		</fieldset>
		
		<div class="clearfix mb15"></div>
              
           <fieldset>
            	<legend>Delivery Location</legend>
            	<div class="row">	              	
              	<?php 
              	$dabbawala_code_str = ($this->dabbawala_code_type=='') ? "text" : $this->dabbawala_code_type
              	?>
              	
              	<?php 
              		//echo "asdas<pre>"; print_r($menus); exit();
					foreach($menus as $key=>$val)
					{
						$class="";
                        if(!empty($selected_menu_arr)){
                            foreach($selected_menu_arr as $selkey=>$selval)
                            {

                                if($val==$selval)
                                //if(array_key_exists($errkey,$menus))
                                {
                                    $class="checked";
                                    break;
                                }

                            }
                        }    
						?>
						<div class="add-customer-checkbox">
							<input type="checkbox" data-id="<?php echo $val;?>" value="<?php echo $val;?>" class="icheck" id="idmenus_<?php echo $key;?>" name="menus[]" <?php echo $class; ?> data-show=".<?php echo $val;?>">
							<label class="loc" for="idmenus_<?php echo $key;?>"><?php echo strtoupper($val);?></label>	
						</div>
						
								
				<?php }?>
				</div>
			
						<div class="clearfix mb15"></div>
						<div class="row">
						<?php if(isset($error_message['menu_error']) && ($error_message['menu_error']!=""))
						{?>
							<small class="error"><?php echo $error_message['menu_error'];?></small>
						<?php }?>	
						</div>
						
								<div class="row cpyAddress" id="divcpyaddr" name="divcpyaddr">
											<div class="checkbox terms checkbox-parent add-customer-checkbox">
											<?php /*<input type="checkbox" class="icheck" id="idcpyaddress" name="idcpyaddress">*/ ?>
											<?php echo $this->formElement($form->get('idcpyaddress')); ?>
											
											<label for="idcpyaddress"><span class="">Copy below address for other locations.</span></label>
											
											</div>
											
								</div>	
								<div class="clearfix"> </div>
				
				<?php 
											//echo "new error<pre>"; print_r($new_arr_menu_err); exit();
											$countfordiv=1;
											
											$error_message = '';
											$error_location = '';
											$error_address = '';
											$location_code = '';
											$location_address ='';
											$delivery_person = '';
											$dabawala_type = '';
											$dabawala_image = '';
											$dabawala_text = '';
											if(!empty($menus) && !empty($new_arr_menu_err)){
                        foreach($menus as $key=>$val)
                        {
                            $error_message="";
                            foreach($new_arr_menu_err as $errkey=>$errval)
                            {
                                if($errkey==$key)
                                {

                                    $error_message = (isset($errval['error']))?$errval['error']:'';
                                    $error_location = (isset($errval['error_location']))?$errval['error_location']:'';
                                    $error_address = (isset($errval['error_address']))?$errval['error_address']:"";
                                    $location_code = (isset($errval['location_code']))?$errval['location_code']:'';
                                    $location_address = (isset($errval['location_address']))?$errval['location_address']:'';
                                    $delivery_person = (isset($errval['delivery_person']))?$errval['delivery_person']:'';
                                    $dabawala_type = (isset($errval['dabbawala_code_type']))?$errval['dabbawala_code_type']:'';
                                    $dabawala_image = (isset($errval['dabbawala_image']))?$errval['dabbawala_image']:'';
                                    $dabawala_text = (isset($errval['dabbawala_code']))?$errval['dabbawala_code']:'';
                                    break;
                                }
                            }
                            $style="none";
                            if(!empty($selected_menu_arr)){
                                foreach($selected_menu_arr as $selkey=>$selval)
                                {
                                    if($val==$selval)
                                    //if(array_key_exists($errkey,$menus))
                                    {
                                        $style="block";
                                        break;
                                    }
                                    //echo "data<pre>"; print_r($selval);
                                }
                            }    
                        ?>

                        <div class="<?php echo $val;?> clsmenus" data-id="<?php echo $val;?>" style="display: <?php echo $style;?>">
                          <fieldset>
                            <legend><?php echo ucwords($val);?></legend>
                            <?php 
                            //if($countfordiv==2)
                            //{
                            ?>

                            <?php  
                            //}?>

                                <div class="row">
                                    <div class="large-4 small-4 medium-4 columns">
                                        <label class="inline right"><?php echo ucwords($val);?> Location<span class="red">*</span></label>
                                    </div>
                                    <div class="large-8  small-8 medium-8 columns" id="div_location_<?php echo $val;?>" data-id="<?php echo $val;?>"> 
                                        <?php
                                            //	echo  $style;
                                                if($style=="block")
                                                {
                                                    echo $this->formElement($form->get('location_code[]')->setValue($location_code));
                                                }
                                                else
                                                {
                                                    echo $this->formElement($form->get('location_code[]')->setValue(""));
                                                }
                                         ?>
                                         <small class="error"><?php echo $error_location;?></small>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="large-4 small-4 medium-4 columns">
                                    <label class="inline right"><?php echo ucwords($val);?> Address<span class="red">*</span></label>
                                    </div>
                                    <div class="large-8 small-8 medium-8 columns" id="div_address_<?php echo $val;?>">      
                                    <?php // echo $this->formElement($form->get('location_address[]')->setValue($location_address));?>
                                    <?php if($style=="block")
                                        {
                                    ?>
                                        <textarea class="form-control locationaddrcopycls" rows="5" cols="60" id="idloccode" placeholder="ENTER  DELIVERY ADDRESS (<?php echo strtoupper($val);?>)" name="location_address[]"><?php echo $location_address;?></textarea>
                                    <?php }
                                    else {?>
                                            <textarea class="form-control locationaddrcopycls" rows="5" cols="60" id="idloccode" placeholder="ENTER  DELIVERY ADDRESS (<?php echo strtoupper($val);?>)" name="location_address[]"></textarea>
                                    <?php } ?>
                                    <small class="error"><?php echo $error_address;?></small>
                                    </div>
                                </div>

                                <div class="row"> 
                                    <div class="large-4 small-4 medium-4 columns">
                                        <label class="inline right"><?php echo ucwords($val);?> Delivery Person</label>
                                    </div>
                                    <div class="large-8  small-8 medium-8 columns future_delivery sel_cls_<?php echo $val;?>" id="div_delivery_<?php echo $val;?>">              
                                      <?php 
                                      //	echo  $style;
                                        if($style=="block")
                                        {
                                            echo $this->formElement($form->get('delivery_person[]')->setValue($delivery_person));
                                        }
                                        else
                                        {
                                            echo $this->formElement($form->get('delivery_person[]')->setValue(""));
                                        }
                                     ?>             
                                    </div>
                                </div>

                                <div class="row"> 
                                    <div class="large-4 small-4 medium-4 columns">
                                        <?php /* <input style="float:right" class="future_change" type="checkbox" name="future[]" value="<?php echo $val;?>"> */?>
                                    </div>
                                    <div class="large-8  small-8 medium-8 columns sel_cls_<?php echo $val;?>">              
                                        <input style="float:right" class="future_change" type="checkbox" name="future[]" value="<?php echo $val;?>"><span style="color:#4d4d4d" >Check if want to apply this address for placed order.</span>
                                    </div>
                                </div>
                                <div class="clearfix mb10"></div>
                                <div class="row">
                                    <div class="large-4 small-4 medium-4 columns">
                                        <label class="inline right"><?php echo ucwords($val);?> Dibbawala Code</label>
                                    </div>
                                    <div class="large-8 small-8 medium-8 columns">                
                                          <div class="large-4 left">
                                              <?php //echo 111222; echo $dabawala_type; ?>
                                              <label for="dabbawala_code_type_1">
                                                <input <?php echo ($dabawala_type=='text') ? "checked" : "" ;?> class="clsdabbawala" data-id="<?php echo $val;?>_text" name="dabbawala_code_type[<?php echo $val;?>]" type="radio" id="dabbawala_code_type_<?php echo $val;?>" value="text">
                                                 <span class="custom radio"></span> Add Text
                                              </label>
                                         </div>
                                         <div class="large-4 left">
                                             <label for="dabbawala_code_type_2">
                                                <input <?php echo ($dabawala_type=='image') ? "checked" : "" ;?> class="clsdabbawala" data-id="<?php echo $val;?>_image"  name="dabbawala_code_type[<?php echo $val;?>]" type="radio" id="dabbawala_code_type_<?php echo $val;?>"  value="image">
                                                 <span class="custom radio"></span> Add Image
                                             </label>
                                        </div>
                                        <?php if($dabawala_type=='image' && $dabawala_image!=""){?>
                                        <img id="imgsrc_<?php echo $val;?>" src="<?php echo $GLOBALS['http_request_scheme'].$dabawala_image; ?>" style="width:100px;height:100px;" />
                                        <?php } ?>
                                    </div>
                                    <div class="clearfix mb15"></div>
                                </div>
                                <?php 
                                    $style_display_text="";
                                    $style_display_image="";
                                    if($dabawala_type=='text')
                                    {
                                        $style_display_text = "style=display:block;";
                                        $style_display_image = "style=display:none;";
                                    }
                                    else
                                    {
                                        $style_display_text = "style=display:none;";
                                        $style_display_image = "style=display:block;";
                                    }
                                    //echo "text--".$style_display_text."--"."image--".$style_display_image; echo "<br>";
                                ?>

                                <div class="row" id="div_dabbawala_code_text<?php echo $val;?>" <?php echo $style_display_text;?> >
                                    <div class="large-4 small-4 medium-4 columns">
                                        <label class="inline right"><?php //echo $this->formLabel($form->get('dabbawala_code')); ?></label>
                                    </div>
                                    <div class="large-8 small-8 medium-8 columns">  <?php //echo "text".$dabawala_text;?>              
                                        <input type="text" class="form-control" id="dabbawala_text_<?php echo $val;?>" placeholder="Please enter dibbawala code for <?php echo $val;?>" value="<?php echo $dabawala_text;?>" name="dabbawala_text[<?php echo $val;?>]">
                                    </div>
                                </div> 

                                <div class="row" id="dabbawala_code_image<?php echo $val;?>" <?php echo $style_display_image;?> >
                                    <?php //echo "image".$dabawala_image;?>  
                                    <div class="large-4 small-4 medium-4 columns">
                                        <label class="inline right"><?php //echo $this->formLabel($form->get('dabbawala_image')); ?></label>
                                    </div>
                                    <div class="large-8 small-8 medium-8 columns">           
                                    <input class="smallinput" type="file" id="dabbawala_image_<?php echo $dabawala_image;?>" accept="image/*" name="dabbawala_image[<?php echo $val;?>]">  
                                     <?php
                                     $imageDir = $_SERVER['DOCUMENT_ROOT']."/data/";  
                                     ?>
                                    </div>
                                </div>

                                <?php if($error_message!=""){?>
                                <!-- <small class="error"><?php //echo $errval['error'];?></small> -->
                                <?php }?>
                                </fieldset>
                            </div>
                        <?php
                        $countfordiv++;
                    }//End of foreach  
                }//End of if    
                else {
                  $style = 'none';
                  foreach($menus as $key=>$val) {
                ?>
                    <div class="<?php echo $val;?> clsmenus" data-id="<?php echo $val;?>" style="display: <?php echo $style;?>">
                      <fieldset>
                        <legend><?php echo ucwords($val);?></legend>
                                <div class="row">
                                  <div class="large-4 small-4 medium-4 columns">
                                      <label class="inline right"><?php echo ucwords($val);?> Location<span class="red">*</span></label>
                                  </div>
                                  <div class="large-8  small-8 medium-8 columns" id="div_location_<?php echo $val;?>" data-id="<?php echo $val;?>"> 
                                      <?php
                                          //  echo  $style;
                                              if($style=="block")
                                              {
                                                  echo $this->formElement($form->get('location_code[]')->setValue($location_code));
                                              }
                                              else
                                              {
                                                  echo $this->formElement($form->get('location_code[]')->setValue(""));
                                              }
                                       ?>
                                       <small class="error"><?php echo $error_location;?></small>
                                  </div>
                                </div>
                                <div class="row">
                                    <div class="large-4 small-4 medium-4 columns">
                                    <label class="inline right"><?php echo ucwords($val);?> Address<span class="red">*</span></label>
                                    </div>
                                    <div class="large-8 small-8 medium-8 columns" id="div_address_<?php echo $val;?>">      
                                    <?php // echo $this->formElement($form->get('location_address[]')->setValue($location_address));?>
                                    <?php if($style=="block")
                                        {
                                    ?>
                                        <textarea class="form-control locationaddrcopycls" rows="5" cols="60" id="idloccode" placeholder="ENTER  DELIVERY ADDRESS (<?php echo strtoupper($val);?>)" name="location_address[]"><?php echo $location_address;?></textarea>
                                    <?php }
                                    else {?>
                                            <textarea class="form-control locationaddrcopycls" rows="5" cols="60" id="idloccode" placeholder="ENTER  DELIVERY ADDRESS (<?php echo strtoupper($val);?>)" name="location_address[]"></textarea>
                                    <?php } ?>
                                    <small class="error"><?php echo $error_address;?></small>
                                    </div>
                                </div>

                                <div class="row"> 
                                    <div class="large-4 small-4 medium-4 columns">
                                        <label class="inline right"><?php echo ucwords($val);?> Delivery Person</label>
                                    </div>
                                    <div class="large-8  small-8 medium-8 columns future_delivery sel_cls_<?php echo $val;?>" id="div_delivery_<?php echo $val;?>">              
                                      <?php 
                                      //  echo  $style;
                                        if($style=="block")
                                        {
                                            echo $this->formElement($form->get('delivery_person[]')->setValue($delivery_person));
                                        }
                                        else
                                        {
                                            echo $this->formElement($form->get('delivery_person[]')->setValue(""));
                                        }
                                     ?>             
                                    </div>
                                </div>

                                <div class="row"> 
                                    <div class="large-4 small-4 medium-4 columns">
                                        <?php /* <input style="float:right" class="future_change" type="checkbox" name="future[]" value="<?php echo $val;?>"> */?>
                                    </div>
                                    <div class="large-8  small-8 medium-8 columns sel_cls_<?php echo $val;?>">              
                                        <input style="float:right" class="future_change" type="checkbox" name="future[]" value="<?php echo $val;?>"><span style="color:#4d4d4d" >Check if want to apply this address for placed order.</span>
                                    </div>
                                </div>
                                <div class="clearfix mb10"></div>
                                <div class="row">
                                    <div class="large-4 small-4 medium-4 columns">
                                        <label class="inline right"><?php echo ucwords($val);?> Dibbawala Code</label>
                                    </div>
                                    <div class="large-8 small-8 medium-8 columns">                
                                          <div class="large-4 left">
                                              <?php //echo 111222; echo $dabawala_type; ?>
                                              <label for="dabbawala_code_type_1">
                                                <input <?php echo ($dabawala_type=='text') ? "checked" : "" ;?> class="clsdabbawala" data-id="<?php echo $val;?>_text" name="dabbawala_code_type[<?php echo $val;?>]" type="radio" id="dabbawala_code_type_<?php echo $val;?>" value="text">
                                                 <span class="custom radio"></span> Add Text
                                              </label>
                                         </div>
                                         <div class="large-4 left">
                                             <label for="dabbawala_code_type_2">
                                                <input <?php echo ($dabawala_type=='image') ? "checked" : "" ;?> class="clsdabbawala" data-id="<?php echo $val;?>_image"  name="dabbawala_code_type[<?php echo $val;?>]" type="radio" id="dabbawala_code_type_<?php echo $val;?>"  value="image">
                                                 <span class="custom radio"></span> Add Image
                                             </label>
                                        </div>
                                        <?php if($dabawala_type=='image' && $dabawala_image!=""){?>
                                        <img id="imgsrc_<?php echo $val;?>" src="<?php echo $GLOBALS['http_request_scheme'].$dabawala_image; ?>" style="width:100px;height:100px;" />
                                        <?php } ?>
                                    </div>
                                    <div class="clearfix mb15"></div>
                                </div>
                                <?php 
                                    $style_display_text="";
                                    $style_display_image="";
                                    if($dabawala_type=='text')
                                    {
                                        $style_display_text = "style=display:block;";
                                        $style_display_image = "style=display:none;";
                                    }
                                    else
                                    {
                                        $style_display_text = "style=display:none;";
                                        $style_display_image = "style=display:block;";
                                    }
                                    //echo "text--".$style_display_text."--"."image--".$style_display_image; echo "<br>";
                                ?>

                                <div class="row" id="div_dabbawala_code_text<?php echo $val;?>" <?php echo $style_display_text;?> >
                                    <div class="large-4 small-4 medium-4 columns">
                                        <label class="inline right"><?php //echo $this->formLabel($form->get('dabbawala_code')); ?></label>
                                    </div>
                                    <div class="large-8 small-8 medium-8 columns">  <?php //echo "text".$dabawala_text;?>              
                                        <input type="text" class="form-control" id="dabbawala_text_<?php echo $val;?>" placeholder="Please enter dibbawala code for <?php echo $val;?>" value="<?php echo $dabawala_text;?>" name="dabbawala_text[<?php echo $val;?>]">
                                    </div>
                                </div> 

                                <div class="row" id="dabbawala_code_image<?php echo $val;?>" <?php echo $style_display_image;?> >
                                    <?php //echo "image".$dabawala_image;?>  
                                    <div class="large-4 small-4 medium-4 columns">
                                        <label class="inline right"><?php //echo $this->formLabel($form->get('dabbawala_image')); ?></label>
                                    </div>
                                    <div class="large-8 small-8 medium-8 columns">           
                                    <input class="smallinput" type="file" id="dabbawala_image_<?php echo $dabawala_image;?>" accept="image/*" name="dabbawala_image[<?php echo $val;?>]">  
                                     <?php
                                     $imageDir = $_SERVER['DOCUMENT_ROOT']."/data/";  
                                     ?>
                                    </div>
                                </div>

                                <?php if($error_message!=""){?>
                                <!-- <small class="error"><?php //echo $errval['error'];?></small> -->
                                <?php }?>
                                </fieldset>
                            </div>                                        
                <?php    
                  } 
                }
								?>
              	</fieldset>
                <div class="clearfix mb15"></div>            
                
                 <div class="large-12 columns pl0 pr0">
                 <div class="row">
            		<div class="large-4 small-4 medium-4 columns">&nbsp;</div>
                  	<div class="large-8 small-8 medium-8 columns pr0"> 
                  		<div class="right">   
	                    	<button type="submit" id="submitbutton" class="button left tiny left5 dark-greenBg btn_theme"><i class="fa fa-save"></i> Save</button>
	                        <button type="submit" class="button left tiny left5 dark-greenBg btn_theme" id="cancelbutton"><i class="fa fa-ban"></i> Cancel</button>  
	                    </div>
                  	</div>
                 </div>
              	</div> 
                 <?php 
                 	echo $this->formElement($form->get('backurl'));
				 ?>
				 
              	<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>         
             
            </div>
    </div>
    
    <script type="text/javascript">

    $(document).ready(function() {
        
    	$(document).on("change",'#city',function(){
        	//alert("ddd");
			var city_val = $(this).val();
			var arr = city_val.split("#");
			var city = arr[0];
			
			
			$.ajax({
				url:"/menu/location",
				type : "POST",
				async : false,
				dataType : 'json',
				data : {city:city},
				success:function(data){
					
					var str="<option value=''>SELECT LOCATION</option>";
					$.each(data,function(index,values){	
						$.each(values,function(i,v){
							str+="<option value='"+v.pk_location_code+"#"+v.location+"'>"+v.location+"</option>";
						});
					});	

					$('.locationcodes').html(str);
					$('.locationcodecopycls .customSelectInner').html('SELECT LOCATION');
				},
				error:function(){
					//yummy_tiffins.generateToken();
				}
			});
			
		});

		$("#city").trigger('change');
		
		<?php
      if(!empty($new_arr_menu_err)) {
        foreach ($new_arr_menu_err as $key=>$val) {
    ?>    
          $("#div_location_<?php echo $val['menu_type'];?>").find('select').val('<?php echo $val['location_code'];?>');
		<?php 
        } 
      }
    ?>
		
    	$(document).on("click",".clsdabbawala",function(){
			var class_name = $(this).attr("data-id");
			var arr = class_name.split('_');
			var dabbawala_menu = arr[0];
			var dabbawala_type = arr[1];

				
			
			if(dabbawala_type=="image")
			{
				$("#dabbawala_text_"+dabbawala_menu).hide();
				$("#div_dabbawala_code_text"+dabbawala_menu).css('display','none');
				$("#dabbawala_image_"+dabbawala_menu).show();
				$("#dabbawala_code_image"+dabbawala_menu).css('display','block');
				$("#imgsrc_"+dabbawala_menu).css('display','block');
			}
			else
			{
				$("#dabbawala_image_"+dabbawala_menu).hide();
				$("#dabbawala_code_image"+dabbawala_menu).css('display','none');
				$("#imgsrc_"+dabbawala_menu).css('display','none');
				$("#dabbawala_text_"+dabbawala_menu).show();
				$("#div_dabbawala_code_text"+dabbawala_menu).css('display','block');
			}
		});

		var count_menu=0;
		
		$("#divcpyaddr").hide();
		
		$(".clsmenus").each(function(i,e){
			if($(this).css("display")=="block" || $(this).attr('style')=="")
			{
				count_menu=count_menu+1;
			}
			if(count_menu>=2)
			{
				$("#divcpyaddr").show();
			}
		});
		

    	$(document).on('click','.icheck',function(){
    		var count_menu=0;
			var menu = $(this).attr('data-id');
			var checked = $(this).parent().attr('class');

			if(checked=="checked")
			{
				$("."+menu).css("display","block");
			}
			else
			{
				$("."+menu).css("display","none");
			}
			$(".clsmenus").each(function(i,e){
				if($(this).css("display")=="block" || $(this).attr('style')=="")
				{
					count_menu=count_menu+1;
				}
				if(count_menu>=2)
				{
					$("#divcpyaddr").show();
				}
				else
				{
					$("#divcpyaddr").hide();
				}
			});
		});
                                                                             
    	$('#datepicker').datepicker({dateFormat:"yy/mm/dd"});

    	$('#sameadd').change(function() {
    		   if($(this).is(':checked'))
    	       {

    			   $('#lunch_add').val( $('#custmer_Address').val() );
    			   $('#dinner_add').val( $('#custmer_Address').val() );
                           lunch_dinner_DP();
                           $("#lunch_code option[value='"+$("#location_code").val()+"']").prop('selected', true);
                           $("#dinner_code option[value='"+$("#location_code").val()+"']").prop('selected', true);
                           
                           $("#lunch_delivery_person option[value='"+$("#delivery_person").val()+"']").prop('selected', true);
                           $("#dinner_delivery_person option[value='"+$("#delivery_person").val()+"']").prop('selected', true);
    	       }
    	       else
    	       {
    	    	   $('#lunch_add').val('');
    			   $('#dinner_add').val('');
    		   }
    	});

	$(document).on("click","#idcpyaddress",function(){
		var count = 1;
		var menu_select;
		var location_code="";
		var delivery_person="";
		var location_address="";

		if($(this).parent().attr('class')=="checked")
		{
			$(".clsmenus").each(function(i,e){
				if($(this).css("display")=="block" || $(this).attr('style')=="")
				{
					menu_select = $(this).attr('data-id');
					if(count==1)
					{
						location_code = $('.'+menu_select+' #div_location_'+menu_select+' select[name="location_code[]"]').val();
						location_address = $('.'+menu_select+' #div_address_'+menu_select+' textarea[name="location_address[]"]').val();
						delivery_person = $('.'+menu_select+' #div_delivery_'+menu_select+' select[name="delivery_person[]"]').val();
					}
					if(location_code!="" || delivery_person!="" || location_address!="")
					{
						count ++;
					}
					if(parseInt(count) > 1)
					{
						$('.'+menu_select+' #div_location_'+menu_select+' select[name="location_code[]"]').val(location_code);
						$('.'+menu_select+' #div_address_'+menu_select+' textarea[name="location_address[]"]').val(location_address);
						$('.'+menu_select+' #div_delivery_'+menu_select+' select[name="delivery_person[]"]').val(delivery_person);
					}
				}
			});	
		}
		
		if($(this).parent().attr('class')=="checked")
		{
			$(".clsmenus").each(function(i,e){
				if($(this).css("display")=="block" || $(this).attr('style')=="")
				{
					menu_select = $(this).attr('data-id');
					$('.'+menu_select+' #div_location_'+menu_select+' select[name="location_code[]"]').val(location_code);
					$('.'+menu_select+' #div_address_'+menu_select+' textarea[name="location_address[]"]').val(location_address);
					$('.'+menu_select+' #div_delivery_'+menu_select+' select[name="delivery_person[]"]').val(delivery_person);
				}
			});
		}
		
	});

    $(document).on('change','.locationcodecopycls',function(){
        var location=$(this).val().split('#');
        var menu=$(this).parent().attr('data-id');
        $.ajax({
			url:"<?php echo $this->url('customer',array('action' => 'ajax-delivery-person')); ?>",
			type: "POST",
            dataType: 'json',
			data: "location="+location[0],//"menu="+menu,
			success:function(data)
			{
                var arr=[];
                for(elem in data) 
                {
                    var temparr=[];
                    temparr.push(data[elem]['pk_user_code']);
                    temparr.push(data[elem]['first_name']+" "+data[elem]['last_name']);
                    arr.push(temparr);
                }
                var str='<option value="">Select Delivery Person</option>';
                for(var i=0;i<arr.length;i++)
                {
                   str+='<option value="'+arr[i][0]+'">'+arr[i][1]+'</option>';
                }
               $('.sel_cls_'+menu+' select').html(str);
            }
        });
    });

    $(document).on('submit','#admin',function(){

		$('.future_change').each(function(i,e){
			var this_element = $(this);
			if($(this_element).is(':checked')){

				var newaddress  = $('#div_address_'+$(this_element).val()).find('textarea').val();
				var deliverperson = $('#div_delivery_'+$(this_element).val()).find('select').val();
				var location = $('#div_location_'+$(this_element).val()).find('select').val();

			$(this_element).val($(this_element).val()+'#_#'+newaddress+'#'+deliverperson+'#_#'+location);
				//$(this_element).val($(this_element).val()+'_'+$('#div_address_'+$(this_element).val()+'').find('textarea').val());
			}
		});
		//return false;
    });


});
</script>
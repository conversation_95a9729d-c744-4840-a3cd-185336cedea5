<?php 
	
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	$setting = $setting_session->setting;
	
?>
<!DOCTYPE html>
<html mozdisallowselectionprint="" moznomarginboxes="">
	<head>
		<meta name="viewport" http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<link rel="shortcut icon" href="/admin/images/favicon.png" />
		<title>Fooddialer Quickserve</title>
	</head>
		<style>
			 @page
    		{
        		size: auto;   /* auto is the initial value */
        		margin: 2mm;  /* this affects the margin in the printer settings */
   			} 
			html {
				font-size: 62.5%;
				-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
				margin: 0px;
			}
			

			body {
				font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
				font-size: 12px;
				color: #333333;
				background: #ffffff;
				margin: 0px;
				padding: 0px;
			}
			table {
				background: white;
				margin-bottom: 1.25 rem;
				border: solid 1px #dddddd;
			}
			
			table thead, table tfoot {
				background: whitesmoke;
			}
			table thead tr th, table thead tr td, table tfoot tr th, table tfoot tr td {
				padding: 0.425 rem 0.625 rem;
				font-size: 1.200 rem;
				font-weight: bold;
				color: #222222;
				text-align: left;
			}
			table tr td {
				color: rgb(34, 34, 34);
				font-size: 1.3 rem;
				padding: 0.500 rem 0.625 rem;
			}
			table tr.even, table tr.alt, table tr:nth-of-type(even) {
				background: #f9f9f9;
			}
			table thead tr th, table tfoot tr th, table tbody tr td, table tr td, table tfoot tr td {
				display: table-cell;
			}
			td.action {
				position: relative;
				border-right: 0 none !important;
			}
			.printOption {
				clear: both;
				width: 98%;
				margin: 5px auto;
				height: 30px;
			}
			.printOption a {
				float: right;
			}
			.printOption .fa-print {
				font-size: 2.5 rem;
			}
			h1, h2, h3, h4, h5, h6 {
				font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
				font-weight: normal;
				font-style: normal;
				color: #222222;
				text-rendering: optimizeLegibility;
				margin-top: 0.2 rem;
				margin-bottom: 0.5 rem;
				line-height: 1.4;
			}
			h1 small, h2 small, h3 small, h4 small, h5 small, h6 small {
				font-size: 60%;
				color: #6f6f6f;
				line-height: 0;
			}
			h1 {
				font-size: 2.125 rem;
			}
			h2 {
				font-size: 1.6875 rem;
			}
			h3 {
				font-size: 1.6875 rem;
			}
			.tableDiv {
				clear: both;
			}
			table#myTable05 {
				border-left: 1px solid #999;
				border-right: 1px solid #999;
				border-top: 0 none !important;
				width: 100%;
				margin-bottom: 25px;
			}
			table#myTable05 td {
				border-bottom: 1px solid rgb(153, 153, 153);
				border-right: 1px solid rgb(153, 153, 153);
				padding: 5px 5px;			
			}
			table#myTable05 thead th {
				font-size: 16px;
				/* background: #555555; */
				/* color: #fff; */
				padding: 5px 5px;
			
			}
			.fht-table thead th {
				border-right: 1px solid #6a6868;
				border-top: 1px solid #6a6868;
				border-bottom: 1px solid #6a6868;
				font-weight: 600;
				text-align: center;
				padding: 5px 5px;
			}
			.fht-table td {
				word-wrap: break-word;
			}
			#date_time {
				text-align: right;
				margin: 10px 0 0 0;
				font-size: 16px;
			}
			.printOption h2 {
				text-align: center;
				position: relative;
				left: 55%;
				float: left;
				margin: 10px 0 0 -200px;
			}

			ul.printOpt li {
				float: left;
				padding: 0 10px;
				display: inline-block;
			}
			a.logo {
				float: left;
				padding: 0 0 0 0px;
			}
			li {
				display: inline-block;
			}
			ul {
				margin: 7px;
				padding: 0;
			}
			.tableAction {
				position: relative;
			}
			.left {
				float: left !important;
			}
			.right {
				float: right !important;
			}

			.subHeadings {
				background: #ccc !important;
				font-weight: 600;
			}
		</style>
		<style media="screen">
			.noPrint {list-style: none;}
			.yesPrint {display: block !important;}
		</style>
		<style media="print">
			.noPrint {display: none;}
			.yesPrint {display: block !important;}
			@page{margin:0mm;}
		</style>
	
	<body>

		<!-- Page main content -->
		<?php $today= date("d-m-Y");?>
		<div class="printOption">
			<h2 class="">Wallet History</h2>
		</div>
		
		<div class="tableDiv yesPrint" style="width:98%; margin:0 auto;">
			<table class="fht-table" id="myTable05" cellpadding="0" cellspacing="0">
				<thead>
					<tr class="subHeadings">
						<th>Date</th>
						<th>Description</th>
						<th>Amount</th>
						<th>Credit/Debit</th>
						<th>Payment Method</th>
						<th>Transaction By</th>
					</tr>
				</thead>
				<tbody>
						<?php foreach($wallet as $val){
						?>
						<tr>
							<td><?php echo $val['payment_date'];?></td>
							<td><?php echo $val['description'];?></td>
							<td><?php echo $val['wallet_amount'];?></td>			
							<td><?php echo $val['amount_type'];?></td>
							<td><?php echo $val['payment_type'];?></td>	
							<td><?php echo $val['context'];?></td>				
						</tr>
						<?php 
						}?>
				</tbody>
			</table>
		</div>
	</body>
	<!-- /Page main content -->

	<script type="text/javascript">
		window.onload = function() {
			window.print();
		}
	</script>
	</body>
</html>


<?php
$form = $this->form;
$form->setAttribute('id','cash');
$form1->setAttribute('name','cash');
$form->setAttribute('method','POST');
$form->prepare();

$form1 = $this->form1;
$form1->setAttribute('id','cheque');
$form1->setAttribute('name','cheque');
$form1->setAttribute('method','POST');
$form1->prepare();

$form2 = $this->form2;
$form2->setAttribute('id','neft');
$form2->setAttribute('name','neft');
$form2->setAttribute('method','POST');
$form2->prepare();


$form3 = $this->form3;
$form3->setAttribute('id','debit');
$form3->setAttribute('name','debit');
$form3->setAttribute('method','POST');
$form3->prepare();

$form4 = $this->form4;
$form4->setAttribute('id','lock');
$form4->setAttribute('name','lock');
$form4->setAttribute('method','POST');
$form4->prepare();

$utility = \Lib\Utility::getInstance ();
$setting_session = new Zend\Session\Container ( "setting" );
$setting = $setting_session->setting;
?>

<div id="content">
	<div class="large-12 columns mb20">
	
			<div class="content bg-image" style="background-color: #333333;">
				<div class="push-15 clearfix">
					<div class="push-15-r pull-left animated fadeIn">
						<i class="fa fa-user user-icon_big img-avatar img-avatar-thumb"></i>
					</div>
					<?php //echo "<pre>"; print_r($customer); die;?>
					<h1 class="h2 text-white push-5-t animated zoomIn"><?php echo $customer->customer_name; ?></h1>
					<h2 class="h5 text-white-op animated zoomIn"><?php //echo $employee[0]['location_name']; ?></h2>
					<input type="hidden" name="custid" id="custid" value="<?php echo $customer->pk_customer_code;?>">
				</div>
			</div>
			<div class="content bg-white border-b">
				<div class="row items-push text-uppercase">
					<div class=" medium-4 columns">
						<div class="font-w700 text-gray-darker animated fadeIn">
							Wallet Total Balance
						</div>
						<a class="h2 font-w300 text-primary animated flipInX" href="javascript:void(0)"><!-- <i class="fa fa-rupee"></i> --> <?php echo $utility->getLocalCurrency($custBalance['avail_bal']+$custBalance['lockedamt']); //echo round(($custBalance['avail_bal'])+($custBalance['lockedamt']),2);?></a>
					</div>
					<div class="medium-4 columns">
						<div class="font-w700 text-gray-darker animated fadeIn">
							Wallet Usable Balance
						</div>
						<a class="h2 font-w300 text-primary animated flipInX" href="javascript:void(0)"><!-- <i class="fa fa-rupee"></i> --> <?php echo $utility->getLocalCurrency($custBalance['avail_bal']);//number_format($custBalance['avail_bal'],2);?></a>
						<input type = "hidden" id = "hdnavailbal" value = "<?php echo $custBalance['avail_bal'];?>">
					</div>
					<div class="medium-4 columns">
						<div class="font-w700 text-gray-darker animated fadeIn">
							Wallet locked
						</div>
						<a class="h2 font-w300 text-primary animated flipInX" href="javascript:void(0)"><!-- <i class="fa fa-rupee"></i> --> <?php echo $utility->getLocalCurrency($custBalance['lockedamt']);//round($custBalance['lockedamt'],2);?></a>
					</div>
				</div>
			</div>
		</div>			
		
		<div class="large-6 columns mb15">
			<fieldset>
                  <?php 
                        if($utility->checksubscription('customer_wallet','allowed')	){
                 ?>
				<legend class="text-center">
					Add Amount
				</legend>
                <?php 
                       }
                 ?>
				<div class="row">
					<div id="w_a"  class="medium-12  columns" style="">
									<!-- <div class="addAmt_container"> -->
						<p class="left mr5">
							Add Amount By:
						</p>
						<p class="wallet_amount left mr10">
							<input class="payment_radio" type="radio" id="cash" name="payment" value="cash" checked/>
								<label for="wallet_amount">Cash</label>
						</p>
						<p class="credit_card left mr10">
							<input class="payment_radio" type="radio" id="chequer" name="payment" value="cheque"/>
								<label for="credit_card">Cheque</label>
						</p>
						<p class="neft">
							<input class="payment_radio" type="radio" id="neftamt" name="payment" value="neft" />
								<label for="neft">NEFT</label>
						</p>
									<!-- </div> -->
							
						<div class="natarionContainer">
							<div class="cash">	
								<?php echo $this->form()->openTag($form);?>
									<div class="addAmt_container">
										<input type="hidden" name="type" value="cash">
											<?php
												echo $this->formElement($form->get('cash_amt'));
												echo $this->formElementErrors($form->get('cash_amt'));
											?>
											<p class="mr5" id="narrationlblcash">
												Narration
											</p>
											<p class=""><textarea style="width:250px; height:50px;" name="cashdiv" id="cashdiv"></textarea></p>
											<p class="right">
												<input type="submit" id="cash_submit" class="sbmt_btn add_amt_div button right tiny" value="Submit" />
											</p>
									</div>
									<div class="clearboth"></div>
									<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
										
							</div>
								<!-- </div>
							
								<div class="natarionContainer"> -->
							<div class="cheque">
								<?php echo $this->form()->openTag($form1);?>
									<div class="addAmt_container">
										<input type="hidden" name="type" value="cheque">
											<div class="large-4 columns">
												<?php
													echo $this->formElement($form1->get('cheque_amt'));
													echo $this->formElementErrors($form1->get('cheque_amt'));
												?>
											</div>
											<div class="large-4 columns">
												<?php
													echo $this->formElement($form1->get('cheque_no'));
													echo $this->formElementErrors($form1->get('cheque_no'));
												?>
											</div>
											<div class="large-4 columns">
												<?php
													echo $this->formElement($form1->get('bank_name'));
													echo $this->formElementErrors($form1->get('bank_name'));
												?>
											</div>
											<p class="mr5" id="narrationlblcheque">
												Narration
											</p>
											<p class="">
												<textarea class="mb10" style="width:250px; height:50px;" name="chqdiv" id="chqdiv"></textarea>
											</p>
											<p class="right">
												<input type="submit" id="cheque_submit" class="sbmt_btn add_amt_div button right tiny" value="Submit" />
											</p>
									</div>
									<div class="clearboth"></div>
										<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form1);?>
									</div>
									<!-- </div>
								<div class="natarionContainer">	  -->
								
								<div class="neft">
								<?php echo $this->form()->openTag($form2);?>
									<div class="addAmt_container">
										<input type="hidden" name="type" value="neft">
									    	<div class="large-4 columns">
									        	<?php
										        	echo $this->formElement($form2->get('neft_amt'));
													echo $this->formElementErrors($form2->get('neft_amt'));
												?>
									         </div>
									    	 <div class="large-4 columns">
									         <?php
										    	echo $this->formElement($form2->get('trans_id'));
												echo $this->formElementErrors($form2->get('trans_id'));
											 ?>
									         </div>
									         <div class="large-4 columns">
									         	<?php
										        	echo $this->formElement($form2->get('neft_date'));
													echo $this->formElementErrors($form2->get('neft_date'));
												?>
									         </div>
									    	<p class="mr5" id="narrationlblneft">
												Narration
											</p>
										    <p class="">
										    	<textarea class="mb10" style="width:250px; height:50px;" name="neftdiv" id="neftdiv"></textarea>
										    </p>
										 	<p class="right">
												<input type="submit" id="neft_submit" class="sbmt_btn add_amt_div button right tiny" value="Submit" />
											</p>
									</div>
								     <div class="clearboth"></div>
								    <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form2);?>	
									
									</div>
								</div>
							</fieldset>	
						
						</div>
						
						<div class="large-6 columns mb15">
							<fieldset>
								<legend  class="text-center">
									Debit/Lock Amount
								</legend>
								<div class="row">
									<div id="w_a" class="medium-12 columns">
										<p class="left mr5"> Debit/Locked Amount: </p>
										<!-- <div class="addAmt_container">  -->
											<p class="wallet_amount left mr10">
												<input class="debit_radio" type="radio" id="debit" name="lockedamt" value="debit" checked/>
												<label for="debit">Debit</label>
											</p>
											
											<p>
												<input class="debit_radio" type="radio" id="lock" name="lockedamt" value="lock" />
												<label for="lock">Lock</label>
											</p>
										<!-- </div> -->
						
										<div class="natarionContainer">
											<div class="debit">
											<?php echo $this->form()->openTag($form3);?>
											<div class="addAmt_container">
												
													<input type="hidden" name="type" value="debit">
												    	<div class="large-4 columns">
												        <?php
													    	echo $this->formElement($form3->get('debit_amt'));
															echo $this->formElementErrors($form3->get('debit_amt'));
														?>
												        </div>
														<div class="clearBoth5"></div>
												 	
														<p class="mr5" id="narrationlbldebit">
															Narration
														</p>
														<p class="">
															<textarea style="width:250px; height:50px;" name="debitdiv" id="debitdiv"></textarea>
														</p>
													
												</div>
												
													<p class="right">
														<input type="submit" id="debit_submit" class="sbmt_btn locked_amount_div button left tiny" value="Submit" />
													</p>
												
											</div>
											<div class="clearboth"></div>
											<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form3);?>
										    </div>
									<!-- 	</div>
							
										<div class="natarionContainer"> -->
											<div class="debit">
												<?php echo $this->form()->openTag($form4);?>
												<div class="addAmt_container">
													
														<input type="hidden" id="hdn_customer_wallet_id" name="hdn_customer_wallet_id" />
														<input type="hidden" id="flag_lock_amt_transfer" name="flag_lock_amt_transfer" />
														<input type="hidden" name="type" value="lock">
														<input type="hidden" name="hdnvaleditlockamt" id="hdnvaleditlockamt"/>
														<div class="large-4 columns">
													        <?php
														    	echo $this->formElement($form4->get('lock_amt'));
																echo $this->formElementErrors($form4->get('lock_amt'));
															?>
											        	</div>
												
														<div class="clearBoth5"></div>
															<p class="mr5" id="narrationlbllock">
																Narration
															</p>
															<p class="">
																<textarea style="width:250px; height:50px;" name="lockdiv" id="lockdiv"></textarea>
															</p>
														
														<p class="right">
															<input type="submit" id="lock_submit" class="sbmt_btn locked_amount_div button left tiny" value="Submit" />
														</p>
													
												</div>
												<div class="clearboth"></div>
												<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form4);?>
										   </div>
										</div>
									</div>
								</div>
							</div>
						</fieldset>
						
						</div>
							<div class="clearfix"></div>
							<div class="large-12 columns">
							
							<?php if(isset($this->custamtpaiddata) && !empty($this->custamtpaiddata) && (count($this->custamtpaiddata) > 0)){?>
								<!-- <h3 class="page-title">History</h3>
								<div class="right">
									<div>
										<select>
											<option>All</option>
											<option>Withdraw</option>
											<option>Deposit</option>
										</select>
									</div>
								</div> -->
								<div class="portlet-title">
									<h4 class="white"><i class="fa fa-table"></i>History</h4>
										<ul class="toolOption">
										<li>
											<div class="print">
												<button id="print_wallet" target="_blank" class="btn dropdown" data-dropdown="dropPrint">
													<i class="fa fa-share"></i>&nbsp;Print/Export
												</button>
											</div>
										</li>
									</ul>
									
								</div>		
								<div class="portlet-body">
								<table id="walletInfo" class="large-12 columns displayTable  mt10">
									<thead>
										<tr>
											<th>Date</th>
											<th>Description</th>
											<th>Amount</th>
											<th>Credit/Debit</th>
											<th>Payment Method</th>
											<th>Transacted By</th>
											<th>Action</th>
										</tr>
									</thead>
								
									<?php }?>
								
								</table>
								</div>
								 <div id="myModal" class="reveal-modal tiny" data-reveal> </div>
							</div>
						</div>
	<style>
		table.dataTable{
			border-collapse: collapse;
		}
	</style>			
	<script type="text/javascript">
	$(document).ready(function(){

		if (RegExp('multipage', 'gi').test(window.location.search)) {
			introJs().start();
		}

	    var currency_entity = '<?php echo $utility->getCurrencySymbol($setting['GLOBAL_CURRENCY'], $setting['GLOBAL_LOCALE']);?>';

		 $('#print_wallet').click(function(){
			 var wal = "/customer/printwallethistory?xdf='<?php echo base64_encode($customer->pk_customer_code);?>'&xdfn='<?php echo base64_encode($customer->pk_customer_code);?>'";
			 window.open(wal, '_blank');
		});		

		jQuery( "#datepicker" ).datepicker(
				{ dateFormat: "yy-mm-dd" }
				);
		
		jQuery( "#datepicker1" ).datepicker({ dateFormat: "yy-mm-dd" });
		
		$('#walletInfo').dataTable( {
			"aaSorting": [[0, 'desc']],
		    "processing": true,
		    "serverSide": true,
		    "bDestroy": true,
		    "ajax": {
	            "url":"/customer/ajx-wallet",
	            "data": function ( d ) {
	                d.id = $("#custid").val();
	         	}
	        },
		    "aoColumnDefs": [
		 	                {
		 	                   bSortable: false,
		 	                   aTargets: [ -1 ]
		 	                }
		 	              ],
            
			});
            
            $("#narrationlbllock, #narrationlblneft, #narrationlblcheque , #lock_amt, #lockdiv, #lock_submit, #cheque_no, #cheque_amt, #bank_name, #trans_id, #neft_amt, #datepicker, #neft_submit, #cheque_submit,#chqdiv,#neftdiv").hide();
		
			//$("#narrationlbllock, #narrationlbldebit, #narrationlblneft, #narrationlblcheque, #debit_amt, #debitdiv, #debit_submit, #lock_amt, #lockdiv, #lock_submit, #cheque_no, #cheque_amt, #bank_name, #trans_id, #neft_amt, #datepicker, #neft_submit, #cheque_submit,#chqdiv,#neftdiv").hide();
			$("#cash_amt").focus();
			
			var cashamt = "";
			var chkamt = "";
			var chkno = "";
			var nefttranid = "";
			var neftamt = "";
			var debitamt = "";
			var lockamt = "";
			var bank_name="";
			
			cashamt = $('#cash_amt').val();
			chkamt = $("#cheque_amt").val();
			chkno = $("#cheque_no").val();
			neftamt = $("#neft_amt").val();
			debitamt = $("#debit_amt").val();
			lockamt = $("#lock_amt").val();
			nefttranid=$("#trans_id").val();
			chkno=$("#cheque_no").val();
			bank_name = $("#bank_name").val();
		//	alert(bank name);
		
			$("#cashdiv").val("Cash "+currency_entity+" "+cashamt+ " deposited by Admin Manager");
			$("#debitdiv").val(currency_entity+" "+debitamt+ " debited by Admin Manager");
			$("#lockdiv").val("Lock amount "+currency_entity+" "+lockamt+ " deposited by Admin Manager");
			$("#chqdiv").val(currency_entity+" "+chkamt+" received by Cheque Number "+chkno+" from Bank "+bank_name);
			$("#neftdiv").val(currency_entity+" "+neftamt+" received by NEFT Transaction id "+nefttranid);

			$("#cash_amt").blur(function(){
				cashamt=$(this).val();
				$("#cashdiv").val("Cash "+currency_entity+" "+cashamt+ " deposited by Admin Manager");
			});

			$("#cheque_amt,#cheque_no,#bank_name").blur(function(){
				chkamt=$("#cheque_amt").val();
				chkno=$("#cheque_no").val();
				bank_name = $("#bank_name").val();
				$("#chqdiv").val(currency_entity+" "+chkamt+" received by Cheque Number "+chkno+" from Bank "+bank_name);
			});

			$("#trans_id,#neft_amt").blur(function(){
				nefttranid=$("#trans_id").val();
				neftamt=$("#neft_amt").val();						
				/*  $.ajax({
					 url:"/customer/convertLock",
					 type: "POST",
					 data : {customer_wallet_id:customer_wallet_id,description:description},
					 async : false,
					 success:function(result)
					 {
						 window.location.reload();
					 }
				});  */
				$("#neftdiv").val(currency_entity+" "+neftamt+" received by NEFT Transaction id "+nefttranid);
			});

			$("#debit_amt").blur(function(){
				debitamt = $("#debit_amt").val();
				$("#debitdiv").val(currency_entity+" "+debitamt+" debited by Admin Manager");
			});

			$("#lock_amt").blur(function(){
				lockamt = $("#lock_amt").val();
				$("#lockdiv").val("Lock amount "+currency_entity+" "+lockamt+" deposited by Admin Manager");
			});

			
			
			$("#cash").click(function(){
				$("#narrationlblcash, #cash_amt, #cash_submit, #cashdiv").show();
				//$("#narrationlbllock, #narrationlb1,500.00ldebit, #narrationlblneft, #narrationlblcheque, #debit_amt, #debit_submit, #debitdiv, #lock_amt, #lock_submit, #lockdiv,#cheque_no, #cheque_amt, #bank_name, #trans_id, #neft_amt, #datepicker, #neft_submit, #cheque_submit,#chqdiv,#neftdiv ").hide();
				$("#narrationlbllock, #narrationlblneft, #narrationlblcheque,#cheque_no, #cheque_amt, #bank_name, #trans_id, #neft_amt, #datepicker, #neft_submit, #cheque_submit,#chqdiv,#neftdiv ").hide();
				//$("#narrationlbllock, #narrationlbldebit, #narrationlblneft, #narrationlblcheque, #debit_amt, #debit_submit, #debitdiv, #lock_amt, #lock_submit, #lockdiv,#cheque_no, #cheque_amt, #bank_name, #trans_id, #neft_amt, #datepicker, #neft_submit, #cheque_submit,#chqdiv,#neftdiv ").hide();
				$("#cash_amt").focus();
			});
            $("#cash_submit").click(function() {
                $(this).closest('submit').find("input[type=submit], textarea").val("");
               
            });
			$("#debit").click(function(){
				//alert("debit");
				$("#narrationlbldebit, #debit_amt, #debit_submit, #debitdiv").show();
				$(" #narrationlbllock, #lock_amt, #lock_submit, #lockdiv").hide();
				$("#debit_amt").focus();
			});

			$("#lock").click(function(){
				$("#lock_amt").val('');
				$("#lockdiv").val("Lock amount deposited by Admin Manager");
				//alert("lock");
				$("#narrationlbllock, #lock_amt, #lock_submit, #lockdiv").show();
				$(" #narrationlbldebit,#debit_amt, #debit_submit, #debitdiv").hide();
				$("#lock_amt").focus();
			});
			
			$("#chequer").click(function(){
				$(" #narrationlbllock,  #narrationlblneft, #narrationlblcash, #cash_amt, #trans_id, #neft_amt, #datepicker,#neft_submit, #cash_submit, #neftdiv, #cashdiv").hide();
				$("#narrationlblcheque, #cheque_no, #cheque_amt, #bank_name, #cheque_submit,#chqdiv").show();
				$("#cheque_no").focus();
			});
	
			$("#neftamt").click(function(){
				$(" #narrationlbllock, #narrationlblcash, #narrationlblcheque, #cheque_no, #cheque_amt, #bank_name, #cash_amt, #cheque_submit, #cash_submit, #cashdiv,#chqdiv").hide();
				$("#narrationlblneft, #trans_id, #neft_amt, #datepicker, #neft_submit,#neftdiv").show();
				$("#trans_id").focus();
			});

			$(document).on('click','.add_amt_div',function(){

				var decimal=  /^[-+]?[0-9]+\.[0-9]+$/;
				var selected_name = $("input[name='payment']:checked").val();
				//alert(selected_name);
				if(typeof(selected_name)=='undefined')
				{
					selected_name="lock";
				}

				
				if(selected_name =="cash")
				{
					cashamt = $('#cash_amt').val();
					if($("#cash_amt").val()=="")
					{
						alert("Please enter amount");
						return false;
					}
					else if(isNaN($("#cash_amt").val())){
						alert("Please enter valid amount");
						$("#cash_amt").val('');
						$("#cashdiv").val("Cash "+currency_entity+" deposited by Admin Manager");
						return false;
					}
					else if(cashamt!=''){
						if(validatedecimal(cashamt,"cash")==false)
						{
							$("#cash_amt").val('');
							$("#cashdiv").val("Cash "+currency_entity+" deposited by Admin Manager");                            
							return false;
						}
						else if(validatezeros(cashamt)==false)
						{
							$("#cash_amt").val('');
							$("#cashdiv").val("Cash "+currency_entity+" deposited by Admin Manager");                           
							return false;
						}
						if($("#cashdiv").val=='')
						{
							$("#cashdiv").val("Cash "+currency_entity+" "+cashamt+" depcheque_amtosited by Admin Manager");
						}
					}
				}

				if(selected_name =="cheque")
				{
					chkamt = $('#cheque_amt').val();
					chkno=$("#cheque_no").val();
					bank_name = $("#bank_name").val();
					
					if($("#cheque_no").val() =="")
					{
						alert("Please enter cheque no");
						return false;
					}
					else if(isNaN($("#cheque_no").val()))
					{
						alert("Please enter valid cheque no");
						$("#cheque_no").val('');
						$("#chqdiv").val(currency_entity+" received by Cheque Number from ");
						return false;
					}
					else if($("#cheque_amt").val() =="")
					{
						alert("Please enter cheque amount");
						return false;
					}
					else if(isNaN($("#cheque_amt").val()))
					{
						alert("Please enter valid cheque amount");
						$("#cheque_amt").val('');
						$("#chqdiv").val(currency_entity+" received by Cheque Number from");
						return false;
					}
					else if($("#bank_name").val() =="")
					{
						alert("Please enter bank name");
						return false;
					}
					else if($("#cheque_amt").val()!="")
					{
						if(validatezeros(chkamt)==false){
							$("#cheque_amt").val('');
							$("#chqdiv").val(currency_entity+" received by Cheque Number from");
							return false;
						}
						else if(validatedecimal(chkamt,"cheque")==false){
							$("#cheque_amt").val('');
							$("#chqdiv").val(currency_entity+" received by Cheque Number from");
							return false;
						}
						if($("#chqdiv").val()=='')
						{
							$("#chqdiv").val(currency_entity+" "+chkamt+" received by Cheque Number "+chkno+" from Bank "+bank_name);
						}
					}
				}

				if(selected_name =="neft")
				{
					neftamt = $('#neft_amt').val();
					nefttranid=$("#trans_id").val();
					if($("#trans_id").val() =="")
					{
						alert("Please enter transaction id");
						return false;
					}
					else if($("#neft_amt").val() =="")
					{
						alert("Please enter amount");
						return false;
					}
					else if(isNaN($("#neft_amt").val()))
					{
						alert("Please enter valid amount");
						$("#neft_amt").val('');
						$("#neftdiv").val(currency_entity+" received by NEFT Transaction id ");
						return false;
					}
					else if($("#datepicker").val() =="")
					{
						alert("Please select date");
						return false;
					}
					else if($("#neft_amt").val()!="")
					{
						if(validatezeros(neftamt)==false)
						{cheque_amt
							$("#neft_amt").val('');
							$("#neftdiv").val(currency_entity+" received by NEFT Transaction id ");
							return false;
						}
						else if(validatedecimal(neftamt,"neft")==false)
						{
							$("#neft_amt").val('');
							$("#neftdiv").val(currency_entity+" received by NEFT Transaction id ");
							return false;
						}
						if($("#neftdiv").val()=='')
						{
							$("#neftdiv").val(currency_entity+" "+neftamt+" received by NEFT Transaction id "+nefttranid);
						}
					}
				}
                $(this).hide();
			});

			$(document).on("click",".locked_amount_div",function(){
				var decimal=  /^[-+]?[0-9]+\.[0-9]+$/;
				
				var selected_name = $("input[name='lockedamt']:checked").val();
			
				/* var a= $('input[name=payment]:checked').val(); */

				/* alert(a);return false; */
				
				//var selected_name = $('input[name=myradiobutton]:radio:checked');
				//alert(selected_name);
				if(typeof(selected_name)=='undefined')
				{
					selected_name="lock";
				}
				
				
				if(selected_name =="debit")
				{
					//alert(parseFloat($("#hdnavailbal").length+' '+$("#hdnavailbal").val()) + ' '+debitamt);return false;
					debitamt = $('#debit_amt').val();
					if($("#debit_amt").val() =="")
					{
						alert("Please enter amount");
						return false;
					}
					else if(isNaN($("#debit_amt").val()))
					{
						alert("Please enter valid amount");
						$("#debit_amt").val('');
						$("#debitdiv").val('');
						return false;
					}
					else if(debitamt!=''){
						
						if(parseFloat($("#hdnavailbal").val()) <  parseFloat(debitamt))
						{
							alert("Amount must be less than or equal to Usable Balance");
							$("#debit_amt").val('');
							$("#debitdiv").val(currency_entity+" debited by Admin Manager");
							return false;
						}
						else if(validatedecimal(debitamt,"debit")==false)
						{
							$("#debit_amt").val('');
							$("#debitdiv").val(currency_entity+" debited by Admin Manager");
							return false;
						}
						else if(validatezeros(debitamt)==false)
						{
							$("#debit_amt").val('');
							$("#debitdiv").val(currency_entity+" debited by Admin Manager");
							return false;
						}
						if($("#debitdiv").val()=='')
						{
							$("#debitdiv").val(currency_entity+" "+debitamt+" debited by Admin Manager");
						}
					}
				}
				if(selected_name =="lock")
				{
					lockamt = $('#lock_amt').val();
					if($("#lock_amt").val() =="")
					{
						alert("Please enter amount");
						return false;
					}
					else if(isNaN($("#lock_amt").val()))
					{
						alert("Please enter valid amount");
						$("#lock_amt").val('');
						$("#lockdiv").val('');
						return false;
					}
					else if(lockamt!=''){
						//var totalamount=parseFloat($("#hdnavailbal").val())+parseFloat($("#hdnvaleditlockamt").val());
						//if(totalamount <  parseFloat(lockamt))
						if(parseFloat($("#hdnavailbal").val()) <  parseFloat(lockamt))
						{
							alert("Please enter lock amount less than or equal to Usable Balance");
							$("#lock_amt").val('');
							$("#lockdiv").val("Lock amount deposited by Admin Manager");
							return false;
						}
						else if(validatedecimal(lockamt,"lock")==false)
						{
							$("#lock_amt").val('');
							$("#lockdiv").val("Lock amount deposited by Admin Manager");
							return false;
						}
						else if(validatezeros(lockamt)==false)
						{
							$("#lock_amt").val('');
							$("#lockdiv").val("Lock amount deposited by Admin Manager");
							return false;
						}
						if($("#lockdiv").val()=='')
						{
							$("#lockdiv").val("Lock amount "+currency_entity+" "+lockamt+" deposited by Admin Manager");
						}
					}
				}
                 $(this).hide();
		});
			
			/* $(document).on("click",".lockedit",function(){
				var customer_wallet_id=$(this).attr('data-id');
				var lockdiv = $(this).closest("tr").children().eq(1).html();
				var lock_amt = $(this).closest("tr").children().eq(2).html();
				updateLockFunction(customer_wallet_id,lockdiv,lock_amt);
			}); */

			$(document).on("click",".lockedit",function(){
				var customer_wallet_id=$(this).attr('data-id');
				var lockdiv = $(this).closest("tr").children().eq(1).html();
				var lock_amt = $(this).closest("tr").children().eq(2).html();
				var mainlockamt=lock_amt.slice(0,lock_amt.indexOf(" "));
				var hdnavailbal=$("#hdnavailbal").val();
				$('#myModal').foundation('reveal', 'open', {
					close_on_background_click: false,
					// url: 'customer/editcustomerwallet/',
				    url: '/customer/editcustomerwallet/'+customer_wallet_id+'/amt/'+mainlockamt+'/description/'+lockdiv+'/hdnavailbal/'+hdnavailbal,
				});


				/* $('#myModal').foundation('reveal', 'open', {
					close_on_background_click: false,
				    url: 'orderconfirm/confirmorder/'+id+'/amt/'+amt+'/custcode/'+custcode,
				  //  data: {id: product_code}
				}); */
				return false;
			});

			$(document).on("click",".locktranfer",function(){
				var customer_wallet_id=$(this).attr('data-id');
				//var lock_amt = $(this).closest("tr").children().eq(2).html();
				//var mainlockamt=lock_amt.slice(0,lock_amt.indexOf(" "));

				var mainlockamt = $(this).closest("tr").children().find(".wallet_amount").html();
				
				if (window.confirm("Do you really want to transfer lock amount to debit amount?")) { 
						$("#hdn_customer_wallet_id").val(customer_wallet_id);
						$("#flag_lock_amt_transfer").val("transferlockamttodebit");
						//var description=currency_entity+" "+mainlockamt+ " debited by Admin Manager";
						var description=mainlockamt+ " debited by Admin Manager";
						 $.ajax({
							 url:"/customer/convertLock",
							 type: "POST",
							 data : {customer_wallet_id:customer_wallet_id,description:description},
//							 async : false,
							 success:function(result)
							 {
								 window.location.reload();
							 }
						}); 
					}
			});

			$(document).on('keyup', '.amtcls', function(e) {
			    var val=$(this).val();
			    val = val.replace(/[^0-9\.]/g,'');
			        if(val.split('.').length>2) 
			            val =val.replace(/\.+$/,"");
			        $(this).val(val);
			});
            
		})
	</script>
	
<script type="text/javascript">
/* function updateLockFunction(customer_wallet_id,lockdiv,lock_amt)
{
	var selected_id = $('input[type=radio]:checked').attr("id");
	$("#"+selected_id).removeAttr("checked");
	$("#"+selected_id).parent().removeClass("checked");
	$("#hdn_customer_wallet_id").val(customer_wallet_id);
	$("#lockdiv").val(lockdiv);
	var mainlockamt=lock_amt.slice(0,lock_amt.indexOf(" "));
	$("#lock_amt").val(mainlockamt);
	$("#hdnvaleditlockamt").val(mainlockamt);
	$("#lock").parent().addClass("checked");
	$("#narrationlblcash, #narrationlbldebit, #cash_amt, #cashdiv, #cash_submit, #debit_amt, #debitdiv, #debit_submit, #cheque_no, #cheque_amt, #bank_name, #trans_id, #neft_amt, #datepicker, #neft_submit, #cheque_submit,#chqdiv,#neftdiv").hide();
	$("#lock_amt, #lock_submit, #lockdiv, #narrationlbllock").show();
} */

function validatedecimal(amount,type)
{
	 if ((amount.indexOf('.') != -1) && (amount.substring(amount.indexOf('.')).length > 3)) {
			alert("Please  enter " + type + " amount till 2 decimal places");
			return false;
	    }
}
function validatezeros(amt)
{
	if(isNaN(amt/0)) {
		alert("Please enter amount greater than zero");
		return false;
	}
}
</script>

<style>
.addAmt_container .large-4{
	padding-left:0;
}
</style>

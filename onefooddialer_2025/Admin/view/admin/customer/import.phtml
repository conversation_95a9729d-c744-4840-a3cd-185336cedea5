<?php
$form = $this->form;
$form->setAttribute('action', $this->url('customer', array('action' => 'import')));
$form->prepare();
?>   
    <div id="content" class="clearfix">
        <div class="large-12 columns">
		<?php 
		if(!empty($errors)){
		?>
			<div class="alert-box alert">
				<?php echo $errors['msg'];?>
			</div>
		<?php 
		}
		?>
		
		<div>
			<?php echo $this->form()->openTag($form);?>
			
			<div class="portlet box grey">
				<div class="portlet-title">
					<h4 class="white"><i class="fa fa-table"></i>Download Template </h4>  
					<ul class="toolOption">
	        			<li></li>
	        			<li>
		        			<div class="tools">
		        				<a class="collapse" href="javascript:;"></a>
		        			</div>
	        			</li>
        			</ul>
				</div>
				
				<div class="portlet-body display">
					<div class="row">
	            		<div class="large-4 small-4 medium-4 columns">
	            			<label class="inline right"><?php echo $this->formLabel($form->get('download_file')); ?></label>
	                  	</div>
	                  	
	                  	<div class="large-8  small-8 medium-8 columns">
	                    <?php  
							echo $this->formElement($form->get('download_file'));
						?>
						 
						<label class="inline"> &nbsp; (If you already have updated file you can skip this step)</label>
				     	</div>
	                  	
	                  </div>
				</div>
			</div>
			
			<div class="portlet box grey">
				<div class="portlet-title">
					<h4 class="white"><i class="fa fa-table"></i>Upload Template File </h4>  
					<ul class="toolOption">
	        			<li></li>
	        			<li>
		        			<div class="tools">
		        				<a class="collapse" href="javascript:;"></a>
		        			</div>
	        			</li>
        			</ul>
				</div>
				
				<div class="portlet-body display">
					<div class="row">
	            		<div class="large-4 small-4 medium-4 columns">
	            			<label class="inline right"><?php echo $this->formLabel($form->get('import_file')); ?></label>
	                  	</div>
	                  	<div class="large-3  small-3 medium-3 columns">
	                    <?php  
							echo $this->formElement($form->get('import_file'));
							
							
							echo $this->formElementErrors()
							->setMessageOpenFormat('<small class="error">')
							->setMessageCloseString('</small>')
							->render($form->get('import_file'));
						?> 
						<label class="inline">(Only template file with .xls  formats are allowed.)</label>
				     	</div>
	                  	<div class="large-5 small-5 medium-5 columns">
	                  		<?php  echo $this->formElement($form->get('submit')); ?>
	                  	</div>
	                  </div>
				</div>
			</div>
			<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>       
		</div>
		
		<?php 
		if(!empty($this->importedData)){
		?>
		
		<form action="/customer/import" name="importFrm" id="importFrm" method="post">
		
			
		<div class="clearfix"></div>
		<br />
			
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4 class="white"><i class="fa fa-table"></i>Import Customers</h4>  
        	</div>  
              
        <div class="portlet-body">   
        	<div class="dataTables_scroll">
        	<div class="dataTables_scrollBody" style="overflow: auto; width: 100%;">	     
        	<table id="customer" class="display displayTable collapsetable" style="width: 100%;height:300px;display:block;">
                  
  					<thead>
                        <tr>
                          <?php
                          
                          //$totalColumn = count($this->columns['mandatory']) + count($this->columns['optional']); 
                  
                          foreach($this->columns as $rowKey=>$rowVal){
                          	?>
                          	<th>		<?php $tempVal = str_replace("_", " ", $rowVal);
											  $columnName =  ucwords($tempVal);
										?>
                            			<?php echo $columnName; ?>
		                            	<input type="hidden" name="column[]" value="<?php echo $rowVal;?>" />
                    		</th>
                       		<?php }
                            		?>
                       	
                        </tr>
                        
                    </thead>
 
                    <tbody>
                    	<?php 
	                    	foreach($this->importedData as $rowKey=>$rowVal){
	                    		if($rowKey!=1){
	                    	?>
		                    	<tr>
		                    	<?php 
		                    	foreach($rowVal as $coulmnKey=>$coulmnVal){
		                        ?>
		                            <td>
		                            	<?php echo $coulmnVal; ?>
		                            	<input type="hidden" name="importData[<?php echo $rowKey;?>][]" value="<?php echo $coulmnVal;?>" />
		                            </td>
		                       <?php 
		                          }
		                       ?>  
		                        </tr>
	                        <?php 
	                    		}
	                    	}
			            ?>
                    </tbody>
                </table> 
                 </div>
                 </div>   
                <div class="right">
                	<ul class="pagination custom-pagination">
                		<li>
                		
                			<?php 
//                 			$mandatoryColumnList = implode(",",array_keys($this->columns['mandatory']));
                			?>
                			<input type="hidden" name="madatoryColumnList" id="madatoryColumnList" value=""/>
                			<input type="hidden" id="selectedColumnsList" name="selectedColumnsList" value=""/>
                			<input type="submit" name="submit" id="import" value="Import" class="button left tiny left5 dark-greenBg" />
                				<input type="button" name="cancel" id="cancel" value="Cancel" class="button left tiny left5 dark-redBg" onclick="javascript:location.href='/customer/import'" />
                		</li>
                	</ul>
				</div>  
				  
				<div class="clearfix"></div> 
          	</div>
          	<input type="hidden" name="columnids" >
          </form>
          
          <?php 
			}
          ?>	
          	
          	
        </div>
        
        <div class="clearBoth20"></div>
        
      </div>
    </div>
    
	<script type="text/javascript">
	
	$(document).ready(function(){

		$(document).on('click','#downloadbutton',function(){
			
				window.location.href = '<?php echo $this->url('customer', array('action'=>'export-import-template'));?>';
				
			});

        $(document).on('click','#import',function(e){
            $("#importFrm").submit();
        });		
		
	});
	</script>  

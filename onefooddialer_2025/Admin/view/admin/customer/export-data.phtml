<form id="exportForm" action="customer/exportData" method="post">

<table class="large-12 small-12 medium-12 columns mt15" style="margin-bottom: 0">
<tr>
<th width="75%"> Fields </th>
<th width="25%"><input type="checkbox"  id="selectAll"/>Select to Export All</th>
</tr>

<tr>
	<td colspan="2">
		<div style="height:500px;width:100%;overflow-y: auto;" class="table-parent">
			<table class="large-12 small-12 medium-12 columns mt15" style="margin-bottom: 0; width:100%">
				<?php foreach ($columns as $col){
					$checked ='';
					$disabled='';
				?>
				<tr>
				<td><?php
				if( $col=='pk_customer_code'){
					$checked='checked=checked';
				//	$disabled='disabled=true';
				} 
				$colname = str_replace("_", " ", $col);
				
				$colname = ($colname == 'pk customer code')?'customer code':$colname;
				$colname = ($colname == 'registered from')?'registered by':$colname;
				$colname = ($colname == 'food referance')?'food reference':$colname;
				$colname = ($colname == 'lunch loc code')?'Lunch Location Code':$colname;
				$colname = ($colname == 'lunch loc name')?'Lunch Location Name':$colname;
				$colname = ($colname == 'dinner loc code')?'Dinner Location Code':$colname;
				$colname = ($colname == 'dinner loc name')?'Dinner Location Name':$colname;
				$colname = ($colname == 'lunch add')?'Lunch Address':$colname;
				$colname = ($colname == 'dinner add')?'Dinner Address':$colname;
				$colname = ($colname == 'CustomerAddress')?'Customer Address':$colname;
				$colname = ($colname == 'DabbawalaCode')?'Dabbawala Code':$colname;
				$colname = ($colname == 'CustomerLocation')?'Customer Location':$colname;
				
				echo ucwords($colname);
				?></td>
				<td><input type="checkbox" class="checkbox" name="columns[]" id="<?php echo $col;?>" <?php echo $checked;?> /></td>
				</tr>

			<?php }?>
			</table>
		</div>
	</td>
</tr>

</table>

<div class="left exportoption mt15">

<!-- <span class="inline left"><input type="radio"  name="export_type" value="pdf" id="pdf">  <img src="/admin/images/pdf.png" alt="" class="left mr5"> <label class="left" for="pokemonGrey">PDF</label></span> -->
<span class="inline left"><input type="radio"  name="export_type" value="xls" id="xls"> <img src="/admin/images/csv.png" alt="" class="left mr5"> <label class="left" for="pokemonRed">XLS</label> </span>
<!-- <span class="inline left"><input type="radio"  name="export_type" value="print" id="print"> <i class="fa fa-print left mr5"></i> <label class="left" for="print">Print</label></span> -->
</div>

<input type="hidden" id="selected_columns" name="selected_columns" value=""/>
<input type="hidden" id="table" name="table" value="<?php echo $table;?>"/>
<input type="hidden" id="filter_order_options" name="filter_order_options" value="<?php echo isset($filterForm['filter_order_options'])?$filterForm['filter_order_options']:'';?>"/>
<input type="hidden" id="filter_year" name="filter_year" value="<?php echo (isset($filterForm['filter_year']))? $filterForm['filter_year']:'';?>"/>
<input type="hidden" id="filter_year_type" name="filter_year_type" value="<?php echo (isset($filterForm['filter_year_type']))? $filterForm['filter_year_type']:'';?>"/>
<input type="hidden" id="filter_month" name="filter_month" value="<?php echo (isset($filterForm['filter_month']))? $filterForm['filter_month']:'';?>"/>
<input type="hidden" id="filter_quarter_number" name="filter_quarter_number" value="<?php echo (isset($filterForm['filter_quarter_number']))?  $filterForm['filter_quarter_number']:'';?>"/>
<input type="hidden" id="filter_week_number" name="filter_week_number" value="<?php echo (isset($filterForm['filter_week_number']))? $filterForm['filter_week_number']:'';?>"/>
<input type="hidden" id="filter_check" name="filter_check" value="<?php echo (isset($filterForm['filter_check']))? $filterForm['filter_check']:'';?>"/>
<input type="hidden" id="minDate" name="minDate"  value="<?php echo isset($filterForm['minDate'])?$filterForm['minDate']:''?>"/>
<input type="hidden" id="maxDate" name="maxDate" value="<?php echo isset($filterForm['maxDate'])?$filterForm['maxDate']:$filterForm['maxDate'];?>"/>
<input type="hidden" id="subaction" name="subaction" value="<?php echo isset($filterForm['subaction'])?$filterForm['subaction']:'';?>"/>
<input type="hidden" id="service" name="service" value="<?php echo (isset($filterForm['service']))? $filterForm['service']:'';?>"/>
<input type="hidden" id="exportUrl" name="exportUrl" value="<?php echo (isset($filterForm['exportUrl']))?  $filterForm['exportUrl']:'';?>"/>
<input type="hidden" id="filter_year" name="menu" value="<?php echo (isset($filterForm['menu']))? $filterForm['menu']:'';?>"/>
<input type="hidden" id="filter_year_type" name="location" value="<?php echo (isset($filterForm['location']))? $filterForm['location']:'';?>"/>
<input type="hidden" id="filter_month" name="deliverypers" value="<?php echo (isset($filterForm['deliverypers']))? $filterForm['deliverypers']:'';?>"/>
<input type="hidden" id="filter_quarter_number" name="statuschange" value="<?php echo (isset($filterForm['statuschange']))?  $filterForm['statuschange']:'';?>"/>
<input type="hidden" id="filter_quarter_number" name="order_date" value="<?php echo (isset($filterForm['order_date']))?  $filterForm['order_date']:'';?>"/>
<input type="hidden" id="exporttype" name="exporttype" value="<?php echo (isset($exporttype))? $exporttype:'';?>"/> 


<div class="right mt15">
<button id="export"><i class="fa fa-share"></i>&nbsp;Export</button> 
</div>
<a class="close-reveal-modal">&#215;</a>
</form>
<script type="text/javascript">

$(document).ready(function(){

	$(":radio, :checkbox").uniform();
	var id = $("#exporttype").val();

	$("#"+id).prop('checked', true).uniform();
	$(document).foundation();

	$(document).on('click','.close-reveal-modal',function(){
	  $('#myModal').foundation('reveal', 'close');
	});
	
	$("#selectAll").click(function(){
		
		if(this.checked){
			$('.checkbox').prop('checked', true).uniform();
		}else{
			$('.checkbox').prop('checked',false).uniform();
		} 
	});
	

});
</script>


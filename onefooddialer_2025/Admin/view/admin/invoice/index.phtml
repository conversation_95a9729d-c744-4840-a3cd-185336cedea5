<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="printinv" class="common-orange-btn-on-hover"> Print invoices</a>
                </li>
            </ul>
        </div>
    </div>
</div>
  <style> #collectionlist{display:block; } </style>
      <div id="content" class="clearfix">
        <div class="large-12 columns">
            <div class="filter">
                <form class="advance_search" id="filterFrm" style="display:block" name="filterFrm" action="/invoice" method="post">
                    <div class="row">
                        <div class="medium-12 columns">
                            <div class="type left">
                                <label style="margin:0px" class="left inline" for="right-label">Status&nbsp;:&nbsp;</label>
                                    <select class="left filterSelect" name="status" id="status">
                                        <option value="">All</option>
                                        <option value="1">Paid</option>
                                        <option value="0">Unpaid</option>
                                    </select>

                                    <label class="left inline" for="minDate"> From&nbsp;:&nbsp; </label>
                                        <input class="left filterSelect" name ="minDate" id="minDate" readonly="readonly" type="text"   />

                                   <label class="left inline" for="maxDate" style="margin-left:0"> To&nbsp;:&nbsp;  </label>
                                        <input class="left filterSelect" name ="maxDate" id="maxDate" readonly="readonly" type="text"   />

                                        <button id="submitButton" class="button left tiny left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="portlet box yellow">
        	<div class="portlet-title">
                    <h4><i class="fa fa-table"></i>Invoice</h4>
                    <ul class="toolOption">
                        <li>
                            <div class="print" style="float:none">
                                <button id="innvoicebill" target="_blank" class="btn " data-dropdown="dropPrint"><i class="fa fa-print"></i>&nbsp;Print Invoice</button>

                            </div>
                        </li>
                         <li>
                            <div class="print" style="float:none">
                               <a id="collectionlist" href="/collectionlist"  target="_blank"  class="btn "><button ><i class="fa fa-print"></i>&nbsp;Print Collection List</button></a>

                            </div>
                        </li>

                    </ul>
                </div>
                <div class="portlet-body sales_data_table">

                    <div class="filter">
                        <div>
                                <a class="advance_search_click"> Hide advance Search </a>
                        </div>
                    </div>
        	

                <form id="frminvoice" target="_blank" class="" action="<?php echo $this->url('invoice',array('action' => 'bill'));?>" method="post" >
                    <input type="hidden" name="fromdate" id="fromdate">
                    <input type="hidden" name="todate" id="todate">
                	<table id="customer" class="display displayTable" width="100%">
                        <thead>
                            <tr>
                                <th class="no_sort" width="12%" style="padding-left:10px;position:relative;">
                                    <input id="selectAll" name="" type="checkbox">
                                        <label for="selectAll" style="margin:-2px 0 0 -13px;float:left;position:relative; width:100px;left:0px;padding:0 0 0 15px;"><b>&nbsp;All</b></label>
                                </th>
                                <th>Invoice No</th>
                                <th>Name</th>
    <!--                             <th>Address</th> -->
                                 <!-- <th>Contact No</th>
                               <th>Total Orders</th> -->
                                <th>Total Amount <!-- <i class="fa fa-rupee"></i> --></th>
                                <th>Invoice Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                    </table>
                </form>
          	</div>

            </div>

      </div>

    </div>
    <!-- END PAGE CONTAINER-->
    <style>
	#collectionlist { float: left; <?php if(!$is_data){ echo 'display:none !important';}  ?>}
	</style>
   <script>
		$(document).ready(function(){
		  $("#innvoicebill").click(function(){
			  var ctr = 0;
			  $('.sel_invoice:checked').each(function() {
				  ctr++ ;
			  });
			  if(ctr > 0 )
			  {

			  	$("#frminvoice").submit();
			  }
			  else
			  {
				 alert("please select invoice to print");
				return false;
			  }
		  });

		  $("#printReportList").on('click',function(e){
			  e.preventDefault();
			  $("#subaction").val("print");

			  $("#reportSearchForm").attr("target","_blank");
			  $("#reportSearchForm").submit();
		  });

		  $("#exportReportList").on('click',function(e){
			  e.preventDefault();
			  $("#subaction").val("export");
			  $("#reportSearchForm").submit();
		  });

		});
	</script>

<script type="text/javascript">
	
$(document).ready(function() {

	//myPageTable.init();

    var aoColumns = [];
    $('#customer thead th').each( function () {
        if ( $(this).hasClass('no_sort')) {
        	aoColumns.push( { "bSortable": false } );
        } else {
        	aoColumns.push( null );
        }
    });


	var invoiceTable = $('#customer').dataTable( {
        "processing": true,
        "serverSide": true,
        "aoColumns":aoColumns,
        "aaSorting": [[0,'desc']],
        /*"ajax": "/invoice/ajx-invoice",*/
        "ajax": {
            "url": "/invoice/ajx-invoice",
            "data": function ( d ) {
                d.status = $("#status").val();
                d.kitchenscreen = $('#selectkitchen').val();
                d.minDate = $('#minDate').val();
                d.maxDate = $('#maxDate').val();
                
         	}
     	},
        "fnInitComplete": function( oSettings, json ) {
        	//$(":checkbox").uniform();

        },
        "fnInfoCallback": function (oSettings, iStart, iEnd, iMax, iTotal, sPre){
        	$(":checkbox").uniform();
        }
    });

	//$.uniform.update();

	$('#selectAll').change(function (event) {
		event.preventDefault();
		var checkbox = $('#selectAll'),
        isChecked = checkbox.is(':checked');

		if(isChecked) {

			$(this).closest('table').find('td input:checkbox').prop('checked', true);
			$(this).closest('table').find('td input:checkbox').parent().addClass('checked')

		} else {

			$(this).closest('table').find('td input:checkbox').prop('checked', false);
			$(this).closest('table').find('td input:checkbox').parent().removeClass('checked')

		}
	});

	/* $("#status").on('change',function(){

		invoiceTable.api().ajax.reload(null, true);
		
	}); */
	 $("#submitButton").on('click',function(){

		 var fromdate = $("#minDate").val();
		 var todate = $("#maxDate").val();

		 if(fromdate !='')
		 { 
			$("#fromdate").val(fromdate);
		 }
		 if(todate !='')
		 {
			 $("#todate").val(todate);
		 }
	     invoiceTable.api().ajax.reload();
	 });


});
</script>


<script>
$(document).ready(function() {
	$('#selectAll').change(function (event) {
		event.preventDefault();
		var checkbox = $('#selectAll'),
        isChecked = checkbox.is(':checked');

		if(isChecked) {
			$(this).closest('table').find('td input:checkbox').prop('checked', true);

		} else {
			$(this).closest('table').find('td input:checkbox').prop('checked', false);
		}
		$.uniform.update();
	});
});
</script>
<script type="text/javascript">
  $(document).on('click',"#printinv",function(e){
      e.preventDefault();
      $('.portlet-title').find('.toolOption').attr("data-step", "1");
      $('.portlet-title').find('.toolOption').attr("data-intro", "1.Print single or multiple invoices by selecting checkbox.<br/>2.Click on Print button.");
      $('.portlet-title').find('.toolOption').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.toolOption').removeAttr("data-step");
      $('.portlet-title').find('.toolOption').removeAttr("data-intro");

  });

</script> 

<link rel="stylesheet" href="/admin/css/pepper-ginder-custom.css" type="text/css" />
<script src="/admin/js/jquery-1.7.2.js"></script>

<script type="text/javascript" src="/admin/js/plugins/colorpicker.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.jgrowl.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.alerts.js"></script>
<script type="text/javascript" src="/admin/js/custom/elements.js"></script>
<script src="/admin/js/widget.js"></script>

<script src="/admin/js/spinner.js"></script>
<script src="/admin//js/plugins/jquery-ui.js"></script>
<script type="text/javascript" src="/admin/js/jquery.ui.core.js"></script>
<script type="text/javascript" src="/admin/js/jquery.ui.datepicker.js"></script>
<script type="text/javascript" src="/admin/js/jquery-ui.multidatespicker.js"></script>
<script src="/admin/js/bootstrap.js"></script>

<style>
.alert-info {
    background-color: #D9EDF7;
    border-color: #BCE8F1;
    color: #3A87AD;
}
.alert-success {
    background-color: #DFF0D8;
    border-color: #D6E9C6;
    color: #468847;
}
.cartmsg{
width:96%;
}
#cart-container .alert strong i{
text-decoration:none;
color:#000;
font-style:normal;
}
.stdform p, .stdform div.par {
	clear: both;
}
.stdform .smallinput {
	width: 100%;
}
.stdform .datecss{margin-left:0 !important;}

.stdform label{text-align:left;width:120px;}
.stdform span.field, .stdform div.field {display: block; margin-left:150px;}
.stdform .stdformbutton {margin-left:150px;}
.stdform .red{margin-left:150px;list-style:none;}
</style>
<!--[if lt IE 9]>
	<script src="http://css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js"></script>
<![endif]-->


<script>
jQuery(document).ready(function(){
	var today = new Date();
	var date = new Date();
	jQuery('#with-altField').multiDatesPicker({
		dateFormat: "yy-mm-dd",
		altField: '#altField',
		minDate: 0 	<?php if($this->cut_time == 1) {?>,
		addDisabledDates: [today] <?php } ?><?php if($this->date_var_to_layout) { ?>,
		addDates: [<?php foreach($this->date_var_to_layout as $date) {?>date.setDate(<?php echo $date?>),<?php } ?>]
		<?php }?>,
		onSelect: function(date) {
			//var dates = $('#with-altField').multiDatesPicker('getDates');
			jQuery.ajax({
				 url:"<?php echo $this->url('backorder',array('action' => 'get-cart')); ?>",
				 type: "POST",
				 success:function(response)
				 {
					updateCartContainer(response);
				 }
			});
        }

	});
	jQuery('#ui-datepicker-div').hide();
});
</script>

		<div class="container">
	<div class="row">
<?php
if(is_array($pre_msg))
{
		if(array_key_exists("error",$pre_msg))
		{
			if($pre_msg['error'])
			{?>

			<div class="alert alert-<?php echo $pre_msg['error_type']; ?>">
			    <button type="button" class="close" data-dismiss="alert">&times;</button>
			    <strong><?php echo $pre_msg['error'] ?></strong>
		    </div>

		<?php }
		} ?>
		<?php
		if(array_key_exists("success",$pre_msg))
		{
			if($pre_msg['success'])
					{?>

					<div class="alert alert-success">
					    <button type="button" class="close" data-dismiss="alert">&times;</button>
					    <strong><?php echo $pre_msg['success']; ?></strong>
				    </div>

				<?php }
		} ?>


<?php
}
 ?>
 <?php
echo $this->flashMessenger()->render('error', array('alert', 'alert-danger'));
echo $this->flashMessenger()->render('success', array('alert', 'alert-success'));
?>

	</div>
</div>
      <div class="content" style="float:left;width:100%;margin-top:-8px;">
        <div class="contenttitle">
          <h2 class="form"><span>Select Order</span></h2>
        </div>
        <!--contenttitle-->

        <div class="col-md-6 meal-box pad-left">
          <!--- SELECT YOUR MEAL Start  -->

          <p class="orHeader">SELECT YOUR MEAL</p>
    <div class="slider">
     <div class="fooddiv">
			<?php
     	$keys = array();
     	if(is_array($cart))
     	{
			$keys = array_keys($cart);
     	}
		$meal_count = count($meal) - 1;
		//$meal_ctr = 0;
		//echo $meal_count;
		for($meal_ctr= 0; $meal_ctr <= $meal_count; $meal_ctr += 4)
		{

			$meal_group = array_slice($meal, $meal_ctr ,4);
			echo '<div class="clearfix">';
			foreach ($meal_group as $product)
			{
				//echo  $product_path.$product['image_path'];exit;
				$img_path = (file_exists('./public'.$product_path.$product['image_path']))? $product_path.$product['image_path']:$default_product_path; ?>
			 <div class="col-md pad-left">
              <article class="lunch-box box-border mar-left">
                <div class="box-type-txt"><?php echo $product['name']; ?></div>
                <img src="<?php echo $img_path; ?>" alt="" class="center-block" height="90"/>
                <div class="luInput">
                  <label class="css-label">
                    <input type="checkbox" name="radiog_dark" <?php echo (in_array($product['pk_product_code'], $keys))?'checked':''; ?> data-amount = "<?php echo $product['unit_price']; ?>" data-type="<?php echo $product['product_type']; ?>" data-name="<?php echo $product['name']; ?>" data-id="<?php echo $product['pk_product_code']; ?>" class="checkbox-check" />
                  </label>
                  <div class="box-type"> INR :<?php  echo $product['unit_price']?>/-&nbsp;&nbsp;&nbsp;Qty:
                    <input id="quantity<?php echo $product['pk_product_code']; ?>" class="spinner qunt" type="text" value="<?php echo (in_array($product['pk_product_code'], $keys))?$cart[$product['pk_product_code']]['quantity']:'1'; ?>"  min="1" />
                  </div>
                </div>
              </article>
            </div>

			<?php }
			echo '</div>';


		}
     	//echo '<pre>';print_r($meal_group);exit;
     	?>




        </div>
      </div>
          <div class="paggignation clearfix">
            <ul class="pagination pull-right">
              <!--<li class="disabled"><a href="#"><i class="fa fa-angle-double-left"></i> First</a></li>-->
              <li><a href="javascript:void(0)" id="go-left"><i class="fa fa-angle-left"></i> Previous</a></li>
              <li><a href="javascript:void(0)" id="go-right">Next <i class="fa fa-angle-right"></i></a></li>
             <!-- <li><a href="#">Last <i class="fa fa-angle-double-right"></i></a></li>-->
            </ul>
          </div>
          <!--- SELECT YOUR MEAL End  -->

          <!--- Select your Extra Start  -->
          <p class="orHeader">SELECT YOUR EXTRA</p>
          <!-- The slider -->
    <div class="slider2">
     <div class="fooddiv">
			 <?php


		$extra_count = count($extra) - 1;
		//$meal_ctr = 0;

		for($extra_ctr= 0; $extra_ctr <= $extra_count; $extra_ctr += 9)
		{

			$extra_group = array_slice($extra, $extra_ctr ,9);
			echo '<div class="clearfix">';
			foreach ($extra_group as $product)
			{
				$img_path = (file_exists('./public'.$product_path.$product['image_path']))? $product_path.$product['image_path']:$default_product_path;
			?>

            <div class="col-md pad-left extras">
              <article class="lunch-box box-border mar-left">
                <div class="box-type-txt"><?php echo $product['name']; ?></div>
                <img src="<?php echo $img_path; ?>" alt="" class="center-block" width="50"/>
                <div class="luInput">
                  <label class="css-label">
                    <input type="checkbox" name="radiog_dark" <?php echo (in_array($product['pk_product_code'], $keys))?'checked':''; ?> data-amount = "<?php echo $product['unit_price']; ?>" data-type="<?php echo $product['product_type']; ?>" data-name="<?php echo $product['name']; ?>" data-id="<?php echo $product['pk_product_code']; ?>" class="checkbox-check" />
                  </label>
                  <div class="box-type">
                    <div class="price">INR : <?php  echo $product['unit_price']?>/- &nbsp;</div>
                    <div class="qty">
                      <div class="qu">Qty :</div>
                      <div class="spin">&nbsp;
                        <input type="text" id="quantity<?php echo $product['pk_product_code']; ?>" class="spinner qunt" value="<?php echo (in_array($product['pk_product_code'], $keys))?$cart[$product['pk_product_code']]['quantity']:'1'; ?>"  min="1" />
                      </div>
                    </div>
                  </div>
                </div>
              </article>
            </div>

			<?php }
			echo '</div>';


		}
     	//echo '<pre>';print_r($meal_group);exit;
     	?>


         </div>
        </div>
          <!--- Select your Extra End  -->
          <div class="paggignation clearfix">
            <ul class="pagination pull-right">
              <!--<li class="disabled"><a href="#"><i class="fa fa-angle-double-left"></i> First</a></li>-->
              <li><a href="javascript:void(0)" id="go-left2"><i class="fa fa-angle-left"></i> Previous</a></li>
              <li><a href="javascript:void(0)" id="go-right2">Next <i class="fa fa-angle-right"></i></a></li>
            <!--  <li><a href="#">Last <i class="fa fa-angle-double-right"></i></a></li>-->
            </ul>
          </div>
        </div>
        <div class="col-md-6">
         <?php
		 $form->setAttribute('action', $this->url('backorder', array('action' => 'customer-order')));
		 $form->setAttribute('class', 'stdform');
		 $form->prepare();
          echo $this->form()->openTag($form);

         ?>
          <div class="todaysOrder padd20">
            <div class="contact">

              <p class="orHeader">YOUR ORDER</p>

            </div>
            <!--<h4>You have Selected :</h4>-->

            <div class="boxMenu" id="cart-container">
             <?php
            	if(count($cart) > 0)
            	{
            		$total_Cart = 0;
            		foreach ($cart as $product)
            		{
            			$total_product = 0;
            			$total_product = $product['price'] * $product['quantity'];
            			$total_Cart += $total_product;
            			$extra = ($product['type'] == 'Extra')?'With Extra':'';
            			?>
					<div class="alert alert-danger alert-dismissable cartmsg">
                		<button type="button" class="close cart-destroy" data-pid="<?php echo $product['id']; ?>" data-dismiss="alert" >&times;</button>
                	<strong><i>INR <?php echo $total_product; ?></i> <?php echo $extra; ?>&nbsp;<?php echo $product['quantity']?>&nbsp;<?php echo $product['name']; ?> <i>/  INR <?php echo $product['price']; ?> each</i></strong> </div>

            	<?php } ?>
            	<div id="temp_cust_total_cart"></div>

            	<?php }
            	?>
              <!--  <div class="alert alert-danger alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <strong>1 Half Tifin</strong> </div>
              <div class="alert alert-danger alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <strong>With Extra 5 Chapati</strong> </div>
              <div class="alert alert-danger alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <strong>With Extra 2 Papad</strong> </div>
              <div class="alert alert-danger alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <strong>With Extra 1 Salad</strong> </div>-->
            </div>
            <div class="box">
              <h5>Click to choose your order dates</h5>
              <div id="with-altField"></div>
               <?php echo $this->formElement($form->get('dates'));
					echo $this->formElementErrors($form->get('dates'),array('class' => "red datecss"));
			  	?>
            </div>
             <script>
            	var dates = $('input[name=dates]').val();
            	var date = dates.split(',');
            	var day_count = date.length;
            	var day_sent = "";
            	if(day_count == 1 ) { day_sent = day_count+' Day'; }
            	else if(day_count == 0 ) { day_count = 1; }
            	else { day_sent = day_count+' Days'; }
				var total_cart = <?php echo $total_Cart; ?>;
				var total_days_cart = total_cart * day_count;
				var data_print = '<div class="alert alert-info cartmsg">';
				data_print += '<strong><i>INR '+total_days_cart+'</i> Total [ '+day_sent+' ]  <i>INR '+total_cart+' / Day</i></strong>';
				data_print += '</div>';
				//document.write(data_print);
				$('#temp_cust_total_cart').html(data_print);
			</script>
            <div class="contact  padd20">
              <p class="headerCo">DELIVERY LOCATION</p>
              <form method="post" action="" class="stdform">


                <p>
                  <label> Name:</label>
                  <span class="field">
                  <input type="text" class="smallinput" name="input1" placeholder="<?php echo $customer['customer_name']; ?>" disabled="disabled">

                  </span></p>
                <p>
                   <?php //echo $this->formLabel($form->get('phone')); ?>
                   <label> Contact Number:</label>
                  <span class="field">
                    <?php //echo $this->formElement($form->get('phone'));
					//echo $this->formElementErrors($form->get('phone'));
			  		?>
			  		 <input type="text" class="smallinput" name="phone" placeholder="<?php echo $customer['phone']; ?>" disabled="disabled">
			      </span> </p>
                <p>
                  <label> Email Id:</label>
                  <span class="field">
                  <input type="text" class="smallinput" name="input1" placeholder="<?php echo $customer['email_address']; ?>" disabled="disabled">
                  </span> </p>
                <p>
                    <?php echo $this->formLabel($form->get('location_code')); ?>
                  <span class="field">
                  <?php echo $this->formElement($form->get('location_code'));
					echo $this->formElementErrors($form->get('location_code'),array('class' => "red"));
			  		?>
                  </span>
                </p>
                <p>
                 <?php echo $this->formLabel($form->get('company_name')); ?>
                  <span class="field">
                  <!-- <input type="text" class="smallinput" name="input1" placeholder="<?php //echo $customer['company_name']; ?>" disabled="disabled">-->
                      <?php echo $this->formElement($form->get('company_name'));
							echo $this->formElementErrors($form->get('company_name'),array('class' => "red"));
			  		?>
                  </span> </p>
                <p>
                  <?php echo $this->formLabel($form->get('ship_address')); ?>
                  <span class="field">
                  <?php echo $this->formElement($form->get('ship_address'));
					echo $this->formElementErrors($form->get('ship_address'),array('class' => "red"));
			  		?>
                  </span> </p>
                <p>
                 <?php echo $this->formLabel($form->get('promo_code')); ?>
                  <span class="field">
                   <?php echo $this->formElement($form->get('promo_code'));
					echo $this->formElementErrors($form->get('promo_code'));
			  		?>
                  </span> </p>
                <!--  <p>
                  <label class="" for="address">&nbsp; </label>
                  <span class="field">
                  <input type="checkbox" class="check" id="termCon" name="termCon" />
                  &nbsp;I agree to Terms and <a href="privacyPolicy.html" title="" class="privacy">Privacy Policy</a>

                  </span></p>-->
                <p class="stdformbutton">
                  <input type="submit" name="Submit" value="Order Now"  class="button radius2">
                 <input type="reset" value="Go Back" class="reset radius2" onclick="window.location='<?php echo $this->url('backorder',array('action' => 'index')); ?>'">
                </p>

            </div>
          </div>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag(); ?>
        </div>
        <!--content-->

      </div>
      <!--maincontentinner-->
<script src="/admin/js/jquery.diyslider.min.js" type="text/javascript"></script>
<script type="application/javascript">

if(jQuery(window).width()<=480){

$(".slider").diyslider({
	width:310,
	height:350,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides
});
// this is all you need!
}


else if (jQuery(window).width()<800){
	$(".slider").diyslider({
	width:400,
	height:370,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides

});
// this is all you need!
}

else if (jQuery(window).width()<1030){
	$(".slider").diyslider({
	width:450,
	height:380,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides

});
// this is all you need!
}

else if (jQuery(window).width()<1366){
	$(".slider").diyslider({
	width:550,
	height:370,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides

});
}

else if (jQuery(window).width()<1680){
	$(".slider").diyslider({
	width:690,
	height:380,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides

});
// this is all you need!
}

else

{
	$(".slider").diyslider({
	width:820,
	height:365,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides
});

}

// use buttons to change slide
$("#go-left").bind("click", function(){
    // Go to the previous slide
    $(".slider").diyslider("move", "back");
});
$("#go-right").bind("click", function(){
    // Go to the previous slide
    $(".slider").diyslider("move", "forth");
});


if(jQuery(window).width()<=480){

$(".slider2").diyslider({
	width:460,
	height:370,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides
});
// this is all you need!
}


else if (jQuery(window).width()<800){
	$(".slider2").diyslider({
	width:400,
	height:370,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides

});
// this is all you need!
}

else if (jQuery(window).width()<1030){
	$(".slider2").diyslider({
	width:450,
	height:370,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides

});
// this is all you need!
}


else if (jQuery(window).width()<1366){
	$(".slider2").diyslider({
	width:550,
	height:370,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides

});
}

else if (jQuery(window).width()<1680){
	$(".slider2").diyslider({
	width:690,
	height:370,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides

});
// this is all you need!
}

else

{
	$(".slider2").diyslider({
	width:825,
	height:370,
	display: 1, // number of slides you want it to display at once
    loop: false // disable looping on slides
});

}

$("#go-left2").bind("click", function(){
    // Go to the previous slide
    $(".slider2").diyslider("move", "back");
});
$("#go-right2").bind("click", function(){
    // Go to the previous slide
    $(".slider2").diyslider("move", "forth");
});

</script>

<script>
function updatecart(data)
{
	//alert(data);
	jQuery.ajax({
		 url:"<?php echo $this->url('backorder',array('action' => 'updatecart')); ?>",
		 type: "POST",
		 data : data,
		 success:function(response)
		 {
			updateCartContainer(response);
		 }
	});
}
function updateCartContainer(result)
{
	var dates = jQuery('#with-altField').multiDatesPicker('getDates');
	var day_count = dates.length;
	var day_sent = "";
	if(day_count == 1 ) { day_sent = day_count+' Day'; }
	else if(day_count == 0 ) { day_count = 1; }
	else { day_sent = day_count+' Days'; }

	//var res = jQuery.parseJSON(result);
	var res = result;
	var data ="";var extra = "";var total_cart = 0;

	jQuery.each( res, function( index, value ){
		var total_product = 0;
		total_product = parseFloat(value.price) * parseFloat(value.quantity);
		total_cart += total_product;
		if(value.type == 'Extra')
		{
			extra ="With Extra";
		}
		else
		{
			extra ="";
		}
		data += "<div class='alert alert-danger alert-dismissable cartmsg'>";
		data +='<button type="button" class="close cart-destroy" data-pid='+value.id+' data-dismiss="alert" aria-hidden="true">&times;</button>';
        data += '<strong><i>INR '+total_product+'</i> '+extra+'&nbsp;'+value.quantity+'&nbsp;'+value.name+'<i> / INR '+value.price+' each</i></strong> </div>';
	});
	total_cart_day = total_cart * day_count;
	data += '<div class="alert alert-info cartmsg">';
	data += '<strong><i>INR '+total_cart_day+'.</i> Total [ '+day_sent+' ]  <i>INR '+total_cart+' / Day</i></strong>';
	data += '</div>';
	jQuery('#cart-container').html(data);

}
function removefromcart(id)
{
	var data = 'id='+id;
	jQuery.ajax({
		 url:"<?php echo $this->url('backorder',array('action' => 'removefromcart')); ?>",
		 type: "POST",
		 data : data,
		 success:function(response)
		 {
			updateCartContainer(response);
			jQuery('.checkbox-check:checked').each(function() {
				   var value = jQuery(this).data("id");
				   if(value == id)
				   {
						this.checked = false;
				   }
			});
		 }
	});
}

	jQuery(function() {
		jQuery(document).on("click",".cart-destroy",function(){
			var id = jQuery(this).data("pid");
			removefromcart(id);

		});

		jQuery('.checkbox-check').bind("click",function(){
			//var action = "";
			var id = jQuery(this).data("id");
			if(this.checked == false)
			{
				removefromcart(id);
				return true;
			}
			var name = jQuery(this).data("name");
			var quantity = jQuery('#quantity'+id).val();
			var type = jQuery(this).data("type");
			var amount = jQuery(this).data("amount");
			var data = 'id='+id+'&name='+name+'&quantity='+quantity+'&type='+type+'&amount='+amount;
			updatecart(data);

		});
		jQuery( ".spinner" ).spinner({
			step: 1,
			numberFormat: "n",
			spin: function(event, ui) {
				var quantity =  ui.value;
				var id = jQuery(this).closest("article").find(".checkbox-check").data("id");
				var name = jQuery(this).closest("article").find(".checkbox-check").data("name");
				var type = jQuery(this).closest("article").find(".checkbox-check").data("type");
				var amount = jQuery(this).closest("article").find(".checkbox-check").data("amount");
				var data = 'id='+id+'&name='+name+'&quantity='+quantity+'&type='+type+'&amount='+amount;
				updatecart(data);
				jQuery('.checkbox-check').each(function() {
					   var value = jQuery(this).data("id");
					   if(value == id)
					   {
							this.checked = true;
					   }
				});
	        }

		});
		jQuery( "#culture" ).change(function() {
			var current = jQuery( "#spinner" ).spinner( "value" );
			Globalize.culture( jQuery(this).val() );
			jQuery( "#spinner" ).spinner( "value", current );
		});
	});
	</script>
<script>
var MDP
$(document).ready(function ($) {
	var name=$("#hname").val();
	$("#imgname").attr("alt","Welcome'"+name+"");
	$("#spanname").text(name);


	$("#crossclsicon").on('click',function(){
				$(this).parent().hide();
		})
    $("#datepicker").multiDatesPicker({
        autoSize: true,
        minDate: 0,
        maxDate: "+2M",
        altField: "#alternate"
    });


	MDP = $('#with-altField').multiDatesPicker({
		dateFormat: "yy-mm-dd",
		altField: '#altField',
		minDate: 0 	<?php if($this->date_var_to_layout) { ?>,
		addDates: [<?php foreach($this->date_var_to_layout as $date) {?>date.setDate(<?php echo $date?>),<?php } ?>]
		<?php }?>,
		onSelect: function(date) {
			//var dates = $('#with-altField').multiDatesPicker('getDates');
			
			var menu = $("#menu").val();
			
			$.ajax({
				 url:"<?php echo $this->url('front',array('action' => 'get-cart')); ?>",
				 type: "POST",
				 data: "menu="+menu,
				 success:function(response)
				 {
					updateCartContainer(response);
				 }
			});
        }

	});
    
});
</script>

<!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!--[if lt IE 9]>
<script src="js/html5shiv.js"></script>
<script src="js/respond.min.js"></script>
<![endif]-->

<body>

	<!-- Wrapper -->

	<!-- END TOP NAVIGATION BAR -->
<?php //echo '<pre>';print_r($pre_msg);?>

	<div id="content">
	
		<div class="row">
			<div class="large-12 columns">
			<?php
			if(is_array($pre_msg))
			{
				if(array_key_exists("error",$pre_msg))
				{
					if($pre_msg['error'])
					{
						if($pre_msg['error_type']=='danger')
						{
							$pre_msg['error_type']='warning';
						}
					?>
					<div class="columns alert-box <?php echo $pre_msg['error_type']; ?>">
							<a id="crossclsicon" type="button" class="close cart-destroy"
								data-dismiss="alert">&times;</a> <strong><?php echo $pre_msg['error'] ?></strong>
					</div>
		
				<?php 
					}
				} 
				?>
				
				<?php
				if(array_key_exists("success",$pre_msg))
				{
					if($pre_msg['success'])
							{?>
		
							<div class="columns alert-box success">
							<a id="crossclsicon" type="button" class="close"
								data-dismiss="alert">&times;</a> <strong><?php echo $pre_msg['success']; ?></strong>
						</div>
		
						<?php }
				} ?>
		
		
			<?php
			}
			 ?>
			
		<?php
			$this->flashMessenger()
			->setMessageOpenFormat('<div%s>')
			->setMessageSeparatorString('<br />')
			->setMessageCloseString('</div>');
			echo $this->flashMessenger()->render('error', array('alert-box', 'warning'));
			echo $this->flashMessenger()->render('success', array('alert-box', 'success'));
		?>
			</div>
		</div>
		
            <?php
				$form->setAttribute('action', $this->url('backorder', array('action' => 'customer-order')));
				$form->prepare();
			    echo $this->form()->openTag($form);

			?>
             <div class="row">
                
					<div class="large-6 columns">
						<div class="large-6 medium-6 columns pad0">
					
						<input type="hidden" name="delivery_charge" id="delivery_charge" value="<?php echo $this->deliverycharges;?>">
	                <?php 
	                
	                foreach($this->setting['MENU_TYPE'] as $index=>$menu){
	                	
	                	$class = "mb0 mr5 left";
	                	$disabledClass = "";
	                	
	                	if($menu==$this->menuSelected){
	                		$disabledClass = " disabled ";
	                	}
	                	
		                if($index == 2){
		                	
		                	$class = "mb0";
		                }
	                ?>		<button type="button" data-menu="<?php echo $menu; ?>" class="clsmenu btn-padding <?php echo $class;echo $disabledClass;?>">
								<?php echo ucfirst($menu); ?><i class="fa fa-check"></i>
							</button>
					
					<?php
						 
		                }
					?>	</div>
					
					<!-- new inner divs -->
					
						  <div class="inner-tab">
                           
                          </div>
                          <!-- new inner divs -->
                       
					</div>

					<div class="large-6 columns">
	
						<div class="row">
							<div class="large-6  medium-6 columns tifinInfo">
								<h1>YOUR ORDER <span id="subOrderHeading"></span> </h1>
                            
                            <div id="cart-container" class="boxMenu">
			            	<?php

			            	if(isset($cart[$this->menuSelected]) && count($cart[$this->menuSelected]) > 0)
			            	{
			            		$total_Cart = 0;
			            		
			            		foreach ($cart[$this->menuSelected] as $product)
			            		{	
									$total_product = 0;
			            			$total_product = $product['price'] * $product['quantity'];
			            			$total_Cart += $total_product;
			            			$extra = ($product['type'] == 'Extra')?'With Extra':'';
			            			$class = ($product['type'] == 'Extra')?'alert-box subMenuItem radius':'alert-box menuItem radius';
			            			?>

                            		<div data-alert class="<?php echo $class;?>">
										&nbsp;

										<div class="proName"><?php echo $product['quantity']?>&nbsp;<?php echo $product['name']; ?> / <!-- <i
												class="fa fa-rupee"></i> --> <?php echo $this->currencyFormat($product['price']); ?> Each</div>
										<div class="totalCost">
											<!-- <i class="fa fa-rupee"></i> --><?php echo $this->currencyFormat($total_product); ?></div>
										<a href="#" data-pid="<?php echo $product['id']; ?>"
											data-dismiss="alert" class="close cart-destroy">&times;</a>
                                <?php /*<button type="button" class="close cart-destroy" data-pid="<?php echo $product['id']; ?>" data-dismiss="alert" >&times;</button>*/?>
                            	</div>
                            <?php } ?>
				            	<div id="temp_cust_total_cart"></div>

				            	<?php }
			            	?>
			            	</div>
							</div>


							<div class="large-6 medium-6 columns tifinInfo">
								<h1>Click to choose your order dates</h1>
							 <div class="orderDate">
									<div class="btn-group btn-group-radio">
											<input type="radio" name="optionsRadios" id="optionsRadios1" value="option1">
												<label class="btn yes" for="optionsRadios1"> All Days</i> </label>
												<input type="radio" name="optionsRadios" id="optionsRadios2" value="option2">
												<label class="btn no" for="optionsRadios2">Working Days </label>
											</div>
									</div>
								<div class="dateBox">
									<div id="with-altField"></div>

					                <?php echo $this->formElement($form->get('dates'));
										echo $this->formElementErrors($form->get('dates'),array('class' => "red datecss"));
								  	?>
                            	</div>
							</div>

						</div>

						<script>
			            	var dates = $('input[name=dates]').val();
			            	var date = dates.split(',');
			            	var day_count = date.length;
			            	var day_sent = "";
			            	if(day_count == 1 ) { day_sent = day_count+' Day'; }
			            	else if(day_count == 0 ) { day_count = 1; }
			            	else { day_sent = day_count+' Days'; }
							var total_cart = <?php if(isset($total_Cart)){ echo $total_Cart;} else{ echo "0";} ?>;
							var total_days_cart = total_cart * day_count;
							var data_print = '<div class="alert-box success radius">';
							data_print += '<strong>INR '+total_days_cart+' Total [ '+day_sent+' ]  INR '+total_cart+' / Day</strong>';
							data_print += '</div>';
							//document.write(data_print);
							$('#temp_cust_total_cart').html(data_print);
		
						</script>


						<div class="clearBoth10"></div>
						<h1>DELIVERY DETAILS</h1>
						
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">
								<label class="inline right">Name :<span class="red"></span></label>
							</div>
							<div class="large-8  small-8 medium-8 columns">
								<label class="inline left"><b><?php echo $customer['customer_name']; ?></b></label>
								<input type="hidden" name="hdnname" id="hdnname"
									value="<?php echo $customer['customer_name'];?>">
							</div>
						</div>
						<?php if($customer['email_address']!='') {?>
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">
								<label class="inline right">Email Id :<span class="red"></span></label>
							</div>
							<div class="large-8  small-8 medium-8 columns">
								<label class="inline left"><b><?php echo $customer['email_address']; ?></b></label>
							</div>
						</div>
						<?php } ?>
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">
								<label class="inline right">Contact Number :<span class="red"></span></label>
							</div>
							<div class="large-8  small-8 medium-8 columns">
								<label class="inline left"><b><?php echo $customer['phone']; ?></b></label>
							</div>
						</div>
						<?php if($customer['company_name']!='') {?>
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">
								<label class="inline right">Company Name<span class="red"></span></label>
							</div>
							<div class="large-8  small-8 medium-8 columns">
								<label class="inline left"><b><?php echo $customer['company_name']; ?></b></label>
							</div>
						</div>
						<?php } ?>
						
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">
                                <?php echo $this->formLabel($form->get('city')); ?>
                            </div>
							<div class="large-8  small-8 medium-8 columns">
                                <?php echo $this->formElement($form->get('city'));
								echo $this->formElementErrors($form->get('city'),array('class' => "red"));
			  				?>
                            </div>
						</div>
						
					  <?php // if($this->menuSelected=='breakfast'){?>
					  <div id="default_locations" class="dn">
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">
                                <?php echo $this->formLabel($form->get('location_code')); ?>
                            </div>
							<div class="large-8  small-8 medium-8 columns">
                                <?php echo $this->formElement($form->get('location_code'));
								echo $this->formElementErrors($form->get('location_code'),array('class' => "red"));
						  		?>
                            </div>
						</div>
						
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">
                                <?php echo $this->formLabel($form->get('ship_address')); ?>
                            </div>
							<div class="large-8  small-8 medium-8 columns">
                                <?php echo $this->formElement($form->get('ship_address'));
								echo $this->formElementErrors($form->get('ship_address'),array('class' => "red"));
			  			?>
                            </div>
						</div>
						
						<!-- New Functionality: Postpaid orders can be made from admin interface 16/04/15 -->
						
						<input type="hidden" name="default_code_hd" id="default_code_hd" value="<?php echo isset($customer['location_code'])?$customer['location_code']."#".$customer['location_name']:'';?>" />
						<input type="hidden" name="default_address_hd" id="default_address_hd" value="<?php echo isset($customer['customer_Address'])?$customer['customer_Address']:'';?>" />
						</div>
						<?php // }?>
						
						<?php //if($this->menuSelected=='lunch'){?>
						<div id="lunch_locations" class="dn">
							<div class="row">
							<div class="large-4 small-4 medium-4 columns">
                                <?php echo $this->formLabel($form->get('lunch_code')); ?>
                            </div>
							<div class="large-8  small-8 medium-8 columns">
                                <?php echo $this->formElement($form->get('lunch_code'));
								echo $this->formElementErrors($form->get('lunch_code'),array('class' => "red"));
						  		?>
                            </div>
                            </div>
                            <div class="row">
                            <div class="large-4 small-4 medium-4 columns">
                                <?php echo $this->formLabel($form->get('lunch_Address')); ?>
                            </div>
							<div class="large-8  small-8 medium-8 columns">
                                <?php echo $this->formElement($form->get('lunch_Address'));
								echo $this->formElementErrors($form->get('lunch_Address'),array('class' => "red"));
						  		?>
                            </div>
                            <input type="hidden" name="lunch_code_hd" id="lunch_code_hd" value="<?php echo (isset($customer['lunch_loc_code']))?$customer['lunch_loc_code']."#".$customer['lunch_loc_name']:'';?>" />
							<input type="hidden" name="lunch_address_hd" id="lunch_address_hd" value="<?php echo isset($customer['lunch_add'])?$customer['lunch_add']:'';?>" />
						</div>
						</div>
						<?php // }?>
						
							<?php //if($this->menuSelected=='dinner'){?>
						<div id="dinner_locations" class="dn">
							<div class="row">
								<div class="large-4 small-4 medium-4 columns">
	                                <?php echo $this->formLabel($form->get('dinner_code')); ?>
	                            </div>
								<div class="large-8  small-8 medium-8 columns">
	                                <?php echo $this->formElement($form->get('dinner_code'));
									echo $this->formElementErrors($form->get('dinner_code'),array('class' => "red"));
							  		?>
	                            </div>
                            </div>
                            <div class="row">
	                            <div class="large-4 small-4 medium-4 columns">
	                                <?php echo $this->formLabel($form->get('dinner_Address')); ?>
	                            </div>
								<div class="large-8  small-8 medium-8 columns">
	                                <?php echo $this->formElement($form->get('dinner_Address'));
									echo $this->formElementErrors($form->get('dinner_Address'),array('class' => "red"));
							  		?>
	                            </div>
							</div>
							<input type="hidden" name="dinner_code_hd" id="dinner_code_hd" value="<?php echo (isset($customer['dinner_loc_code']))?$customer['dinner_loc_code']."#".$customer['dinner_loc_name']:'';?>" />
							<input type="hidden" name="dinner_address_hd" id="dinner_address_hd" value="<?php echo isset($customer['dinner_add'])?$customer['dinner_add']:'';?>" />
						</div>
						<?php //}?>
						
						<!-- <div class="row">
							<div class="large-4 small-4 medium-4 columns">
                               <?php //echo $this->formLabel($form->get('promo_code')); ?>
                            </div>
							<div class="large-8  small-8 medium-8 columns">
                                <?php //echo $this->formElement($form->get('promo_code'));
							//	echo $this->formElementErrors($form->get('promo_code'),array('class' => "red"));
			  					?>
                            </div>
						</div> -->
						
					
						<!-- 	
						 <div class="row">
							<div class="large-4 small-4 medium-4 columns">
								<label class="inline right">Payment method:<span class="red">*</span></label>
							</div>
							<div class="large-8  small-8 medium-8 columns">
							      <input class="chk" type="radio" name="payment_type" id="withpayment" value="withpayment">
							      	<label class="pull-left" for="withpayment">With Payment</label>
								<input class="chk" type="radio" name="payment_type" id="withoutpayment" value="withoutpayment">
								<label class="pull-left" for="withoutpayment">Confirm Without Payment</label>
							</div>
						</div>  -->
						
						<h1>ORDER SUMMARY</h1>
						
						<div class="row">
							<div class="large-4 columns">
								<label class="inline right">Total Amount :</label>
							</div>
							<div class="large-8 columns" name="totalamt" id="totalamt">
								
							</div>
						</div>
												
						<div class="row">
							<div class="large-4 columns">
								<label class="inline right">Delivery Charges :</label>
							</div>
							<div class="large-8 columns" name="deliverychrg" id="deliverychrg">
								
							</div>
						</div>
												
						<div class="row">
							<div class="large-4 columns">
								<label class="inline right">Discount :</label>
							</div>
							<div class="large-8 columns" name="discount" id="discount">
								
							</div>
						</div>
						
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">
                       			<?php echo $this->formElement($form->get('thirdparty'));
									  echo $this->formElementErrors($form->get('thirdparty'),array('class' => "red"));
									  echo $this->formElement($form->get('thirdpartytype'));
							 
									  $thirdparty =  $form->get('thirdpartytype')->getValue();
								?>
                            </div>
							<div class="large-8 small-8 medium-8 columns  dn" id="commision">
							<input id="commissionrate" type="hidden" name="commissionrate">
							<div class="large-2 small-2 medium-2 columns" >
                                <?php 
									echo $this->formElement($form->get('commission'));
									echo $this->formElementErrors($form->get('commission'),array('class' => "red"));
								?>
								</div>
								
								<div class="large-6 small-6 medium-6 columns left"  name="lbltype" id="lbltype" style="line-height:2">
								
								</div>
			  				</div> 
			  				<div class="large-8 small-8 medium-8 columns dn" id="thirdpartylbl">
			  					<?php echo $this->formLabel($form->get('thirdparty')); ?>
			  				</div>
			  				
			  				
						</div>
						
<!-- 						<div id="commision" class="dn"> -->
<!-- 							<div class="row" > -->
<!-- 								<div class="large-4 small-4 medium-4 columns"> -->
	                                <?php //echo $this->formLabel($form->get('commission')); ?>
<!-- 	                            </div> -->
<!-- 								<div class="large-8 small-8 medium-8 columns"> -->
	                                <?php //echo $this->formElement($form->get('commission'));
// 									//echo $this->formElementErrors($form->get('commission'),array('class' => "red"));
// 				  				?>
<!-- 	                            </div> -->
<!-- 							</div> -->
<!-- 						</div> -->
						
						<div class="row" id="taxdiv">
							<div class="large-4 columns">
								<label class="inline right">Inclusive All Taxes :</label>
							</div>
							<div class="large-8 columns" name="taxamt" id="taxamt">
								
							</div>
						</div>
						
						<div class="row">
							<div class="large-4 columns">
								<label class="inline right">Net Payable :</label>
							</div>
							<input type="hidden" name="netpayment" id="netpayment">
							<div class="large-8 columns" name="netpay" id="netpay">
								
							</div>
						</div>
						
						
						<?php 	$pay_methods = $form->get('PAYMENT_METHODS');
								$pay_methods_opt = $pay_methods->getOptions();
						?>
								
						<h1>PAYMENT OPTION</h1>
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">
								<?php echo $this->formLabel($form->get('PAYMENT_METHODS')); ?>
							</div>
							<div class="large-8 small-8 medium-8 columns prepaid">
								<?php
						   	    	foreach($pay_methods_opt['value_options'] as $key=>$val){
							        	$attribute = ($key=='withpayment')? 'id="withpayment" value="withpayment"': 'id="withoutpayment" value="withoutpayment"';
							    ?>
										
								<input  type="radio" class="chk"  name="PAYMENT_METHODS"  <?php echo $attribute;?> <?php echo $checked= ( (isset($seleted_payment) && ($seleted_payment==$val) )?'checked':'' ); ?> >
								<label class="pull-left" for="<?php echo $val;?>"><?php echo $val;?></label>
								<?php }?>
						   </div>
						</div>
						
						<div class="row" id="paydetails">
							<div class="large-4 columns">
								&nbsp;
							</div>
							<div class="large-8 columns">
								<div class="use_wallet mb15 dn" id="withpay" name="withpay">
									<input type="hidden" name="custbal" id="custbal" value="<?php echo $customer['balance']; ?>">
 									<label class="bal green" name="availbal" id="availbal"><b>Availabel Balance :<?php echo $customer['balance']; ?></b></label>
 									<label class="Insf_bal red dn" name="insfbal" id="insfbal"><b>Insufficient Balance</b></label>  
								</div>
								<div class="use_without_payment mb15 dn" id="withoutpay" name="withoutpay">
									<label class="orange"><b>Order will be converted to postpaid</b></label>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="large-4 small-4 medium-4 columns">&nbsp;</div>
							<div class="large-8  small-8 medium-8 columns">
								<button id="custSub" type="submit" class="button left tiny"
									data-text-swap="Processing.. &nbsp;">
									Order Now &nbsp;<i class="fa fa-angle-double-right"></i>
								</button>
								<button id="custSubReset" type="reset" class="button left tiny left5"
									onclick='window.location="/dashboard"'
									data-text-swap="Wait..">
									Back &nbsp;<i class="fa fa-reply"></i>
								</button>
							<?php /* <input type="submit" name="Submit" class="button" value="Order Now" />
                   					<input type="reset" value="Go Back" class="reset button" onclick='window.location="<?php echo $this->url('home'); ?>"'>*/?>
                            </div>
						</div>
					</div>
					 <input type="hidden" id="dhdnspinnercode" name="dhdnspinnercode" value="1"/>
					 <input type="hidden" id="menu" name="menu" value="<?php echo $this->menuSelected;?>"/>
					 <input type="hidden" id="activemenu" name="activemenu">
				</div>
            <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag(); ?>
        </div>

	<!-- BEGIN FOOTER -->

	<!-- wrapper End -->

	<!-- wrapper End -->
 <script type="text/javascript" src="/admin/js/foundation.min.js"></script>
 <script type="text/javascript" src="/admin/js/slick.js"></script>
 <!--  <script type="text/javascript" src="/admin/js/customforms.js"></script> -->
 <!-- <script type="text/javascript" src="/admin/js/jquery.cookie.js"></script> -->       
 <script type="text/javascript" src="/admin/js/vendor/fastclick.js"></script> 
 
	<script type="text/javascript">

	$(document).foundation();
	
    $(function () {
        
        jQuery('#customize-spinner').spinner('changed', function (e, newVal, oldVal) {
      
           // $('#old-val').text(oldVal);
          //  $('#new-val').text(newVal);
        });
    })
    
    var slickMeal = function(){

        $('.Meal').slick({
            dots: false,
            infinite: false,
            speed: 300,
            slidesToShow: 2,
            slidesToScroll: 2,
            responsive: [{
                    breakpoint: 1920,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 3,
                        infinite: false,
                        dots: false
                    }
                },

                {
                    breakpoint: 1366,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 2,
                        infinite: false,
                        dots: false
                    }
                },

                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 2,
                        infinite: false,
                        dots: false
                    }
                }, {
                    breakpoint: 600,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 2,
                        infinite: false,
                        dots: false
                    }
                }, {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        infinite: false,
                        dots: false
                    }
                }
            ]
        });
    	
    }

    var slickExtra = function(){

        $('.Extra').slick({
            dots: false,
            infinite: false,
            speed: 300,
            slidesToShow: 4,
            slidesToScroll: 4,
            responsive: [{
                    breakpoint: 1920,
                    settings: {
                        slidesToShow: 4,
                        slidesToScroll: 4,
                        infinite: false,
                        dots: false
                    }
                },

                {
                    breakpoint: 1366,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 3,
                        infinite: false,
                        dots: false
                    }
                },

                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 3,
                        infinite: false,
                        dots: false
                    }
                }, {
                    breakpoint: 600,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 2,
                        infinite: false,
                        dots: false
                    }
                }, {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        infinite: false,
                        dots: false
                    }
                }
            ]
        });
        	
    }

	var loadProducts = function(menu){
		
		$.ajax({
			
			 url:"/backorder/ajx-product/"+menu,
			 type: "GET",
			 dataType : 'html',
			 beforeSend : function(){

				 $(".inner-tab").html("<img style='margin-top:5px;' src='/front/images/ajax-loader.gif' />");
				//$('.loader_change_status'+id).hide();
				//$('#loaderstatus_'+id).show(100);
			 },
			 success:function(result)
			 {

				$(".inner-tab").html(result);
				slickMeal();
				slickExtra();
				FastClick.attach(document.body);

				$("#subOrderHeading").html(" FOR "+menu.toUpperCase());

				//console.log(result);
				// For making checkbox and other input element decorative display
				/*$('form').cstmForm({ 
					active: 1,
					text: {
					  force: true,
					  'blur_color': '#666'
					},
					file: {
					  holderTxt: "Browse File..."
					}
				 });*/

				 $(":radio, :checkbox").uniform();

				 $('[data-trigger="spinner"]').spinner();

				 data = "menu="+menu;
				 updatecart(data);
			}
			 
		 });

		return false;
	}

	function updatecart(data)
	{
		$.ajax({
			 url:"<?php echo $this->url('front',array('action' => 'updatecart')); ?>",
			 type: "POST",
			 data : data,
			 success:function(response)
			 {
				// console.log(response);
				 
				updateCartContainer(response);
			 }
		});
	}

	function updateCartContainer(result)
	{
		if(result==""){
			
			$("#cart-container").html('<h4 id="dispmsg" class="text-center light-grey"> You Have No Selection </h4>');

			 var paymentdetails = '<label class="inline left"><b>'
					paymentdetails += '0';
					paymentdetails +='</b></label>';
			$("#totalamt").html(paymentdetails);
			
		}else{
		
			var dates = $('#with-altField').multiDatesPicker('getDates');
			var delivery_charge = $('#newdeliverychrg').val();

			var day_count = dates.length;
			var day_sent = "";
			if(day_count == 1 ) { day_sent = day_count+' Day'; }
			else if(day_count == 0 ) { day_count = 1; }
			else { day_sent = day_count+' Days'; }
		
			var res = result.cart;
		
			
			var tax = result.tax
		
			var taxsum = 0;
		
			var data ="";var extra = "";var total_cart = 0;
		
			var discount = 0;
			var delvchrg = 0;
			var total_delivery_charge = 0;
			var netpay = 0;

			var cartlength = Object.keys(res).length;
			if(cartlength == 0)
			{
				$("#discount").html("0");
			}
			else
			{
				var discountrate = result.discountrate;
				var discounttype = result.discounttype;
			}

			$.each( res, function( index, value ){

				if(jQuery.type(value) =='object')
				{
					var total_product = 0;
					total_product = parseFloat(value.price) * parseFloat(value.quantity);
					total_cart += total_product;
					if(value.type == 'Extra')
					{
						classapply='alert-box subMenuItem radius';
						extra ="With Extra";
					}
					else
					{
						delvchrg  +=  value.quantity * delivery_charge;
						classapply='alert-box menuItem radius';
						extra ="";
					}

					data += "<div class='"+classapply+"'>";
					data +='<a href="#" class="close cart-destroy" data-pid='+value.id+' data-dismiss="alert" aria-hidden="true">&times;</a>';
			        data += '<strong>INR '+total_product+' '+extra+'&nbsp;'+value.quantity+'&nbsp;'+value.name+' / INR '+value.price+' each</strong> </div>';
				}
			});

			
			total_delivery_charge = delvchrg * day_count;
			total_cart_day = total_cart * day_count;
			data += '<div class="alert-box success radius">';
			data += '<strong>INR '+total_cart_day+'  Total [ '+day_sent+' ]  INR '+total_cart+' / Day</strong>';
			data += '</div>';
			$('#cart-container').html(data);

			if(discountrate !=0 && discounttype!="")
			{
				if(discounttype==0)
				{
					discount = discountrate;
				}
				else if (discounttype == 1)
				{
					discount = total_cart_day * discountrate/100;
				}
			}
			
		
			var discountdetails = '<label class="inline left"><b>'
				discountdetails += discount
				discountdetails +='</b></label>'
					
			$("#discount").html(discountdetails);

				var deliverychrg = '<label class="inline left"><b>'
					deliverychrg += total_delivery_charge
					deliverychrg +='</b></label>'
						
				$("#deliverychrg").html(deliverychrg);
		
				 var paymentdetails = '<label class="inline left"><b>'
					paymentdetails += total_cart_day
					paymentdetails +='</b></label>'

				
				$("#totalamt").html(paymentdetails);

					if(tax !=0)
					{
						 $.each( tax, function( index, value ){ 
		
								if(value.tax_type == 'Per')
								{
									taxsum = taxsum + (value.tax/100)*total_cart_day;
								}
								else if(value.tax_type == 'Fix')
								{
									taxsum = taxsum + value.tax;
								}
						 });
		
						 var totaltax = '<label class="inline left"><b>'
							 totaltax += taxsum
							 totaltax +='</b></label>'
						$("#taxamt").html(totaltax);
					}
					else
					{
						$("#taxdiv").addClass("dn")
						taxsum = 0;
						
					}
				var commission = 0;	 
				var commissionrate = $("#commissionrate").val();
				var commissiontype = $("#thirdpartytype").val();

				/* if(commissiontype == "fixed")
				{
					var commtype = '<label class="inline left"><b>'
						commtype += "Rs (fixed) per product"
						commtype +='</b></label>'
					$("#commision").html(commtype);
						
					//commission = commissionrate;
				}
				else if(commissiontype == "percentage")
				{
					commission = commissionrate;
				} */
					 
				 netpay = total_cart_day + total_delivery_charge + taxsum  - discount ;

				 var netpaydetails = '<label class="inline left"><b>'
					 netpaydetails += netpay
					 netpaydetails +='</b></label>'
				$("#netpay").html(netpaydetails);
				 $("#netpayment").val(netpay);
				
			
		}
	
	}
	
	function removefromcart(id)
	{

		var menu = $("#menu").val();
		var data = 'id='+id+'&menu='+menu;
		
		$.ajax({
			 url:"<?php echo $this->url('front',array('action' => 'removefromcart')); ?>",
			 type: "POST",
			 data : data,
			 success:function(response)
			 {

				updateCartContainer(response);
		
				$('.checkbox-check:checked').each(function() {
					   var value = $(this).data("id");
					
					   if(value == id)
					   {
							this.checked = false;
							//$('#custom-'+$(this).attr('id')).removeClass('checked');
							$(this).parent().parent().removeClass('selected');
							$(this).parent().removeClass('checked');
							/*$(this).parent().parent().parent().parent().removeClass('selected');*/
					   }
				});
		
			 }
		});
	}	

    jQuery(document).ready(function ($) {

   		$(document).on("change",".loc_code",function(){
			var menu = $("#menu").val();
			var data1 = "menu="+menu;

			var activemenu = $("#activemenu").val();
			var loc = $(this).val();
			
			var data = 'activemenu='+activemenu+'&location='+loc;

			
 			$.ajax({
				 url:"<?php echo $this->url('front',array('action' => 'getlocdiccount')); ?>",
 				 type: "POST",
 				 data : data,
 				 success:function(response)
 				 {
 					 $("#delivery_charge").val(response);

 					 updatecart(data1);
 				 }
 			});

		});
    	
    	var monthNames = ["January", "February", "March", "April", "May", "June",
		                  "July", "August", "September", "October", "November", "December"
		       ];
    	
        /*$(".page-container").mouseover(function () {
            $('.Meal').resize();
        });

        $(".page-container").mouseover(function () {
            $('.Extra').resize();
        });*/

        var selectedMenu = $("#menu").val();
        switch(selectedMenu){
		case 'breakfast':
					$("#default_locations").show();
					$("#lunch_locations").hide();
					$("#dinner_locations").hide();
					$("#activemenu").val("default_locations");
				break;	
		case 'lunch':
					$("#default_locations").hide();
					$("#lunch_locations").show();
					$("#dinner_locations").hide();
					if($("#lunch_code_hd").val()!='' && $("#lunch_code_hd").val()!="0#"){
						$("#lunch_code").val($("#lunch_code_hd").val());
						$("#lunch_Address").val($("#lunch_address_hd").val());
					}else{
						$("#lunch_code").val($("#default_code_hd").val());
						$("#lunch_Address").val($("#default_address_hd").val());
					}
					$("#activemenu").val("lunch_locations");
					
				break;
		case 'dinner':
					$("#default_locations").hide();
					$("#lunch_locations").hide();
					$("#dinner_locations").show();
					if($("#dinner_code_hd").val()!='' &&  $("#dinner_code_hd").val()!="0#"){
							$("#dinner_code").val($("#dinner_code_hd").val());
							$("#dinner_Address").val($("#dinner_address_hd").val());
				     }else{
				        	$("#dinner_code").val($("#default_code_hd").val());
							$("#dinner_Address").val($("#default_address_hd").val());
				     }
					$("#activemenu").val('dinner_locations');// = "dinner_locations";
				break;
				
		}
        

		// To hide the side bar menu.
        $('.page-sidebar .sidebar-toggler').click(function () {
            $(".sidebar-search").removeClass("open");
            var container = $(".page-container");
            if (container.hasClass("sidebar-closed") === true) {
                container.removeClass("sidebar-closed");
                $.cookie('sidebar-closed', null);
            } else {
                container.addClass("sidebar-closed");
                $.cookie('sidebar-closed', 1);
            }           
        });

              

        slickMeal();
        slickExtra();

        FastClick.attach(document.body);

		$(document).on("click",".cart-destroy",function(){
			var id = $(this).data("pid");
			removefromcart(id);

		});

		$(document).on("click",'.checkbox-check',function(){
			//var action = "";
			$("#dispmsg").hide();
			var id = $(this).data("id");
			//alert(id);
			if(this.checked == false)
			{
				removefromcart(id);
				return true;
			}
			var name = $(this).data("name");
			var menu = $("#menu").val();
			
			var quantity = $('#quantity'+id).val();

			if((this.checked) && (quantity<=0)){
				return false;
			}
			 
			var type = $(this).data("type");
			var amount = $(this).data("amount");
			var data = 'id='+id+'&name='+name+'&quantity='+quantity+'&type='+type+'&amount='+amount+'&menu='+menu;
			updatecart(data);

		});
		
		if($('#thirdparty').val() !='')
		{
			var thirdparty = $('#thirdparty').val();
			$.ajax({
				 url:"<?php echo $this->url('front',array('action' => 'getcommission')); ?>",
				 type: "POST",
				 data : "id="+thirdparty,
				 success:function(response)
				 {
					 $("#commission").val(response.comission_rate);
					 $("#commision").show();
					
					 $("#thirdpartytype").val(response.commission_type);
					 $("#commissionrate").val(response.comission_rate);

					 if(response.commission_type == "percentage")
					 {
						 var commtype = '<label class="inline left"><b>'
								commtype += "(%) per product"
								commtype +='</b></label>'
							//$("#commision").html(commtype);
								
						 $('#lbltype').html(commtype);
						 //$('#commission').text("Commission (%)");
					 }
					 else if(response.commission_type == "fixed")
					 {
						 var commtype = '<label class="inline left"><b>'
								commtype += "Rs (fixed) per product"
								commtype +='</b></label>'
							//$("#commision").html(commtype);
								
						 $('#lbltype').html(commtype);
						// $('#commission').text("Commission (fixed)");
					 }
				 }
			});
		}
		else
		{

			$("#thirdpartylbl").removeClass("dn");
			$("#commissionrate").value="";
			$("#commision").hide();
		}
		
		
		$(document).on('change',"#thirdparty",function() {
			var thirdparty = $('#thirdparty').val();
			
			if(thirdparty !='')
			{
				$.ajax({
					 url:"<?php echo $this->url('front',array('action' => 'getcommission')); ?>",
					 type: "POST",
					 data : "id="+thirdparty,
					 success:function(response)
					 {
						 $("#commission").val(response.comission_rate);
						 $("#commision").removeClass("dn");
						 $("#thirdpartylbl").addClass("dn");
						// $("#commission").value = response.comission_rate;
						 $("#commissionrate").val(response.comission_rate);
						 
						 $("#commision").show();
						 $("#thirdpartytype").val(response.commission_type);
						 if(response.commission_type == "percentage")
						 {

							 var commtype = '<label class="inline left"><b>'
									commtype += "(%) per product"
									commtype +='</b></label>'
								//$("#commision").html(commtype);
									
							 $('#lbltype').html(commtype);
						 }
						 else if(response.commission_type == "fixed")
						 {

							 var commtype = '<label class="inline left"><b>'
									commtype += "Rs (fixed) per product"
									commtype +='</b></label>'
								//$("#commision").html(commtype);
									
							 $('#lbltype').html(commtype);
							 
						 }
						
					 }
				});
			}
			else
			{
				$("#commision").hide();
				$("#commission").val('');
				$("#thirdpartylbl").removeClass("dn");
				$("#commissionrate").val('');
				$("#thirdpartytype").val('');
				
			}
		
		});

		$(document).on('change',"#culture",function() {
			var current = $( "#spinner" ).spinner( "value" );
			Globalize.culture( $(this).val() );
			$( "#spinner" ).spinner( "value", current );
		});
		
		/*  $('input:checkbox').change(function(){
		   if($(this).is(':checked'))
		       $(this).parent().parent().addClass('selected');
		  else
		      $(this).parent().parent().removeClass('selected')
		 }); 
 */
		loadProducts('<?php echo $this->menuSelected; ?>');
		
		$(".clsmenu").each(function(){

			if($(this).hasClass("disabled")){
				$(this).find(".fa-check").show();
			}
		});

		// When click on menu.

		$(document).on("click",".clsmenu",function(){

			var menu = $(this).data("menu");
			var loc = "";
			switch(menu){
			case 'breakfast':
			
						$("#location_code").val($("#default_code_hd").val());
						$("#ship_address").val($("#default_address_hd").val());
						$("#default_locations").show();
						$("#lunch_locations").hide();
						$("#dinner_locations").hide();
						loc = "default_locations";
					break;	
			case 'lunch':
						if($("#lunch_code_hd").val()!='' && $("#lunch_code_hd").val()!="0#"){
							$("#lunch_code").val($("#lunch_code_hd").val());
							$("#lunch_Address").val($("#lunch_address_hd").val());
						}else{
							$("#lunch_code").val($("#default_code_hd").val());
							$("#lunch_Address").val($("#default_address_hd").val());
						}
						$("#default_locations").hide();
						$("#lunch_locations").show();
						$("#dinner_locations").hide();
						loc = "lunch_locations";
					break;
			case 'dinner':
					   if($("#dinner_code_hd").val()!='' &&  $("#dinner_code_hd").val()!="0#"){
							$("#dinner_code").val($("#dinner_code_hd").val());
							$("#dinner_Address").val($("#dinner_address_hd").val());
				        }else{
				        	$("#dinner_code").val($("#default_code_hd").val());
							$("#dinner_Address").val($("#default_address_hd").val());
				        }
						$("#default_locations").hide();
						$("#lunch_locations").hide();
						$("#dinner_locations").show();
						loc = "dinner_locations";
					break;
					
			}
			$(".clsmenu").removeAttr("disabled");
			$(".clsmenu").removeClass("disabled");

			$("#menu").val(menu);
			$("#activemenu").val(loc);
			
			$(this).attr("disabled",true);

			loadProducts(menu);

			$(".clsmenu .fa-check").hide();
			$(this).find(".fa-check").show();
			
		});

		$(document).on("click",".tiffin_view",function(){
			
			var product_code = $(this).data('id');

			$('#myModal').foundation('reveal', 'open', {
			    url: '/tiffindetails/'+product_code,
			});
			return false;
		});

		$(document).on("click","#custSub",function(){

			if($('.chk').is(':checked'))
			{
				var selected_name = $('input[type=radio]:checked').attr("value");
				if(selected_name == "withpayment")
				{
					if(parseFloat($("#custbal").val()) < parseFloat($("#netpayment").val()))
					{
						$("#availbal").addClass('dn');
						$("#insfbal").removeClass('dn');
						return false;
					}
					else
					{
						return true;
					}
				}
			}
			else
			{
				alert("Please select payment method");
				return false
			}
			
		});



		// all days
		$(document).on("click","#optionsRadios1",function(){

			/* var prevSelDates = $('#with-altField').multiDatesPicker('getDates');
			MDP.multiDatesPicker('removeDates', prevSelDates); */
			var menu = $("#menu").val();
	         /**
	         Get Selected month text from Multidatepicker
	         **/
			var selectedMonth = $(".ui-datepicker-month").text();
			/**
	         Get Selected year text from Multidatepicker
	         **/	
			var selectedYear  = $(".ui-datepicker-year").text();
			
			var date = new Date();	
			var currentMonthName = monthNames[date.getMonth()];
			var currentYear = date.getFullYear();
		
			// check if selected month is same as currentMonth & selected year is same as current year
			
			if(currentMonthName == selectedMonth  && selectedYear == currentYear){ 
				
				todaysDate = date.getDate();
				var lastDayOfMonth = new Date(date.getFullYear(), date.getMonth()+1, 0);
				lastDayOfMonth = lastDayOfMonth.getDate();
				var dates = [];
				/**
					add date to multidate picker from todays date till month end
				**/
				for(var i=todaysDate; i<=lastDayOfMonth ;i++){
					dates.push(date.setDate(i));
				}

				//MDP.addDates(dates);
				MDP.multiDatesPicker({
					minDate: 0,
					addDates: dates,
					onSelect: function(date) {
						
						$.ajax({
							 url:"<?php echo $this->url('front',array('action' => 'get-cart')); ?>",
							 type: "POST",
							 data: "menu="+menu,
							 success:function(response)
							 {
								updateCartContainer(response);
							 }
						});
			        },
				});

			}else{

				var monthDate = new Date();
				
				monthIndex = monthNames.indexOf(selectedMonth);
				monthDate.setMonth(monthIndex);
				monthDate.setYear(selectedYear);
				
				var lastDayOfMonth = new Date(monthDate.getFullYear(),monthIndex+1, 0);
				lastDayOfMonth = lastDayOfMonth.getDate();
				var dates = [];
				/**
					add date to multidate picker from  from 1st date till month end
				**/

				var dafaultdate = new Date(monthDate.getFullYear(),monthIndex, 1);		// set default date of selected month to 1

				for(var i=1; i<=lastDayOfMonth ;i++){
					dates.push(monthDate.setDate(i));
				}

				//$('#with-altField').multiDatesPicker('destroy');	// destroy already existing instance of MDP
				MDP.multiDatesPicker({
					minDate: 0,
					addDates: dates,
					onSelect: function(date) {
						
						$.ajax({
							 url:"<?php echo $this->url('front',array('action' => 'get-cart')); ?>",
							 type: "POST",
							 data: "menu="+menu,
							 success:function(response)
							 {
								updateCartContainer(response);
							 }
						});
			        },
					
				});
			}


			$.ajax({
				 url:"<?php echo $this->url('front',array('action' => 'get-cart')); ?>",
				 type: "POST",
				 data: "menu="+menu,
				 success:function(response)
				 {
					updateCartContainer(response);
				 }
			});
		});

		// working days excluding sundays
		$(document).on("click","#optionsRadios2",function(){

		 	var menu = $("#menu").val();
			var selectedMonth = $(".ui-datepicker-month").text();
			var selectedYear  = $(".ui-datepicker-year").text();
			var date = new Date();	
			var currentMonthName = monthNames[date.getMonth()];
			var selectedMonthIndex = monthNames.indexOf(selectedMonth);	
			var currentYear = date.getFullYear();
			//alert(selectedMonthIndex);
			var prevSelDates = $('#with-altField').multiDatesPicker('getDates');
		 	var monthDates = [];

			$.each(prevSelDates, function( index, value ){

				var date = new Date(value);
				var month = date.getMonth();
				var year = date.getFullYear();

				if(selectedMonthIndex == month && selectedYear == year){
					monthDates.push(value);
				}
				
			});
			
			//var day_count = dates.length;
			MDP.multiDatesPicker('removeDates', monthDates); 
			
			// check if selected month is same as currentMonth and selected year is same as current year
			if(currentMonthName == selectedMonth && selectedYear == currentYear){ 
				
				todaysDate = date.getDate();
				var lastDayOfMonth = new Date(date.getFullYear(), date.getMonth()+1, 0); // get last date of month
				lastDayOfMonth = lastDayOfMonth.getDate();								// get date e.g 30 /31 of current month
				var dates = [];
				/**
					add date to multidate picker from todays date till month end
				**/
				for(var i=todaysDate; i<=lastDayOfMonth ;i++)
				{
					var currentDate = new Date(date.getFullYear(), date.getMonth(), i);			
					var day = currentDate.getDay();
					if(day!=0){  // if day is not 0 i.e not sunday then only push in date array
						dates.push(date.setDate(i));
					}
				}
				//$('#with-altField').multiDatesPicker('destroy');	
				MDP.multiDatesPicker({
					minDate: 0,
					/* beforeShowDay: function(date) {
				        var day = date.getDay();
				        return [(day != 0), ''];
				        
				    }, */
				   // removeDates:prevSelDates,
					addDates: dates,		// add dates to calendar
					onSelect: function(date) {
					
						$.ajax({
							 url:"<?php echo $this->url('front',array('action' => 'get-cart')); ?>",
							 type: "POST",
							 data: "menu="+menu,
							 success:function(response)
							 {
								updateCartContainer(response);
							 }
						});
			        },
				});
			
			}else{

			
				var monthDate = new Date();			// date object
				monthIndex = monthNames.indexOf(selectedMonth);			// get Index selectedMonth month from Months array
				monthDate.setMonth(monthIndex);							// set Month to selected month into date object
				monthDate.setYear(selectedYear);

				var lastDayOfMonth = new Date(monthDate.getFullYear(),monthIndex+1, 0);
				
				lastDayOfMonth = lastDayOfMonth.getDate();
				var dates = [];
				/**
					add date to multidate picker from 1st date  till month end
				**/
				for(var i=1; i<=lastDayOfMonth ;i++){
					var currentDate = new Date(monthDate.getFullYear(), monthDate.getMonth(), i);
					var day = currentDate.getDay();
					if(day!=0){
						dates.push(monthDate.setDate(i));
					}
				}

				//console.log(dates);

				//var dafaultdate = new Date(monthDate.getFullYear(),monthIndex+1, 1);
				
				//$('#with-altField').multiDatesPicker('destroy');	
				MDP.multiDatesPicker({
					minDate: 0,
				/* 	beforeShowDay: function(date) {
				        var day = date.getDay();
				        return [(day != 0), ''];
				        
				    }, */
					addDates: dates,
					onSelect: function(date) {
						
						$.ajax({
							 url:"<?php echo $this->url('front',array('action' => 'get-cart')); ?>",
							 type: "POST",
							 data: "menu="+menu,
							 success:function(response)
							 {
								updateCartContainer(response);
							 }
						});
			        },
				});
			}


			$.ajax({
				 url:"<?php echo $this->url('front',array('action' => 'get-cart')); ?>",
				 type: "POST",
				 data: "menu="+menu,
				 success:function(response)
				 {
					updateCartContainer(response);
				 }
			});
		
			
		});

		$(document).on("click","#withoutpayment",function(){

			$("#withoutpay").removeClass("dn");
			$("#withpay").addClass("dn");
			
		});

		$(document).on("click","#withpayment",function(){
		
			$("#withoutpay").addClass("dn");
			$("#withpay").removeClass("dn");

			
		});
	});
		  
	</script>

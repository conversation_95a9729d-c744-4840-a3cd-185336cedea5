  <?php
		      $form->setAttribute('action', $this->url('backorder', array('action' => 'index')));
		    //  $form->setAttribute('target',"_blank");
		      $form->setAttribute('class', 'stdform');
		      $form->prepare();
		    
?>
      <!-- END PAGE HEADER-->
    
      <div id="content">
    
     <div class="large-12 columns">
      <div id="msg"></div>  	
      <div class="large-6 columns">
      	 
           <?php  echo $this->form()->openTag($form);?>
        		<div class="row">
            		<div class="large-4 columns">
                    	<label class="inline right">Enter Contact No./ Email ID<span class="red">*</span></label>
                  	</div>
                  	<div class="large-8 columns">                
                       <?php   echo $this->formElement($form->get('phone')); ?>
	      		  		<?php   
	      		  			echo $this->formElementErrors()
							->setMessageOpenFormat('<small class="error">')
							->setMessageCloseString('</small>')
							->render($form->get('phone')); ?> 
							<div id="msg2"></div>	       
                  	</div>
                  	 
              	</div>
                
               
                
                <div class="row">
            		<div class="large-4 columns">&nbsp;</div>
                  	<div class="large-8 columns">     
                    	 <?php echo $this->formElement($form->get('submit')); ?>
                  	</div>
              	</div>              
             
        <?php //echo $this->formElement($form->get('csrf')); ?>
        	<?php  echo $this->form()->closeTag(); ?>
        </div>
        </div>
      </div>
    <!-- END PAGE CONTAINER--> 
<script type="text/javascript">
$(document).ready(function() { 

	$('#cno').keypress(function(e){
        if(e.which == 13){//Enter key pressed
		   //Trigger search button click event
			$("#submitbtn").trigger('click');
            return false;
        }
        return true;
    });
	
	 $('#btnsubmit').click(function(){
		 var phone = $('#cno').val()
		 if(phone == "")
		 {
			 $('#msg2').html("<small class='error'>Please enter Moble no/Email ID</small>");
		 }
		 else
		 {
			$.ajax({
				 url:"/backorder",
				 type: "POST",
				 async : false,
				 dataType : 'json',
				 data : {phone:phone},
				 beforeSend : function(){
					 $(".inner-tab").html("<img style='margin-top:5px;' src='/front/images/ajax-loader.gif' />");
				 },
				 success:function(result)
				 {
					if(result.msg == "error")
					{
						//	$('#msg').html('<div  data-alert="" class="alert-box dark-redBg round"><div>'+result.errormsg.phone.noRecordFound+'</div><a href="#" class="close">&times;</a></div>');
						if(typeof(result.errormsg.phone)!='undefined')
						{
							$('#msg2').html("<small class='error'>"+result.errormsg.phone.noRecordFound+"</small>");
						}
						else
						{
							$('#msg2').html("<small class='error'>"+result.errormsg+"</small>");
						}
					}
					else if(result.msg == "success")
					{
						var phone = result.phone;
						var rolename = result.role;
						var name = result.name;
						$(this).attr('target','_blank');
						url = "/customers/createsession?phone="+phone+"&role="+rolename+"&name="+name;
						//alert(url);
						window.open(url, '_blank');
					}
				}
			});
		}
        
		return false;
      
	});
});
</script>
<?php
$form = $this->form;
$form->setAttribute('action', $this->url('thirdparty', array('action' => 'edit', 'id'=>$this->id)));
$form->setAttribute('class','stdform');
$form->prepare();
?>
      
      <!-- END PAGE HEADER-->
      
      <div id="content">
        <div class="large-6 columns">
          <?php echo $this->form()->openTag($form);?>
            <?php     
		if(trim($errStr)!=""){
//			echo '<div class="alert-box alert">'.$errStr.'</div>';
		}
		?> 
          	<div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('name')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php  
                 	echo $this->formHidden($form->get('third_party_id'));
					echo $this->formElement($form->get('name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						
						->setMessageCloseString('</small>')
						->render($form->get('name'));
				?>
				
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('phone')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                  <?php 
                  	echo $this->formElement($form->get('phone'));
					echo $this->formElementErrors($form->get('phone')); 
				 ?>
              </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                  <label class="inline right"><?php echo $this->formLabel($form->get('email')); ?></label>
                </div>
                <div class="large-8  small-8 medium-8 columns">
                <?php 
                     echo $this->formElement($form->get('email'));
                     echo $this->formElementErrors($form->get('email')); 
                 ?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                  <label class="inline right"><?php echo $this->formLabel($form->get('address')); ?></label>
                </div>
                <div class="large-8  small-8 medium-8 columns">
                <?php 
                     echo $this->formElement($form->get('address'));
                     echo $this->formElementErrors($form->get('address')); 
                 ?>
                </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('comission_rate')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php 
                    echo $this->formElement($form->get('comission_rate'));
                    echo $this->formElementErrors($form->get('comission_rate')); 
                ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('commission_type')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                 	echo $this->formElement($form->get('commission_type'));
                        echo $this->formElementErrors($form->get('commission_type')); 
                ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo "Charges Type";//echo $this->formLabel($form->get('charges_type')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php 
                 	echo $this->formElement($form->get('charges_type'));
                    echo $this->formElementErrors($form->get('charges_type')); 
                ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
               <?php 
               		echo $this->formElement($form->get('status'));
                        echo $this->formElementErrors($form->get('status'));
                        echo $this->formElement($form->get('csrf')); 
                ?>
              </div>
              
               <?php echo $this->formElement($form->get('backurl'));
                ?>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8  small-8 medium-8 columns">
              	<button	type="submit"  id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id="cancelbutton" class="button	left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
              </div>
            </div>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?> 
        </div>
      </div>
    </div>
    
    <!-- END PAGE CONTAINER--> 

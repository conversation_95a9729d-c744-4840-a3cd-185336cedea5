<?php
$form = $this->form;
$form->setAttribute('action', $this->url('thirdparty', array('action' => 'add')));
$form->prepare();
?>
      
      <!-- END PAGE HEADER-->
      
      <div id="content">
        <div class="large-6 columns" style="margin-bottom:50px">
          <?php echo $this->form()->openTag($form);?>
          	<div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('name')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php  
                 	echo $this->formHidden($form->get('third_party_id'));
					echo $this->formElement($form->get('name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						
						->setMessageCloseString('</small>')
						->render($form->get('name'));
				?>
				
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('phone')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                  <?php 
                  	echo $this->formElement($form->get('phone'));
					echo $this->formElementErrors($form->get('phone')); 
				 ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('email')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
               <?php 
               		echo $this->formElement($form->get('email'));
					echo $this->formElementErrors($form->get('email')); 
				?>
            </div>
            </div>
            
            
            <div class="row thirdparty_type">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo "Third Party Type";// echo $this->formLabel($form->get('thirdparty_type')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php 
                    echo $this->formElement($form->get('thirdparty_type'));
                    echo $this->formElementErrors($form->get('thirdparty_type')); 
                ?>
              </div>
            </div>
            
            <div class="row thirdparty_system">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo "Third Party System";//echo $this->formLabel($form->get('thirdparty_system')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php 
                    echo $this->formElement($form->get('thirdparty_system'));
                    echo $this->formElementErrors($form->get('thirdparty_system')); 
                ?>
              </div>
            </div>
            
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo "Charges Type";//echo $this->formLabel($form->get('charges_type')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php 
                    echo $this->formElement($form->get('charges_type'));
                    echo $this->formElementErrors($form->get('charges_type')); 
                ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('commission_type')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                 	echo $this->formElement($form->get('commission_type'));
                        echo $this->formElementErrors($form->get('commission_type')); 
                ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('comission_rate')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php 
                 	echo $this->formElement($form->get('comission_rate'));
			echo $this->formElementErrors($form->get('comission_rate')); 
                ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
               <?php 
               		echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'));
					echo $this->formElement($form->get('csrf')); 
				?>
              </div>
              
               <?php echo $this->formElement($form->get('backurl'));
				 ?>
            </div>
            
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('create_account')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php 
                 	echo $this->formElement($form->get('create_account'));
                        echo $this->formElementErrors($form->get('create_account')); 
                ?>
              </div>
            </div>
            
            <div class="row password">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('password')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php 
                 	echo $this->formElement($form->get('password'));
                        echo $this->formElementErrors($form->get('password')); 
                ?>
              </div>
            </div>
            
            <div class="row confirm_password">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('confirm_password')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php 
                 	echo $this->formElement($form->get('confirm_password'));
                        echo $this->formElementErrors($form->get('confirm_password')); 
                ?>
              </div>
            </div>
             
            <div class="row city">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('city')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                    echo $this->formElement($form->get('city'));
                    echo $this->formElementErrors($form->get('city')); 
                ?>
              </div>
            </div>
            
            <div class="row location">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('location')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                    echo $this->formElement($form->get('location'));
                    echo $this->formElementErrors($form->get('location'));    
                ?>
              </div>
            </div>
            
            <div class="row address">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('address')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                    echo $this->formElement($form->get('address'));
                    echo $this->formElementErrors($form->get('address'));    
                ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8  small-8 medium-8 columns">
              	<button	type="submit"  id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id="cancelbutton" class="button	left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
              </div>
            </div>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?> 
        </div>
      </div>
    </div>

<script>
$(document).ready(function(){
        
    $(document).on("change",'#city',function(){
        
            var city = $(this).val();
            
            $.ajax({
                    url:"/menu/location",
                    type : "POST",
                    async : false,
                    dataType : 'json',
                    data : {city:city},
                    success:function(data){

                        var str="<option value=''>SELECT LOCATION</option>";
                        $.each(data,function(index,values){	
                                $.each(values,function(i,v){
                                        str+="<option value='"+v.pk_location_code+"'>"+v.location+"</option>";
                                });
                        });
                        $('.locationcodes').html(str);
                        $('.locationcodecopycls .customSelectInner').html('SELECT LOCATION');

                    }
            });

    });
        
    function hideCreateAccountFields(){
        $('.password, .confirm_password, .city, .location, .address').removeClass('hide').addClass('show');
    }
       
    function showCreateAccountFields(){
        $('.password, .confirm_password, .city, .location, .address').removeClass('show').addClass('hide');
    }
    
    if($('input[type=radio][name=thirdparty_type][checked=checked]') ){
        var obj = $('input[type=radio][name=thirdparty_type][checked=checked]');
        
        if(obj.parent().parent().parent().text() === 'Third-Party Delivery'){
            $('input[type=checkbox][name=create_account]').attr('disabled', true);
            $('input[type=hidden][name=create_account]').val(1);
            $('.thirdparty_system').removeClass('hide').addClass('show');
        }else{
            $('.thirdparty_system').removeClass('show').addClass('hide');
        }
    }
    
    function checkboxEvent(obj){
        
        if($(obj).is(':checked')){
            hideCreateAccountFields();
        }else{
            showCreateAccountFields();
        }
    }
    
    checkboxEvent('input[type=checkbox][name=create_account]');
   
    
    $('input[type=checkbox][name=create_account]').change(function(){
        
        checkboxEvent(this);
        if($(this).parent().hasClass('checked')){
            $('input[type=hidden][name=create_account]').val(1);
        }else{
            $('input[type=hidden][name=create_account]').val(0);
        }
    })
    
    $('input[type=radio][name=thirdparty_type]').change(function(){
        if($(this).parent().parent().parent().text() === 'Third-Party Aggregator'){           
           $('input[type=checkbox][name=create_account]').attr('disabled', false);
           $('.thirdparty_system').removeClass('show').addClass('hide');
        }else{
            if($('input[type=checkbox][name=create_account]').is(':checked')){
                $('input[type=checkbox][name=create_account]').attr('disabled', true);
            }else{
                $('input[type=checkbox][name=create_account]').parent().addClass('checked');
                $('input[type=checkbox][name=create_account]').prop('checked', true);
                hideCreateAccountFields();
            }
            $('input[type=checkbox][name=create_account]').attr('disabled', true);
            $('input[type=hidden][name=create_account]').val(1);
            $('.thirdparty_system').removeClass('hide').addClass('show');
        }
    })


})
</script>
    <!-- END PAGE CONTAINER--> 

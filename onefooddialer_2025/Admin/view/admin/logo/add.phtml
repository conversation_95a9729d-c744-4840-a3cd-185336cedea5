<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script src=/admin/js/plugins/jquery-ui.js"></script>
<script type="text/javascript" src="/admin/js/custom/form.js"></script>

<div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Logo Edit</span></h2>
          </div>
          <!--contenttitle--> 
          
          <br />
           <?php
		 $form->setAttribute('action', $this->url('logo_crud', array('action' => 'add')));
		 $form->setAttribute('class', 'stdform');
		 $form->prepare();
          echo $this->form()->openTag($form);
         
          ?>
            <p>
              <label><?php  echo $this->formLabel($form->get('logo_code')); ?></label>
              <span class="field">
              <?php 
              echo $this->formHidden($form->get('pk_logos_id'));
              echo $this->formElement($form->get('logo_code'));
              echo $this->formElementErrors($form->get('logo_code'));
              ?>
             
              </span> </p>
            <p>
              <label><?php  echo $this->formLabel($form->get('logo_plan')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('logo_plan'));
					echo $this->formElementErrors($form->get('logo_plan'));
			 ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('logo_property')); ?></label>
              <span class="field">
          		 <?php echo $this->formElement($form->get('logo_property'));
				echo $this->formElementErrors($form->get('logo_property'));
				?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('logo_thumb')); ?></label>
              <span class="formwrapper">
             <?php echo $this->formElement($form->get('logo_thumb'));
					echo $this->formElementErrors($form->get('logo_thumb'));
			 ?>
              </span></p>
            <p>
              <label><?php echo $this->formLabel($form->get('about_logo_style')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('about_logo_style'));
					echo $this->formElementErrors($form->get('about_logo_style'));
             ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('price_plan')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('price_plan'));
					echo $this->formElementErrors($form->get('price_plan'));
			  ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('time_required')); ?></label>
              <span class="field"> 
             <?php echo $this->formElement($form->get('time_required'));echo "  Hours"; echo $this->formElement($form->get('csrf'));
					
			 ?>
              </span>
              <?php echo '<span class="red">'.$this->formElementErrors($form->get('time_required')).'</span>'; ?> </p>
      
            <p class="stdformbutton">
             <?php echo $this->formSubmit($form->get('submit')); ?>
              <?php echo $this->formSubmit($form->get('cancel')); ?>
            </p>
            
             <p>
             
              <span class="field">
             <?php echo $this->formElement($form->get('backurl'));
				 ?>
              </span> </p>
            
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag(); ?>
          <br clear="all" />
          <br />
        </div>
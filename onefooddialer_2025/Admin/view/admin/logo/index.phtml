<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.effects.core.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.effects.explode.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<style type="text/css">

.gallerycontainer{
position: relative;
/*Add a height attribute and set to largest image's height to prevent overlaying*/
}

.thumbnail img{
border: 1px solid white;
margin: 0 5px 5px 0;
}

.thumbnail:hover{
background-color: transparent;
}

.thumbnail:hover img{
border: 1px solid blue;
}

.thumbnail span{ /*CSS for enlarged image*/
position: absolute;
background-color: lightyellow;
padding: 5px;
left: -1000px;
border: 1px dashed gray;
visibility: hidden;
color: black;
text-decoration: none;
}

.thumbnail span img{ /*CSS for enlarged image*/
border-width: 0;
padding: 2px;
}

.thumbnail:hover span{ /*CSS for enlarged image*/
visibility: visible;
top: 0;
left: 230px; /*position where enlarged image should offset horizontally */
z-index: 50;
}

</style>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
<div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="image"><span>Logos</span></h2>
          </div>
          <!--contenttitle--> 
          <!--<div class="tableoptions">
                        <button class="deletebutton radius3">Delete Selected</button> &nbsp;
                    </div><!--tableoptions-->
          
          <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('logo_crud', array('action'=>'add'));?>" class="btn btn_add"><span>Add Record</span></a></div>
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            </colgroup>
            <thead>
              <tr>
                <td class="head0">Logo Code</td>
                <td class="head1">Logo Plan</td>
                <td class="head0">Logo Property</td>
                <td class="head1 center">Logo Thumb</td>
                <td class="head0">About Logo Style</td>
                <td class="head1">Price Plan</td>
                <td class="head0">Time Required(days)</td>
                <td class="head1">Status</td>
                <td class="head0 center">Action</td>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <td class="head0">Logo Code</td>
                <td class="head1">Logo Plan</td>
                <td class="head0">Logo Property</td>
                <td class="head1 center">Logo Thumb</td>
                <td class="head0">About Logo Style</td>
                <td class="head1">Price Plan</td>
                <td class="head0">Time Required(days)</td>
                <td class="head1">Status</td>
                <td class="head0 center">Action</td>
              </tr>
            </tfoot>
            <tbody>
            	 <?php foreach ($paginator as $album) : // <-- change here! ?>
            	  <tr>
				     <td><?php echo $this->escapeHtml($album['logo_code']);?></td>
				     <td><?php echo $this->escapeHtml($album['logo_plan']);?></td>
				     <td><?php echo $this->escapeHtml($album['logo_property']);?></td>
				     <td align="center" class="gallerycontainer">
					 <!--<img src="./data/logos/<?php //echo $this->escapeHtml($album['logo_thumb']); ?>" alt="" style="width:200px;height:150px;text-align:center"/>-->
					 <a class="thumbnail" href="#thumb">Logo<span><img src="<?php echo str_replace("public/","",$path['logo']); ?><?php echo $this->escapeHtml($album['logo_thumb']); ?>" /></span></a>
					 </td>
				     <td><?php echo $this->escapeHtml($album['about_logo_style']);?></td>
				     <td><?php echo $this->escapeHtml($album['price_name']);?></td>
				     <td><?php echo $this->escapeHtml($album['time_required']);?></td>
				      <td><?php echo ($this->escapeHtml($album['logos_status']))?'Active':'<span class="red">Inactive</span>';?></td>
				    <td class="center">
				        <a href="<?php echo $this->url('logo_crud', array('action'=>'edit', 'id' => $album['pk_logos_id']));?>" class="btn btn5 btn_pencil5"></a>
         				  <?php $textadd = ($album['logos_status'])? 'Suspend' :'Activate'; ?>
        				<a href="<?php echo $this->url('logo_crud',array('action'=>'delete', 'id' => $album['pk_logos_id']));?>"  onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this logo plan ?')" class="btn btn5 btn_trash5"></a>
				     </td>
				 </tr>
            	 <?php endforeach; ?>
             
            </tbody>
          </table>
          <br />
          <br />
        </div>
        <!--content--> 
 

 <?php
 // add at the end of the file after the table
 //echo $this->paginationControl( $this->paginator,'sliding',array('partial/paginator.phtml', 'Album'),
     array(
         'route' => 'logo_crud'
     )
 //);
 ?>
<?php 
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");	
	$setting = $setting_session->setting;
?>
<div class="clearfix">
    <div class="large-12 columns medium-12 small-12 columns">
        <form id="frmfilter" class="calForm" method ="post">
            <select id="year" name="year" class="left">
            <?php 
                for($i=(date('Y'));$i<=(date('Y')+1);$i++){ 
                    $selectedYear = '';
                    if($year == $i){
                        $selectedYear = 'selected=selected';
                    }
            ?>
                <option value = "<?php echo $i;?>" <?php echo $selectedYear;?>><?php echo $i;?></option>
            <?php 
                }
            ?>					
            </select>
            <?php $monthArray = array("1"=>"Jan","2"=>"Feb","3"=>"Mar","4"=>"Apr","5"=>"May","6"=>"June","7"=>"July","8"=>"Aug","9"=>"Sep","10"=>"Oct","11"=>"Nov","12"=>"Dec");?>
            <select id="month" name="month" class="left">
            <?php 
            foreach($monthArray as $key=>$val){             
                    $selectedMonth = ($key == date("m"))? 'selected' : '';
                    if($month == $key){                       
                        $selectedMonth == 'selected:selected';
                    }
            ?>
                <option value = <?php echo $key;?> <?php echo $selectedMonth;?>><?php echo $monthArray[$key];?></option>
            <?php 
            }
            ?>				
            </select>
            <select id="selectedmenu" name="selectedmenu" class="left">
            <?php
            foreach ($menus as $menu){
                $selectedmenu ='';
                if($menu==$selected_menu){
                        $selectedmenu = 'selected=selected';                   
                }
            ?>                
                <option value = "<?php echo $menu;?>" <?php echo $selectedmenu;?>><?php echo ucfirst($menu);?></option>
            <?php
            } 
            ?>
            </select>
            
            <button  class="button left" data-weeknum="<?php echo $weeknum;?>" data-text-swap="Processing..  " type="submit">
                            Submit
            </button>  
            
        </form>
        <form id="filterFrm" action="/meal/mealplan/<?php echo $id; ?>" name="filterFrm" class="calForm" method ="post">
            <input type="hidden" name="month" id="newmonth" value="" />
             <input type="hidden" name="kitchen" id="newkitchen" value="" />
            <input type="hidden" name="year" id="newyear" value="" />
            <input type="hidden" name="menu" id="menu" value="" />
            <input type="hidden" name="mealplans" id="mealplans" value="" />
            <input type="hidden" name="meal_name" id="meal_name" value="<?php echo $meal_name;?>" />
            <input type="hidden" name="service" id="service" value="mealplan" />
            <input type="hidden" name="export_type" id="export_type" value="" />
            <input type="hidden" name="exportUrl" id="exportUrl" value="/meal/mealplan" /> 
        </form>
            <div>
                <ul class="toolOption">
                    <li>
                        <div class="print">
                            <button class="btn dropdown" data-dropdown="dropPrint">
                                    <i class="fa fa-print"></i>&nbsp; Export
                            </button>
                            <ul id="dropPrint" data-dropdown-content class="f-dropdown exportPrint">
                                <li data-tooltip data-exporttype="xls" class="has-tip tip-top directExport" title="Export EXCEL">
                                    <a href="#"><i class="fa fa-file-excel-o"></i></a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>            
        <div id="product12"></div>
    </div>
</div>

<span id="toTop" class="go-top"><i class="fa fa-angle-up"></i></span>
       
        	
<script>
    
    function get_list(id,month,year,menu,kitchen,weeknum) {
        
        $.ajax({
               
            url : "/meal/ajx-meal-plan-items",
            type: "POST",
            async : true,
            data: {id:id,month:month,year:year,menu:menu,kitchen:kitchen,weeknum:weeknum},
            success: function(data){

                $('#product12').html(data);
            }
        });
    }
    
    $(document).ready(function() {
        
        var id = <?php echo $id; ?>;
        var menu = $('#selectedmenu option:selected').val();
        var month = $('#month option:selected').val();
        var year = $('#year option:selected').val();
        var kitchen = <?php echo $adminkitchen; ?>;
        var weeknum = $(this).data("weeknum");

        $('#selectedmenu').on('change', function() {
           menu = $(this).val(); 
        });
        
        $('#year').on('change',function(){
            year = $(this).val();
        });
        
        $('#month').on('change',function(){
            month = $(this).val();
        });
        
        get_list(id,month,year,menu,kitchen,weeknum);
        
        $(document).on('click', '.nxtmonth', function() {
            
            var year = $("#year option:selected").val();           
            var month = $("#month option:selected").val();           
            var menu = $("#selectedmenu option:selected").val();           
            var weeknum = $(this).data("weeknum");

            get_list(id,month,year,menu,kitchen,weeknum)                
        });

        $('#frmfilter').on('submit', function() {
            menu = $('#selectedmenu option:selected').val();
            
             get_list(id,month,year,menu,kitchen,weeknum);
            return false;
        });
        
        $(document).on("click",".directExport",function(){
          
            var exporttype = $(this).data('exporttype');
            $("#mealplans").val("export");
            $("#export_type").val(exporttype);
            $("#newmonth").val(month);
            $("#newyear").val(year);
            $("#menu").val(menu);
            $("#newkitchen").val(kitchen);
            $("#filterFrm").attr("target", "_blank");
            $("#filterFrm").submit();
           
        });   
        
    });
</script>
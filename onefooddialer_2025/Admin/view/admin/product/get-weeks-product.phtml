 <style>
.mb10 {
    margin-bottom: 10px;
}
 </style>
 <style> 
	#header {
		position:relative;
		line-height:1em;
	}
	.filterform {
		width:220px;
		font-size:12px;
		display:block;
		margin-top:10px;
	}
	.filterform input {
		-moz-border-radius:5px;
		border-radius:5px;
		-webkit-border-radius:5px;
	}
</style>
<?php

 $utility = \Lib\Utility::getInstance();
 $setting_session = new Zend\Session\Container("setting");
 $setting = $setting_session->setting;
 $date_format = $setting['DATE_FORMAT'];
 $today = date("Y-m-d");

    $monthArray = array("1"=>"January","2"=>"February","3"=>"March","4"=>"April","5"=>"May","6"=>"June","7"=>"July","8"=>"August","9"=>"September","10"=>"October","11"=>"November","12"=>"December");

    $strmonth = '';
    foreach( $monthArray as $k => $v ) {
        if( $k == $weekDetails['month'] ) {
                $strmonth = $v;
        }
    } 
?>
<?php
    $locale = 'en_US';
    $nf = new \NumberFormatter($locale, \NumberFormatter::ORDINAL); 
?>
<h3 class="mb15">Plan For <?php echo $name; ?> on <?php echo $nf->format($currentWeekNo-$weekDetails['firstweek']+1); ?> Week of <?php echo $strmonth;?> - <?php echo $weekDetails['year'];?></h3>
<div class="shuffleContainer clearfix">
<?php 
    foreach($weekDetails['week_dates'] as $wk) {

        $wmonth = date("m",strtotime($wk));
        $wyear = date("Y",strtotime($wk));
        
        if($weekDetails['month'] == $wmonth && $weekDetails['year'] == $wyear){
?>		
    <div class="large-4 small-12 medium-6 columns ">
        <div class="menuBlock">
            <div class="blockHeader">
                <span class="menuDate"><?php echo $wk.' ('.date('l', strtotime($wk)).')';?></span>
                <?php if( strtotime($wk) >= strtotime(date('Y-m-d')) ) { ?>
                <button id="<?php echo $id;?>" data-cat="<?php echo $product_category; ?>" data-name="<?php echo $name; ?>" data-menu="<?php echo $menu; ?>" data-date="<?php echo $wk; ?>" data-kitchen="<?php echo $kitchen; ?>" type="button" data-reveal-id="myModal" class="add-field smBtn has-tip tip-top remove-field addProduct" data-tooltip title="Add more" >
                        <i class="fa fa-plus"></i>
                </button>
                <?php } ?>
            </div>

            <div class="menu_Container product_swap" id="<?php echo date('d-m-Y',strtotime($wk));?>">
                <?php 
                    $chkProducts = false;
                    $currday = date('d-m-Y');
                    if(isset($planned_products) && count($planned_products) > 0){
                        foreach($planned_products as $key => $val) {
                            if( date($date_format,strtotime($wk)) == date($date_format,strtotime($val['date'])) ){
                                $chkProducts = true;
                                $checked = ($val['isdefault'] == "yes") ? "checked" : "";
                ?>
                                <div class="alert-box menuItem radius clearfix" data-alert="">
                                    <div class="large-8 columns">
                                            <span class="menuItem_name"><?php echo $val['specific_product_name']; ?></span>
                                    </div>
                                    <div class="large-4 columns">
                                        <!-- <input type="checkbox" name="check-1" value="4" class="lcs_check" autocomplete="off" /> -->
                                        <label for="status_1">
                                        <?php if( strtotime($wk) >= strtotime($currday) ) { ?>
                                        <input class="setdefault" name="status_<?php echo $menu; ?>_<?php echo $id; ?>_<?php echo $val['date']; ?>" type="radio" id="status_<?php echo $menu; ?>_<?php echo $id; ?>_<?php echo $val['specific_product_code']; ?>_<?php echo $val['date']; ?>" value="text" <?php echo $checked;?>>
                                        <?php } ?>

                                        </label>
                                    </div>
                                    <?php if( strtotime($wk) >= strtotime($currday) ) { ?>									
                                        <a class="close removeProductFrmDate" id="remove_<?php echo $menu; ?>_<?php echo $id; ?>_<?php echo $val['specific_product_code']; ?>_<?php echo $val['date']; ?>">×</a>
                                    <?php } ?>									
                                </div>
                <?php 
                            } 
                        }
                    }
                    if($chkProducts == false && strtotime($wk) >= strtotime(date('Y-m-d'))){
                            echo "<h5>Click (+) to add product.</h5>";
                    } 
                ?>

            </div>
        </div>
    </div>	
<?php 	
        }		
    }
?>
</div>
<div class="clearfix mb10">
    <?php
/* 		echo $currentWeekNo."<br />";
            echo $weekDetails['maxweek']."<br />";
            echo $weekDetails['firstweek']."<br />";
            echo $weekStartDate."<br />";
            echo $today."<br />"; */

        if($currentWeekNo == $weekDetails['firstweek']) {
        ?>
                <button class="button right tiny nxtmonth" data-weeknum="<?php echo $weeknumNext; ?>" type="button">
                        <i class="fa fa-angle-double-right" aria-hidden="true"></i> Next Week
                </button>
        <?php 			
        }
        elseif($currentWeekNo == $weekDetails['maxweek'] && $weekStartDate > $today){
        ?>
                <button class="button left tiny nxtmonth" data-weeknum="<?php echo $weeknumPrev; ?>" type="button">
                        <i class="fa fa-angle-double-left" aria-hidden="true"></i> Previous Week
                </button>
        <?php 
        }
        elseif($currentWeekNo < $weekDetails['maxweek'] && $currentWeekNo > $weekDetails['firstweek']){
        ?>
            <?php 
            //if($weekStartDate > $today){
            ?>
            <button class="button left tiny nxtmonth" data-weeknum="<?php echo $weeknumPrev; ?>" type="button">
                    <i class="fa fa-angle-double-left" aria-hidden="true"></i> Previous Week
            </button>	
            <?php 
            //}
            ?>
            <button class="button right tiny nxtmonth" data-weeknum="<?php echo $weeknumNext; ?>" type="button">
                    <i class="fa fa-angle-double-right" aria-hidden="true"></i> Next Week
            </button>			
        <?php 
        }
    ?>
</div>	

<div id="myModal" class="reveal-modal custPopup prodPopup small" data-v-offset="70%;" data-reveal aria-labelledby="calendarProducts" aria-hidden="true" role="dialog"> 
    <div class="menuList_container">
        <h3 id="header" class="mb15 addmenu"></h3>
        <div id="list" class="menuBlock spproduct  menuBlock1" style="height: 200px;"></div>
        <div class="pull-right">
            <button type="abc" class="butto  tiny mt10 mb0" id="addProduct">
                    <i class="fa fa-plus"></i>&nbsp;Add  
            </button>
            <button type="abc" class="butto  tiny mt10 mb0" id="cancelProduct">
                    <i class="fa fa-ban"></i>&nbsp;Cancel  
            </button>
        </div>
    </div>
</div>

<script type="text/javascript">
			
//browser detect start
var objappVersion = navigator.appVersion; var objAgent = navigator.userAgent; var objbrowserName = navigator.appName; var objfullVersion = ''+parseFloat(navigator.appVersion); var objBrMajorVersion = parseInt(navigator.appVersion,10); var objOffsetName,objOffsetVersion,ix; 
// In Chrome 
if ((objOffsetVersion=objAgent.indexOf("Chrome"))!=-1) { objbrowserName = "Chrome"; objfullVersion = objAgent.substring(objOffsetVersion+7); } 
// In Microsoft internet explorer 
else if ((objOffsetVersion=objAgent.indexOf("MSIE"))!=-1) { objbrowserName = "Microsoft Internet Explorer"; objfullVersion = objAgent.substring(objOffsetVersion+5); } 
// In Firefox 
else if ((objOffsetVersion=objAgent.indexOf("Firefox"))!=-1) { objbrowserName = "Firefox"; } 
// In Safari 
else if ((objOffsetVersion=objAgent.indexOf("Safari"))!=-1) { objbrowserName = "Safari"; objfullVersion = objAgent.substring(objOffsetVersion+7); if ((objOffsetVersion=objAgent.indexOf("Version"))!=-1) objfullVersion = objAgent.substring(objOffsetVersion+8); } 
// For other browser "name/version" is at the end of userAgent 
else if ( (objOffsetName=objAgent.lastIndexOf(' ')+1) < (objOffsetVersion=objAgent.lastIndexOf('/')) ) { objbrowserName = objAgent.substring(objOffsetName,objOffsetVersion); objfullVersion = objAgent.substring(objOffsetVersion+1); if (objbrowserName.toLowerCase()==objbrowserName.toUpperCase()) { objbrowserName = navigator.appName; } } 
// trimming the fullVersion string at semicolon/space if present 
if ((ix=objfullVersion.indexOf(";"))!=-1) objfullVersion=objfullVersion.substring(0,ix); if ((ix=objfullVersion.indexOf(" "))!=-1) objfullVersion=objfullVersion.substring(0,ix); objBrMajorVersion = parseInt(''+objfullVersion,10); if (isNaN(objBrMajorVersion)) { objfullVersion = ''+parseFloat(navigator.appVersion); objBrMajorVersion = parseInt(navigator.appVersion,10); } 
console.log('' +'Browser name = '+objbrowserName+'<br>' +'Full version = '+objfullVersion+'<br>' +'Major version = '+objBrMajorVersion+'<br>' +'navigator.appName = '+navigator.appName+'<br>' +'navigator.userAgent = '+navigator.userAgent+'<br>' )
// browser detect stop
// window height width start
function f_filterResults(n_win, n_docel, n_body) {
	var n_result = n_win ? n_win : 0;
	if (n_docel && (!n_result || (n_result > n_docel)))
		n_result = n_docel;
	return n_body && (!n_result || (n_result > n_body)) ? n_body : n_result;
}
function f_clientWidth() {
	return f_filterResults (
		window.innerWidth ? window.innerWidth : 0,
		document.documentElement ? document.documentElement.clientWidth : 0,
		document.body ? document.body.clientWidth : 0
	);
}
function f_clientHeight() {
	return f_filterResults (
		window.innerHeight ? window.innerHeight : 0,
		document.documentElement ? document.documentElement.clientHeight : 0,
		document.body ? document.body.clientHeight : 0
	);
}
function f_scrollLeft() {
	return f_filterResults (
		window.pageXOffset ? window.pageXOffset : 0,
		document.documentElement ? document.documentElement.scrollLeft : 0,
		document.body ? document.body.scrollLeft : 0
	);
}
function f_scrollTop() {
	return f_filterResults (
		window.pageYOffset ? window.pageYOffset : 0,
		document.documentElement ? document.documentElement.scrollTop : 0,
		document.body ? document.body.scrollTop : 0
	);
}
// window height width stop
function cal_height() {
	var appname = objbrowserName;console.log(appname);
	var ht = f_clientHeight() + f_scrollTop();
	console.log('window client height='+(f_clientHeight() +' + '+ f_scrollTop())+'='+ht);
	var height = parseInt($('#myModal').css('top'));
	console.log(height+'==='+(((f_clientHeight())*20/100) + f_scrollTop()));

	var newTop = ((f_clientHeight())*20/100) + f_scrollTop();
	switch(appname.toLowerCase()) {
		case 'chrome':
		newTop = height;
		break;
		case 'mozilla':
		case 'firefox':
		newTop = parseInt(parseInt(f_clientHeight()+f_scrollTop())*10/100);
		break;
	}
	$("#myModal").css({'top': height -(height - newTop)});
	var val_ht = parseInt(parseInt(f_clientHeight()) / 2);
	//console.log(val_ht);
	$(".listOfmenu").css('max-height', val_ht+'px');
	$(".listOfmenu").css('width', '100%');
}
	
</script>

<script type="text/javascript">


$(document).ready(function() {

    var currentweek = '<?php echo $currentWeekNo; ?>';
    var currentmonth = '<?php echo $month; ?>';
    var currentyear = '<?php echo $year; ?>';

    shuffle_product();

        $(".addProduct").on('click',function(){
            var id = $(this).attr("id");
            var kitchen = $(this).data("kitchen");
            var date = $(this).data("date");
            var name = $(this).data("name");
            var menu = $(this).data("menu");
            var product_cat = $(this).data("cat");

            var spid_from_db = [];

            $.ajax({
                url:"<?php echo $this->url('product',array('action' => 'get-specific-product')); ?>",
                    type:"POST",				
                    data:{id:id,menu:menu,product_cat:product_cat,kitchen:kitchen,date:date,fetch:'daywise'},
                    beforeSend: function(){
                        $('.spproduct').empty();
                    }, 
                    success:function(data){
                        console.log(data);
                        $('h3.addmenu').html('Prepare Menu For '+name+' ('+date+')');

                        $.each(data.planned_products,function(key, val){
                            //console.log('planned products.....');
                            //console.log(val);                            
                            spid_from_db.push(val.specific_product_code);
                            //$.each(val, function(k,v){
                              //spid_from_db = v.ids.split(',');
                              //spid_from_db.push(v.ids.split(','));
                            //});
                        });

                        var strHtml = '';
                        $.each(data.data, function(key, val){

                            var in_db_present = "";
                            if($.inArray( val.pk_product_code, spid_from_db ) !== -1){
                                in_db_present = "checked";
                            }
                            						
                            strHtml += '<div class="menu_Container popup_menu entry">';
                            strHtml += '<div class="alert-box menuItem radius">';
                            strHtml += '<input type="checkbox" id="product'+val.pk_product_code+'" class="product_code selectAll"' +in_db_present+'>';

                            strHtml += '<span class="menuItem_name product_name movieTitle ">'+val.name+'</span>';
                            strHtml += '<div class="menuQty right">';
                            strHtml += '<input type="hidden" class="name" value="'+val.name+'">';
                            strHtml += '<input type="hidden" class="swap_charges" value="'+val.swap_charges+'">';
                            strHtml += '<input type="hidden" class="swap_with" value="'+val.swap_with+'">';
                            strHtml += '<input type="hidden" class="menu" value="'+val.category+'">';
                            strHtml += '<input type="hidden" class="date" value="'+date+'">';
                            /*
                            strHtml += '<select id="productQty'+val.pk_product_code+'" class="mb0">';
                            for(i=1; i<=val.max_quantity_per_meal;i++){
                            strHtml +='<option value="'+i+'">'+i+'</option>';
                            }							            
                            strHtml += '</select>';
                            */    
                            strHtml += '</div>';
                            strHtml += '</div>';
                            strHtml += '</div>';						
                        });
                        listFilter($("#header"), $("#list"));
                        $('.spproduct').append(strHtml);					
                        $('#myModal').foundation('reveal','open');
                    },
                    complete: function() {

                    }
            });
        });

        function removeDuplicates(arr, prop) {
             var new_arr = [];
             var lookup  = {};

             for (var i in arr) {
                 lookup[arr[i][prop]] = arr[i];
             }

             for (i in lookup) {
                 new_arr.push(lookup[i]);
             }

             return new_arr;
        }		

        $('#addProduct').on('click', function(){

            var gen_pr_code = $('.addProduct').attr("id");
            var gen_menu = $('.addProduct').data("menu");

            var id = $(this).attr("id");
            var date = $(this).data("date");
            var name = $(this).data("name");
            var menu = $(this).data("menu");

            var products = [];
            var date = $(this).parent().parent().find('.date').val();

            $(".product_code").each(function(key,value) {
                if($(this).prop("checked") == true){

                    var temp = {};
                    temp['id'] = $(this).attr('id').split('product')[1];
                    temp['name'] = $(this).parent().parent().find('.name').val();
                    temp['swap_charges'] = $(this).parent().parent().find('.swap_charges').val();
                    temp['swap_with'] = $(this).parent().parent().find('.swap_with').val();
                    temp['isdefault'] = $(this).parent().parent().find('.isdefault').val();
                    temp['menu'] = gen_menu;
                    products.push(temp);
                }
            });

            var uniqueProduct = removeDuplicates(products, "id");

                //return false;
            $.ajax({
                    url:"<?php echo $this->url('product', array('action' => 'add-product-on-date')); ?>",
                    type:"POST",
                    data:{date:date,products:uniqueProduct,gpid:gen_pr_code,menu:gen_menu},
                    success:function(data){
                        if(data.newly_added_rows) {
                            $('#myModal').foundation('reveal','close');
                            $('.reveal-modal-bg').css('display','none');
                            $('#currentweek').val(currentweek);
                            renderDates(gen_pr_code,gen_menu,currentyear,currentmonth,currentweek);
                        } 
                    }
                });
            //return false;
        });

        $('#cancelProduct').on('click', function(){
            $('#myModal').foundation('reveal','close');
            $('.reveal-modal-bg').css('display','none');
        });

        $(".setdefault").on('click', function(){
                var isdefault = $(this).attr('id').split("_");
                var menu = isdefault[1];
                var gpid = isdefault[2];
                var spid = isdefault[3];
                var date = new Date(isdefault[4]);
				var date1 = isdefault[4];

            $.ajax({
                    url:"<?php echo $this->url('product', array('action' => 'add-product-on-date')); ?>",
                    type:"POST",
                    data:{date:date1,gpid:gpid,spid:spid,menu:menu,mode:'setdefault'},
                    success:function(data){
                            console.log(data);
                        }
                });			
        });

        $(".removeProductFrmDate").on('click', function(){
                var remove = $(this).attr('id').split("_");
                var elem = this;
                var menu = remove[1];
                var gpid = remove[2];
                var spid = remove[3];
                var date = remove[4];			

            $.ajax({
                    url:"<?php echo $this->url('product', array('action' => 'add-product-on-date')); ?>",
                    type:"POST",
                    data:{date:date,gpid:gpid,spid:spid,menu:menu,mode:'remove'},
                    success:function(data){
                            console.log(data);
                            $(elem).parent().remove();
                        }
                });			
        });

  $.expr[':'].Contains = function(a,i,m){
      return (a.textContent || a.innerText || "").toUpperCase().indexOf(m[3].toUpperCase())>=0;
  };

  function listFilter(header, list) {

      var form = $("<form>").attr({"class":"filterform","action":"#"}),
          input = $("<input>").attr({"class":"filterinput","type":"text","placeholder":"Product Search"});
      $(form).append(input).appendTo(header);

      $(input)
        .change( function () {
          var filter = $(this).val();
          if(filter) {
            $(list).find(".movieTitle:not(:Contains(" + filter + "))").parent().slideUp();
            $(list).find(".movieTitle:Contains(" + filter + ")").parent().slideDown();
          } else {
            $(list).find(".entry").slideDown();
          }
          return false;
        })
      .keyup( function () {
          $(this).change();
      });
    }

        $(document).on('opened.fndtn.reveal', '[data-reveal]', function() {
                $('.nicescroll-rails').show();
                $(".menuBlock1").niceScroll();
        });

        $(document).on('closed.fndtn.reveal', '[data-reveal]', function() {
                $('.nicescroll-rails').hide();
        });	    

});
</script>
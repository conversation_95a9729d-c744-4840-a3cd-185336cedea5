<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addmeals" class="common-orange-btn-on-hover"> Add Meals/combos</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updmeals" class="common-orange-btn-on-hover">Update Meals</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="deactmeals" class="common-orange-btn-on-hover">Deactivate Meals</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
<?php 
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	$calendar_setting = $setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'];
?>
      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
            <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i> Meals </h4>  
            <ul class="toolOption">
            	<li>
            	 <?php
          			if($acl->isAllowed($loggedUser->rolename,'product','add'))	{ ?>
	                <div class="addRecord">
	                	<button class="btn" onClick="location.href='<?php echo $this->url('meal', array('action'=>'add-meal'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Meal/Combo</button>
                    </div>
                  <?php } ?>
                </li>
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                            <th class="no_sort"></th>
                            <th>Meal Name</th>
                            <th>Price <!-- <i class="fa fa-rupee"></i> --></th>
                            <th>Kitchen Capacity</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                  
                </table>          
          	</div>
        </div>
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
    
    
<script type="text/javascript">
$(document).ready(function() {

	 var aoColumns = [];
	 $('#customer thead th').each( function () {
	     if ( $(this).hasClass('no_sort')) {
	         aoColumns.push( { "bSortable": false } );
	     } else {
	         aoColumns.push( { "asSorting": [ "asc" ] } );
	     }
	 } );
	

	$('#customer').dataTable( {
        "processing": true,
        "serverSide": true,
        "bDestroy" :true,
    	"stateSave": true,
    	//"aoColumns":aoColumns,
        "ajax": "/meal/ajx-meal",
        "aoColumnDefs": [
        	                {
        	                   bSortable: false,
        	                   aTargets: [0,5]
        	                }
        	              ],
    });
});
</script>
<script type="text/javascript">
  $(document).on('click',"#addmeals",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addRecord').attr("data-step", "1");
      $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add Meals/combos");
      $('.portlet-title').find('.addRecord').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.addRecord').removeAttr("data-step");
      $('.portlet-title').find('.addRecord').removeAttr("data-intro");

  });
  $(document).on('click',"#updmeals",function(e){
      e.preventDefault();
    //  alert($('.displayTable').find('tbody tr:first td:eq(5) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(5) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(5) button:first').attr("data-intro", "Click here to edit meals/combos details");
      $('.displayTable').find('tbody tr:first td:eq(5) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(5) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(5) button:first').removeAttr("data-intro");
  });

    $(document).on('click',"#deactmeals",function(e){
      e.preventDefault();
      $('.displayTable').find('tbody tr:first td:eq(5) button:eq(1)').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(5) button:eq(1)').attr("data-intro", "Click here to deactivate meals/combos.");
      $('.displayTable').find('tbody tr:first td:eq(5) button:eq(1)').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(5) button:eq(1)').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(5) button:eq(1)').removeAttr("data-intro");
    });
</script>  
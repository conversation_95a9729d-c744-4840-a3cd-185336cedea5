 <?php
?>
      <!-- END PAGE HEADER-->
      <form method="post" autocomplete="off">
      <div id="content" class="clearfix">
        <div class="large-12 columns">
            <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Product</h4>  
            <ul class="toolOption">
            	<li>
            	 <?php
          			if($acl->isAllowed($loggedUser->rolename,'product','add'))	{ ?>
	                <div class="addRecord">
	                	<button class="btn" onClick="location.href='<?php echo $this->url('product', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Product</button>
	                 </div>
                  <?php } ?>
                </li>
             
                
            </ul>
        </div>  
        </div>      
        	<div class="portlet-body">      
        		<div class="large-8 small-12 medium-12 columns">

								<h1>Click to choose dates for products</h1>
								<div class="orderDate">
									<div class="btn-group btn-group-radio">
										<input type="radio" name="optionsRadios" id="optionsRadios1" value="option1">
										<label class="btn yes" for="optionsRadios1"> All Days</i> </label>
										<input type="radio" name="optionsRadios" id="optionsRadios2" value="option2">
										<label class="btn no" for="optionsRadios2">Working Days </label>
									</div>
								</div>
								<div class="dateBox">
									<div id="datepicker"></div>
									<input type="text" id="alternate" name = "calendar_date"/>
								</div>

							</div>  
          	</div>
        
        <div class="large-12 columns pl0 pr0">
                 <div class="row">
            		<div class="large-4 small-4 medium-4 columns">&nbsp;</div>
                  	<div class="large-8 small-8 medium-8 columns pr0"> 
                  		<div class="right">   
                  			<input type = "hidden" name = "fk_product_code" value = "<?php echo $id;?>">
	                    	<button type="submit" id="submitbutton" class="button left tiny left5 dark-greenBg">Save &nbsp;<i class="fa fa-save"></i></button>
	                        <button type="submit" class="button left tiny left5 redBg" id="cancelbutton">Cancel &nbsp;<i class="fa fa-ban"></i></button>  
	                    </div>
                  	</div>
                 </div>
              	</div> 
        
      </div>
    </div>
    </form> 
    <!-- END PAGE CONTAINER--> 
    
    
    <script type="text/javascript">
$(document).ready(function() {
	var monthNames = ["January", "February", "March", "April", "May", "June",
	                  "July", "August", "September", "October", "November", "December"
	           ];
	
		$("#datepicker").multiDatesPicker({
			autoSize : true,
			minDate : 0,
			//maxDate : "+2M",
			altField : "#alternate"
		});
		<?php $frm_db_calendar_dates = $product['calendar_date'];?>
	    <?php if(isset($frm_db_calendar_dates) && $frm_db_calendar_dates!=''){ ?>
	    	<?php $frm_db_calendar_dates = explode(',',$frm_db_calendar_dates);?>
	    		    var db_selected_dates = [];
	    		    <?php foreach($frm_db_calendar_dates as $key=>$val){?>
	    		    	db_selected_dates.push("<?php echo date('m/d/Y',strtotime($val));?>");
	    		    <?php }?>
		$("#datepicker").multiDatesPicker('addDates', db_selected_dates);
		$("#alternate").val($("#datepicker").multiDatesPicker('getDates'));
		<?php }?>
		$(document).on("click","#optionsRadios1",function(){

			var menu = $("#menu").val();
	         /**
	         Get Selected month text from Multidatepicker
	         **/
			var selectedMonth = $(".ui-datepicker-month").text();
			/**
	         Get Selected year text from Multidatepicker
	         **/	
			var selectedYear  = $(".ui-datepicker-year").text();	
			var date = new Date();	
			var currentMonthName = monthNames[date.getMonth()];
			var currentYear = date.getFullYear();

			// check if selected month is same as currentMonth & selected year is same as current year
			if(currentMonthName == selectedMonth && selectedYear == currentYear){
				
				todaysDate = date.getDate();
				var lastDayOfMonth = new Date(date.getFullYear(), date.getMonth()+1, 0);
				lastDayOfMonth = lastDayOfMonth.getDate();
				var dates = [];
				/**
					add date to multidate picker from todays date till month end
				**/
				for(var i=todaysDate; i<=lastDayOfMonth ;i++){
					dates.push(date.setDate(i));
					//console.log(date.setDate(i));
				}
				console.log(dates);
				//MDP.addDates(dates);
				/*$("#datepicker").multiDatesPicker({
					minDate: 0,
					addDates: dates,
					//defaultDate : new Date(2015, 7, 1),
					onSelect: function(date) {
			        },
				});*/
				//console.log(dates);
				$("#datepicker").multiDatesPicker('addDates', dates);
				$("#alternate").val($("#datepicker").multiDatesPicker('getDates'));
			}else{

				var monthDate = new Date();
				
				monthIndex = monthNames.indexOf(selectedMonth);
				monthDate.setMonth(monthIndex);
				monthDate.setYear(selectedYear);
				
				var lastDayOfMonth = new Date(monthDate.getFullYear(),monthIndex+1, 0);
				lastDayOfMonth = lastDayOfMonth.getDate();
				var dates = [];
				/**
					add date to multidate picker from  from 1st date till month end
				**/

				var dafaultdate = new Date(monthDate.getFullYear(),monthIndex, 1);		// set default date of selected month to 1

				for(var i=1; i<=lastDayOfMonth ;i++){
					dates.push(monthDate.setDate(i));
				}

				//$('#with-altField').multiDatesPicker('destroy');	// destroy already existing instance of MDP
				/*$("#datepicker").multiDatesPicker({
					minDate: 0,
					addDates: dates,
					defaultDate:"85-2-19",
					onSelect: function(date) {
			        },
					
				});*/
				$("#datepicker").multiDatesPicker('addDates', dates);
				$("#alternate").val($("#datepicker").multiDatesPicker('getDates'));
				//alert("Lol");
				//alert(new Date(2015, 7, 1));datepicker("setDate", new Date()
				//$("#datepicker").multiDatesPicker('defaultDate', );
			}
		});

		// working days excluding sundays
		$(document).on("click","#optionsRadios2",function(){

			var menu = $("#menu").val();
			var selectedMonth = $(".ui-datepicker-month").text();
			var selectedYear  = $(".ui-datepicker-year").text();
			var date = new Date();	
			var currentMonthName = monthNames[date.getMonth()];
			var selectedMonthIndex = monthNames.indexOf(selectedMonth);	
			var currentYear = date.getFullYear();
			var prevSelDates = $("#datepicker").multiDatesPicker('getDates');
		 	var monthDates = [];

			$.each(prevSelDates, function( index, value ){

				var date = new Date(value);
				var month = date.getMonth();
				var year = date.getFullYear();
				if(selectedMonthIndex == month && selectedYear == year){
					monthDates.push(value);
				}
				
			});
			//var day_count = dates.length;
			$("#datepicker").multiDatesPicker('removeDates', monthDates); 

			// check if selected month is same as currentMonth and selected year is same as current year
			if(currentMonthName == selectedMonth && selectedYear == currentYear){ 
				
				todaysDate = date.getDate();
				var lastDayOfMonth = new Date(date.getFullYear(), date.getMonth()+1, 0); // get last date of month
				lastDayOfMonth = lastDayOfMonth.getDate();								// get date e.g 30 /31 of current month
				var dates = [];
				/**
					add date to multidate picker from todays date till month end
				**/
				for(var i=todaysDate; i<=lastDayOfMonth ;i++)
				{
					var currentDate = new Date(date.getFullYear(), date.getMonth(), i);			
					var day = currentDate.getDay();
					if(day!=0){  // if day is not 0 i.e not sunday then only push in date array
						dates.push(date.setDate(i));
					}
				}
				//$('#with-altField').multiDatesPicker('destroy');	
				/*$("#datepicker").multiDatesPicker({
					minDate: 0,
					addDates: dates,		// add dates to calendar
					defaultDate : new Date(2015, 7, 1),
					onSelect: function(date) {
			        },
				});*/
				$("#datepicker").multiDatesPicker('addDates', dates);
				$("#alternate").val($("#datepicker").multiDatesPicker('getDates'));
			}else{
				var monthDate = new Date();			// date object
				monthIndex = monthNames.indexOf(selectedMonth);			// get Index selectedMonth month from Months array
				monthDate.setMonth(monthIndex);							// set Month to selected month into date object
				monthDate.setYear(selectedYear);

				var lastDayOfMonth = new Date(monthDate.getFullYear(),monthIndex+1, 0);
				
				lastDayOfMonth = lastDayOfMonth.getDate();
				var dates = [];
				/**
					add date to multidate picker from 1st date  till month end
				**/
				for(var i=1; i<=lastDayOfMonth ;i++){
					var currentDate = new Date(monthDate.getFullYear(), monthDate.getMonth(), i);
					var day = currentDate.getDay();
					if(day!=0){
						dates.push(monthDate.setDate(i));
					}
				}

		
				/*$("#datepicker").multiDatesPicker({
					minDate: 0,
					addDates: dates,
					defaultDate : new Date(2015, 7, 1),
					onSelect: function(date) {
			        },
				});*/
				$("#datepicker").multiDatesPicker('addDates', dates);
				$("#alternate").val($("#datepicker").multiDatesPicker('getDates'));
			}
		});
});
</script>
 
<style>
.producttypecls label{float:left; width:50%}
</style>

<?php
$form = $this->form;
$form->setAttribute('action', $this->url('product', array('action' => 'add')));
$form->setAttribute('class','stdform');
$form->prepare();
?>

      
      <!-- END PAGE HEADER-->
      
      <div id="content">
      <?php echo $this->form()->openTag($form);?>
        <div class="large-6 columns">
			<fieldset>
					<legend>
					PRODUCT INFO
				</legend>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right">Name<span	class="red">*</span></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php 
               		echo $this->formHidden($form->get('pk_product_code'));
					echo $this->formElement($form->get('name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						->setMessageCloseString('</small>')
						->render($form->get('name'));
				?>
              </div>
            </div>
            
            
              <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('kitchen_code')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                	echo $this->formElement($form->get('kitchen_code'));
					echo $this->formElementErrors($form->get('kitchen_code'));
				 ?>
              </div>
            </div>
            
              <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('product_subtype')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns radiocls">
                <?php 
                	echo $this->formElement($form->get('product_subtype'));
					echo $this->formElementErrors($form->get('product_subtype'));
				 ?>
              </div>
            </div>            
            
            
              <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('food_type')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                	echo $this->formElement($form->get('food_type'));
					echo $this->formElementErrors($form->get('food_type'));
				 ?>
              </div>
            </div>
            
            
              <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('product_category')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php
                	echo $this->formElement($form->get('product_category'));
					echo $this->formElementErrors($form->get('product_category'));
				 ?>
              </div>
            </div>
            
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('category')); ?></label>
              </div>
              <div class="large-5 small-5 medium-5 columns">
               <?php
               		echo $this->formElement($form->get('category'));
					echo $this->formElementErrors($form->get('category')); 
				?>
              </div>
              <div class="large-3 small-3 medium-3 columns">
               <label class="disabled">( Press Ctrl key for <br/>multiple selection. )</label>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('screen')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
               		echo $this->formElement($form->get('screen'));
					echo $this->formElementErrors($form->get('screen')); 
				?>
              </div>
            </div>            

            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('description')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                	echo $this->formElement($form->get('description'));
					echo $this->formElementErrors($form->get('description'));
				 ?>
              </div>
            </div>
            
             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('recipe')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                	echo $this->formElement($form->get('recipe'));
					echo $this->formElementErrors($form->get('recipe'));
				 ?>
              </div>
            </div>
            
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('unit_price')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                	<?php
                	 echo $this->formElement($form->get('unit_price'));
					 echo $this->formElementErrors($form->get('unit_price')); 
					?>
            </div>
            </div>
            
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('product_type')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns radiocls">
               <?php
               		echo $this->formElement($form->get('product_type'));
					echo $this->formElementErrors($form->get('product_type')); 
				?>
              </div>
            </div>
            

            
            <?php /*
            $setting = new \Zend\Session\Container('setting');
            $setting = $setting->setting;
            if( $setting['MAP_PRODUCT_TO_LOCATION'] == 1 ) {
            ?>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('location')); ?></label>
              </div>
              <div class="large-5 small-5 medium-5 columns">
               <?php
               		echo $this->formElement($form->get('location'));
					echo $this->formElementErrors($form->get('location')); 
				?>
              </div>
              <div class="large-3 small-3 medium-3 columns">
               <label class="disabled">( Press Ctrl key for <br/>multiple selection. )</label>
              </div>
            </div>
            <?php
            }
            */?>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('threshold')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php 
               		echo $this->formElement($form->get('threshold'));
					echo $this->formElementErrors($form->get('threshold')); ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('max_quantity_per_meal')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
               		echo $this->formElement($form->get('max_quantity_per_meal'));
					echo $this->formElementErrors($form->get('max_quantity_per_meal')); ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('image_path')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php
					echo $this->formElement($form->get('image_path'));
					echo $this->formElementErrors($form->get('image_path'));
				?>
              </div>
            </div>
			<div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right">Quantity<span class="red">*</span></label>
              </div>
              <div class="large-4 small-4 medium-4 columns">
              	<?php 
               		echo $this->formElement($form->get('quantity'));
					echo $this->formElementErrors($form->get('quantity')); 
				?>
              </div>
              <div class="large-4 small-4 medium-4 columns">
                 <?php 
               		echo $this->formElement($form->get('unit'));
					echo $this->formElementErrors($form->get('unit')); 
				?>          	
              </div>
            </div>            
            
            
            
            	<?php 
              		$status = ($this->status=='') ? "active" : $this->status;
              	?>
              	 <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">      
                  	
                  	
                  		<div class="large-3 left">
		                 <label for="status1">
								<input <?php echo ($status=='active') ? "checked" : "" ;?> name="status" type="radio" id="status" value="1">
								 <span class="custom radio"></span> Active
							  </label>
						 </div>
						 <div class="large-3 left">
						 	 <label for="status2">
								<input <?php echo ($status=='inactive') ? "checked" : "" ;?> name="status" type="radio" id="status"  value="0">
								 <span class="custom radio"></span>Inactive
							 </label>
						</div>
						          
                      <?php 
                      	/* echo $this->formElement($form->get('status'));
						echo $this->formElementErrors($form->get('status')); */
						echo $this->formElement($form->get('csrf')); 
						 echo $this->formElement($form->get('backurl'));
						
					 ?>     
                  	</div>
                  	
              	</div>
              	
            </fieldset>
            
         <?php 
          if( (isset($this->settings['GLOBAL_ALLOW_MENU_PLANNER']) && $this->settings['GLOBAL_ALLOW_MENU_PLANNER']=='yes') ){
          ?>
          <fieldset>
        <legend> Swapping Options</legend>
        <div class="row">
          <div class="large-4 small-4 medium-4 columns">
            <?php echo $this->formLabel($form->get('is_swappable')); ?> 
          </div>
          <div class="large-8 smal-8 medium-8 columns radiocls">
            <?php echo $this->formelement($form->get('is_swappable'));?>
            <?php echo $this->formElementErrors($form->get('is_swappable'));?>
          </div>
        </div>
        
        <div class="row swaprow dn">
          <div class="large-4 small-4 medium-4 columns">
            <?php echo $this->formLabel($form->get('swap_with')); ?>
          </div>
          <div class="large-8 smal-8 medium-8 columns radiocls">
            <?php echo $this->formelement($form->get('swap_with'));?>
            <?php echo $this->formElementErrors($form->get('swap_with'));?>
            <?php echo $this->formelement($form->get('swap_charges'));?>
            <?php echo $this->formElementErrors($form->get('swap_charges'));?>
            
          </div>
        </div>
        
      </fieldset>
      <?php 
          }else{
      ?>
      <input type="hidden" name="is_swappable" id="is_swappable" value="0">
      <?php 
          }
      ?>             
            <div class="large-12 columns pl0 pr0">
	            <div class="row">
	              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
	              <div class="large-8 small-8 medium-8 columns"> 
	               <button	type="submit" id ='submitbutton' class="button	left tiny left5	dark-greenBg" onclick="javascript:validate_multilingual_code(); return submit_status;">Save &nbsp;<i	class="fa fa-save"></i></button>
	                <button	type="button" id ='cancelbutton' class="button	left tiny left5	redBg" >Cancel &nbsp;<i class="fa	fa-ban"></i></button>
	              </div>
	            </div>
            </div>

        </div>
        <div class="large-6 columns">
        	<?php
        	if( isset($language_array) && is_array($language_array) && !empty($language_array) ) {
        	/*<div class="clearBoth10"></div>*/
        	?>
        	<fieldset>
        		<legend>Product Name in other language
        			<br>
        			<span class="" style=" text-transform: none" >To type in other language please</span><!-- style="color: #fc6e51" -->
					<a href="http://www.quillpad.in/index.html" target="_blank" class=""><!-- To type in other language use QuillPad -->Click here</a>
        		</legend>
        	<div class="multi-field-wrapper">
	            <div class="multiple-fields"><?php
        			foreach($language_array as $code => $name) {
        		?>
        	<div class="multiple-field">
        	<div class="row">
        		<div class="large-2 small-2 medium-2 columns">
					<label class="inline left"><?php echo $name; ?></span></label>
			    </div>
        		<div class="large-10 small-10 medium-10 columns">
        			<div class="large-2 small-2 medium-2 columns">
        			<label class="inline right">Name</label>
        			<input type="hidden" class="smallinput" id="supported_for_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][context]" value="product_language" />
        			<?php
        			if( isset($local_language_array[$code]['id']) && !empty($local_language_array[$code]['id']) ) {
        			?>
        			<input type="hidden" class="smallinput" id="supported_id_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][id]" value="<?php echo $local_language_array[$code]['id']; ?>" />
        			<?php
        			}
        			?>
        			</div>
			    	<div class="large-4 small-4 medium-4 columns">
					<input type="text" class="smallinput" id="supported_product_name_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][context_name]" 
					value="<?php echo ( isset($_POST['other_language'][$code]['context_name']) && !empty($_POST['other_language'][$code]['context_name']) ) ? $_POST['other_language'][$code]['context_name'] : ( ( isset($local_language_array[$code]['context_name']) && !empty($local_language_array[$code]['context_name']) ) ? $local_language_array[$code]['context_name'] : ''); ?>" language-name="<?php echo $name; ?>" />
				    </div>
					<div class="large-2 small-2 medium-2 columns">
					<label class="inline right">Code</label>
					</div>
					<div class="large-4 small-4 medium-4 columns">
					<input type="text" class="smallinput" maxlength="9" id="supported_product_code_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][context_code]" value="<?php echo ( isset($_POST['other_language'][$code]['context_code']) && !empty($_POST['other_language'][$code]['context_code']) ) ? $_POST['other_language'][$code]['context_code'] : ( ( isset($local_language_array[$code]['context_code']) && !empty($local_language_array[$code]['context_code']) ) ? $local_language_array[$code]['context_code'] : '' ); ?>" />
					</div>
				</div>
        	</div>
        	</div>
        	<?php
        			}#end of foreach
        	?>
        	</div>
        	</div>
        	</fieldset><?php
        		}
        	?>
        </div>
        <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
      </div>
    </div>
    
    <!-- END PAGE CONTAINER--> 
<?php
if( $setting['MAP_PRODUCT_TO_LOCATION'] == 1 ) {
?>
<script type="text/javascript">
$('document').ready(function() {

	$('#screen').bind('change', function() {
		if( $.trim($('#screen').val()).length > 0 ) {
			$.post('/product/getKitchenLocations', {kitchen_id: $('#screen').val() }, function($data) {
				//alert($data);alert($.parseJSON($data));
				$result = $.parseJSON($data);
				$prev_selected_ids = '<?php echo ( isset($_POST['location']) && !empty($_POST['location']) ) ? json_encode($_POST['location']) : ( !empty($location_mapped_array) ? json_encode($location_mapped_array) : '[]' ); ?>';
				$prev_data = $.parseJSON($prev_selected_ids);
				$('#location').html('');
				for(var i in $result) {
					var $selected = false;
					if($prev_data.length > 0) {
						for(var j in $prev_data) {
							if($prev_data[j] == $result[i].value) { $selected = true; break; }
						}// end of foreach
					}
					else if(i == 0) { $selected = true; }
					var strOption = '<option value="'+$result[i].value+'" '+($selected ? ' selected="selected"' : '')+'>'+$result[i].text+'</option>';
					$('#location').append(strOption);
				}// end of for
				$('#location').attr('title', 'Select atleast one location from the list.');
			});
		}
		else {
			$('#location').html('').attr('title', 'Select a kitchen to list appropriate locations.');
		}
	});

	<?php
	if( isset($_POST['location']) && !empty($_POST['location']) ) {
	?>
	$('#screen').trigger('change');
	<?php
	}
	?>
});
</script>
<?php
}
?>
<script type="text/javascript">
var submit_status = false;
function validate_multilingual_code() {
	var error_message = '';
	$('input[id^="supported_product_name_"]').each(function() {
		var product_name = $.trim($(this).val());
		var product_name_array = $(this).attr('id').split('_');
		var product_code = $.trim($('#supported_product_code_'+product_name_array[product_name_array.length - 1]).val());

		if( product_name.length > 0 && product_code.length == 0 ) {
			error_message = 'Product code in '+( $(this).attr('language-name') )+' is not specified.';
			$('#supported_product_code_'+product_name_array[product_name_array.length - 1]).focus();
			return false;
		}
		else if( product_name.length == 0 && product_code.length > 0 ) {
			error_message = 'Product name in '+( $(this).attr('language-name') )+' is not specified.';
			$('#supported_product_name_'+product_name_array[product_name_array.length - 1]).focus();
			return false;
		}
	});
	$('input[id^="supported_product_name_"]').promise().done(function() {
		if(error_message.length > 0) { alert(error_message);submit_status = false; }
		else { submit_status = true; }
	});
}

$(document).ready(function(){

    // Swap radio button click handler

    $(document).on("click",".swappable",function() {

		if($("input[name='is_swappable']:checked").val() == '1'){
			$(".swaprow").show();	
		}else{
			$(".swaprow").hide();
		}
	});

    if($("input[name='is_swappable']:checked").val() == '1'){
		$(".swaprow").show();	
	}else{
		$(".swaprow").hide();
	}

});
</script>
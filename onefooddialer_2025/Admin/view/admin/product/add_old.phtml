<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/form.js"></script>
<style>
.disradio label{
width:auto;
}
</style>

<?php
$form = $this->form;
$form->setAttribute('action', $this->url('product', array('action' => 'add')));
$form->setAttribute('class','stdform');

$form->prepare();

?>
<?php echo $this->form()->openTag($form);?>
    <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>New Product</span></h2>
          </div>
         	 <!--contenttitle-->
          <br/>
          <form class="stdform" action="" method="post">
            <p>
              <label><?php echo $this->formLabel($form->get('name')); ?></label>
              <span class="field">
             <?php  echo $this->formHidden($form->get('pk_product_code'));
					echo $this->formElement($form->get('name'));
					echo $this->formElementErrors($form->get('name'),array('class' => 'red')); ?>
              </span>
            </p>

            <p>
              <label><?php echo $this->formLabel($form->get('description')); ?></label>
              <span class="field">
             	<?php echo $this->formElement($form->get('description'));
				echo $this->formElementErrors($form->get('description'),array('class' => 'red')); ?>
              </span>
            </p>

            <p>
              <label><?php echo $this->formLabel($form->get('unit_price')); ?></label>
              <span class="field">
              <?php echo $this->formElement($form->get('unit_price'));
					echo $this->formElementErrors($form->get('unit_price'),array('class' => 'red')); ?>
              </span>
            </p>

            <p>
              <label><?php echo $this->formLabel($form->get('product_type')); ?></label>
              <span class="field">
              <?php echo $this->formElement($form->get('product_type'));
				echo $this->formElementErrors($form->get('product_type'),array('class' => 'red')); ?>
              </span>
            </p>

            <p>
              <label><?php echo $this->formLabel($form->get('screen')); ?></label>
              <span class="field">
              <?php echo $this->formElement($form->get('screen'));
					echo $this->formElementErrors($form->get('screen'),array('class' => 'red')); ?>
              </span>
            </p>

			<p>
              <label><?php echo $this->formLabel($form->get('threshold')); ?></label>
              <span class="field">
              <?php echo $this->formElement($form->get('threshold'));
					echo $this->formElementErrors($form->get('threshold'),array('class' => 'red')); ?>
              </span>
            </p>
			<p><label><?php echo $this->formLabel($form->get('image_path')); ?></label>
			<span class="field"><?php
				echo $this->formElement($form->get('image_path'));
				echo $this->formElementErrors($form->get('image_path'),array('class' => 'red'));?> </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('status')); ?></label>
              <span class="field">
              <?php echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'),array('class' => 'red'));
					echo $this->formElement($form->get('csrf')); ?>
              </span>
            </p>

            <p class="stdformbutton">
				<?php echo $this->formSubmit($form->get('submit')); ?>
				<?php echo $this->formSubmit($form->get('cancel')); ?>
            </p>

            <p>
              <span class="field">
            	 <?php echo $this->formElement($form->get('backurl'));
				 ?>
              </span>
            </p>
           </form>
          <br clear="all" />
          <br />
        </div>
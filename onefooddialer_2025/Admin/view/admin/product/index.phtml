<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                   <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addprod" class="common-orange-btn-on-hover"> Add Products</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="impprod" class="common-orange-btn-on-hover">Import products</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updProd" class="common-orange-btn-on-hover">Update Product details</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="deactprod" class="common-orange-btn-on-hover">Deactivate Products</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
<?php
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	$calendar_setting = $setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'];
?>
      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
            <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Products</h4>  
            <ul class="toolOption">
            
            	
            	<li>
            	 <?php
            	 if($acl->isAllowed($loggedUser->rolename,'product','add'))	{ ?>
          			<div class="addRecord addProduct">
	                	<button class="btn" onClick="location.href='<?php echo $this->url('product', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Product</button>
	                 </div>
          			
          			<?php 
          			
          			if($calendar_setting == 1){  ?>
	          			<div class="addRecord">
		                	<button class="btn" onClick="location.href='<?php echo $this->url('product', array('action'=>'product-calendar'));?>'"><i class="fa fa-calendar"></i> &nbsp;Product Calendar</button>
		                 </div>
	                 <?php } ?>
	                
                  <?php } ?>
                </li>
                <li>
					<div class="addRecord imports">
						<button onClick="location.href='<?php echo $this->url('product', array('action'=>'import'));?>'" class="btn">
							<i class="fa fa-reply"></i> &nbsp; Import Product
						</button>
					</div>
				</li>
             
                
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable ">
                    <thead>
                    
                        <tr>
                            <th>Product Name</th>
                            <th>Kitchen Code</th>
                            <!-- <th width="">Description</th> -->
                            <th>Unit Price <!-- <i class="fa fa-rupee"></i> --></th>
                            <!-- <th>Threshold</th> -->
                            <th>Kitchen Capacity</th>
                            <!-- <th>Product Type</th> -->
                            <th>Kitchen Screen</th>
                            <th>Product Subtype</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 			</table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
    
    
    <script type="text/javascript">
$(document).ready(function() {


	$('#customer').dataTable( {
        "processing": true,
        "serverSide": true,
        "bDestroy" :true,
    	"stateSave": true,
    	 //"aoColumns":aoColumns,
        "ajax": "/product/ajx-product",
        "aoColumnDefs": [
        	                {
        	                   bSortable: false,
        	                   aTargets: [ -1,-2 ]
        	                }
        	              ],
    });
});
</script>
 <script type="text/javascript">
  $(document).on('click',"#addprod",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addProduct').attr("data-step", "1");
      $('.portlet-title').find('.addProduct').attr("data-intro", "Click here to add Products/items.");
      $('.portlet-title').find('.addProduct').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.addProduct').removeAttr("data-step");
      $('.portlet-title').find('.addProduct').removeAttr("data-intro");

  });
  $(document).on('click',"#impprod",function(e){
      e.preventDefault();
      $('.portlet-title').find('.imports').attr("data-step", "1");
      $('.portlet-title').find('.imports').attr("data-intro", "Import products in bulk");
      $('.portlet-title').find('.imports').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.imports').removeAttr("data-step");
      $('.portlet-title').find('.imports').removeAttr("data-intro");

  });
  $(document).on('click',"#updProd",function(e){
      e.preventDefault();
      //alert($('.displayTable').find('tbody tr:first td:eq(7) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(7) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(7) button:first').attr("data-intro", "Click here to edit product details");
      $('.displayTable').find('tbody tr:first td:eq(7) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(7) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(7) button:first').removeAttr("data-intro");
  });

    $(document).on('click',"#deactprod",function(e){
      e.preventDefault();
      $('.displayTable').find('tbody tr:first td:eq(7) button:eq(1)').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(7) button:eq(1)').attr("data-intro", "Click on to deactivate products.");
      $('.displayTable').find('tbody tr:first td:eq(7) button:eq(1)').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(7) button:eq(1)').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(7) button:eq(1)').removeAttr("data-intro");
    });
</script>  
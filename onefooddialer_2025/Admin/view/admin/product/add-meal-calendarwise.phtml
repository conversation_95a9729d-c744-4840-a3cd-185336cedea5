 <style>
 .reveal-modal.custPopup {
margin-left: -40%;
width: 80%;
}
 </style>
<script src="/admin/js/shuffle.js" type="text/javascript"></script>
<script src="/admin/js/modernizr.js" type="text/javascript"></script>

 <?php
 
 $utility = \Lib\Utility::getInstance();
 $setting_session = new Zend\Session\Container("setting");
 $setting = $setting_session->setting;
 $date_format = $setting['DATE_FORMAT'];
 $total_days_in_month = cal_days_in_month(CAL_GREGORIAN, $month, $year);
 $first_day_of_month = '1';
 $first_date_of_month = $year.'-'.$month.'-01';
 $last_date_of_month = $year.'-'.$month.'-'.$total_days_in_month;
 $last_day_of_month = $total_days_in_month;
 $total_rows = ceil($total_days_in_month/4);
 //echo $year." ".$month." ". $first_day_of_month." ".$last_day_of_month." ".$total_rows;exit;
?>
 <div id="menuList" class="reveal-modal custPopup" data-v-offset="70%;" data-reveal aria-labelledby="calendarProducts" aria-hidden="true" role="dialog">

				<div class="menuList_container">
					<h3 class="mb15 addmenu"></h3>
					<div class="listOfmenu" style="width: 100%;">

					</div>
					<div style="display: block">
						<button	type="abc" class="butto right tiny mt10 mb0" id = "addProductToMeal">
							<i class="fa fa-plus"></i>&nbsp;Add  
						</button>
					</div>

				</div>
				<input type = "hidden" class = "selectedDate" value = "">
				<a class="close-reveal-modal">&#215;</a>

			</div>
			
      <!-- END PAGE HEADER-->
      <div id="content" class="clearfix">
        <div class="large-12 columns medium-12 small-12 columns">
            <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	 

	<form id = "reload" class="calForm" method = "POST">
		
	
		<select name = "year" class="left">
			<?php for($i=(date('Y')-1);$i<=(date('Y')+1);$i++){ 
				$selectedYear = '';
				if($year == $i){
					$selectedYear = 'selected=selected';
				}
			?>
				<option value = "<?php echo $i;?>" <?php echo $selectedYear;?>><?php echo $i;?></option>
			<?php }?>
		</select>
		<?php $monthArray = array("1"=>"Jan","2"=>"Feb","3"=>"Mar","4"=>"Apr","5"=>"May","6"=>"June","7"=>"July","8"=>"Aug","9"=>"Sep","10"=>"Oct","11"=>"Nov","12"=>"Dec");?>
		<select name = "month" class="left">
			<?php foreach($monthArray as $key=>$val){
				$selectedMonth = '';
				if($month == $key){
					$selectedMonth = 'selected=selected';
				}$selectedmenu
			?>
				<option value = <?php echo $key;?> <?php echo $selectedMonth;?>><?php echo $monthArray[$key];?></option>
			<?php }?>
		</select>
		
			<select id="selectedmenu" name="selectedmenu" class="left">
				<?php
					foreach ($menus as $menu){
						$selectedmenu ='';
						if($menu==$selected_menu){
							$selectedmenu = 'selected=selected';
						}
						?>
						<option value = "<?php echo $menu;?>" <?php echo $selectedmenu;?>><?php echo ucfirst($menu);?></option>
						
			<?php 	} 
			?>
		</select>
		
		<button  class="button left tiny" data-text-swap="Processing..  " type="submit"> Submit </button>
	</form>
	<?php 
		$showCalendarMonth = $month;
		$pos = strpos($month, '0');
		if ($pos === false) {
			
		} else {
			if($pos=='0' || $pos==0){
				$showCalendarMonth = substr($showCalendarMonth, 1);
			}
		}
	?>
	<h3 class="mb15"><?php echo isset($meal_name)?$meal_name.' :':'';?>Menus For <?php echo isset($monthArray[$showCalendarMonth])? $monthArray[$showCalendarMonth]:'';?> - <?php echo $year;?></h3>
	<div class="row shuffleContainer">
	<?php for($k=$first_day_of_month;$k<=$last_day_of_month;$k++){
		$date_convert = $year.'-'.$month.'-'.$k;
		$date = date($date_format,strtotime($date_convert));
		$date_for_compare = str_replace('/', '-', $date);
	?>
	
		<?php if($k%3==1){?>
			
		<?php }?>
		
			<div class="large-4 small-12 medium-6 columns left">
				<div class="menuBlock">
					<div class="blockHeader">
						<span class="menuDate"><?php echo $date.' ('.date('l', strtotime($date_convert)).')';?></span>
						<?php if(strtotime($date_convert)>=strtotime(date('Y-m-d'))){?>
						<button type="button" class="add-field smBtn has-tip tip-top remove-field addProduct" data-tooltip title="Add more" id = "<?php echo $k;?>">
							<i class="fa fa-plus"></i>
						</button>
						<?php } ?>
					</div>
					<!---blockHeader-->

					<div class="menu_Container" id = "<?php echo date('d-m-Y',strtotime($date_convert));?>">
						<?php 
						$chkProducts = false;
							if(isset($products_from_db) && count($products_from_db) > 0){
								foreach($products_from_db as $key=>$val){
									if($date == date($date_format,strtotime($val['calendar_date']))){	
										$chkProducts = true;
						?>	
										<div class="alert-box menuItem radius" data-alert="">
										<span class="menuItem_name"><?php echo $val['product_name'];?></span><label><?php echo $val['product_qty'];?></label>
										<?php if(strtotime($val['calendar_date']) >= strtotime(date('Y-m-d'))) {?>
											<a class="close removeProductFrmDate" id = "<?php echo $val['meal_calendar_id']; ?>">×</a>
										<?php }?>
										</div>
						<?php 
									}
								}
									
								}
								if($chkProducts == false && strtotime($date_convert)>=strtotime(date('Y-m-d'))){
									echo "<h5>Click (+) to add product.</h5>";
							}
						?>
					</div><!---menu_Container-->

				</div><!---menuBlock---->
			</div>
			<?php if($k%3==0){?>
			
		<?php }?>
	<?php }?>
	</div>
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
<script type = "text/javascript">
// browser detect start
var objappVersion = navigator.appVersion; var objAgent = navigator.userAgent; var objbrowserName = navigator.appName; var objfullVersion = ''+parseFloat(navigator.appVersion); var objBrMajorVersion = parseInt(navigator.appVersion,10); var objOffsetName,objOffsetVersion,ix; 
// In Chrome 
if ((objOffsetVersion=objAgent.indexOf("Chrome"))!=-1) { objbrowserName = "Chrome"; objfullVersion = objAgent.substring(objOffsetVersion+7); } 
// In Microsoft internet explorer 
else if ((objOffsetVersion=objAgent.indexOf("MSIE"))!=-1) { objbrowserName = "Microsoft Internet Explorer"; objfullVersion = objAgent.substring(objOffsetVersion+5); } 
// In Firefox 
else if ((objOffsetVersion=objAgent.indexOf("Firefox"))!=-1) { objbrowserName = "Firefox"; } 
// In Safari 
else if ((objOffsetVersion=objAgent.indexOf("Safari"))!=-1) { objbrowserName = "Safari"; objfullVersion = objAgent.substring(objOffsetVersion+7); if ((objOffsetVersion=objAgent.indexOf("Version"))!=-1) objfullVersion = objAgent.substring(objOffsetVersion+8); } 
// For other browser "name/version" is at the end of userAgent 
else if ( (objOffsetName=objAgent.lastIndexOf(' ')+1) < (objOffsetVersion=objAgent.lastIndexOf('/')) ) { objbrowserName = objAgent.substring(objOffsetName,objOffsetVersion); objfullVersion = objAgent.substring(objOffsetVersion+1); if (objbrowserName.toLowerCase()==objbrowserName.toUpperCase()) { objbrowserName = navigator.appName; } } 
// trimming the fullVersion string at semicolon/space if present 
if ((ix=objfullVersion.indexOf(";"))!=-1) objfullVersion=objfullVersion.substring(0,ix); if ((ix=objfullVersion.indexOf(" "))!=-1) objfullVersion=objfullVersion.substring(0,ix); objBrMajorVersion = parseInt(''+objfullVersion,10); if (isNaN(objBrMajorVersion)) { objfullVersion = ''+parseFloat(navigator.appVersion); objBrMajorVersion = parseInt(navigator.appVersion,10); } 
console.log('' +'Browser name = '+objbrowserName+'<br>' +'Full version = '+objfullVersion+'<br>' +'Major version = '+objBrMajorVersion+'<br>' +'navigator.appName = '+navigator.appName+'<br>' +'navigator.userAgent = '+navigator.userAgent+'<br>' )
// browser detect stop
// window height width start
function f_filterResults(n_win, n_docel, n_body) {
	var n_result = n_win ? n_win : 0;
	if (n_docel && (!n_result || (n_result > n_docel)))
		n_result = n_docel;
	return n_body && (!n_result || (n_result > n_body)) ? n_body : n_result;
}
function f_clientWidth() {
	return f_filterResults (
		window.innerWidth ? window.innerWidth : 0,
		document.documentElement ? document.documentElement.clientWidth : 0,
		document.body ? document.body.clientWidth : 0
	);
}
function f_clientHeight() {
	return f_filterResults (
		window.innerHeight ? window.innerHeight : 0,
		document.documentElement ? document.documentElement.clientHeight : 0,
		document.body ? document.body.clientHeight : 0
	);
}
function f_scrollLeft() {
	return f_filterResults (
		window.pageXOffset ? window.pageXOffset : 0,
		document.documentElement ? document.documentElement.scrollLeft : 0,
		document.body ? document.body.scrollLeft : 0
	);
}
function f_scrollTop() {
	return f_filterResults (
		window.pageYOffset ? window.pageYOffset : 0,
		document.documentElement ? document.documentElement.scrollTop : 0,
		document.body ? document.body.scrollTop : 0
	);
}
// window height width stop
function cal_height() {
	var appname = objbrowserName;console.log(appname);
	var ht = f_clientHeight() + f_scrollTop();
	//console.log('window client height='+(f_clientHeight() +' + '+ f_scrollTop())+'='+ht);
	var height = parseInt($('#menuList').css('top'));
	//console.log(height+'==='+(((f_clientHeight())*20/100) + f_scrollTop()));
	var newTop = ((f_clientHeight())*20/100) + f_scrollTop();
	switch(appname.toLowerCase()) {
		case 'chrome':
		newTop = height;
		break;
		case 'mozilla':
		case 'firefox':
		newTop = parseInt(parseInt(f_clientHeight()+f_scrollTop())*10/100);
		break;
	}
	$("#menuList").css({'top': height -(height - newTop)});
	var val_ht = parseInt(parseInt(f_clientHeight()) / 2);
	//console.log(val_ht);
	$(".listOfmenu").css('max-height', val_ht+'px');
	$(".listOfmenu").css('width', '100%');
}

function open_scroll() {
	$(document).on('opened.fndtn.reveal', '[data-reveal]', function() {
		shuffle_product();
		cal_height()
		$(".listOfmenu").niceScroll({
			cursorwidth: "12px"
		});
	});
	$(document).on('closed.fndtn.reveal', '[data-reveal]', function() {
		$(".listOfmenu").getNiceScroll().hide();
	});
}

function close_scroll() {
	$(document).on('closed.fndtn.reveal', '[data-reveal]', function() {
		//shuffle_product();
		$(".listOfmenu").getNiceScroll().remove();
		
		//open_scroll();
		// $('.listOfmenu').niceScroll({
			// cursorwidth: "10px"
		// }).resize();
	});
}

function shuffle_product(){	
	//alert(2);
	$('.shuffleContainer').each(function() {
		//alert(12);
		gridContainer = $(this);
		var sizer = gridContainer.find('.columns');
		gridContainer.shuffle({
			sizer : sizer,
			speed : 500,
			easing : 'ease-out'
		});
	});
}
</script>
<script type = "text/javascript">
	$(document).ready(function(){
		//shuffle_product();
		// open_scroll();
		//close_scroll();

		$(document).scroll(function(){
			 $(".listOfmenu").getNiceScroll().resize();
		});
		
		$(".addProduct").on('click',function(){

			var selectedDate = $(this).attr('id')+'-'+<?php echo $month;?>+'-'+<?php echo $year;?>;
			var selected_menu = "<?php echo $selected_menu;?>";
			var meal_name = "<?php echo $meal_name;?>";	
			$.ajax({
				url : "<?php echo $this->url('product',array('action' => 'get-product-on-date')); ?>",
				type: "POST",
				data: {day:$(this).attr('id'),month:<?php echo $month;?>,year:<?php echo $year;?>,id:<?php echo $id;?>,selected_menu:selected_menu,kitchen:<?php echo $kitchen;?>},
				success: function(html){
					var db_product = [];
					var db_product_qty = {};
					
					$.each(html.db_data, function(key, value){
						db_product[key] = value.product_code;
						db_product_qty[value.product_code] = value.product_qty;
					});
					
					//console.log("product."+db_product.length);
					//console.log("proQty=>"+db_product_qty.length);
					var htmlAppend = '';
					var count_category = 1;
					if(html.count.length>0 && html.data.length>0){
						htmlAppend +='<div class="row shuffleContainer">';
						$.each(html.count, function(key, value){
							/* if(count_category % 2 == 0){
								
							} */
							htmlAppend +='<div class="large-6 small-12 medium-12 columns ">'+
								'<div class="menuBlock">'+
									'<div class="blockHeader">'+
										'<span class="menuDate">'+value.product_category+'</span>'+
									'</div>';
									<!---blockHeader-->
									$.each(html.data, function(key1, value1){
										
										//db_product_qty[value.product_code] = value.max_quantity_per_meal;
										//console.log( value1.max_quantity_per_meal);
										console.log( value1.fk_product_code);
										if(value.product_category == value1.product_category){
											var in_db_present = "";
											var qty_db_present = "";
											if(jQuery.inArray( value1.fk_product_code, db_product )!== -1){
												in_db_present = "checked";
											}
//											htmlAppend +='<div class="menu_Container popup_menu">'+
//												'<div class="alert-box menuItem radius" data-alert="">'+
//													'<input type="checkbox" id = "product'+value1.fk_product_code+'" class = "product_code  selectAll" '+in_db_present+'>'+
//													'<input type="hidden" class = "product_category" value = "'+value.product_category+'">'+
//													'<input type="hidden" class = "fk_product_code" value = "<?php echo $id;?>">'+
//													'<span class="menuItem_name product_name">'+value1.product_name+'</span>'+
//													'<div class="menuQty">'+
//														'<select id = "productQty'+value1.fk_product_code+'">';
//														  for(i=1;i<=value1.max_quantity_per_meal;i++){
//															  htmlAppend +='<option value="'+i+'">'+i+'</option>';
//														  }	
//														  /*'<option value="0">0</option>'+
//														  '<option value="1" selected="selected">1</option>'+
//														  '<option value="2">2</option>'+
//														  '<option value="3">3</option>'+
//														  '<option value="4">4</option>'+
//														  '<option value="5">5</option>'+*/
//														  htmlAppend += '</select>'+
//													'</div>'+
//												'</div>'+
//											'</div>';
                                                                                
                                                                                htmlAppend +=   '<div class="menu_Container popup_menu">'+
                                                                                                    '<div class="alert-box menuItem radius" data-alert="">'+
                                                                                                        '<div class="" style="float: left;" >' +
                                                                                                            '<span>' +
                                                                                                                '<input style="width: 15px;height: 15px;margin-top:5px; margin-bottom:0px" type="checkbox" id = "product'+value1.fk_product_code+'" class = "product_code  selectAll" '+in_db_present+'>'+
                                                                                                                '<input type="hidden" class = "product_category" value = "'+value.product_category+'">'+
                                                                                                                '<input type="hidden" class = "fk_product_code" value = "<?php echo $id;?>">'+
                                                                                                            '</span>' +
                                                                                                        '</div>' +
                                                                                                        '<span style="margin-top:5px;" class="menuItem_name product_name">'+value1.product_name+'</span>'+
                                                                                                        '<div class="menuQty right">'+
                                                                                                                '<select class="mb0" id = "productQty'+value1.fk_product_code+'">';
                                                                                                                  for(i=1;i<=value1.max_quantity_per_meal;i++){
                                                                                                                          htmlAppend +='<option value="'+i+'">'+i+'</option>';
                                                                                                                  }	
                                                                                                                  /*'<option value="0">0</option>'+
                                                                                                                  '<option value="1" selected="selected">1</option>'+
                                                                                                                  '<option value="2">2</option>'+
                                                                                                                  '<option value="3">3</option>'+
                                                                                                                  '<option value="4">4</option>'+
                                                                                                                  '<option value="5">5</option>'+*/
                                                                                                                  htmlAppend += '</select>'+
                                                                                                        '</div>'+
                                                                                                    '</div>'+
                                                                                                '</div>';
										}
									});
									htmlAppend +='</div></div>';
									/* if(count_category % 2 == 0){
										
									} */
							count_category++;
						});
						htmlAppend +='</div>';
					}else{
						htmlAppend +='<h4>There are no products added to this meal.</h4>';
					}

					$(".selectedDate").val(selectedDate);
					$(".addmenu").html("Prepare menu for ("+meal_name+') date -'+selectedDate);
					$(".listOfmenu").html(htmlAppend);
					$.each(html.data, function(key1, value1){
						$.each(db_product_qty, function (index, value) {
							if(index == value1.fk_product_code){
								$("#productQty"+value1.fk_product_code).val(value);
							}
						});
					});   
				 	$('#menuList').foundation('reveal','open');
				},
			});
		});

		$(document).on('click','#addProductToMeal',function(){
			//alert(123);
			
			var product_code = [];
			var product_name = [];
			var product_qty = [];
			var product_category = [];
			var fk_product_code = '';
			var validate_flag = false;
			var chk = false;
			$( ".product_code" ).each(function( key,value ) {
				if($(this).prop("checked") == true){
					if($('#productQty'+$(this).attr('id').split('product')[1]).val()==0){
//						alert("Please select quantity for "+$(this).parent().find('.product_name').html()+".");
						alert("Please select quantity for "+$(this).parent().parent().parent().find('.product_name').html()+".");
						validate_flag = false;
						chk = true;
						return false;
					}else{
						validate_flag = true;
						chk = false;
						product_code.push($(this).attr('id').split('product')[1]);
						product_qty.push($('#productQty'+$(this).attr('id').split('product')[1]).val());
						
                                                // parent => parent => parent added because of new css fixes
                                                product_name.push($(this).parent().parent().parent().find('.product_name').html());
						product_category.push($(this).parent().parent().parent().find('.product_category').val());
                                                fk_product_code = $(this).parent().parent().parent().find('.fk_product_code').val();

					}
                                }
			});
                        
			if(validate_flag == true){
				var selected_menu = "<?php echo $selected_menu;?>";
				$.ajax({
					url : "<?php echo $this->url('product',array('action' => 'add-meal-on-date')); ?>",
					type: "POST",
					data: {product_code:product_code,product_qty:product_qty,product_name:product_name,product_category:product_category,fk_product_code:fk_product_code,date:$(".selectedDate").val(),selected_menu:selected_menu,kitchen:<?php echo $kitchen;?>},
					success : function(data){
						if(data){
//                                                    console.log(data);debugger;
							alert("Meal has been added succesfully for "+$(".selectedDate").val());
                                                        
							$("#"+data.newly_added_date).html('');
							$.each(data.newly_added_rows, function(key, value){
								$("#"+data.newly_added_date).append('<div class="alert-box menuItem radius" data-alert=""><span class="menuItem_name">'+value.product_name+'</span><label>'+value.product_qty+'</label><a class="close removeProductFrmDate" id = "'+value.meal_calendar_id+'">×</a></div>');
							});
//							console.log(data);debugger;
							$('#menuList').foundation('reveal','close');
							/* setTimeout(function(){
								alert(1)
								shuffle_product();
							},3000); */
							//window.location=window.location;
							$("#reload").submit();
						}
					},
					error : function(e){
						console.log("Error in ajax of addMealOnDateAction");
					},
			    });
			}else if(validate_flag == false && chk == false){
				alert("Please select product.");
			}
		});
		$(document).on('click','.removeProductFrmDate',function(){
			var th = $(this);
			$.ajax({
				url : "<?php echo $this->url('product',array('action' => 'remove-product-from-meal')); ?>",
				type: "POST",
				data: {idtodelete:$(this).attr('id')},
				async:false,
				success: function(res){
					console.log("Id deleted"+res);
					console.log($(th).parents('.menu_Container').find('.alert-box').length);
					if($(th).parents('.menu_Container').find('.alert-box').length<=1){
						$(th).parents('.menu_Container').html('<h5>Click (+) to add product.</h5>');
					}
				},
			});
			$(this).parent('div').remove();
		});
		$(document).on('click','.close-reveal-modal',function(){
			$('.reveal-modal-bg').css('display','none');
		});
	});

// 	
	
/* 	$(window).resize(function() {
		//cal_height();
		open_scroll();
		close_scroll();
		shuffle_product();
	}); */
</script>

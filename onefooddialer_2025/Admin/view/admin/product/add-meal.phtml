
<style>
.statuscls label{float:left; width:50%}
</style>

<?php

$form = $this->form;

if($this->mealRow != null){
	$form->setAttribute('action', $this->url('meal', array('action' => 'add-meal','id' => $this->mealRow->pk_product_code)));
}else{
	$form->setAttribute('action', $this->url('meal', array('action' => 'add-meal')));
}


$form->setAttribute('class','stdform');
$form->prepare();

?>

       
      <!-- END PAGE HEADER-->
      
      <div id="content">
          <?php echo $this->form()->openTag($form);?>
        <div class="large-7 columns">
        <fieldset>
				<legend>
					MEAL INFO
				</legend>
          <?php 
        	if(isset($this->messages['error']) && $this->messages['error'] !=""){
           ?>
          	<div class="alert-box alert radius">
          		<?php 
          			echo $this->messages['error'];
          		?>
          	</div>
          	
          <?php 		
        	}
        	
          ?>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right">Name<span	class="red">*</span></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php 
               		echo $this->formHidden($form->get('pk_product_code'));
					echo $this->formElement($form->get('name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						->setMessageCloseString('</small>')
						->render($form->get('name'));
				?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('description')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                	echo $this->formElement($form->get('description'));
					echo $this->formElementErrors($form->get('description'));
				 ?>
              </span>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('unit_price')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                	<?php
                	 echo $this->formElement($form->get('unit_price'));
					 echo $this->formElementErrors($form->get('unit_price')); 
					?>
            </div>
            </div>
			
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('category')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
               		echo $this->formElement($form->get('category'));
					echo $this->formElementErrors($form->get('category')); 
				?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('screen')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
               		echo $this->formElement($form->get('screen'));
					echo $this->formElementErrors($form->get('screen')); 
				?>
              </div>
            </div>            
            <input type="hidden" name="hdnaction" id="hdnaction" value="<?php echo (!empty($this->mealRow))?"edit":"add"?>" />
            <?php 
            
            if( $calendar_setting == 0 ) {
            ?>
            <div class="multi-field-wrapper" id="divmealsetting" name="divmealsetting">
	            <div class="multi-fields">
	            
	            <?php 
	            
	            if(!empty($this->mealRow)){
	            	
	            	$items = json_decode($this->mealRow->items);
	            	if($items==null){
	            		$items = array("0"=>""); // for rendering at least one element
	            	}
	            }else{
	            	$items = array("0"=>""); // for rendering at least one element
	            }
	            
	            $i = 1;
	            $cnt = count((array)$items);
	            
	          

	            foreach($items as $productId=>$quantity){
	            	$form->get('product[]')->setValue($productId);
	            	$form->get('product_quantity[]')->setValue($quantity);
	            ?>
	              <div class="multi-field">
	              	 <div class="row">
			              <div class="large-4 small-4 medium-4 columns">
			                <label class="inline right">
								Product
								<span class="red">*</span>
							</label>
			              </div>
			              <div class="productadd">
				              <div class="large-3 columns">
				               <?php 
				               		echo $this->formElement($form->get('product[]'));
									echo $this->formElementErrors($form->get('product[]')); 
								?>
				              </div>
				              <div class="large-4 small-4 medium-4 columns">
				              	 	<div class="large-4 small-4 medium-4 columns">
				               			<label class="inline right">Quantity<span class="red">*</span>
				               	   </div>
					               <div class="large-8 small-8 medium-8 columns">
					               		<?php 
					               			echo $this->formElement($form->get('product_quantity[]')); 
					               			echo $this->formElementErrors($form->get('product_quantity[]'));
					               		?>
					               </div>
				              </div>
				              <?php 
				              if($i==$cnt){
				              ?>
				              <?php 
				              if($cnt > 1){
				              ?>
				              <button title="remove" type="button" class="smBtn has-tip tip-top remove-field" data-tooltip="" data-selector="tooltip-i6htcnc50" aria-describedby="tooltip-i6htcnc50" title="">
							  	<i class="fa fa-trash-o"></i>
							  </button>
							  <?php } ?>
				              <button class="add-field smBtn has-tip tip-top" data-tooltip="" title="add more" type="button" data-selector="tooltip-i6hptux91" aria-describedby="tooltip-i6hptux91" title="">
								<i class="fa fa-plus"></i>
							  </button>
							  
							  <?php 
				              }else{
							  ?>
							  <button title="remove" type="button" class="smBtn has-tip tip-top remove-field" data-tooltip="" data-selector="tooltip-i6htcnc50" aria-describedby="tooltip-i6htcnc50" title="">
							  	<i class="fa fa-trash-o"></i>
							  </button>
							  <?php } ?>
							  
			              </div>
		              </div>
	              </div>	
	              
	              <?php 
	              	$i++;
	            } 
	            
				
	            ?>
	              
	           </div> 
            </div>
            <?php 
			}
            ?>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('food_type')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                	echo $this->formElement($form->get('food_type'));
					echo $this->formElementErrors($form->get('food_type'));
				?>
              </div>
            </div>
            
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('product_category')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php
                	echo $this->formElement($form->get('product_category'));
					echo $this->formElementErrors($form->get('product_category'));
				 ?>
              </div>
            </div>
            
           
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('meal_plans')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                  <?php
                    echo $this->formElement($form->get('pk_meal_code'));
                    echo $this->formElement($form->get('meal_plans'));
					//echo $this->formElementErrors($form->get('meal_plans'));

				?>
              </div>
            </div>            
            
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('threshold')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php 
               		echo $this->formElement($form->get('threshold'));
					echo $this->formElementErrors($form->get('threshold')); ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('image_path')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <div class="mb5">
                 <?php
          			echo $this->formElement($form->get('image_path'));
          			echo $this->formElementErrors($form->get('image_path'));
          		 ?>
				</div>   
                <div class="disable mb20">
                    <label>Image size should not be more than 400kb and dimension should be 253pxX155px</label> 
                </div>
              </div>
            </div>
                        
            <div class="row">
              <div class="large-4 small-4 medium-4 columns ">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns statuscls">
                <?php 
                	echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'));
					echo $this->formElement($form->get('csrf')); 
				?>
              </div>
               <?php echo $this->formElement($form->get('backurl'));
				 ?>
            </div>
            
           </fieldset>
          <?php 
          if(isset($this->settings['GLOBAL_ALLOW_MEAL_SWAP']) && $this->settings['GLOBAL_ALLOW_MEAL_SWAP']=='yes'){
          ?>
          <fieldset>
        <legend> Swapping Options</legend>
        <div class="row">
          <div class="large-4 small-4 medium-4 columns">
            <?php echo $this->formLabel($form->get('is_swappable')); ?> 
          </div>
          <div class="large-8 smal-8 medium-8 columns radiocls">
            <?php echo $this->formelement($form->get('is_swappable'));?>
            <?php echo $this->formElementErrors($form->get('is_swappable'));?>
          </div>
        </div>
        
        <div class="row  swaprow dn">
          <div class="large-4 small-4 medium-4 columns">
            <?php echo $this->formLabel($form->get('swap_with')); ?>
          </div>
          <div class="large-8 smal-8 medium-8 columns radiocls">
            <?php echo $this->formelement($form->get('swap_with'));?>
            <?php echo $this->formElementErrors($form->get('swap_with'));?>
            <?php echo $this->formelement($form->get('swap_charges'));?>
            <?php echo $this->formElementErrors($form->get('swap_charges'));?>
            
          </div>
        </div>
        
      </fieldset>
      <?php 
          }else{
      ?>
      <input type="hidden" name="is_swappable" id="is_swappable" value="0">
      <?php 
          }
      ?>           
             <div class="large-12 columns pl0 pr0">
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8 small-8 medium-8 columns"> 
               <button	type="submit" id ='submitbutton' class="button	left tiny left5	dark-greenBg" onclick="javascript:validate_multilingual_code(); return submit_status;">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="button" id ='cancelbutton' class="button	left tiny left5	redBg" >Cancel &nbsp;<i class="fa	fa-ban"></i></button>
              </div>
            </div>
            </div>
        </div>
        
        <div class="large-5 columns">
        <?php if($this->mealRow != null){?>
    
        	<fieldset>
        		<legend>Image</legend>
	        	<div class="row">
		        	<div class="large-12 small-12 medium-12 columns center">
		        	<?php 
		        		$imagepath = $GLOBALS['http_request_scheme'].$this->aws_bucket_url."/".$this->aws_folder."/product/".$this->mealRow->image_path;
		        	
		        		if(!empty($this->mealRow->image_path)) {
		        			if($this->image_on_aws != false ) {
		    		?>
		    				<img src="<?php echo $imagepath; ?>" />
		    		<?php     			
		        			}
		        			else {
		        				echo '<div class="large-12 small-12 medium-12 columns"><label>The image file '.($this->image_path).'is not found.<br/>You may upload a new image for the meal.</label></div>';
		        			}
		        		}
		        		else{
		        			echo '<div class="large-12 small-12 medium-12 columns"><label>The image file '.($this->image_path).'is not found.<br/>You may upload a new image for the meal.</label></div>';
		        		}?>
		        		
		        	</div>
	        	</div>
        	</fieldset> 
        <?php 		
        	}
        	if( isset($language_array) && is_array($language_array) && !empty($language_array) ) {
        	?>
        	<div class="clearBoth10"></div>
        	<fieldset>
        		<legend>Product Name in other language
        		<br>
        			<span class="" style=" text-transform: none" >To type in other language please</span><!-- style="color: #fc6e51" -->
					<a href="http://www.quillpad.in/index.html" target="_blank" class=""><!-- To type in other language use QuillPad -->Click here</a>
        		
        		</legend>
        	<div class="multi-field-wrapper">
	            <div class="multiple-fields"><?php
        			foreach($language_array as $code => $name) {
        		?>
        	<div class="multiple-field">
        	<div class="row">
        		<div class="large-2 small-2 medium-2 columns">
					<label class="inline left"><?php echo $name; ?></span></label>
			    </div>
        		<div class="large-10 small-10 medium-10 columns">
        			<div class="large-2 small-2 medium-2 columns">
        			<label class="inline right">Name</span></label>
        			<input type="hidden" class="smallinput" id="supported_for_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][context]" value="meal_language" />
        			<?php
        			if( isset($local_language_array[$code]['id']) && !empty($local_language_array[$code]['id']) ) {
        			?>
        			<input type="hidden" class="smallinput" id="supported_id_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][id]" value="<?php echo $local_language_array[$code]['id']; ?>" />
        			<?php
        			}
        			?>
        			</div>
			    	<div class="large-4 small-4 medium-4 columns">
					<input type="text" class="smallinput" id="supported_product_name_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][context_name]" 
					value="<?php echo ( isset($_POST['other_language'][$code]['context_name']) && !empty($_POST['other_language'][$code]['context_name']) ) ? $_POST['other_language'][$code]['context_name'] : ( ( isset($local_language_array[$code]['context_name']) && !empty($local_language_array[$code]['context_name']) ) ? $local_language_array[$code]['context_name'] : ''); ?>" 
					language-name="<?php echo $name; ?>" />
				    </div>
					<div class="large-2 small-2 medium-2 columns">
					<label class="inline right">Code</label>
					</div>
					<div class="large-4 small-4 medium-4 columns">
					<input type="text" class="smallinput" maxlength="9" id="supported_product_code_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][context_code]" value="<?php echo ( isset($_POST['other_language'][$code]['context_code']) && !empty($_POST['other_language'][$code]['context_code']) ) ? $_POST['other_language'][$code]['context_code'] : ( ( isset($local_language_array[$code]['context_code']) && !empty($local_language_array[$code]['context_code']) ) ? $local_language_array[$code]['context_code'] : '' ); ?>" />
					</div>
				</div>
        	</div>
        	</div>
        	<?php
        			}#end of foreach
        	?>
        	</div>
        	</div>
        	</fieldset><?php
        		}
        	?>
			
        </div>
         	<?php echo $this->form()->closeTag($form);?>
        
      </div>
    </div>
    
    <!-- END PAGE CONTAINER--> 
<script>

$(document).ready(function(){

    var config = {
            '.chosen-select': {},
            '.chosen-select-deselect': {
                allow_single_deselect: true
            },
            '.chosen-select-no-single': {
                disable_search_threshold: 10
            },
            '.chosen-select-no-results': {
                no_results_text: 'Oops, nothing found!'
            },
            '.chosen-select-width': {
                width: "95%"
            }
        }
        for (var selector in config) {
            $(selector).chosen(config[selector]);
        }

	    var selectedPlans = [];
	    
		$('.chosen-select option:selected').each(function(i) {
	    	//obj.push($(this).index());
		    selectedPlans.push($(this).val());
	    });

		console.log('selected plans');
    	console.log(selectedPlans.length);    
		var planLength = selectedPlans.length;
		var screen = $('#screen option:selected').val();
	
	    $.ajax({
			 url:"<?php echo $this->url('product',array('action' => 'ajax-get-meal-plan')); ?>",
			 type: "POST",
	         dataType: 'json',
			 data: "screen="+screen,
			 success:function(data)
			 {
			    var str_plan='';
/* 				if( selectedPlans.length == 0 ) {
					str_plan+='<option value="" selected="selected">All</option>';
				} 
				else {
				    str_plan+='<option value="">All</option>';
				}
 */	 		 	
				$.each(data, function(k,v){
				    var selectable = '';
				    var plan = v['pk_plan_code']+'@'+v['plan_name'];
				    for( var i = 0; i < planLength; i++ ) {
					    console.log(selectedPlans[i]);
					    if( plan == selectedPlans[i] ) {
					        selectable = 'selected="selected"';
     						break;
			        	}
				    }
				    str_plan+='<option value="'+v['pk_plan_code']+'@'+v['plan_name']+'" '+selectable+'>'+v['plan_name']+'</option>';
		     	});

				$(".chosen-select").html(str_plan);
 			   	$(".chosen-select").trigger("chosen:updated");
			 }
	     });
      
    <?php /*
	var obj = [];
	   
	$('.testSelAll2 option:selected').each(function(i) {
    	//obj.push($(this).index());
	    obj.push($(this).val());
    });

	var screen = $('#screen option:selected').val();

    $.ajax({
		 url:"<?php echo $this->url('product',array('action' => 'ajax-get-meal-plan')); ?>",
		 type: "POST",
         dataType: 'json',
		 data: "screen="+screen,
		 success:function(data)
		 {
			var len = $('.testSelAll2 option').length;
			if( data.length > 0) {
   			
  				for(var i=len; i>=1; i--)
				{
				   $('.testSelAll2')[0].sumo.remove(i-1);
				}

  				$.each(data, function(k,v){
  	  				var plan = v['pk_plan_code']+'@'+v['plan_name'];
				    $('.testSelAll2')[0].sumo.add(v['pk_plan_code']+'@'+v['plan_name'],v['plan_name']);
				    $.each(obj, function(k1,v1) {
					    if( plan == v1 ) {
						    console.log($('.testSelAll2').val(v1).index());
					    }
					});
    			});

 				$.each(obj, function(k,v){
 	 				//console.log(k+' '+v);
				    //$('.testSelAll2')[0].sumo.selectItem(v);
				}); 

 				$('.testSelAll2').SumoSelect({selectAll:true });
			}
			
		 }
     });
	 */?>
    

    var menusetting=<?php echo $calendar_setting;?>;
	if(menusetting=='1')
	{
		$("#divmealsetting").hide();
	}
	else
	{
		$("#divmealsetting").show();
	}
	
	//alert($('.multi-field').length);
	$(".administrations").addClass("active");
	$(".administrations ul li:nth-child(3)").addClass("active");

	window.testSelAll2 = $('.testSelAll2').SumoSelect({selectAll:true });
	
	//$('.multi-field-wrapper').each(function() {
		
	var wrapper = $('.multi-fields');
	
	//var strhtml='<option value="">Select Products</option>';
	//$('#items').html(strhtml);
	
	$(document).on('change','#screen',function(){

		var screen = $(this).val();
		 $.ajax({
			 url:"<?php echo $this->url('product',array('action' => 'ajax-get-products')); ?>",
			 type: "POST",
             dataType: 'json',
			 data: "screen="+screen+"&prod_subtype=generic",//"menu="+menu,
			 success:function(data)
			 {
				 console.log('change event of screen');
				 console.log(data);
                 var arr=[];
                 for(elem in data) 
                 {
                     var temparr=[];
                     temparr.push(data[elem]['pk_product_code']);
                     temparr.push(data[elem]['name']);
                     arr.push(temparr);
                  }
                  var str='<option value="">Select Products</option>';
                  for(var i=0;i<arr.length;i++)
                  {
                  	str+='<option value="'+arr[i][0]+'">'+arr[i][1]+'</option>';
                  }
                  //$('#items').html(str);
                  $(".itemscls").html(str);
              }
           });

         $.ajax({
 			 url:"<?php echo $this->url('product',array('action' => 'ajax-get-meal-plan')); ?>",
 			 type: "POST",
             dataType: 'json',
 			 data: "screen="+screen,
 			 success:function(data)
 			 {	 

 	 		 	var str_plan='';
				$.each(data, function(k,v){
	     			str_plan+='<option value="'+v['pk_plan_code']+'@'+v['plan_name']+'">'+v['plan_name']+'</option>';
		     	});
				$(".chosen-select").html(str_plan);
 			   	$(".chosen-select").trigger("chosen:updated");
 			    /*
 				console.log('plans based on kitchen');
 				console.log(data.length);
 				//$('select.testSelAll2')[0].sumo.reload();
				//$("#meal_plans .testSelAll2").empty();
				//$('select#meal_plans')[0].sumo.unload();
				
				var len = $('.testSelAll2 option').length;

    			if( data.length == 0) {
    			    //$('.CaptionCont').removeClass('kplan');
    			    $('.kplan').remove();
    			    console.log("In if");
    			    console.log(len);
        			//$('.select-all').hide();
        			$('.CaptionCont').after('<span class="kplan" style="margin-top: 0px; color:red" >No Plans</span>');
        			//$('.testSelAll2').SumoSelect({selectAll:false });
        			//$('.testSelAll2')[0].sumo({ placeholder: "No Plans" });
     			}

    			if( data.length > 0) {
        			$('.kplan').hide();
        			
					for(var i=len; i>=1; i--)
					{
					   $('.testSelAll2')[0].sumo.remove(i-1);
					}
	         		//$('select#meal_plans').empty();
	 				//var str_plan = '<option value="">Select Plans</option>';	
	 				 $.each(data, function(k,v){
	 				    $('.testSelAll2')[0].sumo.add(v['pk_plan_code']+'@'+v['plan_name'],v['plan_name']);
	     				//console.log(v);
	     				//str_plan+='<option value="'+v['pk_plan_code']+'@'+v['plan_name']+'">'+v['plan_name']+'</option>';
	     			 });

	      			 //console.log(str_plan);

    	 			 //$(".testSelAll2").html(str_plan).SumoSelect();
 					 //$(".testSelAll2").html(str_plan);
 					 //$('select.testSelAll2')[0].sumo.add(str_plan);
 					 $('.testSelAll2').SumoSelect({selectAll:true });
    			}
    			*/
 			 }
          });

	});

	var actionname = $("#hdnaction").val();

	if(actionname=="add")
	{
		$("#screen").trigger('change');
	}
	
	//$("#screen").trigger('change');
	
    $(document).on('click',".add-field",function(e) {
		
    	$(this).removeClass("add-field").html('<i class="fa fa-trash-o"></i>');
    	$(this).addClass("remove-field");
    	$(this).attr("title","");

    	var thsele = this.outerHTML;
    	
    	var btns = $(this).parent().find("button");

    	$(this).parent().append(thsele);

    	btns.each(function(){
			$(this).remove();
	    });
    	
	    var ele = $('.multi-field:first-child').clone(true);

	    ele.find("button").remove();
	    //ele.prependTo($wrapper).find('input').val('').focus();
	    ele.appendTo(wrapper).find('input').val('').focus();
	    //ele.find(".productadd").append('<button title="add more" type="button" class="smBtn has-tip tip-top add-field" data-tooltip="" data-selector="tooltip-i6htcnc50" aria-describedby="tooltip-i6htcnc50" title=""><i class="fa fa-plus"></i></button><div class="clearfix"></div>');
	    //ele.find(".productadd").append('<button class="smBtn has-tip tip-top remove-field" aria-describedby="tooltip-i6hptux91" data-selector="tooltip-i6hptux91" type="button" data-tooltip="" title=""><i class="fa fa-trash-o"></i></button>');
	    ele.find(".productadd").append('<button class="smBtn has-tip tip-top remove-field" aria-describedby="tooltip-i6hptux91" data-selector="tooltip-i6hptux91" type="button" data-tooltip="" title=""><i class="fa fa-trash-o"></i></button><button title="add more" type="button" class="smBtn has-tip tip-top add-field" data-tooltip="" data-selector="tooltip-i6htcnc50" aria-describedby="tooltip-i6htcnc50" title=""><i class="fa fa-plus"></i></button>');
	    //ele.appendTo($wrapper).find('input').val('').focus();
	    
	    
    });
    
    $(document).on('click','.multi-field .remove-field',function() {
	    
        if ($('.multi-field').length > 1)
        {
        	
        	var btns = $(this).parent().find("button");

        	//return false;
        	
        	if(btns.length=='2'){

        		var addBtn = btns[1].outerHTML;

        		$(this).parent().parent().parent().remove();
        		
        		var ele = $('.multi-field:last-child');

        		ele.find(".productadd").append(addBtn);
							
        		 // Check again if only one row is remaining the allow only plus button. and delete remove button.
        		 if ($('.multi-field').length == 1){
	        		 
        			 var btns = $('.multi-field').find(".productadd").find("button");

        			 if(btns.length=='2'){
        				 $(btns[0]).remove();
        			 }
        		 }

	        	
        	}else{
        		$(this).parent().parent().parent().remove();

        		// Check again if only one row is remaining the allow only plus button. and delete remove button.
        		 if ($('.multi-field').length == 1){
	        		 
        			 var btns = $('.multi-field').find(".productadd").find("button");

        			 if(btns.length=='2'){
        				 $(btns[0]).remove();
        			 }
        		 }
        	}
            
        }
    });

    // Swap radio button click handler

    $(document).on("click",".swappable",function() {

		if($("input[name='is_swappable']:checked").val() == '1'){
			$(".swaprow").show();	
		}else{
			$(".swaprow").hide();
		}
	});

    if($("input[name='is_swappable']:checked").val() == '1'){
		$(".swaprow").show();	
	}else{
		$(".swaprow").hide();
	}

});

var submit_status = false;
function validate_multilingual_code() {
	var error_message = '';
	$('input[id^="supported_product_name_"]').each(function() {
		var product_name = $.trim($(this).val());
		var product_name_array = $(this).attr('id').split('_');
		var product_code = $.trim($('#supported_product_code_'+product_name_array[product_name_array.length - 1]).val());
		
		if( product_name.length > 0 && product_code.length == 0 ) {
			error_message = 'Meal code in '+( $(this).attr('language-name') )+' is not specified.';
			$('#supported_product_code_'+product_name_array[product_name_array.length - 1]).focus();
			return false;
		}
		else if( product_name.length == 0 && product_code.length > 0 ) {
			error_message = 'Meal name in '+( $(this).attr('language-name') )+' is not specified.';
			$('#supported_product_name_'+product_name_array[product_name_array.length - 1]).focus();
			return false;
		}
	});
	$('input[id^="supported_product_name_"]').promise().done(function() {
		if(error_message.length > 0) { alert(error_message);submit_status = false; }
		else { submit_status = true; }
	});
}
</script>
<?php
$form = $this->form;
$form->setAttribute('action', $this->url('role', array('action' => 'add')));
//$form->setAttribute('class','stdform');
$form->prepare();
$actionarr = array("read","write","delete","export","import","print");
?>
      <!-- END PAGE HEADER-->
      
      <div id="content">
       <?php echo $this->form()->openTag($form);?>
              <div class="large-6 columns">
		          <fieldset>
					<legend>
					ROLE INFO
				</legend>
          	<div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('role_name')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php  
                 	echo $this->formHidden($form->get('pk_role_id'));
					echo $this->formElement($form->get('role_name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						
						->setMessageCloseString('</small>')
						->render($form->get('role_name'));
				?>
				
              </div>
            </div>
            <div class="row">
            	<table id="" class="display displayTable addRole">
					<thead>
						<tr>
							<th>Modules</th>
							<th>Read</th>
							<th>Write</th>
							<th>Delete</th>
							<th>Export</th>
							<th>Import</th>
							<th>Print</th>
						</tr>
					</thead>
					<tbody>
						<?php 
							$arr=array();
							$aclmodule_name = '';
							foreach($acltpl as $aclmodule){ 
								if($aclmodule_name != $aclmodule->module){
									$aclmodule_name = $aclmodule->module;
									$arr[$aclmodule_name]=array();
								}
								$arr[$aclmodule_name][]=$aclmodule->type;
						?>
						<?php }
							foreach($arr as $key=>$val){ ?>
								<tr>
									<td><?php echo $key;?></td>
									<?php 
										foreach($actionarr as $key1=>$val1){ 
											
											//echo "<pre>";print_r($val1);
											//print_r($val);print_r(in_array($val1, $val));exit;
											if (in_array($val1, $val)){
												$enable = "";
											}else{
												$enable = "disabled";
											}?>
											<td><input type="checkbox"  name="role[]" value="<?php echo $key.'_'.$val1;?>"  <?php echo $enable;?> class="inline inline_check_box"></td>
									<?php }
									?>
								</tr>
						<?php }
						?>
					</tbody>
				</table>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
	                echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'));
					echo $this->formElement($form->get('csrf')); 
				?>
              </div>
            </div>
			<?php echo $this->formElement($form->get('backurl'));?>
			
			<div class="large-12 columns pl0 pr0">
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8  small-8 medium-8 columns">
              	<button	type="submit"  id="submitbutton" class="button left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id="cancelbutton" class="button left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
              </div>
            </div>
            </div>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?> 
        </div>
      </div>
    </div>

    <!-- END PAGE CONTAINER--> 

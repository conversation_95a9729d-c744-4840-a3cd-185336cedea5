<meta http-equiv="Content-Language" content="hi">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<!DOCTYPE html>
		<html moznomarginboxes mozdisallowselectionprint>
            <head>
            <link rel="shortcut icon" href="/admin/images/favicon.png" />
            <script src="/admin/js/jquery-1.10.2.min.js" type="text/javascript"></script>
<!--             <link rel="stylesheet" type="text/css" href="/admin/css/foundation.css"> -->
            <style type="text/css">
            html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p,
            	blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn,
            	em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup,
            	tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label,
            	legend, table, caption, tbody, tfoot, thead, tr, th, td {
            	color: #666;
            	font-family: Arial, Helvetica, sans-serif;
            	font-size: 16px;
            	margin: 0;
            	padding: 0;
            }
            
            .bold {
            	font-weight: 600;
            	margin-right: 5px;
            	text-align: left;
            	width: 114px;
            }
            
            .labi {
            	width: 200px;
            	word-break: break-all;
            }
            
            .out {
            	border: 1px solid;
            	margin: 2px;
            	padding: 0 5px;
            }
            
            .left {
            	float: left;
            	width: 280px;
            }
            
            table { /*max-height:220px; height:220px;*/
            	min-height: 220px;
            }
            
            .outertable {
            	float: none;
            	font-size: 11px;
            	margin: 2px;
            }
            
            td span {
            	Float: right;
            	font-size: 9px;
            	margin-top: 2px
            }
            /*
            .div_border1 {
                border: 4px solid #3bafda;
            }
            .dispatch_div {
                box-shadow: 0 0 2px #d1d1d1;
                color: #656d78;
                padding-bottom: 10px;
            }
            
            
            .order_details {
                color: #fff;
                padding-bottom: 1px;
                padding-left: 5px;
            }
            .blueBg {
                background: #3bafda none repeat scroll 0 0;
            }
            .dispatch_div p {
                margin-bottom: 5px;
            }
            .dispatch_tbl.tbl1, .dispatch_tbl.tbl1 tr th, .dispatch_tbl.tbl1 tr td, .ttl1 p {
                color: #3bafda;
            }*/
            .dispatch_tbl {
                -moz-border-bottom-colors: none;
                -moz-border-left-colors: none;
                -moz-border-right-colors: none;
                -moz-border-top-colors: none;
                background: transparent none repeat scroll 0 0;
                border-color: -moz-use-text-color -moz-use-text-color #f1f1f1;
                border-image: none;
                border-style: none none solid;
                border-width: 0 0 1px;
                margin-bottom: 0.25rem;
                width: 100%;
            }
            .dispatch_tbl.tbl1, .dispatch_tbl.tbl1 tr th, .dispatch_tbl.tbl1 tr td, .ttl1 p {
                color: #3bafda;
            }
            .dispatch_tbl tr th, .dispatch_tbl tr td {
                border: 0 none;
                font-size: 16px;
                padding: 10px;
                text-align: center;
            }
            </style>
            </head>
            <body>
            		<?php echo $this->content; ?>	
            <script type="text/javascript">
            
            	$(document).ready(function(){
            
            		//window.print();
            			
            	});
            	
            </script>
            </body>
	</html>
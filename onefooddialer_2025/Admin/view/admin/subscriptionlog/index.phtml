					<div class="large-12 columns">
						<h3 class="page-title"><?php if(!empty($date)){ echo date('F Y', strtotime($date)); } else { echo Date('F Y'); };?></h3>
					</div>
						<!-- END PAGE HEADER-->

						<div id="dashboard">
							<div class="row">
								<div class="large-14 columns ">
									<div class="dashboard-div subscribe_log bg-white" style="border: 1px solid #a0d468" >
										<div class="visual1 greenBg">
											<i class="fa fa-cutlery"></i>
										</div>
										<div class="details" >
											<div class="num">
												<?php echo $data['ordercount'];?>
											</div>
											<div class="desc">
												Total Orders
											</div>
										</div>
										<!-- <a href="todaysOrders.php" class="more">View more<i class="fa fa-hand-o-up"></i></a> -->
									</div>
								</div>
								
								<div class="large-14 columns">
									<div class="dashboard-div subscribe_log bg-white" style="border: 1px solid #3bafda">
										<div class="visual1 blueBg">
											<i class="fa fa-file-text"></i>
										</div>
										<div class="details">
											<div class="num">
												<?php echo $data['smscount'];?>	
											</div>
											<div class="desc">SMS Sent</div>
										</div>
										<!-- <a href="#" class="more">View more<i class="fa fa-hand-o-up"></i></a> -->
									</div>
								</div>
								<div class="large-14 columns">
									<div class="dashboard-div subscribe_log bg-white " style="border: 1px solid #ED5565">
										<div class="visual1 redBg">
											<i class="fa fa-envelope-o"></i>
										</div>
										<div class="details">
											<div class="num">
												<?php echo $data['emailcount'];?>
											</div>
											<div class="desc">
												Email Sent
											</div>
										</div>
										<!-- <a href="#" class="more">View more <i class="fa fa-hand-o-up"></i></a> -->
									</div>
								</div>
								<div class="large-14 columns">
									<div class="dashboard-div subscribe_log bg-white" style="border: 1px solid #AC92EC">
										<div class="visual1 vavendarRBg">
											<i class="fa fa-user-plus"></i>
										</div>
										<div class="details">
											<div class="num">
												<?php echo $data['customercount'];?>
											</div>
											<div class="desc">
												Active Customers
											</div>
										</div>
										<!-- <a href="#" class="more">View more <i class="fa fa-hand-o-up"></i></a> -->
									</div>
								</div>
								<div class="large-14 columns ">
									<div class="dashboard-div subscribe_log bg-white" style="border: 1px solid #D770AD">
										<div class="visual1 pinkBg">
											<i class="fa fa-user-secret"></i>
										</div>
										<div class="details">
											<div class="num">
												<?php echo $data['admincount'];?>
											</div>
											<div class="desc">
												Admin Accounts
											</div>
										</div>
										<!-- <a href="todaysOrders.php" class="more">View more<i class="fa fa-hand-o-up"></i></a> -->
									</div>
								</div>
								
								<div class="large-14 columns">
									<div class="dashboard-div subscribe_log bg-white" style="border: 1px solid #16A085">
										<div class="visual1 greenBg1">
											<i class="fa fa-users"></i>
										</div>
										<div class="details">
											<div class="num">
												<?php echo $data['usercount'];?>
											</div>
											<div class="desc">
												User Accounts
											</div>
										</div>
										<!-- <a href="#" class="more">View more<i class="fa fa-hand-o-up"></i></a> -->
									</div>
								</div>
								<div class="large-14 columns">
									<div class="dashboard-div subscribe_log bg-white" style="border: 1px solid #FFC40D">
										<div class="visual1 yellowBg">
											<i class="fa fa-cutlery"></i>
										</div>
										<div class="details">
											<div class="num">
												<?php echo $data['kitchencount'];?>
											</div>
											<div class="desc">No. of Kitchens</div>
										</div>
										<!-- <a href="#" class="more">View more <i class="fa fa-hand-o-up"></i></a> -->
									</div>
								</div>

							</div>
				</div>
					
				
						
				<div class="container-fluid">
						<div class="clearfix" id="content">
							<div class="large-12 columns">
								<div class="portlet box yellow">
									<div class="portlet-body sales_data_table">
											<table style="margin: 0 auto; width: 100%" class="display displayTable dataTable no-footer" id="customer" role="grid" aria-describedby="customer_info">
												<thead>
													<tr role="row">
														<th>Months</th>
														<th>Total Orders</th>
														<th>SMS Sent</th>
														<th>Email Sent</th>
														<th>Active Customer</th>
														<th>Admin Accounts </th>
														<th>User Accounts</th>
														<th>No. of Kitchen</th>
														<th>Action</th>
													</tr>
												</thead>
												
										</table>
									</div>
								 </div>

								<div class="clearBoth20"></div>

							</div>
						</div>
						<!-- END PAGE CONTAINER-->
					</div>
					
		  <script type="text/javascript">

		  var customerTable = $('#customer').dataTable( {
	            "processing": true,
	            "serverSide": true,
	            "ajax": {
	            	"url":"/subscriptionlog/ajx-customer",
	            	"data": function ( d ) {
	                    d.menu = $("#menu option:selected").val();
	                    d.location = $("#location option:selected").val();
						d.deliverypers = $("#deliverypers option:selected").val();
						d.order_date =  $("#order_date").val();

	             	}	
				},
	            "aoColumnDefs": [
	            	                {
	            	                   bSortable: false,
	            	                   aTargets: [ -1 ]
	            	                }
	            	              ],
	        });


	    	customerTable.fnSort( [ [0,'desc']] );
	    	
		  </script>
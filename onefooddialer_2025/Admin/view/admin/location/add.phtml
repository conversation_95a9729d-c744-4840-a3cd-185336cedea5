<?php
$form = $this->form;
$form->setAttribute('action', $this->url('location', array('action' => 'add')));
//$form->setAttribute('class','stdform');
$form->prepare();
?>
      
      <!-- END PAGE HEADER-->
      
      <div id="content">
      	<?php echo $this->form()->openTag($form);?>
        <div class="large-6 columns">
			 <fieldset>
					<legend>
					LOCATION INFO
				</legend>
          	<div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('location')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php  
                 	echo $this->formHidden($form->get('pk_location_code'));
					echo $this->formElement($form->get('location'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						
						->setMessageCloseString('</small>')
						->render($form->get('location'));
				?>
				
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('city')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                  <?php 
                  	echo $this->formElement($form->get('city'));
					echo $this->formElementErrors($form->get('city')); 
				 ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('pin')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
               <?php 
               		echo $this->formElement($form->get('pin'));
					echo $this->formElementErrors($form->get('pin')); 
				?>
            </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('sub_city_area')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                 	echo $this->formElement($form->get('sub_city_area'));
					echo $this->formElementErrors($form->get('sub_city_area')); 
				?>
              </div>
            </div>
             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('delivery_charges')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                 	echo $this->formElement($form->get('delivery_charges'));
					echo $this->formElementErrors($form->get('delivery_charges')); 
				?>
        </div>
            </div>
             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('delivery_time')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                  echo $this->formElement($form->get('delivery_time'));
          echo $this->formElementErrors($form->get('delivery_time')); 
        ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('fk_kitchen_code')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
               		echo $this->formElement($form->get('fk_kitchen_code'));
					echo $this->formElementErrors($form->get('fk_kitchen_code')); 
				?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
               <?php 
               		echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'));
					echo $this->formElement($form->get('csrf')); 
				?>
              </div>
              
               <?php echo $this->formElement($form->get('backurl'));
				 ?>
            </div>
            </fieldset>
             <div class="large-12 columns pl0 pr0">
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8  small-8 medium-8 columns">
              	<button	type="submit"  id="submitbutton" class="button	left tiny left5	dark-greenBg" onclick="javascript:validate_multilingual_code(); return submit_status;">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="button" id="cancelbutton" class="button	left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
              </div>
            </div>
			</div>
        </div>
        <div class="large-6 columns">
        	<?php
        	if( isset($language_array) && is_array($language_array) && !empty($language_array) ) {
        	/*<div class="clearBoth10"></div>*/
        	?>
        	<fieldset>
        		<legend>Multi-lingual labels</legend>
        	<div class="multi-field-wrapper">
	            <div class="multiple-fields"><?php
        			foreach($language_array as $code => $name) {
        		?>
        	<div class="multiple-field">
        	<div class="row">
        		<div class="large-2 small-2 medium-2 columns">
					<label class="inline left"><?php echo $name; ?></span></label>
			    </div>
        		<div class="large-10 small-10 medium-10 columns">
        			<div class="large-2 small-2 medium-2 columns">
        			<label class="inline right">Name</span></label>
        			<input type="hidden" class="smallinput" id="supported_for_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][context]" value="location_language" />
        			<?php
        			if( isset($local_language_array[$code]['id']) && !empty($local_language_array[$code]['id']) ) {
        			?>
        			<input type="hidden" class="smallinput" id="supported_id_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][id]" value="<?php echo $local_language_array[$code]['id']; ?>" />
        			<?php
        			}
        			?>
        			</div>
			    	<div class="large-4 small-4 medium-4 columns">
					<input type="text" class="smallinput" id="supported_product_name_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][context_name]" 
					value="<?php echo ( isset($_POST['other_language'][$code]['context_name']) && !empty($_POST['other_language'][$code]['context_name']) ) ? $_POST['other_language'][$code]['context_name'] : ( ( isset($local_language_array[$code]['context_name']) && !empty($local_language_array[$code]['context_name']) ) ? $local_language_array[$code]['context_name'] : ''); ?>" 
					language-name="<?php echo $name; ?>" />
				    </div>
					<div class="large-2 small-2 medium-2 columns">
					<label class="inline right">Code</label>
					</div>
					<div class="large-4 small-4 medium-4 columns">
					<input type="text" class="smallinput" maxlength="9" id="supported_product_code_<?php echo $code; ?>" name="other_language[<?php echo $code; ?>][context_code]" value="<?php echo ( isset($_POST['other_language'][$code]['context_code']) && !empty($_POST['other_language'][$code]['context_code']) ) ? $_POST['other_language'][$code]['context_code'] : ( ( isset($local_language_array[$code]['context_code']) && !empty($local_language_array[$code]['context_code']) ) ? $local_language_array[$code]['context_code'] : '' ); ?>" />
					</div>
				</div>
        	</div>
        	</div>
        	<?php
        			}#end of foreach
        	?>
        	</div>
        	</div>
        	</fieldset><?php
        		}
        	?>
        </div>
        <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
      </div>
    </div>
    
    <!-- END PAGE CONTAINER--> 
<script type="text/javascript">
var submit_status = false;
function validate_multilingual_code() {
	var error_message = '';
	$('input[id^="supported_product_name_"]').each(function() {
		var product_name = $.trim($(this).val());
		var product_name_array = $(this).attr('id').split('_');
		var product_code = $.trim($('#supported_product_code_'+product_name_array[product_name_array.length - 1]).val());

		if( product_name.length > 0 && product_code.length == 0 ) {
			error_message = 'Location code in '+( $(this).attr('language-name') )+' is not specified.';
			$('#supported_product_code_'+product_name_array[product_name_array.length - 1]).focus();
			return false;
		}
		else if( product_name.length == 0 && product_code.length > 0 ) {
			error_message = 'Location name in '+( $(this).attr('language-name') )+' is not specified.';
			$('#supported_product_name_'+product_name_array[product_name_array.length - 1]).focus();
			return false;
		}
	});
	$('input[id^="supported_product_name_"]').promise().done(function() {
		if(error_message.length > 0) { alert(error_message);submit_status = false; }
		else { submit_status = true; }
	});
}
</script>
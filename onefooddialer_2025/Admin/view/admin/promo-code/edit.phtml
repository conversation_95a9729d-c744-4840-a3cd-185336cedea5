
     <?php
$form = $this->form;
$form->setAttribute('action', $this->url('promocode', array('action' => 'edit', 'id'=>$this->id)));
$form->setAttribute('class','stdform');
$form->prepare();
?> 
      <!-- END PAGE HEADER-->
      
      <div id="content">
          
        <?php 
        if(!empty($errorMsg)){
        ?>    
          <div class="large-12 columns">
          <div  data-alert="" class="alert-box alert round">
 			 <?php echo $errorMsg; ?>
  				<a href="#" class="close">&times;</a>
			</div>  
          </div>
        <?php } ?>  
      <?php echo $this->form()->openTag($form);?>
        <div class="large-6 columns">
        <fieldset>
			<legend>
			PROMOCODE INFO
		</legend>
            
                <?php  echo $this->formElement($form->get('pk_promo_code')); ?>

            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('promo_code')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                echo $this->formElement($form->get('promo_code'));
//				echo $this->formElementErrors($form->get('promo_code'));
                echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						->setMessageCloseString('</small>')
						->render($form->get('promo_code'));
				?>
              </div>
            </div>
            
            <div class="row"  style="margin-bottom: 15px">
              <div class="large-4 small-4 medium-4 columns">
                  <span class="right"><?php echo $this->formLabel($form->get('applied_on')); ?></span>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                echo $this->formElement($form->get('applied_on'));
                echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error" style="clear:both">')
						->setMessageCloseString('</small>')
						->render($form->get('applied_on'));
				?>
              </div>
            </div>
            
            <div class="row"  style="margin-bottom: 15px">
              <div class="large-4 small-4 medium-4 columns">
                <!--label class="inline right">< ?php echo $this->formLabel($form->get('promo_type')); ?></label-->
                 <span class="inline right"><?php echo $this->formLabel($form->get('promo_type')); ?></span>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                echo $this->formElement($form->get('promo_type'));
                echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error" style="clear:both">')
						->setMessageCloseString('</small>')
						->render($form->get('promo_type'));
				?>
              </div>
            </div>
            
            
            <div class="row"  style="margin-bottom: 15px" id="meal_name">
              <div class="large-4 small-4 medium-4 columns">
                  <span class="right" style="margin-right: -10px"><?php echo $this->formLabel($form->get('product_code')); ?></span>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                  <?php
					echo $this->formElement($form->get('product_code'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error" style="margin-top:5px">')
						->setMessageCloseString('</small>')
						->render($form->get('product_code'));
				?>
              </div>
            </div>
            
            
            <div class="row " id="Product_order_quantity">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('Product_order_quantity')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                 <?php echo $this->formElement($form->get('Product_order_quantity'));?></span>
		   		 <?php echo $this->formElementErrors($form->get('Product_order_quantity')); ?>
              </div>
            </div> 
            
            <div class="row"  style="margin-bottom: 15px" id="menu_operator">
              <div class="large-4 small-4 medium-4 columns">
                 <span class="inline right"><?php echo $this->formLabel($form->get('menu_operator')); ?></span>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php 
                echo $this->formElement($form->get('menu_operator'));
                echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						->setMessageCloseString('</small>')
						->render($form->get('menu_operator'));
				?>
              </div>
            </div>
            <div class="row" style="margin-bottom: 15px" id="menu_type">
              <div class="large-4 small-4 medium-4 columns">
                  <span class="right" style="margin-right: -10px"><?php echo $this->formLabel($form->get('menu_type')); ?></span>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                  <?php
					echo $this->formElement($form->get('menu_type'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error" style="margin-top:5px">')
						->setMessageCloseString('</small>')
						->render($form->get('menu_type'));
				?>
              </div>
            </div>
            <div class="row hide" id="wallet_amount">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('wallet_amount')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php 
                echo $this->formElement($form->get('wallet_amount'));
				echo $this->formElementErrors($form->get('wallet_amount'));
				?>
              </div>
            </div>

             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('discount_type')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                 <?php echo $this->formElement($form->get('discount_type'));?></span>
		   		 <?php echo $this->formElementErrors($form->get('discount_type')); ?>
              </div>
            </div>
            
             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('amount')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php 
                echo $this->formElement($form->get('amount'));
				echo $this->formElementErrors($form->get('amount'));
				?>
              </div>
            </div>

            
             
              <div class="row" id="promo_limit">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('promo_limit')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                 <?php echo $this->formElement($form->get('promo_limit'));?></span>
		   		 <?php echo $this->formElementErrors($form->get('promo_limit')); ?>
              </div>
            </div>   
            
            
               <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('start_date')); ?></label>
              </div>
              
              <div class="large-8 columns">
              <div class="row">
              	<div class="large-5 columns">
                 <?php 
                 	echo $this->formElement($form->get('start_date'));
					echo $this->formElementErrors($form->get('start_date'));
				?>
                </div>
               
              </div>
            </div>
           </div>
            
              <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('end_date')); ?></label>
              </div>
              
              <div class="large-8 columns">
              <div class="row">
              	<div class="large-5 columns">
                 <?php 
                 	echo $this->formElement($form->get('end_date'));
					echo $this->formElementErrors($form->get('end_date'));
				?>
                </div>
               
              </div>
            </div>
           </div>
         
           <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                 <?php echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'));
				 ?>
              </div>
            </div>
             </fieldset>
             <?php echo $this->formElement($form->get('csrf'));
           			echo $this->formElement($form->get('backurl'));
				 ?>
			 <div class="large-12 columns pl0 pr0">
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8 small-8 medium-8 columns">
              
                <button	type="submit" id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id="cancelbutton" class="button	left tiny left5	redBg" onClick="location.href='promocode.html'">Cancel &nbsp;<i class="fa	fa-ban"></i></button>
              </div>
            </div>
            </div>
         <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>     
        </div>
      </div>
    </div>
    
    <!-- END PAGE CONTAINER--> 
     <script type="text/javascript">
    
 $(document).ready(function() {

	    var now = new Date();
	 	var curyear=now.getFullYear();
	 	var month=now.getMonth() + 1;
	 	var date=now.getDate();
	 	var CurrentMonth = month + '/' + date + '/' + curyear;
	
		$("#start_date").datepicker({minDate:CurrentMonth});
		$("#end_date").datepicker({minDate:CurrentMonth});
	
		$(document).on('click',"#start_date",function(){
			$( "#start_date" ).datepicker({minDate:CurrentMonth});
		});

		$(document).on('click',"#end_date",function(){
			$( "#end_date" ).datepicker({minDate:CurrentMonth});
		});

		window.testSelAll2 = $('.testSelAll2').SumoSelect({selectAll:true });

		$(document).on("click","#submitbutton",function(){
			
			var startDateStr = $("#start_date").val();
			var startDateArr = startDateStr.split("/");
			var startDate = new Date(startDateArr[2], startDateArr[0], startDateArr[1]);
			
			var endDateStr = $("#end_date").val();
			var endDateArr = endDateStr.split("/");
			var endDate = new Date(endDateArr[2], endDateArr[0], endDateArr[1]);

			
			if(startDate  > endDate)
			{
				alert("Start date should is greater than end date..!!");
				return false;
			}
		});

        $('input[type=radio][name=promo_type]:not(:checked)').prop('disabled', true);

		if( $('input[name=applied_on]:checked').val() == 'wallet' ){
            $("#meal_name,#menu_type,#menu_operator,#Product_order_quantity").removeClass('show').addClass('hide');
            $("#wallet_amount,#promo_limit").removeClass('hide').addClass('show');
            
        }else if( $('input[name=applied_on]:checked').val() == 'order' ){
            $("#wallet_amount,#menu_type,#menu_operator").removeClass('show').addClass('hide');
            $("#meal_name,#Product_order_quantity,#promo_limit").removeClass('hide').addClass('show'); 
           
            
        }else if( $('input[name=applied_on]:checked').val() == 'menu' ){
            $("#menu_type,#promo_limit").removeClass('hide').addClass('show');
            $("#wallet_amount,#menu_operator,#meal_name,#Product_order_quantity").removeClass('show').addClass('hide'); 
            
        }else if( $('input[name=applied_on]:checked').val() == 'plan' ){
//            $("#Product_order_quantity").removeClass('hide').addClass('show');
            $("#wallet_amount,#menu_operator,#meal_name,#menu_type,#Product_order_quantity,#promo_limit").removeClass('show').addClass('hide'); 
            
        }			
        
        $('input[type=radio][name=applied_on]').change(function(event) {
            event.preventDefault();
            if (this.value == 'order') {
                $('input[name=promo_type][value="discount"]').prop('disabled', false).prop("checked", true).parent().addClass('checked');
                $('input[name=promo_type][value="cashback"]').prop("checked", false).prop('disabled', true).parent().removeClass('checked');

                $("#meal_name,#Product_order_quantity,#promo_limit").removeClass('hide').addClass('show');
                $("#menu_type,#wallet_amount,#menu_operator").removeClass('show').addClass('hide');
               
            }else if(this.value =='menu'){

                $('input[name=promo_type][value="discount"]').prop('disabled', false).prop("checked", true).parent().addClass('checked');
                $('input[name=promo_type][value="cashback"]').prop("checked", false).prop('disabled', true).parent().removeClass('checked');

                $("#meal_name,#wallet_amount,#menu_operator,#Product_order_quantity").removeClass('show').addClass('hide');
                $("#menu_type,#promo_limit").removeClass('hide').addClass('show');
                
            }else if (this.value == 'plan') {
                $('input[name=promo_type][value="discount"]').prop('disabled', false).prop("checked", true).parent().addClass('checked');
                $('input[name=promo_type][value="cashback"]').prop("checked", false).prop('disabled', true).parent().removeClass('checked');

                $('input[type=radio][name=promo_type]:not(:checked)').attr('disabled', true);
                $("#meal_name,#menu_operator,#menu_type,#wallet_amount,#Product_order_quantity,#promo_limit").removeClass('show').addClass('hide');
            
            }else if (this.value == 'wallet') {
                $('input[name=promo_type][value="cashback"]').prop('disabled', false).prop("checked", true).parent().addClass('checked');
                $('input[name=promo_type][value="discount"]').prop("checked", false).prop('disabled', true).parent().removeClass('checked');

                $("#meal_name,#menu_operator,#menu_type,#Product_order_quantity").removeClass('show').addClass('hide');
                 $("#wallet_amount,#promo_limit").removeClass('hide').addClass('show');
               

            }
            
        })
    });
    </script>

<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
<?php 
$url_order = 'ASC';
if ($order_by == 'title')
    $url_order = $order == 'ASC' ? 'DESC' : 'ASC';
elseif ($order_by == 'artist')
    $url_order = $order == 'ASC' ? 'DESC' : 'ASC';

?>
<div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="image"><span>Price Plan</span></h2>
          </div>
          <!--contenttitle-->
          
          <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('price-plan', array(
    'action' => 'add'))?>" class="btn btn_add"><span>Add Record</span></a></div>
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            </colgroup>
            <thead>
              <tr>
                <td class="head0">Name</td>
                <td class="head1">Amount</td>
                <td class="head0">Currency</td>
                <td class="head1">Country</td>
                <td class="head0">Status</td>
                <td class="head1 center">Action</td>
              </tr>
            </thead>
            <tfoot> 
              <tr>
                <td class="head0">Name</td>
                <td class="head1">Amount</td>
                <td class="head0">Currency</td>
                <td class="head1">Country</td>
                <td class="head0">Status</td>
                <td class="head1 center">Action</td>
              </tr>
            </tfoot>
            <tbody>
            	  <?php foreach ($paginator as $plan) : ?>
              <tr>
                <td><?php echo $this->escapeHtml($plan->price_name);?></td>
                <td><?php echo $this->escapeHtml($plan->amount);?></td>
                <td><?php echo $this->escapeHtml($plan->currency);?></td>
                <td><?php echo $this->escapeHtml($plan->country); ?></td>
                <td><?php echo ($this->escapeHtml($plan->status))?'Active':'<span style="color:red;">Inactive</span>'; ?></td>
                <?php $textadd = ($plan->status)? 'Suspend' :'Activate'; ?>
                <td class="center"><a href="<?php echo $this->url('price-plan', array('action' => 'edit', 'id' => $plan->pk_price_id)); ?>" class="btn btn5 btn_pencil5"></a>&nbsp;
                <a href="<?php echo $this->url('price-plan', array('action' => 'delete', 'id' => $plan->pk_price_id)); ?>" class="btn btn5 btn_trash5"  onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this price plan ?')" ></a></td>
              </tr>
              <?php endforeach; ?>
              
            </tbody>
          </table>
          <br />
          <br />
        </div>
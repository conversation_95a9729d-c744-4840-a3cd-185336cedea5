<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/custom/general.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
 <div class="content">
                    
                   
                    <div class="contenttitle radiusbottom0">
                    	<h2 class="image"><span>Pre-build Content</span></h2>
                    </div><!--contenttitle-->
                    <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('prebuildcontent_crud', array('action' => 'add')); ?>" class="btn btn_add"><span>Add Record</span></a></div>
                    <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
                        <colgroup>
                            <col class="con0" />
                            <col class="con1" />
                            <col class="con0" />
                            <col class="con1" />
                            <col class="con0" />
                            <col class="con1" />
														<col class="con0" />
                        </colgroup>
                        <thead>
                            <tr>
																<td class="head0">Page Name</td>
																<td class="head1">Title</td>
																<td class="head0">Content</td>
																<td class="head1">Language</td>
																<td class="head0">Nature of business</td>
																<td class="head1">Status</td>
																<td class="head0 center">Action</td>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr>
                                								<td class="head0">Page Name</td>
																<td class="head1">Title</td>
																<td class="head0">Content</td>
																<td class="head1">Language</td>
																<td class="head0">Nature of business</td>
																<td class="head1">Status</td>
																<td class="head0 center">Action</td>
                            </tr>
                        </tfoot>
                        <tbody>
                        	 <?php
                        	 #print_r(get_class_methods(get_class($paginator)));echo $paginator->count();exit();
                        	 if( ( is_object($paginator) && ($paginator->count() > 0) ) && !empty($paginator) ) {
                        	 	foreach ($paginator as $rowId => $prebuildcontent) :
                        	 ?>
						        <tr>
						            <td><?php echo $this->escapeHtml($prebuildcontent->page_name); ?></td>
						            <td><?php echo $this->escapeHtml($prebuildcontent->title); ?></td>
						            <?php
						            $content = htmlspecialchars($prebuildcontent->content, ENT_COMPAT|ENT_HTML5, 'UTF-8', false);
						            $content = (strlen($content) > 200) ? substr($content,0,50).'...' : $content;
						            ?>
						            <td><?php echo $content; #$this->escapeHtml(); ?></td>
						            <td><?php echo $this->escapeHtml($prebuildcontent->language); ?></td>
						            <td><?php echo $this->escapeHtml($prebuildcontent->nature_of_busines); ?></td>
						            <td><?php echo ($prebuildcontent->status)?'Active':'<span class="red">Inactive</span>';?></td>
						            <td class="center">
						            <a href="<?php echo $this->url('prebuildcontent_crud', array('action' => 'edit', 'id' => $prebuildcontent->pk_pre_content_id)); ?>" class="btn btn5 btn_pencil5"></a>&nbsp;
						             <?php $textadd = ($prebuildcontent->status)? 'Suspend' :'Activate'; ?>
						            <a onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this content plan ?');" href="<?php echo $this->url('prebuildcontent_crud', array('action' => 'delete', 'id' => $prebuildcontent->pk_pre_content_id));
						        	?>" class="btn btn5 btn_trash5"></a>
						        	</td>

						        </tr>
						    <?php
						    	endforeach;
						    }
						    else {
						    ?>
						        <tr>
						            <td>&nbsp;</td>
						            <td>&nbsp;</td>
						            <td>&nbsp;</td>
						            <td>&nbsp;</td>
						            <td>&nbsp;</td>
						            <td>&nbsp;</td>
						            <td class="center">&nbsp;</td>
						               
						            </td>
						        </tr>
						    <?php
						    }
						    ?>
                            
                        </tbody>
                    </table>
                    
                    <br /><br />
                    
                </div><!--content-->

<?php
    /*echo $this->paginationControl(
            $paginator, 'Sliding', 'paginator-slide', array('order_by' => $order_by, 'order' => $order)
    );*/
    ?>
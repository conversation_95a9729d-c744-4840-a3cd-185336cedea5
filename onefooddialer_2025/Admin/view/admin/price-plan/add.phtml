<link rel="stylesheet" href="/admin/css/jquery-ui.css">
<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<?php $country_code=''; $country_name='';
if($countries->count() > 0 ) {
	foreach($countries as $key=>$country) { 
		$country_code .='<option value="'.$country['currency_code'].'">'.$country['currency_code'].'</option>';
		$country_name .= '<option value="'.$country['country_code'].'">'.$country['country_name'].'</option>';			
	}
}
?>

<style>
.removeme {cursor:pointer;}
</style>

<script type="text/javascript">
$(document).ready(function(){
	//first form
    $("#cancelbutton").click(function(){

    	var url= $("#backurl").val();
       window.location.href=url;
       return false;
    });

});
</script> 
  

  <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Price Plan Edit</span></h2>
          </div>
          <!--contenttitle--> 
          
          <br />
          <form class="stdform" action="add" method="post">
			<p>
			
			<label class="error" style="margin-left:220px;"><?php echo ($error)?$error:''; ?></label>
		
			</p>
           	<p>
				<label>Name</label>
				<span class="field">
				<input type="text" name="plan[price_name]" class="smallinput" class="planname" required/>
				</span> 
			</p>
            <!-- <p>
              <label>Amount</label>
              <span class="field">
              <input type="text" name="input1" class="smallinput" />
              </span> </p>-->
            <?php if($priceplan->count()) {
            	foreach($priceplan as $key=>$grp) { $incr = $key+1; ?>
            				<p>
            	
            					<label>Amount</label>
            					<input type="text" name="plan[<?php echo $incr; ?>][amount]" class="vsmallinput planamt" value="" required/>
            					  &nbsp;Currency&nbsp;
            					 
            					<input type="text" name="plan[<?php echo $incr; ?>][currency]" class="smallSelect" style="width:100px;" value="<?php echo $grp->currency; ?>" readonly />
            					 
            					  &nbsp;Country&nbsp;
            					   <input type="hidden" name="plan[<?php echo $incr; ?>][country]" class="contSelect" value="<?php echo $grp->country; ?>" readonly />
								   <input type="text"  class="vsmallinput" value="<?php echo $grp->country_name; ?>" class="smallinput" readonly />
            					<?php if($grp->isDefault) { ?>
            					 <input type="hidden" name="plan[default]" value="<?php echo $incr; ?>" <?php echo ($grp->isDefault)?'checked':''; ?> >	<img src="/admin/images/icons/Check.png" />&nbsp;default
            					 <!-- <span class="removeme">X</span> -->
            					 <?php } ?>
            	            </p>
            	<?php }
            } else {?>
             <p>
				  <label>Amount</label><input type="text" name="plan[1][amount]" class="vsmallinput" class="planamt" required/>
				  &nbsp;Currency&nbsp; 
				  <select name="plan[1][currency]" class="smallSelect">
				 	<?php echo $country_code; ?>
				  </select>
				  &nbsp;Country&nbsp;
				  <select name="plan[1][country]" class="contSelect">
					<?php echo $country_name; ?>
				  </select>
				  <input type="radio" name="plan[default]" value="1" >&nbsp;default  
				  <!--<span class="removeme">X</span>-->
            </p> 
             <p>
				  <label>Amount</label><input type="text" name="plan[2][amount]" class="vsmallinput" class="planamt" required />
				  &nbsp;Currency&nbsp;
				  <select name="plan[2][currency]" class="smallSelect">
						<?php echo $country_code; ?>
				  </select>
				  &nbsp;Country&nbsp;
				  <select name="plan[2][country]" class="contSelect">
					<?php echo $country_name; ?>
				  </select>
				   <input type="radio" name="plan[default]" value="2" >&nbsp;default  
				    <!--<span class="icon-user removeme">X</span>-->
            </p>
              <p id="addmore" >
              			<label></label>
					<a onclick="addModule();" class="btn btn_add"><span>Add</span></a>
			 </p> 
			 <?php }
			 ?>
           
            <!--  
            <p>
              <label>Currency</label>
              <span class="field">
              <select name="select city">
                <option value="">Indian Rs</option>
                <option value="">US Dollar</option>
                <option value="">Australian Dollar</option>
              </select>
              </span> </p>
            <p>
              <label>Language</label>
              <span class="field">
              <select name="select city">
                <option value="">English UK</option>
                <option value="">Hindi</option>
                <option value="">Arebic</option>
              </select>
              </span> </p>
            <p>
              <label>Created</label>
              <span class="formwrapper">
              <input class="dsmallinput" type="text" id="datepicker">
              </span></p>-->
           
             <p class="stdformbutton">
              <input type="submit" onclick="return validateForm();" class="button radius2"  value="Submit Button"  />
              <input type="submit" id ="cancelbutton" class="reset radius2" value="Cancel Button" />
              <input type="hidden" value="/price-plan" id="backurl"/>
            </p>
          </form>
          <br clear="all" />
          <br />
        </div>
      
          

<script>


function validateForm()
{
	jQuery('.planamt').each(function(){
		//alert(jQuery(this).val());
	});
	return true;
}
var module_row_wage = 3;

function addModule() {
	
	html  = '<p>';
	html += '<label>Amount</label><input type="text" name="plan[' + module_row_wage + '][amount]" class="vsmallinput" class="planamt"/>';
	html += '&nbsp; Currency&nbsp; <select name="plan[' + module_row_wage + '][currency]" class="smallSelect"><?php echo $country_code; ?></select>';
	html += '&nbsp; Country&nbsp; <select name="plan[' + module_row_wage + '][country]" class="contSelect"><?php echo $country_name; ?></select>';
	html += '<input type="radio" name="plan[default]" value="' + module_row_wage + '" >&nbsp;default ';
	html += '</p>';
	
	jQuery('#addmore').before(html);
	
	module_row_wage++;
}
/*
jQuery('.removeme').live("click",function(){
	jQuery(this).closest("p").remove();
});
*/
</script>
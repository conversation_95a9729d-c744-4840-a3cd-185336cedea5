
      <div id="content" class="clearfix">
        <div class="large-12 columns">        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Customer Information</h4>  
        
        </div>        
        	<div class="portlet-body">        
        	<table id="" class="display">                   
 
                    <tbody>
                        <tr>
                            <td width="12%">Order No</td>
                            <td width="1%">:</td>
                            <td width="89%"><?php echo $orders[0]['pk_order_no']; ?></td>
                        </tr>
                        <tr>
                            <td>Order Date</td>
                            <td>:</td>
                            <td><?php echo $orders[0]['order_date']; ?></td>
                        </tr>
                        <tr>
                            <td>Customer Name</td>
                            <td>:</td>
                            <td><b><?php echo $orders[0]['customer_name']; ?></b></td>
                        </tr>
                        <tr>
                            <td>Contact Number</td>
                            <td>:</td>
                            <td><b><?php echo $orders[0]['phone']; ?></b></td>
                        </tr>
                        <tr>
                            <td>Email Id</td>
                            <td>:</td>
                            <td><?php echo $orders[0]['email_address']; ?></td>
                        </tr>
                        <tr>
                            <td>Group</td>
                            <td>:</td>
                            <td><?php if(isset($orders[0]['group_name']) && $orders[0]['group_name']!="0"){echo $orders[0]['group_name'];}else{ echo "N/A"; }; ?></td>
                        </tr>
                         <tr>
                            <td>Order Dates</td>
                            <td>:</td>
                           
                           <td> 
                            <?php //echo $orders[0]['phone'];
									$days_str='';
									$days = explode(',', $orders[0]['order_days']);
									//echo "<pre>";print_r($days);die;
									
									$day_array = array();
									foreach($days as $day)
									{
										//$day_array[] = date('d-M Y',strtotime("January 1st +".($day)." days"));
										
										echo '<span class="blueBg white padding5">'.$day.',</span> &nbsp;';
										
									}
									
									// implode(',', $day_array);
							
							
							
						?>
						<?php if(count($days)>1):?>
						 <a data-id="<?php echo $orders[0]['order_days'];?>" data-orderid= <?php echo $orders[0]['pk_order_no']; ?> data-reveal-id="myModal" class="editOrder has-tip" data-tooltip title="Edit Your Order"><i class="fa fa-pencil-square-o"></i></a>
						 <?php endif;?>
                         </td>
                            
                        </tr>
                         <tr>
                            <td>Ship address</td>
                            <td>:</td>
                            <td><?php echo $orders[0]['ship_address']; ?></td>
                        </tr>
                        
                        
                       
                        
                    </tbody>
                </table>          
          	</div>
        </div>   
           
        <div class="clearBoth10"></div>      
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Order Details</h4>  
          
        	</div>        
        	<div class="portlet-body">        
        	<table id="" class="display">
                    <thead>
                        <tr>
                            <th>Sr No.</th>
                            <th>Type</th>
                            <th>Product</th>
                            <th>Quantity</th>
                        </tr>
                    </thead>
 
                    <tbody>
			        <?php foreach ($orders as $key=>$order){ ?>
			              <tr>
			                <td><?php echo $key+1; ?> </td>
			                <td><?php echo $order['product_type'];?></td>
			                <td><?php echo $order['name'];?></td>
			                <td><?php echo $order['quantity'];?></td>
			              </tr>
						<?php }?>
			                       
                        
                    </tbody>
                </table>          
          	</div>
        </div>
        
        <div class="clearBoth10"></div>        
        
        <div id="myModal" class="reveal-modal" data-reveal>
								
								<h2> Preorder Details </h2>
								
								<!-- modify preorder starts -->
								<div  class="large-12 columns tifinInfo">

									<table>
										<tbody>
											<tr>
												<td width="20%">Order No</td>
												<td width="1%">:</td>
												<td width="79%" id="order_id"></td>
											</tr>
											<tr id="todaysorder">
												
											</tr>
											<tr id="orderdeliverd">
											
											</tr>
											<tr id="tobeserved">
											
											</tr>
										</tbody>
									</table>
										<div class="pull-right">
											<button class="greenBg btn" id="saveOrder">Save <i class="fa fa-save"></i></button>
											<button class="redBg btn" id="closeModal">Cancel <i class="fa fa-ban"></i></button>
										</div>
								</div>
								<a class="close-reveal-modal">&#215;</a>
							<input type="hidden" value="" name="orderDates[]" id="orderDates"/>
							<input type="hidden" value="" name="todaysorderDates[]" id="todaysorderDates"/>
							<input type="hidden" value="" name="servedsorderDates[]" id="servedsorderDates"/>
							<input type="hidden" value="" name="tobeservedsorderDates[]" id="tobeservedsorderDates"/>
							</div>

							<!-- modify preorder ends -->
  
        
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
  
  <script>

  $(".editOrder").click(function(){
	  
		var order_days = $(this).data('id').toString();
		var key = $(this).data('orderid');
		var order_days_arr = [];
		order_days_arr = order_days.split(",");
		
  	   	$("#orderDates").val(order_days_arr);

	  if(order_days==''){
         alert("No orders");
         return false;
     }
     else{ 
     	var ele ="";
		var ele1 ="";
		var today = "";
		var served_arr =[];
		var tobeserved_arr =[];
		var served_str =[];
		var tobeserved_str =[];
		var append = true;
		var appendserved = true;
		$("#todaysorder").empty();
		$("#servedDates").empty();
		$("#foobar").empty();
		$("#orderdeliverd").empty();
		$("#tobeserved").empty();
		
		$.each(order_days_arr, function(index, value){
		
			var now = new Date();
			var todaysDate = $.datepicker.formatDate('yy-mm-dd', new Date(now));
		
			$("#order_id").html(key);
			if(value==todaysDate){

			    today+='<td width="20%">Todays Order In Process</td><td width="1%">:</td><td width="79%">';
				today+='<div class="pull-left datesalert">';
				today+='<div data-alert class="alert-box todayalert alert menuItem radius">&nbsp;';
				today+='<div class="proName">'+$.datepicker.formatDate('DD, dd-M-y', new Date(value))+'</div>';
				today+='</div></div></td>';
				$('#todaysorder').html(today);
				$("#todaysorderDates").val(($.datepicker.formatDate('yy-mm-dd', new Date(value))));
			}
			else if(value<todaysDate)
			{
				served_arr.push(value);
				if(appendserved){

					var ele1 ='<td width="20%">Order Delivered</td><td width="1%">:</td><td width="79%" id="servedDates"></td></td>';
					$('#orderdeliverd').html(ele1);
					appendserved =false;
				}
			}
			else if(value>todaysDate){

				tobeserved_arr.push(value);
				if(append){
					ele+= '<td width="10%">To be Served</td><td width="1%">:</td><td width="89%"><select id="foobar" data-placeholder="Choose Area..." class="chosen-select" multiple >';
					ele+= '</select></td>';
					$('#tobeserved').html(ele);
					append = false;
				} 
			}
	 });
		$.each(served_arr, function(ind, val){
			$("#servedDates").append('<div class="pull-left datesalert"><div data-alert class="alert-box deliveralert alert menuItem radius">&nbsp<div class="proName">'+$.datepicker.formatDate('DD, dd-M-y', new Date(val))+'</div></div></div>');
			served_str.push($.datepicker.formatDate('yy-mm-dd', new Date(val)));
		});
		$("#servedsorderDates").val(served_str);
		
		$.each(tobeserved_arr, function(ind, val1){
			$("#foobar").append('<option value='+($.datepicker.formatDate('yy-mm-dd', new Date(val1)))+' selected>'+$.datepicker.formatDate('DD, dd-M-y', new Date(val1))+'</option>');
			tobeserved_str.push($.datepicker.formatDate('yy-mm-dd', new Date(val1)));
		});
		$("#tobeservedsorderDates").val(tobeserved_str);
		
		var config = {
				  '.chosen-select'           : {},
				  '.chosen-select-deselect'  : {allow_single_deselect:true},
				  '.chosen-select-no-single' : {disable_search_threshold:10},
				  '.chosen-select-no-results': {no_results_text:'Oops, nothing found!'},
				  '.chosen-select-width'     : {width:"95%"}
				}
				for (var selector in config) {
				  $(selector).chosen(config[selector]);
				}
		
     }
	});

  $("#saveOrder").on('click',function(){

	 	var tobeserved = $('#foobar').chosen().val();
		if(tobeserved==null){
			alert("You should have atleast one date selected");
			return false;
		}
		var todaysorderDates = $("#todaysorderDates").val();
		var servedsorderDates = $("#servedsorderDates").val();
		var order_id = $("#order_id").text();
		if(tobeserved=='null'){
			var order_days  = todaysorderDates.concat(servedsorderDates);
		}else{
			var order_days  = tobeserved.concat(todaysorderDates,servedsorderDates);
		}

		  $.ajax({
				 url:"<?php echo $this->url('front',array('action' => 'updatePreorder')); ?>",
				 type: "POST",
				 data : {'daysArray':order_days,'order_id':order_id},
				 success:function(data)
				 {
					 alert("Preorder Dates Modified");
					 $('.close-reveal-modal','#myModal').click();
					  window.location.href ='/preorders';
				 }
		  });
	});


  $("#closeModal").click(function(){
	  $('.close-reveal-modal','#myModal').click()
	});
  </script>
<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
		<?php 
			if ($this->FlashMessenger()->hasSuccessMessages()){ 
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){ 
		?>
			<div class="isa_success col-md-8"><?php echo $msg ?></div>
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div class="isa_error col-md-8"><?php echo $msg ?></div>
		<?php 	}
			} 
		?>
   <div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="table"><span>Pre Order List</span></h2>
          </div>
          <!--contenttitle-->
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            </colgroup>
            <thead>
              <tr>
                <th class="head0">Order No.</th>
                <th class="head1">Customer  Name</th>
                <!-- <th class="head0">Group</th>-->
                <th class="head1">Phone</th>
                <th class="head0">Delivery Location</th>
                <!-- <th class="head1">City</th>
                <th class="head0">Product</th>-->
                <!-- <th class="head1">Quantity</th>
                <th class="head0">Order Type</th> 
                <th class="head1">Order Days</th>
                <th class="head0">Order Weeks</th>
                <th class="head1">Order Months</th>-->
                <th class="head0">Promo Code</th>
                <th class="head1">Amount</th>
                <!-- <th class="head0">Applied Discount</th>-->
                <th class="head1">Order Status</th>
                <th class="head0">Order Date</th>
                <th class="head0">Last Modified</th>
                <th class="head1">Action</th>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <th class="head0">Order No.</th>
                <th class="head1">Customer  Name</th>
                <!-- <th class="head0">Group</th>-->
                <th class="head1">Phone</th>
                <th class="head0">Delivery Location</th>
                <!-- <th class="head1">City</th>
                <th class="head0">Product</th>-->
                <!-- <th class="head1">Quantity</th>
                <th class="head0">Order Type</th> 
                <th class="head1">Order Days</th>
                <th class="head0">Order Weeks</th>
                <th class="head1">Order Months</th>-->
                <th class="head0">Promo Code</th>
                <th class="head1">Amount</th>
                <!-- <th class="head0">Applied Discount</th>-->
                <th class="head1">Order Status</th>
                <th class="head0">Order Date</th>
                <th class="head0">Last Modified</th>
                <th class="head1">Action</th>
              </tr>
            </tfoot>
            <tbody>
            <?php foreach ($paginator as $order_show) {  ?>
            <tr>

				<td><a href="<?php echo $this->url('preorders', array('action' => 'view', 'id' => $order_show['pk_order_no'])); ?>"><?php echo $this->escapeHtml($order_show['pk_order_no']); ?></a></td>
                <td><?php echo $this->escapeHtml($order_show['customer_name']); ?></td>
                <!-- <td><?php //echo $this->escapeHtml($order_show['group_name']); ?></td>-->
                <td><?php echo $this->escapeHtml($order_show['phone']); ?></td>
                <td><?php echo $this->escapeHtml($order_show['location']); ?></td>
                <!-- <td><?php //echo $this->escapeHtml($order_show['city']); ?></td>
                <td><?php //echo $this->escapeHtml($order_show['name']); ?></td>-->
                <!-- <td><?php //echo $this->escapeHtml($order_show['quantity']);?></td>
                <td><?php //echo $this->escapeHtml($order_show['order_type']);?></td> 
                <td><?php //echo $this->escapeHtml($order_show['order_days']);?></td>
                <td><?php //echo $this->escapeHtml($order_show['order_weeks']);?></td>
                <td><?php //echo $this->escapeHtml($order_show['order_months']);?></td>-->
                <td><?php echo $this->escapeHtml($order_show['promo_code']); ?><span class="blue"></span></td>
                <td><?php echo $this->escapeHtml($order_show['amount']); ?></td>
                <!-- <td><?php //echo $this->escapeHtml($order_show['applied_discount']); ?></td> -->
                <td><?php echo ($order_show['order_status']=="Cancel")?"Cancelled":$this->escapeHtml($order_show['order_status']); ?></td>
                <td><?php echo $this->escapeHtml($order_show['order_date']); ?></td>
                <td><?php echo $this->escapeHtml($order_show['last_modified']); ?></td>

				<td class="center">
					<?php if($acl->isAllowed($loggedUser->rolename,'preorders','cancelpreorder') && $order_show['order_status']!="Cancel"){ ?>
						<a href="<?php echo $this->url('preorders', array('action'=>'cancelpreorder', 'id' =>  $order_show['pk_order_no'],'c_id'=>$order_show['customer_code']));?>" class="btn btn5 cancel" onclick="return confirm('Are you sure you want to Cancel this order ?')"></a>
					<?php } ?>

				</td>
             </tr>
            <?php }//endforeach; ?>
            </tbody>
          </table>
        </div>
        <!--content-->
		 <?php
		//echo $this->paginationControl($paginator, 'Sliding','paginator-slide-order-summary', array('order_by' => $order_by, 'order' => $order));
		?>

<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
 <div class="content">
                    
                   
                    <div class="contenttitle radiusbottom0">
                    	<h2 class="image"><span>Content</span></h2>
                    </div><!--contenttitle-->
                    <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('content_crud', array('action' => 'add')); ?>" class="btn btn_add"><span>Add Record</span></a></div>
                    <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
                        <colgroup>
                            <col class="con0" />
                            <col class="con1" />
                            <col class="con0" />
                            <col class="con1" />
                            <col class="con0" />
                            <col class="con1" />
														<col class="con0" />
                        </colgroup>
                        <thead>
                            <tr>
																<td class="head0">Content Plan</td>
																<td class="head1">Desc</td>
																<td class="head0">Max Word Count(Per Page)</td>
																<td class="head1">Price Plan</td>
																<td class="head0">Time Required</td>
																<td class="head1">Status</td>
																<td class="head0 center">Action</td>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr>
                                								<td class="head0">Content Plan</td>
																<td class="head1">Desc</td>
																<td class="head0">Max Word Count(Per Page)</td>
																<td class="head1">Price Plan</td>
																<td class="head0">Time Required</td>
																<td class="head1">Status</td>
																<td class="head0 center">Action</td>
                            </tr>
                        </tfoot>
                        <tbody>
                        	 <?php
                        	 foreach ($paginator as $content) :
                        	 	$words_per_page = (int) $page_price['content']['words_per_page'];
                        	 	$total_pages = (int) ($content->max_word_count/$words_per_page);
                        	 ?>
						        <tr>
						            <td><?php echo $this->escapeHtml($content->content_plan); ?></td>
						            <?php $desc = (strlen($content->content_description) > 53) ? substr($content->content_description,0,50).'...' : $content->content_description; ?>
						            <td><?php echo $this->escapeHtml($desc); ?></td>
						             <td><?php echo $this->escapeHtml($content->max_word_count).' ('.( ($total_pages > 25) ? 'More than 25 pages' : $total_pages.( ($total_pages == 1) ? ' page' : ' pages' ) ).')'; ?></td>
						            <td><?php echo $this->escapeHtml($content->price_name); ?></td>
						             <td><?php echo $this->escapeHtml($content->time_required); ?></td>
						             <td><?php echo ($content->content_status)?'Active':'<span class="red">Inactive</span>';?></td>
						            <td class="center">
						            <a href="<?php echo $this->url('content_crud', array('action' => 'edit', 'id' => $content->pk_content_id)); ?>" class="btn btn5 btn_pencil5"></a>&nbsp;
						             <?php $textadd = ($content->status)? 'Suspend' :'Activate'; ?>
						            <a onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this content plan ?')" href="<?php echo $this->url('content_crud', array('action' => 'delete', 'id' => $content->pk_content_id));
						        	?>" class="btn btn5 btn_trash5"></a></td>
						               
						            </td>
						        </tr>
						    <?php endforeach; ?>
                            
                        </tbody>
                    </table>
                    
                    <br /><br />
                    
                </div><!--content-->

<?php
    /*echo $this->paginationControl(
            $paginator, 'Sliding', 'paginator-slide', array('order_by' => $order_by, 'order' => $order)
    );*/
    ?>
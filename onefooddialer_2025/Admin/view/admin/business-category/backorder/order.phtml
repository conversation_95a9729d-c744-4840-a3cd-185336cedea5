<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
<meta charset="UTF-8" />
<!--[if IE]><meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'><![endif]-->
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>ED Delivery | Order Detail</title>
<meta name="description" content="" />
<meta name="keywords" content="" />
<meta name="author" content="" />
<link rel="shortcut icon" href="images/fav.png">
<link rel="stylesheet" type="text/css" href="/delivery_assets/css/foundation.css" />
<link rel="stylesheet" href="/delivery_assets/css/general_enclosed_foundicons.css">
<link rel="stylesheet" href="/delivery_assets/css/font-awesome.css">
<link rel="stylesheet" type="text/css" href="/delivery_assets/css/normalize.css" />
<link rel="stylesheet" type="text/css" href="/delivery_assets/css/component.css" />
<!--[if IE]>
<script src="http://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
<![endif]-->
</head>
<body>
<header>
  <div class="large-4 columns small-4">
    <div class="logo"><a href="javascript:void(0)"><img src="/delivery_assets/images/edlogo-red.png" class="" width="110" alt="ED"/></a></div>
  </div>
  <div class="large-8 columns small-8">
  	<div class="right">
  	<div class="logOutSys"><a href="<?php echo $this->url('Delivery',array('action' => 'logout')); ?>" data-tooltip class="tip-bottom logOut" title="Log Out"><img src="/delivery_assets/images/exit.png" alt="Log Out" /></a></div>
    <p class="portel-top right"><img src="/delivery_assets/images/Food-Dialer_logo.png" alt="Food Dialer" width="43" /></p>
 	</div>
  </div>
</header>
<hr>
<div class="row">
  <div class="large-12 columns">
    <div class="row collapse">
      <div class="small-4 columns">

        <input type="text" name="search_order" id="search_order" placeholder="Enter Order Number" />
      </div>
      <div class="small-2 columns"> <a href="javascript:void(0)" class="button postfix">Go</a> </div>
      <div class="small-4 columns">
        <a href="#"  class="small button split">
        <?php
        	foreach ($cities as $city)
			{
				if($loguser->city == $city->pk_location_code)
				{
					$cityname = $city->location;
				}
			}
		?>
        <?php echo ($cityname)?$cityname:'Location'; ?>
          <span data-dropdown="drop"></span>
        </a><br>
        <ul id="drop" class="f-dropdown" data-dropdown-content>

	        <?php
	        $cityname = "";
			if(count($cities) > 0)
			{
				//echo '<li><a href="'.$this->url('Delivery',array('action'=>'order','city'=>0 )).'">All Locations </a></li>';

				foreach ($cities as $city)
				{
					if($loguser->city == $city->pk_location_code)
					{
						$cityname = $city->location;
					}
					echo '<li><a href="'.$this->url('Delivery',array('action'=>'order','city'=>$city->pk_location_code)).'">'.$city->location.'</a></li>';
				}
			}
	    	?>

       </ul>
     </div>
    </div>
  </div>
</div>

<div class="row clearfix">
  <div class="large-12 columns">
    <table>
      <thead>
        <tr>
          <th>Order No.</th>
          <th>Location</th>
          <th class="right">Action</th>
        </tr>
      </thead>
      <tbody id="order_grid">
	    <?php
	    if(count($orders) > 0)
	    {
	    	foreach($orders as $order)
	    	{
	    		$found_class = ($order->delivery_status == "Delivered")?"foundicon-checkmark success":"foundicon-remove error";
	    		$class_change = ($order->delivery_status == "Delivered")?"no":"yes";
	    		?>
				 <tr>
		          <td class="user-name"><?php echo $order->pk_order_no; ?></td>
		          <td class="user-email"><?php echo $order->ship_address; ?>
		          <?php echo $order->del_sub_city; ?>, <?php echo $order->del_location; ?>,
		            Navi Mumbai - <?php echo $order->del_pin; ?>
		             </td>
		          <td class="user-phone right"><a href="#" data-changeclass=<?php echo $class_change; ?> data-delstatus="<?php echo $order->pk_order_no;?>" class="order_delivery_status"><i class="<?php echo $found_class; ?>"></i></a></td>
		        </tr>
	    <?php }
	    }

	     ?>


      </tbody>
    </table>
    <br>
     <?php
			echo $this->paginationControl($orders, 'Sliding','paginator-slide-delivery');
		?>

    <br>
  </div>
</div>
<!-- Footer -->
<footer>
  <div class="large-12 columns ftr-bg">
      <p class="right">Powered by <a href= <?php echo $_SERVER['REQUEST_SCHEME'];?>."://www.futurescapetech.com/" target="_blank" class="fs"><img src="/delivery_assets/images/fs-icon.png" alt="FS" /></a></p>
  </div>
</footer>
<!-- Footer -->

<script src="/delivery_assets/js/vendor/modernizr.js"></script>
<script src="/delivery_assets/js/jquery.min.js"></script>
<script>
$(document).ready(function(){
	$('#search_order').on("input",function(){
		var val = $(this).val();
		if(val == '')
		{
			var href = "<?php echo $this->url('Delivery',array('action'=>'order','city'=>$loguser->city)); ?>";
			window.location.href = href ;
		}
		var data = 'val='+val;
		$.ajax({
			 url:"/delivery/searchorder",
			 type: "POST",
			 data : data,
			 beforeSend : function(){
					//$('.loader_change_status'+id).hide();
					//$('#loaderstatus_'+id).show(100);
			 },
			 success:function(result)
			 {
				//alert(result);
				var res = $.parseJSON(result);
				if(res.error)
				{
						alert(res.error);

						return false;
				}
				var data = "";
				$.each(res, function( index, value ){
					var class_name = "";var class_change = "";
					if(value.delivery_status == "Delivered")
					{
						class_change="no"; class_name="foundicon-checkmark success";
					}else {
						class_change="yes"; class_name="foundicon-remove error";
					}
					data += "<tr>";
					data += "<td class='user-name'>"+value.pk_order_no+"</td>";
					data += "<td class='user-email'>OM Nivas, Vashi Plaza, Navi Mumbai Sector - 17, Vashi Navi Mumbai - 400705 Maharashtra </td>";
					data += "<td class='user-phone right'><a href='#' data-delstatus='"+value.pk_order_no+"' data-changeclass='"+class_change+"' class='order_delivery_status'><i class='"+class_name+"'></i></a></td>";
					data += "</tr>";
				});
				$('#order_grid').empty().html(data);
				return false;
			 }
		 });

	});
	$(document).on("click",'.order_delivery_status',function (e) {
			e.preventDefault();
			var change = $(this).data("changeclass");
			if(change == 'yes')
			{
				var that =  $(this);
				var id = $(this).data("delstatus");
				data = 'id='+id;
				$.ajax({
					 url:"<?php echo $this->url('Delivery',array('action' => 'updatedeleverystatus')); ?>",
					 type: "POST",
					 data : data,
					 success:function(data)
					 {

						 that.find("i").removeClass('foundicon-remove error');
						 that.find("i").addClass('foundicon-checkmark success');
						 that.attr({ 'data-changeclass': 'no' });

					 }
				});

			}

	});
});
	</script>
<script src="/delivery_assets/js/jquery.ba-throttle-debounce.min.js"></script>
<script src="/delivery_assets/js/jquery.stickyheader.js"></script>
<script src="/delivery_assets/js/foundation.min.js"></script>
<script src="/delivery_assets/js/foundation/foundation.tooltip.js"></script>
<script>
 $(document).foundation();
</script>
<script src="/delivery_assets/js/foundation/foundation.dropdown.js"></script>
</body>
</html>
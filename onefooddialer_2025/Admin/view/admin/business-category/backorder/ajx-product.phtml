<div class="large-6 medium-6 columns pad0">
	<h1 class="mar0 fix-left">
	<input type="hidden" name="newdeliverychrg" id="newdeliverychrg" value="<?php echo $deliverycharges?>">
		<span class="right" id="menuTimes" data-menuallowed="<?php echo $menuAllowed[$menu]; ?>">
			Time: <?php echo date("h:i a",strtotime($this->accepted_menu->menu['menu'][$menu]['startTime']));?> To 
			<?php echo date("h:i a",strtotime($this->accepted_menu->menu['menu'][$menu]['endTime']));?>
		</span>
	</h1>
</div>
<div class="clearfix"></div>
	<div class="tab1-content active tab-content">
		<div class="inner-tab">
        	<h1>Select Your Meal</h1>	
	
	
			<div class="slider Extra">
		<!-- Group 1-->

		<!-- Meal 1-->
        <?php
     	$keys = array();
     	
     	if(isset($cart[$menu]) && is_array($cart[$menu]))
     	{
			$keys = array_keys($cart[$menu]);
     	}
     	
     	$group_ctr = 1;
     	
     	$meal = $meals[$menu];
     	
		$meal_count = count ( $meal ) - 1;
			// $meal_ctr = 0;
			// echo $meal_count;
			for($meal_ctr = 0; $meal_ctr <= $meal_count; $meal_ctr += 2) {

				$meal_group = array_slice ( $meal, $meal_ctr, 2 );
				echo '<div class="group' . $group_ctr . '">';
				echo '<figure class="crsl-item">';
				foreach ( $meal_group as $product ) {
					//$img_path = (! isset ( $product ['image_path'] ) && file_exists ( './public' . $product_path . $product ['image_path'] )) ? $product_path . $product ['image_path'] : $default_product_path;
					$img_path = (file_exists ( './public'.$product_path.$product['image_path']))? $product_path.$product['image_path']:$default_product_path;
					?>

                     <div class="tifinBox">
                     	<!-- <div class="tiffin_content"> -->
							 <div class="cusCheckBox">
	                               <input type="checkbox"
									id="checkbox<?php echo $product['pk_product_code'];?>" name="radiog_dark"
									<?php echo (in_array($product['pk_product_code'], $keys))?'checked':''; ?>
									data-amount="<?php echo $product['unit_price']; ?>"
									data-type="<?php echo $product['product_type']; ?>"
									data-name="<?php echo $product['name']; ?>"
									data-id="<?php echo $product['pk_product_code']; ?>"
									class="checkbox-check" /> 
								<label class="checkbox" for="checkbox<?php echo $product['pk_product_code'];?>"><?php echo $product['name']; ?></label>
							</div>
							<div class="backendBox"><img class="mealImg" src="<?php echo $img_path; ?>" class="center-block" alt="" /></div>
						
							<div class="tifinBottom clearfix">
								<div class="small-6 columns pad0">
									<div class="tifinCost">
										<!-- <i class="fa fa-rupee"></i> --> <?php  echo $this->currencyFormat($product['unit_price']);?>/-
									</div>
								</div>
									<div class="small-6 columns pad0">
										<div class="spinerBox">
											<div class="input-append spinner has-tip tip-top" data-trigger="spinner" title="Quantity" data-tooltip>
												<input type="text" id="quantity<?php echo $product['pk_product_code']; ?>" data-id="<?php echo $product['pk_product_code']; ?>"
												class="qunt" value="<?php echo (in_array($product['pk_product_code'], $keys))?$cart[$menu][$product['pk_product_code']]['quantity']:'1'; ?>"
												min="1" readonly />
												<div class="add-on" data-id="<?php echo $product['pk_product_code']; ?>">
													<a href="javascript:;" class="spin-up" data-spin="up">
														<i class="fa fa-sort-up"></i>
													</a>
													<a href="javascript:;" class="spin-down" data-spin="down">
														<i class="fa fa-sort-down"></i>
													</a>
												</div>
											</div>
										</div>
									</div>
							</div>
						<!-- </div> -->
						<div class="on_hover1">
								<a  class="tiffin_view" data-id="<?php echo $product['pk_product_code']; ?>">View Details</a>
							</div>
					  <div id="myModal" class="reveal-modal tiny" data-reveal>
					</div>
				</div>
        <?php

				$group_ctr ++;
			}
			echo ' </div></figure>';
		}
		?>
		</div>
	 <!--- Meal Tifin End  -->

	<!--- Extra Tifin Start  -->
	<?php 
	
	if(!empty($extra)) {?>
	
	<h1>Select Your Extra</h1>
	
	<div class="slider Extra">

	<?php
	
	$extra = $extras[$menu];
	
	$extra_count = count ( $extra ) - 1;
	// $meal_ctr = 0;
	
	for($extra_ctr = 0; $extra_ctr <= $extra_count; $extra_ctr += 2) {
	
		$extra_group = array_slice ( $extra, $extra_ctr, 2 );
		echo '<div class="group' . $group_ctr . '">';
		echo '<figure class="crsl-item">';
		foreach ( $extra_group as $product ) {
			//$img_path = (file_exists ( './public'.$product_path.$product['image_path']))? $product_path.$product['image_path']:$default_product_path;
			$img_path = (file_exists ( './public'.$product_path.$product['image_path']))? $product_path.$product['image_path']:$default_product_path;
	?>
	
     <div class="tifinBox">
		<div class="cusCheckBox">
                 <input type="checkbox"
					id="checkbox<?php echo $product['pk_product_code'];?>" name="radiog_dark"
					<?php echo (in_array($product['pk_product_code'], $keys))?'checked':''; ?>
					data-amount="<?php echo $product['unit_price']; ?>"
					data-type="<?php echo $product['product_type']; ?>"
					data-name="<?php echo $product['name']; ?>"
					data-id="<?php echo $product['pk_product_code']; ?>"
					class="checkbox-check" /> 
					<label class="checkbox" for="checkbox<?php echo $product['pk_product_code'];?>"><?php echo $product['name']; ?></label>
			</div>
			<div class="backendBox"><img class="extraImg" src="<?php echo $img_path; ?>" alt="" /></div>
			<div class="tifinBottom clearfix">
				<div class="small-6 columns pad0">
					<div class="tifinCost">
						<!-- <i class="fa fa-rupee"></i> --><?php  echo $this->currencyFormat($product['unit_price']);?>/-
					</div>
				</div>
				<div class="small-6 columns pad0">
					<div class="spinerBox">
						<div class="input-append spinner has-tip tip-top" data-trigger="spinner" title="Quantity" data-tooltip>
                             <input type="text" data-id="<?php echo $product['pk_product_code']; ?>" id="quantity<?php echo $product['pk_product_code']; ?>" class="qunt" value="<?php echo (in_array($product['pk_product_code'], $keys))?$cart[$product['pk_product_code']]['quantity']:'1'; ?>" min="1" readonly />
							<div class="add-on" data-id="<?php echo $product['pk_product_code']; ?>">
								<a href="javascript:;" class="spin-up" data-spin="up">
									<i class="fa fa-sort-up"></i>
								</a>
								<a href="javascript:;" class="spin-down" data-spin="down">
									<i class="fa fa-sort-down"></i>
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
       <?php  $group_ctr++;
			}
			echo ' </div></figure>';
			}
      ?>
     </div>
   <?php } ?>
	
	 </div>
        <!-- new inner divs -->
</div>   
	<script>
	jQuery(document).ready(function ($) {
		
    $('.Meal').slick({
        dots: false,
        infinite: false,
        speed: 300,
        slidesToShow: 2,
        slidesToScroll: 2,
        responsive: [{
                breakpoint: 1920,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 3,
                    infinite: false,
                    dots: false
                }
            },

            {
                breakpoint: 1366,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 2,
                    infinite: false,
                    dots: false
                }
            },

            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 2,
                    infinite: false,
                    dots: false
                }
            }, {
                breakpoint: 600,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 2,
                    infinite: false,
                    dots: false
                }
            }, {
                breakpoint: 480,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    infinite: false,
                    dots: false
                }
            }
        ]
    });


	}); 

	</script>

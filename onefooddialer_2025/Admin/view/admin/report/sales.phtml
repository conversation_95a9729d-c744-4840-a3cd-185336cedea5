<script>
function hideMainFields(){
	$('#filter_month').hide();
	$('#filter_quarter_number').hide();
	$('#filter_week_number').hide();
}
function afterMonthChanged(month_val){
	$('#filter_quarter_number').hide();
	var month = month_val;
	var year = $('#filter_year').val();
	 $.post("<?php echo $this->url('report',array('action' => 'get-ajax-weeks-using-month')); ?>",{"month":month,"year":year},function(result){
		 $('#filter_week_number').html(result.options);
		 $('#filter_week_number').show();
	});
}
function validFilter(){
	$("#subaction").val("");
	$("#filter_form").removeAttr("target");
	if(($('#filter1').is(':checked')) || ($('#filter2').is(':checked'))) {
		$('#filter_form').submit();
    }else{
    alert("Please select filter option");
    return false;
    }
}
$(function(){
	hideMainFields();
	<?php if( $this->filter->get('filter_year_type')->getValue()){
				if($this->filter->get('filter_year_type')->getValue() == 'monthly'){ ?>
					$('#filter_month').show();
					$('#filter_week_number').show();
	<?php 		}else if($this->filter->get('filter_year_type')->getValue() == 'quarterly'){ ?>
					$('#filter_quarter_number').show();	
	<?php 		}
	 } ?>
	$('#filter_year_type').on("change",function(){
		hideMainFields();
		if($(this).val() == 'monthly') {
			$('#filter_month').show();
			$('#filter_week_number').show();
		}
		else if($(this).val() == 'quarterly'){
			$('#filter_quarter_number').show();
		}
	});

	$('#filter1').change(function(){
		if($('#filter1').is(':checked')){
			$('.filtertype1 select').prop('disabled', false);
			$('.filtertype2 input[type=text],.filtertype2 select').prop('disabled', true);
		}
	});
	$('#filter2').change(function(){
		if($('#filter2').is(':checked')){
			$('.filtertype1 select').prop('disabled', true);
			$('.filtertype2 input[type=text],.filtertype2 select').prop('disabled', false);
		}
	});
	$('#filter_month').on("change",function(){
		var month_val = $(this).val();
		afterMonthChanged(month_val);
	});
	$('#filter_year').on("change",function(){
		hideMainFields();
		if($('#filter_year_type').val() == 'monthly') {
			afterMonthChanged($('#filter_month').val());
			$('#filter_month').show();
			$('#filter_week_number').show();
		}else if($('#filter_year_type').val() == 'quarterly') {
			$('#filter_quarter_number').show();
		}
	});
	 $("#printReportList").on('click',function(e){
		  e.preventDefault();
		  $("#subaction").val("print");
		  $("#filter_form").attr("action","/report/sales");
		  $("#filter_form").attr("target","_blank");
		  $('#filter_form').submit();
	  });

	  $("#exportReportList").on('click',function(e){
		  e.preventDefault();
		  $("#subaction").val("export");
		  $("#filter_form").attr("action","/report/export-pdf-save");
		  $("#filter_form").attr("target","_blank");
		  $('#filter_form').submit();
	  });
	  

});
</script>
<div id="content" class="clearfix">
        <div class="large-12 columns">
          <div class="filter">
              <?php $filter = $this->filter; ?>
			  <?php $filter->setAttribute('action', $this->url('report', array('action' => 'sales'))); ?>
			  <?php $filter->setAttribute('class','advance_search'); ?>
			  <?php $filter->prepare(); ?>
			  <?php echo $this->form()->openTag($filter); ?>
              <div class="row">
                <div class="medium-12 columns">
                  <div class="type left">
                   
                    <?php echo $this->formElement($filter->get('filter_sales_options')); ?>
                    
                    
                     <select name="location_code" id="location_code" class="left filterSelect">
        				<option value="all"> Select Delivery Location</option>
        				<option value="all">All</option>
						<?php 
		        			foreach($location_data as $locationkey=>$locationval){
		        		?>
		        		<option value="<?php echo $locationval['pk_location_code'];?>"><?php echo $locationval['location'];?></option>
		        		<?php 
	        				}
	        			?>
		       	 	</select>
		       	 	
		       	 	<select name="menu" id="menu" class="left filterSelect">
	                	 <option value="">Select Menu Type</option>
				        	<?php 
			        		foreach($this->menus as $menu){
			        			//$selected = ($this->menuSelected==$menu) ? "selected" : "";
			        	?>
			        		<option <?php //echo $selected;?> value="<?php echo $menu;?>"><?php echo ucfirst($menu);?></option>
			        	<?php 
		        		}
		        	?>
			        </select>
                      <?php echo $this->formElement($filter->get('filter_delivery_person')); ?> <!-- element form added by pardeep 28feb17 -->
                      <?php echo $this->formElement($filter->get('filter_payment_mode')); ?> 
                  </div>
                  
                  <div class="clearfix"></div>
                  
                  <div class="mt10">
                    <div class="left filtertype1" >
                      <div class="radioBut left">

                      <input id="filter1" name="filter_check" type="radio" value="1" <?php echo ( ( isset($_POST['filter_check']) && ($_POST['filter_check'] == 1) ) || !isset($_POST['filter_check']) ) ?'checked':'' ?> /></div>
                      <?php echo $this->formElement($filter->get('filter_year')); ?>
			          <?php echo $this->formElement($filter->get('filter_year_type')); ?>
			          <?php echo $this->formElement($filter->get('filter_month')); ?>
			          <?php echo $this->formElement($filter->get('filter_quarter_number')); ?>
			          <?php echo $this->formElement($filter->get('filter_week_number')); ?>
					</div>
                    <div class="left filtertype2">
                        <div class="radioBut left">

                        <input type="radio"  name="filter_check" id="filter2" value="2" <?php echo ( ( isset($_POST['filter_check']) && ($_POST['filter_check'] == 2)) || !isset($_POST['filter_check']) )?'checked':'' ?>/></div>
                        <?php echo $this->formlabel($filter->get('minDate')); ?>
                        <?php echo $this->formElement($filter->get('minDate')); ?>
                        <?php echo $this->formlabel($filter->get('maxDate')); ?>
                        <?php echo $this->formElement($filter->get('maxDate')); ?>
                        <button style="font-size:12px;" class="button left left5 dark-greenBg" data-text-swap="Wait.." type="button" id="submitButton" >Go</button>
                        <!-- <button class="button left tiny left5 dark-greenBg" data-text-swap="Wait.." type="submit">Go</button> -->
                        <?php //echo $this->formSubmit($filter->get('submit')); ?>
                      </div>


                  </div>
                </div>
              </div>
               <input type="hidden" name="subaction" id="subaction" value="" />
			  <input type="hidden" name="service" id="service" value="sales" />
			  <input type="hidden" name="exportUrl" id="exportUrl" value="report/sales" />
            <?php echo $this->form()->closeTag($filter) ?>
          </div>
          <div class="portlet box yellow">
            <div class="portlet-title">
              <h4><i class="fa fa-table"></i>Sales Report</h4>
              <ul class="toolOption">
                <li>
                  <div class="print">
                   	<button class="btn directExport"  data-exporttype="print"  id="exportPrint"><i class="fa fa-print" ></i>&nbsp;Print</button>&nbsp;&nbsp;
                    <button class="btn  dropdown" data-dropdown="dropPrint" ><i class="fa fa-print"></i>&nbsp;Export</button>
                   	 <ul id="dropPrint" data-dropdown-content class="f-dropdown">
                   	  <li data-tooltip class="has-tip tip-top columnModal"  data-id="sales" data-exporttype="xls" title="Export XLS"><a href="javascript:void(0);"  id="exportXLS"><i class="fa fa-file-excel-o" ></i></a></li>
                      <li data-tooltip class="has-tip tip-top columnModal"  data-id="sales" data-exporttype="pdf" title="Export PDF"><a href="javascript:void(0);"  id="exportPDF"><i class="fa fa-file-pdf-o" ></i></a></li>
                      <li data-tooltip class="has-tip tip-top directExport"  data-id="sales" data-exporttype="quickbook" title="Export Quickbook"><a href="javascript:void(0);"  id="exportQuickbook"><i class="fa fa-file-pdf-o" ></i></a></li>
                      <li data-tooltip class="has-tip tip-top directExport"  data-id="sales" data-exporttype="tally" title="Export Tally"><a href="javascript:void(0);"  id="exportTally"><i class="fa fa-file-pdf-o" ></i></a></li>
                    </ul>
                    <div id="myModal" class="reveal-modal custPopup"  data-reveal>
                  </div>
                </li>
              </ul>
            </div>
            <div class="portlet-body sales_data_table">
            
            <div class="filter">
                <div>
                        <a class="advance_search_click">Hide advance search</a>
                </div>
            </div>
				
            <table id="sales" class="display displayTable" width="100%">
                <thead>
                  <tr>
                    <th>Order No.</th>
                    <th>Customer Name</th>
                    <th>Phone</th>
                    <th>Email Address</th>
                    <th>Delivery Location</th>
                    <th>Meal</th>
                    <th>Amount <!-- <i class="fa fa-rupee"></i> --></th>
                    <th>Order Status</th>
                    <th>Delivery Status</th>
                    <th>Order Date</th>
                  </tr>
                </thead>
              </table>
                <table id="reportsales" class="display displayTable">
                    <tbody>
                     <tr>
                        <th>Total Meal Quantity</th>
                        <th>Total Delivered</th>
                        <th>Total Rejected</th>
                        <th>Total Amount <!-- <i class="fa fa-rupee"></i> --></th>
                     </tr>
                     <tr>
                        <td class="total"></td>
                        <td class="delivered"></td>
                        <td class="rejected"></td>
                        <td class="amount"></td>
                     </tr>
                    </tbody>
                </table>              
            </div>
          </div>
          <div class="clearBoth20"></div>
        </div>
      </div>

 <script type="text/javascript">


 	getSummaryReport = function(){

 		var params = {
		  	start:$('#minDate').val(),
		  	end:$('#maxDate').val(),
		  	filter_check:$('input:radio[name="filter_check"]:checked').val(),
		  	filter_year:$('#filter_year').val(),
		  	filter_year_type:$('#filter_year_type').val(),
		  	filter_month:$('#filter_month').val(),
		  	filter_week_number:$('#filter_week_number').val(),
		  	filter_delivery_option:$('#filter_sales_options').val(),
                        //filter_location_code:$('#location_code').val(),
		  	//filter_menu:$('#menu').val(),
                        //filter_sales_options:$('#filter_sales_options').val(),
                        location_code:$('#location_code').val(),
                        menu:$('#menu').val(),
		  	kitchen:$('#selectkitchen').val(),
                       filter_delivery_person:$('#filter_delivery_person').val(),  //applied filter pradeep 28 feb 17
                       filter_payment_mode:$('#filter_payment_mode').val()
		}	

       $.ajax({
			url:'/report/ajax-summary',
			method:'POST',
			data:params,
			success:function(data){
				$('.total').html(data['TotalSalesQuantity']?data['TotalSalesQuantity']:0);
				$('.delivered').html(data['Delivered']);
				$('.rejected').html(data['Rejected']);
				$('.amount').html(data['Amount']);
			},
			error:function(){
				alert("Error");
			}
       });    

 	}

      $(document).ready(function() {
    	  
        var aoColumns = [];
        
        $('#sales thead th').each( function () {
            
            if ( $(this).hasClass('no_sort')) {
                aoColumns.push( { "bSortable": false } );
            } else {
            	aoColumns.push(null);
            }
      
        });

		var kitchenScreen = $('#selectkitchen').val();
		
        var table  = $('#sales').dataTable( {
            "processing": true,
            "serverSide": true,
            "bDestroy" :true,
            "aoColumns":aoColumns,
            "aaSorting": [[0,'desc']],
            "ajax": { 
	            "url":"/report/ajax-sales",
	            "data": function ( d ) {
	                d.filter_sales_options = $('#filter_order_options').val(),
	                d.filter_year = $('#filter_year').val(),
	                d.filter_check = $('input:radio[name="filter_check"]:checked').val(),
	                d.filter_year_type = $('#filter_year_type').val(),
	                d.filter_month = $('#filter_month').val(),
	                d.filter_week_number = $('#filter_week_number').val(),
	                d.minDate = $('#minDate').val(),
	                d.maxDate = $('#maxDate').val(),
	                d.kitchenscreen = $('#selectkitchen').val();
	                d.type = $('#filter_sales_options').val();
	                d.location_code = $('#location_code').val();
	                d.menu = $('#menu').val();
                        d.filter_delivery_person = $('#filter_delivery_person').val(); //applied filter pradeep 28 feb 17
                        d.filter_payment_mode = $('#filter_payment_mode').val();
	            }
	   		}
        });

		getSummaryReport();

     	$('#submitButton').click(function(e){

    		e.preventDefault();
    		$("#subaction").val("");
    		$("#filter_form").attr("action","/report/sales");
    		$('#filter_form').removeAttr("target");
    		if(($('#filter1').is(':checked')) || ($('#filter2').is(':checked'))) {
    			//$('#filter_form').submit();
    			table.api().ajax.reload();
    			getSummaryReport();
    	    }else{
        	    alert("Please select filter option");
        	    return false;
    	    }
    	});         
      
	 	$(document).on("click",".columnModal",function(){
                    
			var table = $(this).data('id');
			var exporttype = $(this).data('exporttype');
			var datastring = $("#filter_form").serialize();
			$('#myModal').foundation('reveal', 'open', {
			    url: '/report/exportData',
			    data: {table: table,form:datastring,exporttype:exporttype}
			});
			return false;
		}); 


		 $(document).on("click",".directExport",function(){

			 var exporttype = $(this).data('exporttype');
			 $("#subaction").val(exporttype);
			  $("#filter_form").attr("target", "_blank");
			 $("#filter_form").submit();
			
		}); 
		 $(document).on('opened.fndtn.reveal', '[data-reveal]', function() {
				$(".table-parent").niceScroll();
		});


		 $(document).on('click','#export',function(e){
	    		e.preventDefault();
	    		if(document.querySelectorAll('input[type="checkbox"]:checked').length==0){
	    			alert('Please select columns to export');
	    			return false;
	    		}
	    		else
	    		{	
	    			var values =[]; 
	    			var type = $("input[name=export_type]:checked").val();
	    			values = $('.checkbox:checked.checkbox').map(function () {
	    				  return this.id;
	    			}).get();
	    			if(type=='pdf' && values.length>8){
	    				alert("You can select maximum 8 columns");
	    				return false;
	    			}
	    		    var fromTable = $("#table").val();
	    			$("#selected_columns").val(values.join());
	    			$("#exportForm").attr("target", "_blank");
	    			$("#exportForm").submit();
	    			return false;
	    		}
	    	});
      });
</script>


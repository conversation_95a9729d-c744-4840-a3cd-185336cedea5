<script>
function hideMainFields(){
	$('#filter_month').hide();
	$('#filter_quarter_number').hide();
	$('#filter_week_number').hide();
}
function afterMonthChanged(month_val){
	$('#filter_quarter_number').hide();
	var month = month_val;
	var year = $('#filter_year').val();
	 $.post("<?php echo $this->url('report',array('action' => 'get-ajax-weeks-using-month')); ?>",{"month":month,"year":year},function(result){
		 $('#filter_week_number').html(result.options);
		 $('#filter_week_number').show();
	});
}
function validFilter(){
	$("#subaction").val("");
	$("#filter_form").removeAttr("target");
	if(($('#filter1').is(':checked')) || ($('#filter2').is(':checked'))) {
		$('#filter_form').submit();
    }else{
    alert("Please select filter option");
    return false;
    }
}
$(function(){
	hideMainFields();
	<?php if( $this->filter->get('filter_year_type')->getValue()){
				if($this->filter->get('filter_year_type')->getValue() == 'monthly'){ ?>
					$('#filter_month').show();
					//$('#filter_week_number').show();
	<?php 		}else if($this->filter->get('filter_year_type')->getValue() == 'quarterly'){ ?>
					$('#filter_quarter_number').show();
	<?php 		}
	 } ?>
	$('#filter_year_type').on("change",function(){
		hideMainFields();
		if($(this).val() == 'monthly') {
			$('#filter_month').show();
			//$('#filter_week_number').show();
		}
		else if($(this).val() == 'quarterly'){
			$('#filter_quarter_number').show();
		}
	});
	/* $('#submitButton').click(function(e){
		e.preventDefault();
		$("#subaction").val("");
		$("#filter_form").attr("action","/report/invoice");
		$('#filter_form').removeAttr("target");
		if(($('#filter1').is(':checked')) || ($('#filter2').is(':checked'))) {
			$('#filter_form').submit();
	    }else{
	    alert("Please select filter option");
	    return false;
	    }
	}); */
	$('#filter1').change(function(){
		if($('#filter1').is(':checked')){
			$('.filtertype1 select').prop('disabled', false);
			$('.filtertype2 input[type=text],.filtertype2 select').prop('disabled', true);
		}
	});
	$('#filter2').change(function(){
		if($('#filter2').is(':checked')){
			$('.filtertype1 select').prop('disabled', true);
			$('.filtertype2 input[type=text],.filtertype2 select').prop('disabled', false);
		}
	});
	/*$('#filter_month').on("change",function(){
		var month_val = $(this).val();
		afterMonthChanged(month_val);
	});*/
	$('#filter_year').on("change",function(){
		hideMainFields();
		if($('#filter_year_type').val() == 'monthly') {
			afterMonthChanged($('#filter_month').val());
			$('#filter_month').show();
			$('#filter_week_number').show();
		}else if($('#filter_year_type').val() == 'quarterly') {
			$('#filter_quarter_number').show();
		}
	});
	 $("#printReportList").on('click',function(e){
		  e.preventDefault();
		  $("#subaction").val("print");
		  $("#filter_form").attr("action","/report/invoice");
		  $("#filter_form").attr("target","_blank");
		  $('#filter_form').submit();
	  });

	  $("#exportReportList").on('click',function(e){
		  e.preventDefault();
		  $("#subaction").val("export");
		 // $("#filter_form").attr("action","/report/export-pdf-save");
		  $("#filter_form").attr("target","_blank");
		  $('#filter_form').submit();
	  });

	  $("#exportxlsReportList").on('click',function(e){
		  e.preventDefault();
		  $("#subaction").val("xls");
		 // $("#filter_form").attr("action","/report/export-pdf-save");
		  $("#filter_form").attr("target","_blank");
		  $('#filter_form').submit();
	  });

});
</script>
<div id="content" class="clearfix">
        <div class="large-12 columns">
          <div class="filter">
              <?php $filter = $this->filter; ?>
			  <?php $filter->setAttribute('action', $this->url('report', array('action' => 'invoice'))); ?>
			  <?php $filter->setAttribute('class','advance_search'); ?>
			  <?php $filter->prepare(); ?>
			  <?php echo $this->form()->openTag($filter); ?>
              <div class="row">
                <div class="medium-12 columns">
                  <div class="type left">
                  	<!-- <label for="right-label" class="left inline" style="margin:0px">Type &nbsp;</label> -->
                    <?php echo $this->formElement($filter->get('filter_invoice_options')); ?>
                  </div>
                  <div class="right">
                    <div class="left filtertype1">
                      <div class="radioBut left">

                      <input id="filter1" name="filter_check" type="radio" value="1" <?php echo ( ( isset($_POST['filter_check']) && ($_POST['filter_check'] == 1))  || !isset($_POST['filter_check']) )?'checked':'' ?> /></div>
                      <?php echo $this->formElement($filter->get('filter_year')); ?>
			          <?php echo $this->formElement($filter->get('filter_year_type')); ?>
			          <?php echo $this->formElement($filter->get('filter_month')); ?>
			          <?php echo $this->formElement($filter->get('filter_quarter_number')); ?>
			          <?php //echo $this->formElement($filter->get('filter_week_number')); ?>
					</div>
                      <div class="left filtertype2">
                        <div class="radioBut left">

                        <input type="radio"  name="filter_check" id="filter2" value="2" <?php echo (isset($_POST['filter_check']) && ($_POST['filter_check'] == 2))?'checked':'' ?>/></div>
                        <?php echo $this->formlabel($filter->get('minDate')); ?>
                        <?php echo $this->formElement($filter->get('minDate')); ?>
                        <?php echo $this->formlabel($filter->get('maxDate')); ?>
                        <?php echo $this->formElement($filter->get('maxDate')); ?>
                         <button style="font-size: 12px;" class="button left left5 dark-greenBg" data-text-swap="Wait.." type="button" id="submitButton" >Go</button>
                        <!-- <button class="button left tiny left5 dark-greenBg" data-text-swap="Wait.." type="submit">Go</button> -->
                        <?php //echo $this->formSubmit($filter->get('submit')); ?>
                      </div>


                  </div>
                </div>
              </div>
              <input type="hidden" name="subaction" id="subaction" value="" />
			  <input type="hidden" name="service" id="service" value="invoice" />
			  <input type="hidden" name="exportUrl" id="exportUrl" value="report/invoice" />
            <?php echo $this->form()->closeTag($filter) ?>
          </div>
          <div class="portlet box yellow">
            <div class="portlet-title">
              <h4><i class="fa fa-table"></i>Invoice & Collection Report</h4>
              <ul class="toolOption">
                <li>
                  <div class="print">
                    <button class="btn dropdown" data-dropdown="dropPrint"><i class="fa fa-print"></i>&nbsp;Print/Export</button>
                    <ul id="dropPrint" data-dropdown-content class="f-dropdown exportPrint">
                      <li data-tooltip class="has-tip tip-top" title="Print"><a href="javascript:void(0);"  id="printReportList"><i class="fa fa-print" ></i></a></li>
                      <li data-tooltip class="has-tip tip-top" title="Export PDF"><a href="javascript:void(0);"  id="exportReportList"><i class="fa fa-file-pdf-o" ></i></a></li>
                      <li data-tooltip class="has-tip tip-top" title="Export XLS"><a href="javascript:void(0);"  id="exportxlsReportList"><i class="fa fa-file-excel-o" ></i></a></li>
                    </ul>
                  </div>
                </li>
              </ul>
            </div>
            <div class="portlet-body sales_data_table">
            	<div class="filter">
					<div>
						<a class="advance_search_click"> Hide advance Search </a>
					</div>
				</div>
            
              <table id="invoice" class="display displayTable" style="width: 100%">
                <thead>
                  <tr>
                    <th>Invoice No.</th>
                    <th>Customer Name</th>
                    <th>Invoice Date</th>
                    <th>Due Date</th>
                    <th>Amount (A) <!-- <i class="fa fa-rupee"></i> --></th>
                    <th>Discount (B) <!-- <i class="fa fa-rupee"></i> --></th>
                    <th>Tax (C) <!-- <i class="fa fa-rupee"></i> --></th>
                    <th>Delivery Charges (D) <!-- <i class="fa fa-rupee"></i> --></th>
                    <th>Total amount</th>
                    <th>Paid amount</th>
                    <th>Due amount</th>
                    <th>Status</th>
                  </tr>
                </thead>
              </table>
              
			  <table id="invoicesummary" class="display displayTable">
				<tbody>
					<tr>
						<th>Total Invoice Amount (A)<!-- <i class="fa fa-rupee"></i> --></th>
						<th>Total Sales Discount (B)<!-- <i class="fa fa-rupee"></i> --></th>
						<th>Total Tax (C)<!-- <i class="fa fa-rupee"></i> --></th>
						<th>Total Amount (A+C-B) <!-- <i class="fa fa-rupee"></i> --></th>
						<th>Total Amount Paid <!-- <i class="fa fa-rupee"></i> --></th>
						<th>Total Amount Due <!-- <i class="fa fa-rupee"></i> --></th>
					</tr>
					<tr>
						<td class='inv_amt'></td>
						<td class='discount'></td>
						<td class='tax'></td>
						<td class='net_amt'></td>
						<td class='amt_paid'></td>
						<td class='amt_due'></td>
					</tr>
				</tbody>
			  </table>              
            </div>
          </div>
          <div class="clearBoth20"></div>
        </div>
      </div>

  <script type="text/javascript">
      
      $(document).ready(function() {
          getInvoiceSummaryReport1 = function(){
            //alert($("input[name='status']"));
              var params = {                 
                    start:$('#minDate').val(),
                    end:$('#maxDate').val(),
                    filter_check:$('input:radio[name="filter_check"]:checked').val(),
                    filter_year:$('#filter_year').val(),
                    filter_year_type:$('#filter_year_type').val(),
                    filter_month:$('#filter_month').val(),
                    //filter_week_number:$('#filter_week_number').val(),
                    filter_quarter_number:$('#filter_quarter_number').val(),
                    status:$('#filter_invoice_options').val(),
                   // kitchen:'1'
                   
              }
         //          console.debug(params);debugger;
          $.ajax({
				url:'/report/ajax-invoice-summary',
				method:'POST',
				data:params,
				success:function(data){
					
/* 					var net_amt = parseFloat(data[0]['tl_inv_amt']) + (parseFloat(data[0]['tl_tax']) - (parseFloat(data[0]['tl_sale_discount'])));
					if(data[0]['tl_inv_amt'] > 0){
						$('.inv_amt').html(data[0]['tl_inv_amt']);
					}else{
						$('.inv_amt').html('0'); 
					}
					if(data[0]['tl_sale_discount'] > 0){$('.discount').html(data[0]['tl_sale_discount']);}else{$('.discount').html('0'); }
					if(data[0]['tl_tax'] > 0){$('.tax').html(data[0]['tl_tax']);}else{$('.tax').html('0'); }
					if(net_amt > 0){$('.net_amt').html(net_amt.toFixed(2));}else{$('.net_amt').html('0'); }
					if(data[0]['tl_amt_paid'] > 0){$('.amt_paid').html(data[0]['tl_amt_paid']);}else{$('.amt_paid').html('0'); }
					if(data[0]['tl_amt_due'] > 0){$('.amt_due').html(data[0]['tl_amt_due']);}else{$('.amt_due').html('0'); }
 */
				    $('.inv_amt').html(data['tl_inv_amt']);
				    $('.discount').html(data['tl_sale_discount']);
				    $('.tax').html(data['tl_tax']);
				    $('.net_amt').html(data['net_amt']);
				    $('.amt_paid').html(data['tl_amt_paid']);
				    $('.amt_due').html(data['tl_amt_due']);
				},
				error:function(){
					alert("Error");
				}
         });           

    }

    	  var aoColumns = [];
          $('#invoice thead th').each( function () {
              if ( $(this).hasClass('no_sort')) {
                  aoColumns.push( { "bSortable": false } );
              } else {
                  aoColumns.push( null );
              }
          } );

          table =   $('#invoice').dataTable( {
              "processing": true,
              "serverSide": true,
              "bDestroy" :true,
              "aoColumns":aoColumns,
              "aaSorting": [[0,'desc']],
         
              "ajax": {
                  "url":"/report/ajax-invoice",
                  "data": function (d,loadScroll){
                     // d.filter_check = $('#filter1').val();
                      d.filter_check = $('input:radio[name="filter_check"]:checked').val(),
                      d.filter_year = $("#filter_year").val();
                      d.filter_year_type = $("#filter_year_type").val();
                      d.filter_month = $("#filter_month").val();
                      d.filter_quarter_number = $("#filter_quarter_number").val();
                      d.minDate = $("#minDate").val();
                      d.maxDate = $("#maxDate").val();
                      d.filter_invoice_options = $("#filter_invoice_options").val();
                      d.kitchenscreen = $('#selectkitchen').val();
                  },
                 
      		} 
            });
              getInvoiceSummaryReport1();
              
             //getInvoiceSummaryReport1();
              
          $('#submitButton').click(function(e){

        	e.preventDefault();
       		$("#subaction").val("");
       		$('#filter_form').removeAttr("target");
       		if(($('#filter1').is(':checked')) || ($('#filter2').is(':checked'))) {

           		if($('#filter2').is(':checked')){

   					if($('#minDate').val() == '' && $('#maxDate').val()=='')
               		{
                   		alert('Please select date'); return false;

                   	}                      
               	}         	
                        
       			getInvoiceSummaryReport1();
                        table.api().ajax.reload();

       			
       	    }else{
       	    	alert("Please select filter option");
       	    	return false;
       	    }
       	   
				
           });
          
      });
</script>



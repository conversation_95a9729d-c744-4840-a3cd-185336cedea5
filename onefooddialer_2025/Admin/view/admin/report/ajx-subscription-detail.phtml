<?php 
if(!empty($subscriptionDetails) && count($subscriptionDetails) > 0){
    $utility = \Lib\Utility::getInstance();
?>
<h5 class="headingTbl">Order Details</h5>
<table cellpadding="5" cellspacing="0" border="0" style="padding-left:50px;"  class="display collapseTbl ">
        <thead>
            <tr>
                <th>Order No</th>
                <th>Location</th>
                <th>Subscribed On</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th>Meals Delivered</th>
                <th>Meals Pending</th>

               <?php  /* <th>Order Status</th> */ ?>

            </tr>
        </thead>
        <?php 
        foreach ($subscriptionDetails as $detail){
        ?>
        <tr>
            <td><?php echo $detail['order_no'];?></td>
            <td><?php echo $detail['location_name'];?></td>
            <td><?php echo $utility->displayDate($detail['created_date'],$settings['DATE_FORMAT']);?></td>
            <td><?php echo $utility->displayDate($detail['start_date'],$settings['DATE_FORMAT']);?></td>
            <td><?php echo $utility->displayDate($detail['end_date'],$settings['DATE_FORMAT']);?></td>
            <td><?php echo $detail['meals_delivered'];?></td>
            <td><?php echo $detail['meals_pending'];?></td>

           <?php  /*<td><?php echo ucfirst($detail['order_status']);?></td> */ ?>

        </tr>
        <?php 
        }
        ?>
</table>
<?php 
}
?>
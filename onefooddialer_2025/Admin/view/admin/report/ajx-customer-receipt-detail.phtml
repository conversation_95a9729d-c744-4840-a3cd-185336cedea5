<?php 
$utility = \Lib\Utility::getInstance();
if(!empty($receiptDetails) && count($receiptDetails) > 0){
    
?>
<h5 class="headingTbl">Receipt Details</h5>
<table cellpadding="5" cellspacing="0" border="0" style="padding-left:50px;"  class="display collapseTbl ">
    	<thead>
	    	<tr>
	            <th width="10%"> Date </th>
	            <th>Description</th>
	            <th>Payment Mode</th>
	            <th>Transacted By</th>
                <th>Reference No.</th>
	            <th>Amt Paid (Cr)</th>
	        </tr>
        </thead>
        <?php 
        foreach ($receiptDetails as $receipt){
        ?>
        <tr>
            <td><?php echo $utility->displayDate($receipt['created_date'],$settings['DATE_FORMAT']);?></td>
            <td><?php echo $receipt['description'];?></td>
            <td><?php echo $receipt['payment_type'];?></td>
            <td><?php echo $receipt['context'];?></td>
            <td><?php echo (empty($receipt['reference_no']) ) ? " - " : ( (empty($receipt['bank_name'])) ? $receipt['reference_no'] : strtoupper($receipt['bank_name']).": ".$receipt['reference_no'] ) ;?></td>
            <td><?php echo $utility->getLocalCurrency($receipt['wallet_amount']);?></td>
        </tr>
        <?php 
        }
        ?>
</table>
<?php 
}
if(!empty($receiptOrderDetails) && count($receiptOrderDetails) > 0){
?>
<h5 class="headingTbl">Order Details</h5>
<table cellpadding="5" cellspacing="0" border="0" style="padding-left:50px;"  class="display collapseTbl ">
        <thead>
            <tr>
                <th>Order No</th>
                <th>Order Menu</th>
                <th>Meals</th>
                <th>Quantity</th>
                <th>Amount</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th>Payment Mode</th>
            </tr>
        </thead>
        <?php 
        foreach ($receiptOrderDetails as $orderReceipt){
        ?>
        <tr>
            <td><?php echo $orderReceipt['order_no'];?></td>
            <td><?php echo $orderReceipt['order_menu'];?></td>
            <td><?php echo $orderReceipt['meals'];?></td>
            <td><?php echo $orderReceipt['qty'];?></td>
            <td><?php echo $utility->getLocalCurrency($orderReceipt['net_amount']);?></td>
            <td><?php echo $utility->displayDate($orderReceipt['start_date'],$settings['DATE_FORMAT']);?></td>
            <td><?php echo $utility->displayDate($orderReceipt['end_date'],$settings['DATE_FORMAT']);?></td>
            <td><?php echo $orderReceipt['payment_mode'];?></td>
        </tr>
        <?php 
        }
        ?>
</table>
<?php } ?>
 <!-- popup-->
 	<form name="frmsendnotification" id="frmsendnotification">
 	<h1 style="margin-bottom: 0">Send SMS to selected <span class="num"><?php echo $count; ?></span> Customers</h1>
	<table class="large-12 small-12 medium-12 columns mt15">
		<tr>
			<th width="80%"> Message </th>
			<th width="20%"> Select  </th>
		</tr>
		<?php foreach ($sms_templates as $template){
			//$template_msg = preg_replace('/\#[a-zA-Z][a-zA-Z0-9_-]*\#/', '<input type="text" class="popuptext checkbox'.$template['sms_template_id'].'" name="var_'.$template['sms_template_id'].'[]">', $template['sms_content']);
		    if($template['template_key']=='firstmeal_notification'){
                $template_msg = preg_replace('/\#order_date\#/', '<input type="text" class="popuptext checkbox'.$template['sms_template_id'].'" name="var_'.$template['sms_template_id'].'[]" value='.$filter['orderdate'].' >', $template['sms_content']);
                $template_msg = preg_replace('/\#type_of_order\#/', '<input type="text" class="popuptext checkbox'.$template['sms_template_id'].'" name="var_'.$template['sms_template_id'].'[]" value='.$filter['menu'].' >', $template_msg);
            }else{
                $template_msg = preg_replace('/\#[a-zA-Z][a-zA-Z0-9_-]*\#/', '<input type="text" class="popuptext checkbox'.$template['sms_template_id'].'" name="var_'.$template['sms_template_id'].'[]">', $template['sms_content']);    
            }			
		?>
		<tr>
			<td><?php echo $template_msg;?></td>
			<td><input type="checkbox" class='checkbox noticheck' name="<?php echo 'chk_'.$template['sms_template_id'].'[]'?>" id="<?php echo $template['sms_template_id'];?>"/></td>
		</tr>
		<?php }?>
	</table>
	<div class="right">
		<div id="inner-tab">
		</div>
		<button class="sendAll" id="send_sms_only" name="send_sms_only" type="button"> <i class="fa fa-file-text-o"></i> Send SMS </button>
	</div>
	<div class="clearBoth5"></div>	
	<input type="hidden" name="order_date" class="ord_date" id="order_date" value="<?php echo $filter['orderdate'];?>" >
	<input type="hidden" name="menu" class="menu" id="menu" value="<?php echo $filter['menu'];?>" >
	<div class="right">
		<div id="inner-tab">
		</div>
	</div>
	</form>
	  <a class="close-reveal-modal">&#215;</a>
  
<script>

$(document).foundation();

	$('a.custom-close-reveal-modal').click(function(){
	  $('#myModal1').foundation('reveal', 'close');
	
	});

	$(document).ready(function() {


            	$('.sendAll').click(function(){

            		var order_date = $(".ord_date").val();
            		var menu = $(".menu").val();
            		var operation = 'send';
            		var action = $(this).attr("id");

            		if(action == 'send_sms_only') {
                		if($('.noticheck:checked').size()==0){
                			
                			alert('Please Select messsage to be send');
                			return false;
                			
                		}else{
                			
                			var smsArr = [];
                			var valid = true;	
                			/**
                				Iterate all checked checkboxes
                			**/
                			$('.checkbox').each(function(){

                				if($(this).is(":checked")){
                					
                					postArray = [];
                					var templateId = $(this).attr('id');
                					var album_text = [];
                					var name = 'var_'+templateId+'[]';

                					/**
                						iterate all input with same name 
                					**/
                					 $("input[name='"+name+"']").each(function() {
                					    var value = $(this).val();
                					  	if(value==''){
                					  		valid = false;
                					  	}
                					  	
                					  	if(valid == true)
                						{
                					  		 postArray.push(value);
                					  	}	
                					  
                					});
                					/**
                						if none of the template variable is blank then only push in smsArr
                					**/
                					if(valid == true)
                					{	
                						smsArr.push({str : postArray,templateId:templateId});
                					}
                				} 
                			});

                			/*
                			 if any of the selected template variable is blank
                			*/
                			
                			if(valid == false)
                			 {
                			  	  alert("Please fill all values");
                			 }
                			else
                			{
                				$.ajax({
                					 url:"<?php echo $this->url('report',array('action' => 'sendNotification')); ?>",
                					 type: "POST",
                					 async: false,
                					 beforeSend : function(){
                						 $("#inner-tab").html("<img style='margin-top:5px;' src='/front/images/ajax-loader.gif' />");
                						 $("#sendAll").attr("disabled","disabled");
                					},
                					data : {templates:smsArr,order_date:order_date,operations:operation,menu:menu},
                					success:function(data)
                					{
                						if(data.success)
                						{	  
                    						alert(data.msg);
                    						$("#inner-tab").hide();
                							//window.location.href ='/customer';
                						}else{
                							alert(data.msg);
                							//window.location.href ='/customer';
                						}
                					}
                				});
                			}	
                			return false;  
               			}
            		}
            	});

            	$(":radio, :checkbox").uniform();
                   
    });
</script>
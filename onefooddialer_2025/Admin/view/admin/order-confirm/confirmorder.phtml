<?php $utility = \Lib\Utility::getInstance();?>
	
<!-- BEGIN CONTAINER -->
<div class="page-container clearfix"> 
  
    <!-- BEGIN PAGE CONTAINER-->
    <div class="container-fluid"> 
      <!-- BEGIN PAGE HEADER-->
      
  
      <!-- END PAGE HEADER-->
      <div>
      
      <div id="msg2"></div>
      
      <form name="orderconf" id="orderconf">
	      	<table style="width:100%;align:centre" id="confirmord" >
	      		<tr><th style="align:centre;width:100%" colspan="2"><h3 class="page-title text-center tiffin_name"> Order Details </h3></th></tr>
						<tr>
							<th>Order No</th><input type="hidden" name="orderid" id="orderid" <?php if($orderdetails['temp_order_id']==0){ $orderidhdn = $orderdetails['temp_preorder_id'];}else{$orderidhdn  = $orderdetails['temp_order_id'];};?> value="<?php echo $orderidhdn;?>" /> 
							<input type="hidden" name="orderflg" id="orderflg" <?php if($orderdetails['temp_order_id'] == 0){ $ordertype = "preorder";}else{$ordertype = "order";};?>  value="<?php echo $ordertype; ?>" />
							<input type="hidden" name="orderidfrm" id="orderidfrm" value="<?php echo $orderid;?>">
							<input type="hidden" name="amount" id="amount" value="<?php echo $amount;?>">
							<input type="hidden" name="customercode" id="customercode" value="<?php echo $customercode;?>">
							<input type="hidden" name="pay_method" id="pay_method" value="<?php echo $orderdetails['type'];?>">
							<input type="hidden" name="ref_no" id="ref_no" value="<?php echo $orderdetails['reference_no'];?>">
							
							<td><?php echo $orderidhdn;?></td>
						</tr>
						<tr>
							<th>Customer Name</th>
							<td><?php echo $orderdetails['customer_name']?></td>
						</tr>
						<tr>
							<th>Order Date</th>
							<td>  <?php
		
									$dates_all = explode(',',$orderdetails['order_days']);
							
									foreach ($dates_all as $day){
										$date_arr[] = date($dateformat,strtotime($day));
									}
									$datearray = implode(', ',$date_arr);
							
							echo $datearray ; ?> </td>
						</tr>
						<tr>
							<th>Menu</th>
							<td><?php echo $orderdetails['order_menu'];?></td>
						</tr>
						<tr>
							<th>Amount</th>
							<td><?php echo $utility->getLocalCurrency($amount);?></td>
						</tr>
						<tr>
							<th>Payment Type</th>
							<td><?php echo $orderdetails['type'];?></td>
						</tr>
						<tr>
							<th>Reference No</th>
							<td><?php if($orderdetails['type'] == "cash" || $orderdetails['type'] == "online"){ echo "NA";}else{echo $orderdetails['reference_no'];}?></td>
						</tr>
						<tr>
							<th>Order Status</th>
							<td><?php echo $orderdetails['status'];?></td>
						</tr>
						<tr>
							<td colspan = "2"><input type="radio" class="chk" name="confpayment" id="confwithoutpayment" value="withoutpayment">  Confirm Without receiving payment </td>
						</tr>
						<tr>
							<td colspan = "2"><input type="radio" class="chk" name="confpayment" id="confwithpayment" value="withpayment"> Payment Received (Payment of <?php echo $utility->getLocalCurrency($amount);?> will be credited to customer wallet.)</td>
						</tr>
						<tr>
							<td colspan="2"><div class="loaderflg"><input type="button" name="btnsubmit" id="btnsubmit" style="align: center;" class="secondary button" value="Confirm Order"></div></td>
						</tr>
	      	 </table>
      	</form>
      </div>
      
       <div class="clearfix"></div>
     	
      </div>

 	  <a class="close-reveal-modal">&#215;</a>
 	  
</div>

<script type="text/javascript">

$("#btnsubmit").on("click",function(){

	var orderid = $("#orderid").val();
	var orderflag = $("#orderflg").val();

	var orderidfrm = $("#orderidfrm").val();
	var amount = $("#amount").val();

	var custcode = $("#customercode").val();

	var pay_method = $("#pay_method").val();
	var ref_no = $("#ref_no").val();

	var paymenttype = $( "input:checked" ).val();

	var formdata = $(this).serialize();

	var url = "/orderconfirm/confirmorder"; 
		
	if($('.chk').is(':checked'))
	{
	
	 /* if($("input[name='confpayment']:checked"))
	 {
		 var paytype = $("input[name='bill']:checked").attr('id'); */

		// alert("ff");return false;
			$.ajax({
				url 	: url,
				type 	:'POST',
				data 	: {orderid : orderid,orderflag:orderflag,orderidfrm:orderidfrm,amount:amount,custcode:custcode,paymenttype:paymenttype,pay_method:pay_method,ref_no:ref_no},
				beforeSend : function(){

					 $(".loaderflg").html("<img style='margin-top:5px;' src='/front/images/ajax-loader.gif' />");
					
				 },
				success	: function(response)
				{
					if(response=="success")
					{
// 						$('#msg2').html('<div data-alert="" class="alert alert-success"><div style="color:red">Order Confirmed Successfully</div></div>');
// 						setTimeout(function(){
// 							$(".close-reveal-modal").trigger("click");
// 						},3000);
						
						window.location.reload();
// 						table.api().ajax.reload();
					}
					
				}
			});
	// }
	}
	else
	{
		alert("Please select the payment option");
		return false
	}
	return false;
});


$(document).foundation();

$('a.custom-close-reveal-modal').click(function(){

	$('#myModal').foundation('reveal', 'close');

});

</script>



<!-- END CONTAINER --> 
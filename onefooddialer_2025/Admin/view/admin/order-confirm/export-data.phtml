<form id="exportForm" action="exportData" method="post">

<table class="large-12 small-12 medium-12 columns mt15" style="margin-bottom: 0">
<tr>
<th width="75%"> Columns </th>
<th width="25%"><input type="checkbox"  id="selectAll"/>Select to Export All</th>
</tr>

<tr>
	<td colspan="2">
		<div style="height:500px;overflow:hidden;width:100%;overflow-y: auto;">
			<table class="large-12 small-12 medium-12 columns mt15" style="margin-bottom: 0; width:100%">
				<?php foreach ($columns as $col){ 
				$checked ='';
				$disabled='';
				?>
				<tr>
				<td><?php
				if( $col=='pk_order_no'){
					$checked='checked=checked';
				//	$disabled='disabled=true';
				} 
				$colname = str_replace("_", " ", $col);
				$colname = ($colname == 'pk order no')?'order no':$colname;
				echo ucwords($colname);
				?></td>
				<td><input type="checkbox" class="checkbox" name="columns[]" id="<?php echo $col;?>" <?php echo $checked;?> /></td>
				</tr>

			<?php }?>
			</table>
		</div>
	</td>
</tr>

</table>

<div class="left exportoption mt15">

<span class="inline left"><input type="radio"  name="export_type" value="pdf" id="pdf">  <img src="/admin/images/pdf.png" alt="" class="left mr5"> <label class="left" for="pokemonGrey">PDF</label></span>
<span class="inline left"><input type="radio"  name="export_type" value="xls" id="xls"> <img src="/admin/images/csv.png" alt="" class="left mr5"> <label class="left" for="pokemonRed">XLS</label> </span>
<!-- <span class="inline left"><input type="radio"  name="export_type" value="print" id="print"> <i class="fa fa-print left mr5"></i> <label class="left" for="print">Print</label></span> -->
</div>

<input type="hidden" id="selected_columns" name="selected_columns" value=""/>
<input type="hidden" id="table" name="table" value="<?php echo $table;?>"/>
<input type="hidden" id="filter_sales_options" name="filter_sales_options" value="<?php echo $filterForm['filter_sales_options'];?>"/>
<input type="hidden" id="filter_order_options" name="filter_order_options" value="<?php echo isset($filterForm['filter_order_options'])?$filterForm['filter_order_options']:'';?>"/>
<input type="hidden" id="location_code" name="location_code" value="<?php echo $filterForm['location_code'];?>"/>
<input type="hidden" id="menu" name="menu" value="<?php echo $filterForm['menu'];?>"/>
<!--<input type="hidden" id="filter_year" name="filter_year" value="<?php echo $filterForm['filter_year'];?>"/>
<input type="hidden" id="filter_year_type" name="filter_year_type" value="<?php echo $filterForm['filter_year_type'];?>"/>
<input type="hidden" id="filter_month" name="filter_month" value="<?php echo $filterForm['filter_month'];?>"/>
<input type="hidden" id="filter_quarter_number" name="filter_quarter_number" value="<?php echo $filterForm['filter_quarter_number'];?>"/>
<input type="hidden" id="filter_week_number" name="filter_week_number" value="<?php echo $filterForm['filter_week_number'];?>"/>-->
<input type="hidden" id="filter_check" name="filter_check" value="<?php echo $filterForm['filter_check'];?>"/>
<input type="hidden" id="minDate" name="minDate"  value="<?php echo isset($filterForm['minDate'])?$filterForm['minDate']:''?>"/>
<input type="hidden" id="maxDate" name="maxDate" value="<?php echo isset($filterForm['maxDate'])?$filterForm['maxDate']:$filterForm['maxDate'];?>"/>
<input type="hidden" id="subaction" name="subaction" value="<?php echo isset($filterForm['subaction'])?$filterForm['subaction']:'';?>"/>
<input type="hidden" id="service" name="service" value="<?php echo $filterForm['service'];?>"/>
<input type="hidden" id="exportUrl" name="exportUrl" value="<?php echo $filterForm['exportUrl'];?>"/>
<input type="hidden" id="exporttype" name="exporttype" value="<?php echo $exporttype;?>"/> 
<input type="hidden" id="exporttype" name="exportexclude" value="<?php echo $exportexclude;?>"/> 

<!-- added by sankalp -->
<input type="hidden" id="dateFilterParam" name="dateFilterParam" value="<?php  echo $filterForm['dateFilterParam'];?>"/> 
<input type="hidden" id="delivery_person" name="delivery_person" value="<?php  echo $filterForm['delivery_person'];?>"/> 


<?php  if(  array_key_exists('pre-order-days', $filterForm)){ ?>
<input type="hidden" id="pre-order-days" name="pre-order-days" value="<?php  echo $filterForm['pre-order-days'];?>"/> 
    
<?php } ?>

<!-- sankalp : for orderconfirm export -->
<?php  if(  array_key_exists('status', $filterForm)){ ?>
<input type="hidden" id="pre-order-days" name="status" value="<?php  echo $filterForm['status'];?>"/> 
<input type="hidden" id="kitchenscreen" name="kitchenscreen" value="<?php  echo $filterForm['kitchenscreen'];?>"/> 

<?php } ?>



<div class="right mt15">
<button id="export">Export 	</button>
</div>
<a class="close-reveal-modal">&#215;</a>
</form>
<script type="text/javascript">

$(document).ready(function(){
      
	$(document).on('opened.fndtn.reveal', '[data-reveal]', function() {
		$('.nicescroll-rails').hide();
		$(".table-parent").niceScroll();
	});

	$(document).on('closed.fndtn.reveal', '[data-reveal]', function() {
		$('.nicescroll-rails').hide();
	});

	$(":radio, :checkbox").uniform();
	var id = $("#exporttype").val();

	$("#"+id).prop('checked', true).uniform();
	$(document).foundation();

	$(document).on('click','.close-reveal-modal',function(){
	  $('#myModal').foundation('reveal', 'close');
	});
	
	$("#selectAll").click(function(){
		
		if(this.checked){
			$('.checkbox').prop('checked', true).uniform();
		}else{
			$('.checkbox').prop('checked',false).uniform();
		} 
	});
	

});
</script>



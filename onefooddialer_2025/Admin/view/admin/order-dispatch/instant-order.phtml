<div id="content" class="clearfix">
    <div class="large-12 columns">
        <div class="portlet box yellow mb20">
            <div class="portlet-title">
                <ul class="toolOption">
                    <li>
                        <div class="print">
                            <button class="btn printBill" data-subaction="kot" data-type="bulk" data-dropdown="dropPrint">
                                    <i class="fa fa-print"></i>&nbsp; Print Kot
                            </button>
                            <button class="btn printBill" data-subaction="bills" data-type="bulk" data-dropdown="dropPrint">
                                    <i class="fa fa-print"></i>&nbsp; Print Bill
                            </button>
                            <button class="btn dispatchOrder" data-type="bulk" data-dropdown="dropPrint">
                                    <i class="fa fa-truck"></i>&nbsp; Dispatch
                            </button>
                            <button class="btn" onClick="location.href='/barcodedispatch'" data-dropdown="dropPrint">
                                    <i class="fa fa-truck"></i>&nbsp; Dispatch by Barcode
                            </button>

                        </div>
                    </li>

                </ul>
            </div>

        </div>
		
        <div class="portlet box yellow mb20">
            <table id="instantorder" class="display displayTable1">
                <thead>
                    <tr>
                        <th width="1%" style="padding-left:10px;position:relative;">
                            <input id="selectAll" name="" type="checkbox">
                        </th>
                        <th>Time </th>
                        <th>Customer</th>
                        <th>Mobile</th>
                        <th>Bill No</th>
                        <th>Order Items</th>
                        <th>Location</th>
                        <th>Delivery Boy</th>
                        <th>Action</th>
                    </tr>
                </thead>

                <tbody id="instantorderbody">

                </tbody>
            </table>
        </div>
    </div>
</div>
<span id='sound'></span>
<script type="text/javascript">
var document_root = "<?php echo $_SERVER['DOCUMENT_ROOT'] ?>";

var latestOrder = null;
var loadOrders = function(){

	$.ajax({
		url:"/orderdispatch/instant-order",
		type: "POST",
		data : {menu:'instantorder',lon:latestOrder,mode:'json'},
		dataType:'json',
		beforeSend : function(){
			//$('.loader_change_status'+id).hide();
			//$('#loaderstatus_'+id).show(100);
		},
		success:function(result){

			var html = '';
			
			if(result.slotOrders !=null && Object.keys(result.slotOrders).length > 0){

				if(latestOrder != result.latestOrder){

					latestOrder = result.latestOrder;

					$.each(result.slotOrders,function(slots,orders){

						$.each(orders,function(key,order){
							
							html += '<tr>';
								html += '<td>';
									html += '<input type="checkbox" name="checkedOrders[]" class="case" value="'+order.order_no+'" >';
								html += '</td>';
								html += '<td>'+order.delivery_time+'</td>';
								html += '<td>'+order.customer_name+'</td>';
								html += '<td>'+order.phone+'</td>';
								html += '<td>'+order.pk_order_no+'</td>';
								html += '<td>'+order.mealnames+'</td>';
								html += '<td>'+order.location_name+'</td>';
								html += '<td>';
									html += '<select name="deliveryBoy" class="deliveryBoy" style="margin: 0px;">';
									html += '<option>--Select--</option>';
										$.each(result.deliveryPersons,function(key1,deliveryPerson){
											var selected = "";
											if(order.delivery_person==deliveryPerson.pk_user_code){
												selected = "selected";
											}
											html += '<option '+selected+' data-date="'+order.order_date+'" data-orderno="'+order.order_no+'" value="'+deliveryPerson.pk_user_code+'">'+deliveryPerson.first_name+' '+deliveryPerson.last_name+'</option>';
										});
										
									html += '</select>';
								html += '</td>';
								html += '<td>';
									html += '<button class="smBtn redBg1 printBill" data-subaction="bills" data-type="ind" data-no="'+order.order_no+'" title="Print Bill"  data-text-swap="Wait..">';
										html += '<i class="fa fa-print"></i>';
									html += '</button>';
									html += '<button class="smBtn orangeBg printBill" data-subaction="kot" data-type="ind"  data-no="'+order.order_no+'" title="Print Kot" data-text-swap="Wait..">';
										html += '<i class="fa fa-sticky-note" aria-hidden="true"></i>';
									html += '</button>';
									html += '<button class="smBtn greenBg dispatchOrder" data-type="ind"  data-no="'+order.order_no+'" title="Dispatch" data-text-swap="Wait..">';
										html += '<i class="fa fa-truck"></i>';
									html += '</button>';
								html += '</td>';
										
							html += '</tr>';

						});

					});

					$("#instantorderbody").html(html);

					playSound('buzzer');
				}

			}

		}
	});

};

function playSound(filename){   
    document.getElementById("sound").innerHTML='<audio autoplay="autoplay"><source src="/' + filename + '.mp3" type="audio/mpeg" /><source src="/'+ filename + '.ogg" type="audio/ogg" /><embed hidden="true" autostart="true" loop="false" src="/'+filename +'.mp3" /></audio>';
}

$(document).ready(function(){

	loadOrders();

	setInterval(loadOrders,4000);

	$(document).on("click",".printBill",function(){

		var type = $(this).data("type");
		var subaction = $(this).data("subaction");
		
		var date = "<?php echo $date;?>";
		var orders = [];
		var strOrders = "";
		
		switch(type){
			case "ind":
				orders.push($(this).data("no"));
				strOrders += "&ord[]="+$(this).data("no");
				break;
			case "bulk":
				$("input[name='checkedOrders[]']").each(function(){
					if($(this).is(":checked")){
						orders.push($(this).val());
						strOrders += "&ord[]="+$(this).val();
					}			
				});
				break;
		}

		if(subaction=='bills'){
			var url = "/orderdispatch/print-bills?date="+date+""+strOrders;
		}else if(subaction=='kot'){
			var url = "/orderdispatch/print-kot?date="+date+""+strOrders;
		}else{
			var url = "/orderdispatch/print-bills?date="+date+""+strOrders;
		}

		if(subaction=='bills'){
			window.open(url,"","width=350,height=540,status=yes, toolbar=no, menubar=no, location=no, addressbar=no, top=200, left=300");
		}else{
			//window.location.href = url;
			window.open(url, '_blank');
			window.focus();
		}
	});

	$(document).on("change",".deliveryBoy",function(){

		var orderNo = $(this).find(":selected").data('orderno');
		var date = $(this).find(":selected").data('date');
		var deliveryPerson = $(this).val();

		var data = {date:date,orderno:orderNo,dp:deliveryPerson};
		
		$.ajax({
			 url:"/order/update-order",
			 type: "POST",
			 data : data,
			 dataType:'json',
			 beforeSend : function(){
					//$('.loader_change_status'+id).hide();
					//$('#loaderstatus_'+id).show(100);
			 },
			 success:function(result)
			 {
				console.log(result);
				/*if(result.status =="success")
				{
					alert("Order has been dispatched successfully.");
					window.location.reload();
				}
				if(result.status =="error")
				{
					alert(result.msg);
				}*/

			 }
		});

	});

	$(document).on("click",".dispatchOrder",function(){

		var type = $(this).data("type");
		var date = "<?php echo $date;?>";
		var orders = [];
		var strOrders = "";
		
		switch(type){
			case "ind":
				orders.push($(this).data("no"));
				strOrders += "&ord[]="+$(this).data("no");
				break;
			case "bulk":
				$("input[name='checkedOrders[]']").each(function(){
					if($(this).is(":checked")){
						orders.push($(this).val());
						strOrders += "&ord[]="+$(this).val();
					}			
				});
				break;
		}

		$.ajax({
			 url:"/orderdispatch/dispatch-instant",
			 type: "POST",
			 data : {ord:orders,date:date},
			 dataType:'json',
			 beforeSend : function(){
					//$('.loader_change_status'+id).hide();
					//$('#loaderstatus_'+id).show(100);
			 },
			 success:function(result)
			 {
				if(result.status =="success")
				{
					alert("Order has been dispatched successfully.");
					window.location.reload();
				}
				if(result.status =="error")
				{
					alert(result.msg);
				}

			 }
		});

	});	
	
});
</script>
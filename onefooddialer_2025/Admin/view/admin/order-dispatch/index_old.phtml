<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
 <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Order Dispatch</span></h2>
          </div>
          <div id="dyntable_filter" class="dataTables_add"><a class="btn btn_document" id="print_button" target="_blank" href="<?php //echo $this->url('orderdispatch', array('action'=>'printLabel'));?>" style=""  ><span>Print Label &amp; Process</span></a></div>
          <!--contenttitle-->
           <div class="subHeader"><p style="padding:5px 0 5px 8px;">Prepared Food</p></div>
          <div class="notification msginfo-dispatch" style="height:auto;">
           <?php foreach ($prepared_orders as $orders) : ?>
            		<div class="quicko"> <span><?php echo  $this->escapeHtml($orders->name);?></span> <span><b><?php echo $orders->prepared - $orders->dispatch;?></b></span> </div>
		  <?php endforeach; ?>
          </div>
          <form class="stdform" action="" method="post" id="form3">

           <?php foreach ($orderdata as $location=>$orders) : ?>
            <table class="stdtable">
              <tbody>
                <tr>
                  <td colspan="10"><div class="subHeader">
                      <p class="dis"><input class="location" type="radio" value="<?php echo $location;?>" name="location"  id="<?php echo $location;?>"/>&nbsp;&nbsp;<?php echo $orders['location'];?></p>
                    </div></td>
                </tr>
                 <?php foreach($orders as $arrinside) :?>
					<?php foreach($arrinside as $product_code=>$productdata) : ?>
						<?php //foreach($arrinside as $product_code=>$productdata) : ?>
			                <tr>
			                  <td class="label"><?php echo $productdata['name'];?></td>
			                  <td><?php echo $productdata['total'];?></td>
						    </tr>
					    <?php //endforeach; ?>
					<?php endforeach; ?>
               	<?php endforeach; ?>
           </tbody>
            </table>
	    <?php endforeach; ?>
          </form>
        </div>
        <!--content-->


      <!--maincontentinner-->

    <!--maincontent-->
<script type="text/javascript">
		$(document).ready(function(){
		$('#print_button').click(function(){
			 var location_code= $('.location:checked').val();

			 if(!location_code) { alert("Select any location"); return false;}

			  var loc = "/orderdispatch/printLabel/location/"+location_code;
			  window.open(loc, '_blank');
			  return false;
		});

	});
</script>
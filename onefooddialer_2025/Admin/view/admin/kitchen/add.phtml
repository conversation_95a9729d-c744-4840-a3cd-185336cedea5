<?php
$form = $this->form;
$form->setAttribute('action', $this->url('kitchen_master', array('action' => 'add')));
//$form->setAttribute('class','stdform');
$form->prepare();

$base_kitchen  = $form->get('base_kitchen');
$base_kitchen_opt = $base_kitchen->getOptions();

$seleted_print = $form->get('base_kitchen')->getValue();

?>

        <?php     
            if(trim($errStr)!=""){
                echo '<div class="alert-box alert">'.$errStr.'</div>';
            }
		?>
<style>
ul, ol, dl {
	font-size: inherit;
}
div.checker, div.checker span, div.checker input {
	float: left;
    height: 19px;
    margin-top: 1px;
    width: 19px;
}
.calendar {
    left: 6px;
}
.calendar .selector .date-selector, .calendar .selector .time-selector {
	height: 25px;
    padding: 0 0 0 5px;
}
</style>
<!-- END PAGE HEADER-->
      
      <div id="content">
          
       <?php echo $this->form()->openTag($form);?>
              <div class="large-8 columns">
         
		          <fieldset>
					<legend>
					KITCHEN INFO
				</legend>
          	<div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('kitchen_name')); ?></label>
              </div>
              <div class="large-4 small-8 medium-8 columns left">
                <?php  
                 	echo $this->formHidden($form->get('pk_kitchen_code'));
					echo $this->formElement($form->get('kitchen_name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						
						->setMessageCloseString('</small>')
						->render($form->get('kitchen_name'));
				?>
				
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('kitchen_alias')); ?></label>
              </div>
              <div class="large-4  small-8 medium-8 columns left">
                  <?php 
                  	echo $this->formElement($form->get('kitchen_alias'));
					echo $this->formElementErrors($form->get('kitchen_alias')); 
				 ?>
              </div>
            </div>
            <div class="row">
             	<div class="large-4 small-4 medium-4 columns">
            		<label class="inline right"><?php echo $this->formLabel($form->get('city_id')); ?></label>
             	</div>
            	<div class="large-4 small-8 medium-8 columns left">
            	<?php 
               		echo $this->formElement($form->get('city_id'));
					echo $this->formElementErrors($form->get('city_id')); 
				?>
            	</div>
            </div>
            <div class="row">
             	<div class="large-4 small-4 medium-4 columns">
            		<label class="inline right"><?php echo $this->formLabel($form->get('location_id')); ?></label>
             	</div>
            	<div class="large-4 small-8 medium-8 columns left">
            	<?php 
               		echo $this->formElement($form->get('location_id'));
					//echo $this->formElementErrors($form->get('location_id')); 
				?>
            	</div>
            </div>
                              
            <div class="row">
             	<div class="large-4 small-4 medium-4 columns">
            		<label class="inline right"><?php echo $this->formLabel($form->get('kitchen_address')); ?></label>
             	</div>
            	<div class="large-4 small-8 medium-8 columns left">
            	<?php 
               		echo $this->formElement($form->get('kitchen_address'));
					echo $this->formElementErrors($form->get('kitchen_address')); 
				?>
            	</div>
            </div>
                              
            <div class="row">
				<div class="large-4 small-4 medium-4 columns">
					<?php echo $this->formLabel($form->get('base_kitchen')); ?>
				</div>
				<div class="large-4 small-8 medium-8 columns left prepaid">
				
	            <?php
	                foreach($base_kitchen_opt['value_options'] as $key=>$val){
	            ?>
				<input  type="radio" id="<?php echo $key;?>" name="base_kitchen"  value="<?php echo $key;?>" <?php echo $checked=($seleted_print==$key)?'checked':''?>>
				<label class="pull-left" for="<?php echo $key;?>" id="<?php echo $key;?>"><?php echo $val;?></label>
				<?php }?>
			
				</div>
				<?php echo $this->formElementErrors($form->get('base_kitchen'));?>
			</div>
			
			<fieldset>
				<legend><?php echo 'General Settings';?></legend>
					<div class="row">
		             	<div class="large-4 small-4 medium-4 columns">
		            		<label class="inline right"><?php echo $this->formLabel($form->get('CUSTOMER_PAYMENT_MODE')); ?></label>
		             	</div>
		            	<div class="large-8 small-8 medium-8 columns left">
		            	<?php 
		               		echo $this->formElement($form->get('CUSTOMER_PAYMENT_MODE'));
							echo $this->formElementErrors($form->get('CUSTOMER_PAYMENT_MODE')); 
						?>
		   	         	</div>
            		</div>
            		
            		<div class="row">
		             	<div class="large-4 small-4 medium-4 columns">
		            		<label class="inline right"><?php echo $this->formLabel($form->get('MIN_ORDER_PRICE')); ?></label>
		             	</div>
		            	<div class="large-4 small-8 medium-8 columns left">
		            	<?php 
		               		echo $this->formElement($form->get('MIN_ORDER_PRICE'));
							echo $this->formElementErrors($form->get('MIN_ORDER_PRICE')); 
						?>
		   	         	</div>
            		</div>
            		
            		<div class="row">
		             	<div class="large-4 small-4 medium-4 columns">
		            		<label class="inline right"><?php echo $this->formLabel($form->get('MAX_ORDER_PRICE')); ?></label>
		             	</div>
		            	<div class="large-4 small-8 medium-8 columns left">
		            	<?php 
		               		echo $this->formElement($form->get('MAX_ORDER_PRICE'));
							echo $this->formElementErrors($form->get('MAX_ORDER_PRICE')); 
						?>
		   	         	</div>
            		</div>
            		
            		<div class="row">
		             	<div class="large-4 small-4 medium-4 columns">
		            		<label class="inline right"><?php echo $this->formLabel($form->get('MENU_TYPE')); ?></label>
		             	</div>
		            	<div class="large-6 small-8 medium-8 columns left">
		            	<?php 
		               		echo $this->formElement($form->get('MENU_TYPE'));
							echo $this->formElementErrors($form->get('MENU_TYPE')); 
						?>
		   	         	</div>
            		</div>
            		
            		<div class="row">
		             	<div class="large-4 small-4 medium-4 columns">
		            		<label class="inline right"><?php echo $this->formLabel($form->get('ORDER_NOTIFICATION_EMAIL')); ?></label>
		             	</div>
		            	<div class="large-4 small-8 medium-8 columns left">
		            	<?php 
		               		echo $this->formElement($form->get('ORDER_NOTIFICATION_EMAIL'));
							echo $this->formElementErrors($form->get('ORDER_NOTIFICATION_EMAIL')); 
						?>
		   	         	</div>
            		</div>
	           </fieldset>
			
			<?php 
				$timearray = array("ORDER_CUT_OFF_TIME","ORDER_CANCEL_CUT_OFF_TIME","AUTO_DELIVERY","AUTO_DISPATCH");
				foreach($menu_type as $k=>$v){
                    $v = strtoupper($v);
                    ?>
				<fieldset>
					<legend><?php echo $v.' Settings';?></legend>
						<div class="row">
				             	<div class="large-4 small-4 medium-4 columns">
				            		<label class="inline right"><?php echo 'order cut off day / time';?></label>
				             	</div>
				            	<div class="large-8  small-8 medium-8 columns">
				            		<div class="large-3 small-3 medium-3 columns">
										<select name = "<?php echo 'settings_'.$v;?>_ORDER_CUT_OFF_DAY">
											<option value = "0">Same Day</option>
											<option value = "1">One Day Before</option>
											<option value = "2">Two Day Before</option>
											<option value = "3">Three Day Before</option>
										</select>
									</div>
									<div class="large-3 small-3 medium-3 columns pl0 left">
										<input type="text"  name = "<?php echo 'settings_'.$v.'_'.$timearray[0];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[0]];?>"/>
									</div>
				            	</div>
	            		</div>
	            		<?php /* <div class="row">
            				<div class="large-4 small-4 medium-4 columns">
            					<label class="inline right"><?php echo str_replace("_"," ",strtolower($timearray[0]));?></label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
            					<div class="large-3 small-3 medium-3 columns fromto pl0">
            						<input type="text" data-time name = "<?php echo 'settings_'.$v.'_'.$timearray[0];?>"/>
	            				</div>
	            			</div>
            			</div> */ ?>
	            		<?php foreach($timearray as $f=>$g){?>
	            			
	            		<?php }?>
	            		<div class="row">
				             	<div class="large-4 small-4 medium-4 columns">
				            		<label class="inline right"><?php echo 'order cancel cut off day / time';?></label>
				             	</div>
				            	<div class="large-8  small-8 medium-8 columns">
				            		<div class="large-3 small-3 medium-3 columns">
										<select name = "<?php echo 'settings_'.$v;?>_ORDER_CANCEL_CUT_OFF_DAY">
											<option value = "0">Same Day</option>
											<option value = "1">One Day Before</option>
											<option value = "2">Two Day Before</option>
											<option value = "3">Three Day Before</option>
										</select>
									</div>
									<div class="large-3 small-3 medium-3 columns pl0 left">
										<input type="text" name = "<?php echo 'settings_'.$v.'_'.$timearray[1];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[1]];?>"/>
									</div>
				            	</div>
	            		</div>
	            		<?php /*<div class="row">
		            		<div class="large-4 small-4 medium-4 columns">
		            			<label class="inline right"><?php echo str_replace("_"," ",strtolower($timearray[1]));?></label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
            					<div class="large-3 small-3 medium-3 columns fromto pl0">
            						<input type="text" data-time name = "<?php echo 'settings_'.$v.'_'.$timearray[1];?>"/>
	            				</div>
	            			</div>
            			</div> */ ?>
            			<?php if(array_key_exists('ENABLE_AUTO_DELIVERY', $setting) && $setting['ENABLE_AUTO_DELIVERY']=='yes'){ ?>
            			<div class="row">
		            		<div class="large-4 small-4 medium-4 columns">
	            				<label class="inline right"><?php echo str_replace("_"," ",ucfirst(strtolower($timearray[2])));?></label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
                                <input type="hidden" id="auto_delivery" name= "<?php echo 'settings_'.$v.'_'.$timearray[2];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[2]] ;?>"/>
                                <div class="large-3 small-3 medium-3 columns">
									<select id="auto_delivery_day" class="" name = "<?php echo $v.'_'.$timearray[2].'_DAY';?>" >
										<option value = "0">Same Day</option>
										<option value = "1">One Day After</option>
										<option value = "2">Two Day After</option>
										<option value = "3">Three Day After</option>
									</select>
                                </div> 
                                <div class="large-3 small-3 medium-3 columns pl0 left">
            						<input id="auto_delivery_time" type="text" name = "<?php echo 'settings_'.$v.'_'.$timearray[2];?>" value = "<?php if($auto_delivery_array) echo $auto_delivery_array[1];?>"/>
	            				</div>
	            			</div>
            			</div>
            			<?php } ?>
            			<?php if(array_key_exists('ENABLE_AUTO_DISPATCH', $setting) && $setting['ENABLE_AUTO_DISPATCH']=='yes'){ ?>
            			<div class="row">
		            		<div class="large-4 small-4 medium-4 columns">
	            				<label class="inline right"><?php echo str_replace("_"," ",ucfirst(strtolower($timearray[3])));?></label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
                                <input type="hidden" id="auto_delivery" name= "<?php echo 'settings_'.$v.'_'.$timearray[3];?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.$timearray[3]] ;?>"/>
                                <div class="large-3 small-3 medium-3 columns">
										<select id="auto_delivery_day" class="" name = "<?php echo $v.'_'.$timearray[3].'_DAY';?>" >
											<option value = "0">Same Day</option>
											<option value = "1">One Day After</option>
											<option value = "2">Two Day After</option>
											<option value = "3">Three Day After</option>
										</select>
                                </div> 
                                <div class="large-3 small-3 medium-3 columns pl0 left">
            						<input id="auto_dispatch_time"  data-time type="text" name = "<?php echo 'settings_'.$v.'_'.$timearray[3];?>" value = ""/>
	            				</div>
	            			</div>
            			</div>
      				<?php } ?>	
                    <?php if(array_key_exists('GLOBAL_THIRDPARTY_DELIVERY', $setting) && !empty($setting['GLOBAL_THIRDPARTY_DELIVERY']) ){
                                $tpArray = explode(',',$setting['GLOBAL_THIRDPARTY_DELIVERY']);
                                foreach($tpArray as $tp){
                        ?>
	            		<div class="row">
	            			<div class="large-4 small-4 medium-4 columns">
                                <label class="inline right"><?php echo strtoupper($tp)?> Pickup Time</label>
            				</div>
            				<div class="large-8  small-8 medium-8 columns">
            					<div class="large-3 small-3 medium-3 columns fromto">
            						<input type="text" data-time name = "<?php echo 'settings_'.$v.'_'.strtoupper($tp).'_PICKUPTIME';?>" value = "<?php echo $arr['K'.$kitchen->pk_kitchen_code.'_'.$v.'_'.strtoupper($tp).'_PICKUPTIME'];?>"/>
	            				</div>
	            			</div>
            			</div>              
                    <?php      } 
                            }
                    ?>          
                                        <!-- 12th april - sankalp --> 
                                <div class="row prepaidrow " style="display:<?php //echo $display_day;?>">
                                    <div class="large-4 small-4 medium-4 columns">
                                           <label class="inline right">Choose Week-Off Days</label>
                                   </div>
                                    <div class="large-6 small-8 medium-8 columns left">

                                        <select name = "<?php echo 'settings_'.$v;?>_WEEKOFFS[]" multiple="multiple" placeholder="select day" class="SlectBox day unidays">
                                <?php
                                    foreach ($allDays as $key_u => $val_u){
                                ?>
                                                <option  value="<?php echo $key_u;?>"><?php echo $val_u; ?></option>
                                <?php 
                                    }
                                ?> 
                                        </select>
                                    </div>

                                </div> 
           
                            

            	</fieldset>
			<?php }
			?>
                      </fieldset>
			</div>
			
			<?php echo $this->formElement($form->get('csrf')); ?>
			<?php echo $this->formElement($form->get('backurl'));?>
			
			 <div class="large-12 columns pl0 pr0">
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8  small-8 medium-8 columns">
              	<button	type="submit"  id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id="cancelbutton" class="button	left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
              </div>
            </div>
            </div>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?> 
        </div>
      </div>
    </div>

    <!-- END PAGE CONTAINER--> 
<script type="text/javascript">
    
 $(document).ready(function(){

    window.asd = $('.SlectBox').SumoSelect({ csvDispCount: 3 });

	$("#city_id").on('change',function(event){


		getAllLocations($(this).val(),"location_id");
		renderLocationOptions("location_id");
		
	});	

	$(".chosen-select").chosen();
		
 });
</script>    

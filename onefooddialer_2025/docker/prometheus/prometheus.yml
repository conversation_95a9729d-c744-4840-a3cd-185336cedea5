global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  - job_name: "kong"
    scrape_interval: 5s
    static_configs:
      - targets: ["kong:8001"]
    metrics_path: /metrics

  - job_name: "auth-service"
    scrape_interval: 5s
    static_configs:
      - targets: ["auth-service-v12:8000"]
    metrics_path: /api/v1/metrics

  - job_name: "quickserve-service"
    scrape_interval: 5s
    static_configs:
      - targets: ["quickserve-service-v12:8000"]
    metrics_path: /api/v1/metrics

  - job_name: "payment-service"
    scrape_interval: 5s
    static_configs:
      - targets: ["payment-service-v12:8000"]
    metrics_path: /api/v1/metrics

  - job_name: "customer-service"
    scrape_interval: 5s
    static_configs:
      - targets: ["customer-service-v12:8000"]
    metrics_path: /api/v1/metrics

  - job_name: "mysql"
    static_configs:
      - targets: ["mysql-exporter:9104"]

  - job_name: "redis"
    static_configs:
      - targets: ["redis-exporter:9121"]

  - job_name: "rabbitmq"
    static_configs:
      - targets: ["rabbitmq:15692"]

<?php
/**
 * Security Check Script
 * 
 * This script checks the security improvements implemented in the authentication system.
 * 
 * Usage:
 * php bin/security-check.php
 */

// Define application path
define('APPLICATION_PATH', realpath(__DIR__ . '/..'));

// Initialize output
echo "===========================================\n";
echo "Security Improvements Check\n";
echo "===========================================\n\n";

// Check for security files
echo "Checking for security files...\n";
$securityFiles = [
    'module/SanAuth/src/SanAuth/Service/PasswordHashingService.php',
    'module/SanAuth/src/SanAuth/Service/CsrfTokenManager.php',
    'module/SanAuth/src/SanAuth/Service/ErrorHandlingService.php',
    'module/SanAuth/src/SanAuth/Form/Element/Csrf.php',
    'module/SanAuth/src/SanAuth/Session/SessionManager.php',
    'module/SanAuth/src/SanAuth/Service/AuthenticationServiceInterface.php',
    'module/SanAuth/src/SanAuth/Service/UnifiedAuthService.php',
    'vendor/Lib/QuickServe/Auth/JwtTokenUtil.php',
    'vendor/Lib/QuickServe/Env/EnvLoader.php',
    'config/autoload/session.local.php',
    'bin/revoke-token.php',
    'SECURITY_IMPROVEMENTS.md'
];

$allFilesExist = true;
foreach ($securityFiles as $file) {
    $filePath = APPLICATION_PATH . '/' . $file;
    $exists = file_exists($filePath);
    echo "- " . $file . ": " . ($exists ? "✓" : "✗") . "\n";
    if (!$exists) {
        $allFilesExist = false;
    }
}
echo "\n";

// Check for security features in files
echo "Checking for security features...\n";

// Check for password hashing
$passwordHashingFile = APPLICATION_PATH . '/module/SanAuth/src/SanAuth/Service/PasswordHashingService.php';
if (file_exists($passwordHashingFile)) {
    $content = file_get_contents($passwordHashingFile);
    $hasArgon2 = strpos($content, 'PASSWORD_ARGON2ID') !== false;
    $hasComplexityValidation = strpos($content, 'validatePasswordComplexity') !== false;
    $hasSecurePasswordGeneration = strpos($content, 'generateSecurePassword') !== false;
    
    echo "- Argon2id password hashing: " . ($hasArgon2 ? "✓" : "✗") . "\n";
    echo "- Password complexity validation: " . ($hasComplexityValidation ? "✓" : "✗") . "\n";
    echo "- Secure password generation: " . ($hasSecurePasswordGeneration ? "✓" : "✗") . "\n";
} else {
    echo "- Password hashing features: ✗ (file not found)\n";
}

// Check for CSRF protection
$csrfFile = APPLICATION_PATH . '/module/SanAuth/src/SanAuth/Service/CsrfTokenManager.php';
if (file_exists($csrfFile)) {
    $content = file_get_contents($csrfFile);
    $hasTokenGeneration = strpos($content, 'generateToken') !== false;
    $hasTokenValidation = strpos($content, 'validateToken') !== false;
    $hasTokenCleanup = strpos($content, 'cleanExpiredTokens') !== false;
    
    echo "- CSRF token generation: " . ($hasTokenGeneration ? "✓" : "✗") . "\n";
    echo "- CSRF token validation: " . ($hasTokenValidation ? "✓" : "✗") . "\n";
    echo "- CSRF token cleanup: " . ($hasTokenCleanup ? "✓" : "✗") . "\n";
} else {
    echo "- CSRF protection features: ✗ (file not found)\n";
}

// Check for session security
$sessionFile = APPLICATION_PATH . '/module/SanAuth/src/SanAuth/Session/SessionManager.php';
if (file_exists($sessionFile)) {
    $content = file_get_contents($sessionFile);
    $hasSessionFixationProtection = strpos($content, 'regenerateId') !== false;
    $hasSessionTimeout = strpos($content, 'SESSION_LAST_ACTIVITY') !== false;
    
    echo "- Session fixation protection: " . ($hasSessionFixationProtection ? "✓" : "✗") . "\n";
    echo "- Session timeout detection: " . ($hasSessionTimeout ? "✓" : "✗") . "\n";
} else {
    echo "- Session security features: ✗ (file not found)\n";
}

// Check for JWT token security
$jwtFile = APPLICATION_PATH . '/vendor/Lib/QuickServe/Auth/JwtTokenUtil.php';
if (file_exists($jwtFile)) {
    $content = file_get_contents($jwtFile);
    $hasTokenBlacklisting = strpos($content, 'blacklistToken') !== false;
    $hasTokenValidation = strpos($content, 'validateTokenForQuickServe') !== false;
    
    echo "- JWT token blacklisting: " . ($hasTokenBlacklisting ? "✓" : "✗") . "\n";
    echo "- JWT token validation: " . ($hasTokenValidation ? "✓" : "✗") . "\n";
} else {
    echo "- JWT token security features: ✗ (file not found)\n";
}

// Check for error handling
$errorHandlingFile = APPLICATION_PATH . '/module/SanAuth/src/SanAuth/Service/ErrorHandlingService.php';
if (file_exists($errorHandlingFile)) {
    $content = file_get_contents($errorHandlingFile);
    $hasCentralizedErrorHandling = strpos($content, 'handleAuthError') !== false;
    $hasErrorLogging = strpos($content, 'logError') !== false;
    
    echo "- Centralized error handling: " . ($hasCentralizedErrorHandling ? "✓" : "✗") . "\n";
    echo "- Error logging: " . ($hasErrorLogging ? "✓" : "✗") . "\n";
} else {
    echo "- Error handling features: ✗ (file not found)\n";
}

echo "\n";

// Check for test files
echo "Checking for test files...\n";
$testFiles = [
    'module/SanAuth/test/SanAuthTest/Service/PasswordHashingServiceTest.php',
    'module/SanAuth/test/SanAuthTest/Service/CsrfTokenManagerTest.php',
    'module/SanAuth/test/SanAuthTest/Auth/JwtTokenUtilTest.php',
    'module/SanAuth/phpunit.xml'
];

$allTestFilesExist = true;
foreach ($testFiles as $file) {
    $filePath = APPLICATION_PATH . '/' . $file;
    $exists = file_exists($filePath);
    echo "- " . $file . ": " . ($exists ? "✓" : "✗") . "\n";
    if (!$exists) {
        $allTestFilesExist = false;
    }
}
echo "\n";

// Summary
echo "===========================================\n";
echo "Security Check Summary\n";
echo "===========================================\n\n";

echo "Security files: " . ($allFilesExist ? "All present" : "Some missing") . "\n";
echo "Test files: " . ($allTestFilesExist ? "All present" : "Some missing") . "\n";

echo "\nSecurity improvements have been " . ($allFilesExist ? "successfully" : "partially") . " implemented.\n";
echo "See SECURITY_IMPROVEMENTS.md for more details.\n\n";

echo "===========================================\n";
echo "Security check completed!\n";
echo "===========================================\n";

// Make the script executable
chmod(__FILE__, 0755);

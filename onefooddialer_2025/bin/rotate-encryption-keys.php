<?php
/**
 * Encryption Key Rotation Utility
 * 
 * This script rotates the encryption keys used for token encryption.
 * It should be run every 90 days as part of the key rotation schedule.
 * 
 * Usage:
 * php bin/rotate-encryption-keys.php
 */

// Define application path
define('APPLICATION_PATH', realpath(__DIR__ . '/..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(APPLICATION_PATH . '/.env')) {
    $dotenv = new \Dotenv\Dotenv(APPLICATION_PATH);
    $dotenv->load();
}

// Import required classes
use Lib\QuickServe\Env\EnvLoader;
use Zend\Db\Adapter\Adapter;
use Zend\Crypt\BlockCipher;

// Output header
echo "===========================================\n";
echo "Encryption Key Rotation Utility\n";
echo "===========================================\n\n";

// Get current keys
$currentKey = EnvLoader::get('TOKEN_ENCRYPTION_KEY');
$previousKey = EnvLoader::get('TOKEN_ENCRYPTION_KEY_PREVIOUS');

if (empty($currentKey)) {
    echo "ERROR: TOKEN_ENCRYPTION_KEY is not set in the environment variables.\n";
    exit(1);
}

// Generate a new key
echo "Generating new encryption key...\n";
$newKey = bin2hex(random_bytes(32));
echo "New key generated.\n\n";

// Update .env file
echo "Updating .env file...\n";
$envFile = APPLICATION_PATH . '/.env';
$envContent = file_exists($envFile) ? file_get_contents($envFile) : '';

// Update TOKEN_ENCRYPTION_KEY
$envContent = preg_replace(
    '/^TOKEN_ENCRYPTION_KEY=.*$/m',
    "TOKEN_ENCRYPTION_KEY={$newKey}",
    $envContent
);

// Update TOKEN_ENCRYPTION_KEY_PREVIOUS
$envContent = preg_replace(
    '/^TOKEN_ENCRYPTION_KEY_PREVIOUS=.*$/m',
    "TOKEN_ENCRYPTION_KEY_PREVIOUS={$currentKey}",
    $envContent
);

// Update rotation date
$rotationDate = date('Y-m-d', strtotime('+90 days'));
$envContent = preg_replace(
    '/^TOKEN_ENCRYPTION_KEY_ROTATION_DATE=.*$/m',
    "TOKEN_ENCRYPTION_KEY_ROTATION_DATE={$rotationDate}",
    $envContent
);

file_put_contents($envFile, $envContent);
echo "Environment variables updated.\n\n";

// Re-encrypt tokens in the database
echo "Re-encrypting tokens in the database...\n";

try {
    // Get database configuration
    $dbConfig = require APPLICATION_PATH . '/config/autoload/database.local.php';
    
    // Create database adapter
    $adapter = new Adapter($dbConfig['db']);
    
    // Create block ciphers
    $oldCipher = BlockCipher::factory('openssl');
    $oldCipher->setKey($currentKey);
    
    $newCipher = BlockCipher::factory('openssl');
    $newCipher->setKey($newKey);
    
    // Get tokens from database
    $sql = "SELECT * FROM user_tokens";
    $statement = $adapter->query($sql);
    $results = $statement->execute();
    
    $count = 0;
    foreach ($results as $row) {
        // Skip if no tokens
        if (empty($row['access_token']) || empty($row['refresh_token'])) {
            continue;
        }
        
        try {
            // Decrypt tokens with old key
            $accessToken = $oldCipher->decrypt($row['access_token']);
            $refreshToken = $oldCipher->decrypt($row['refresh_token']);
            
            // Re-encrypt with new key
            $newAccessToken = $newCipher->encrypt($accessToken);
            $newRefreshToken = $newCipher->encrypt($refreshToken);
            
            // Update database
            $updateSql = "UPDATE user_tokens SET 
                access_token = ?, 
                refresh_token = ?,
                updated_at = NOW()
                WHERE id = ?";
            
            $adapter->query(
                $updateSql,
                [$newAccessToken, $newRefreshToken, $row['id']]
            );
            
            $count++;
        } catch (\Exception $e) {
            echo "Error re-encrypting tokens for user ID {$row['user_id']}: {$e->getMessage()}\n";
        }
    }
    
    echo "Successfully re-encrypted {$count} token pairs.\n\n";
} catch (\Exception $e) {
    echo "Error accessing database: {$e->getMessage()}\n";
    echo "Tokens in the database will need to be re-encrypted manually.\n\n";
}

echo "===========================================\n";
echo "Key rotation completed successfully!\n";
echo "New key: {$newKey}\n";
echo "Previous key: {$currentKey}\n";
echo "Next rotation date: {$rotationDate}\n";
echo "===========================================\n";

// Make the script executable
chmod(__FILE__, 0755);

#!/bin/bash

# Script to convert codebase to PSR-4 structure
echo "Starting PSR-4 conversion..."

# Run Rector with PSR-4 configuration
echo "Running Rector with PSR-4 configuration..."
vendor/bin/rector process --config=psr4-rector.php --dry-run

# If dry run is successful, ask for confirmation to proceed
read -p "Do you want to proceed with the actual conversion? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    # Run Rector with PSR-4 configuration (actual conversion)
    echo "Running Rector with PSR-4 configuration (actual conversion)..."
    vendor/bin/rector process --config=psr4-rector.php

    # Move files to the new PSR-4 structure
    echo "Moving files to the new PSR-4 structure..."

    # SanAuth module
    echo "Processing SanAuth module..."
    find module/SanAuth/src/SanAuth/Controller -name "*.php" -exec cp {} src/Auth/Controller/ \;
    find module/SanAuth/src/SanAuth/Model -name "*.php" -exec cp {} src/Auth/Model/ \;
    find module/SanAuth/src/SanAuth/Service -name "*.php" -exec cp {} src/Auth/Service/ \;
    find module/SanAuth/src/SanAuth/Form -name "*.php" -exec cp {} src/Auth/Form/ \;
    find module/SanAuth/src/SanAuth/Validator -name "*.php" -exec cp {} src/Auth/Validator/ \;
    find module/SanAuth/src/SanAuth/Factory -name "*.php" -exec cp {} src/Auth/Factory/ \;
    find module/SanAuth/src/SanAuth/Middleware -name "*.php" -exec cp {} src/Auth/Middleware/ \;
    find module/SanAuth/src/SanAuth/Session -name "*.php" -exec cp {} src/Auth/Session/ \;

    # Api module
    echo "Processing Api module..."
    # Create directories if they don't exist
    mkdir -p src/Api/Model src/Api/Service

    # Copy existing directories
    find module/Api/src/Api/Controller -name "*.php" -exec cp {} src/Api/Controller/ \; 2>/dev/null || echo "No Controller files found in Api module"
    find module/Api/src/Api/Middleware -name "*.php" -exec cp {} src/Api/Middleware/ \; 2>/dev/null || echo "No Middleware files found in Api module"
    find module/Api/src/Api/Factory -name "*.php" -exec cp {} src/Api/Factory/ \; 2>/dev/null || echo "No Factory files found in Api module"

    # Check Api-new module for additional files
    echo "Processing Api-new module..."
    find module/Api-new/src/Api/Controller -name "*.php" -exec cp {} src/Api/Controller/ \; 2>/dev/null || echo "No Controller files found in Api-new module"
    find module/Api-new/src/Api/Model -name "*.php" -exec cp {} src/Api/Model/ \; 2>/dev/null || echo "No Model files found in Api-new module"
    find module/Api-new/src/Api/Service -name "*.php" -exec cp {} src/Api/Service/ \; 2>/dev/null || echo "No Service files found in Api-new module"

    # QuickServe module
    echo "Processing QuickServe module..."
    # Create additional directories
    mkdir -p src/QuickServe/Event src/QuickServe/Factory src/QuickServe/Listener src/QuickServe/Service

    # Copy files
    find module/QuickServe/src/QuickServe/Controller -name "*.php" -exec cp {} src/QuickServe/Controller/ \; 2>/dev/null || echo "No Controller files found in QuickServe module"
    find module/QuickServe/src/QuickServe/Model -name "*.php" -exec cp {} src/QuickServe/Model/ \; 2>/dev/null || echo "No Model files found in QuickServe module"
    find module/QuickServe/src/QuickServe/Event -name "*.php" -exec cp {} src/QuickServe/Event/ \; 2>/dev/null || echo "No Event files found in QuickServe module"
    find module/QuickServe/src/QuickServe/Factory -name "*.php" -exec cp {} src/QuickServe/Factory/ \; 2>/dev/null || echo "No Factory files found in QuickServe module"
    find module/QuickServe/src/QuickServe/Listener -name "*.php" -exec cp {} src/QuickServe/Listener/ \; 2>/dev/null || echo "No Listener files found in QuickServe module"
    find module/QuickServe/src/QuickServe/Service -name "*.php" -exec cp {} src/QuickServe/Service/ \; 2>/dev/null || echo "No Service files found in QuickServe module"

    # Lib module
    echo "Processing Lib module..."
    # Create additional directories based on actual structure
    mkdir -p src/Lib/Auth src/Lib/Barcode src/Lib/CronHelper src/Lib/Email src/Lib/ICICI src/Lib/TPDelivery

    # Copy files from existing directories
    find vendor/Lib/QuickServe -name "*.php" -exec cp --parents {} src/Lib/ \; 2>/dev/null || echo "No QuickServe files found in Lib module"
    find vendor/Lib/Auth -name "*.php" -exec cp --parents {} src/Lib/ \; 2>/dev/null || echo "No Auth files found in Lib module"
    find vendor/Lib/Barcode -name "*.php" -exec cp --parents {} src/Lib/ \; 2>/dev/null || echo "No Barcode files found in Lib module"
    find vendor/Lib/CronHelper -name "*.php" -exec cp --parents {} src/Lib/ \; 2>/dev/null || echo "No CronHelper files found in Lib module"
    find vendor/Lib/Email -name "*.php" -exec cp --parents {} src/Lib/ \; 2>/dev/null || echo "No Email files found in Lib module"
    find vendor/Lib/ICICI -name "*.php" -exec cp --parents {} src/Lib/ \; 2>/dev/null || echo "No ICICI files found in Lib module"
    find vendor/Lib/TPDelivery -name "*.php" -exec cp --parents {} src/Lib/ \; 2>/dev/null || echo "No TPDelivery files found in Lib module"

    echo "PSR-4 conversion completed successfully!"
    echo "Please update your composer.json to include the PSR-4 autoloading configuration:"
    echo
    echo '"autoload": {'
    echo '    "psr-4": {'
    echo '        "App\\\\": "src/"'
    echo '    }'
    echo '}'
    echo
    echo "Then run 'composer dump-autoload' to regenerate the autoloader."
else
    echo "PSR-4 conversion cancelled."
fi

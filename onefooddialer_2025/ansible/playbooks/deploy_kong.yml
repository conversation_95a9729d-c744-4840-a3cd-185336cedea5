---
- name: Deploy Kong API Gateway to EKS
  hosts: bastion
  become: false
  vars:
    namespace: kong
    chart_version: "2.35.1"
    values_file: /tmp/kong-values.yaml
  
  tasks:
    - name: Create namespace
      shell: |
        kubectl create namespace {{ namespace }} --dry-run=client -o yaml | kubectl apply -f -
    
    - name: Create Kong values file
      copy:
        dest: "{{ values_file }}"
        content: |
          # Kong values for Helm chart
          image:
            repository: kong
            tag: "3.4.0"
          
          env:
            database: postgres
          
          admin:
            enabled: true
            http:
              enabled: true
              servicePort: 8001
              containerPort: 8001
            tls:
              enabled: true
              servicePort: 8444
              containerPort: 8444
            ingress:
              enabled: true
              hostname: kong-admin.cubeonebiz.com
              path: /
              annotations:
                kubernetes.io/ingress.class: nginx
                cert-manager.io/cluster-issuer: letsencrypt-prod
                nginx.ingress.kubernetes.io/ssl-redirect: "true"
          
          proxy:
            enabled: true
            type: LoadBalancer
            http:
              enabled: true
              servicePort: 80
              containerPort: 8000
            tls:
              enabled: true
              servicePort: 443
              containerPort: 8443
          
          postgresql:
            enabled: true
            auth:
              username: kong
              password: kong
              database: kong
            persistence:
              size: 10Gi
          
          ingressController:
            enabled: true
            installCRDs: false
          
          plugins:
            configMaps:
            - name: kong-plugin-prometheus
              pluginName: prometheus
            - name: kong-plugin-cors
              pluginName: cors
            - name: kong-plugin-rate-limiting
              pluginName: rate-limiting
            - name: kong-plugin-jwt
              pluginName: jwt
    
    - name: Add Kong Helm repository
      shell: |
        helm repo add kong https://charts.konghq.com
        helm repo update
    
    - name: Deploy Kong using Helm
      shell: |
        helm upgrade --install kong kong/kong \
          --namespace {{ namespace }} \
          --version {{ chart_version }} \
          --values {{ values_file }}
    
    - name: Wait for Kong deployment to be ready
      shell: |
        kubectl rollout status deployment/kong-kong -n {{ namespace }} --timeout=300s
    
    - name: Create Kong plugin ConfigMaps
      shell: |
        kubectl create configmap kong-plugin-prometheus -n {{ namespace }} \
          --from-literal=config='{"status_code_metrics":true,"latency_metrics":true,"bandwidth_metrics":true,"upstream_health_metrics":true}' \
          --dry-run=client -o yaml | kubectl apply -f -
        
        kubectl create configmap kong-plugin-cors -n {{ namespace }} \
          --from-literal=config='{"origins":["*"],"methods":["GET","POST","PUT","DELETE","OPTIONS","PATCH"],"headers":["Accept","Accept-Version","Content-Length","Content-MD5","Content-Type","Date","X-Auth-Token","Authorization"],"exposed_headers":["X-Auth-Token"],"credentials":true,"max_age":3600}' \
          --dry-run=client -o yaml | kubectl apply -f -
        
        kubectl create configmap kong-plugin-rate-limiting -n {{ namespace }} \
          --from-literal=config='{"second":null,"minute":60,"hour":3600,"day":86400,"policy":"local","fault_tolerant":true,"hide_client_headers":false,"redis_timeout":2000,"redis_database":0,"namespace":"kong_rate_limiting"}' \
          --dry-run=client -o yaml | kubectl apply -f -
        
        kubectl create configmap kong-plugin-jwt -n {{ namespace }} \
          --from-literal=config='{"uri_param_names":["jwt"],"cookie_names":[],"claims_to_verify":["exp"],"key_claim_name":"kid","secret_is_base64":false}' \
          --dry-run=client -o yaml | kubectl apply -f -
    
    - name: Get Kong proxy service details
      shell: |
        kubectl get service kong-kong-proxy -n {{ namespace }} -o jsonpath='{.status.loadBalancer.ingress[0].hostname}'
      register: kong_lb
    
    - name: Display Kong API Gateway endpoint
      debug:
        msg: "Kong API Gateway is available at: {{ kong_lb.stdout }}"

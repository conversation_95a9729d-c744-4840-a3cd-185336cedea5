---
- name: Setup EKS Worker Nodes
  hosts: eks_nodes
  become: true
  
  tasks:
    - name: Update all packages
      yum:
        name: "*"
        state: latest
        update_only: yes
    
    - name: Install required packages
      yum:
        name:
          - git
          - unzip
          - wget
          - curl
          - vim
          - htop
          - jq
          - python3
          - python3-pip
          - amazon-cloudwatch-agent
          - amazon-ssm-agent
        state: present
    
    - name: Set up CloudWatch Agent
      copy:
        dest: /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json
        content: |
          {
            "agent": {
              "metrics_collection_interval": 60,
              "run_as_user": "root"
            },
            "logs": {
              "logs_collected": {
                "files": {
                  "collect_list": [
                    {
                      "file_path": "/var/log/messages",
                      "log_group_name": "eks-node-system-logs",
                      "log_stream_name": "{instance_id}-messages"
                    },
                    {
                      "file_path": "/var/log/secure",
                      "log_group_name": "eks-node-security-logs",
                      "log_stream_name": "{instance_id}-secure"
                    },
                    {
                      "file_path": "/var/log/kubelet.log",
                      "log_group_name": "eks-node-kubelet-logs",
                      "log_stream_name": "{instance_id}-kubelet"
                    },
                    {
                      "file_path": "/var/log/kube-proxy.log",
                      "log_group_name": "eks-node-kube-proxy-logs",
                      "log_stream_name": "{instance_id}-kube-proxy"
                    }
                  ]
                }
              }
            },
            "metrics": {
              "metrics_collected": {
                "mem": {
                  "measurement": [
                    "mem_used_percent"
                  ]
                },
                "disk": {
                  "measurement": [
                    "used_percent"
                  ],
                  "resources": [
                    "/"
                  ]
                }
              }
            }
          }
    
    - name: Start CloudWatch Agent
      systemd:
        name: amazon-cloudwatch-agent
        state: started
        enabled: yes
    
    - name: Ensure SSM Agent is running
      systemd:
        name: amazon-ssm-agent
        state: started
        enabled: yes
    
    - name: Set up security hardening
      block:
        - name: Ensure SSH uses strong ciphers
          lineinfile:
            path: /etc/ssh/sshd_config
            regexp: '^Ciphers '
            line: 'Ciphers aes128-ctr,aes192-ctr,aes256-ctr'
            state: present
        
        - name: Disable root login
          lineinfile:
            path: /etc/ssh/sshd_config
            regexp: '^PermitRootLogin '
            line: 'PermitRootLogin no'
            state: present
        
        - name: Disable password authentication
          lineinfile:
            path: /etc/ssh/sshd_config
            regexp: '^PasswordAuthentication '
            line: 'PasswordAuthentication no'
            state: present
        
        - name: Restart SSH service
          systemd:
            name: sshd
            state: restarted
    
    - name: Set up log rotation
      copy:
        dest: /etc/logrotate.d/kubernetes
        content: |
          /var/log/kubelet.log
          /var/log/kube-proxy.log
          {
            rotate 7
            daily
            compress
            missingok
            notifempty
            create 0644 root root
          }

---
- name: Setup Bastion Host
  hosts: bastion
  become: true
  vars:
    php_version: "8.2"
  
  tasks:
    - name: Update all packages
      yum:
        name: "*"
        state: latest
        update_only: yes
    
    - name: Install required packages
      yum:
        name:
          - git
          - unzip
          - wget
          - curl
          - vim
          - htop
          - jq
          - python3
          - python3-pip
          - amazon-cloudwatch-agent
        state: present
    
    - name: Install AWS CLI
      pip:
        name: awscli
        state: latest
        executable: pip3
    
    - name: Install kubectl
      shell: |
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        chmod +x kubectl
        mv kubectl /usr/local/bin/
      args:
        creates: /usr/local/bin/kubectl
    
    - name: Install Helm
      shell: |
        curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
        chmod 700 get_helm.sh
        ./get_helm.sh
        rm get_helm.sh
      args:
        creates: /usr/local/bin/helm
    
    - name: Install PHP repository
      shell: |
        amazon-linux-extras enable php{{ php_version }}
      args:
        creates: /etc/yum.repos.d/amzn2-extras.repo
    
    - name: Install PHP and extensions
      yum:
        name:
          - php
          - php-cli
          - php-common
          - php-json
          - php-mysqlnd
          - php-zip
          - php-gd
          - php-mbstring
          - php-curl
          - php-xml
          - php-pear
          - php-bcmath
          - php-fpm
        state: present
    
    - name: Install Composer
      shell: |
        curl -sS https://getcomposer.org/installer | php
        mv composer.phar /usr/local/bin/composer
        chmod +x /usr/local/bin/composer
      args:
        creates: /usr/local/bin/composer
    
    - name: Configure AWS CLI
      shell: |
        aws configure set region us-east-1
      become: false
    
    - name: Configure kubectl for EKS
      shell: |
        aws eks update-kubeconfig --name cubeonebiz-cluster --region us-east-1
      become: false
      args:
        creates: ~/.kube/config
    
    - name: Set up CloudWatch Agent
      copy:
        dest: /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json
        content: |
          {
            "agent": {
              "metrics_collection_interval": 60,
              "run_as_user": "root"
            },
            "logs": {
              "logs_collected": {
                "files": {
                  "collect_list": [
                    {
                      "file_path": "/var/log/messages",
                      "log_group_name": "bastion-system-logs",
                      "log_stream_name": "{instance_id}-messages"
                    },
                    {
                      "file_path": "/var/log/secure",
                      "log_group_name": "bastion-security-logs",
                      "log_stream_name": "{instance_id}-secure"
                    }
                  ]
                }
              }
            },
            "metrics": {
              "metrics_collected": {
                "mem": {
                  "measurement": [
                    "mem_used_percent"
                  ]
                },
                "disk": {
                  "measurement": [
                    "used_percent"
                  ],
                  "resources": [
                    "/"
                  ]
                }
              }
            }
          }
    
    - name: Start CloudWatch Agent
      systemd:
        name: amazon-cloudwatch-agent
        state: started
        enabled: yes
    
    - name: Set up security hardening
      block:
        - name: Ensure SSH uses strong ciphers
          lineinfile:
            path: /etc/ssh/sshd_config
            regexp: '^Ciphers '
            line: 'Ciphers aes128-ctr,aes192-ctr,aes256-ctr'
            state: present
        
        - name: Disable root login
          lineinfile:
            path: /etc/ssh/sshd_config
            regexp: '^PermitRootLogin '
            line: 'PermitRootLogin no'
            state: present
        
        - name: Disable password authentication
          lineinfile:
            path: /etc/ssh/sshd_config
            regexp: '^PasswordAuthentication '
            line: 'PasswordAuthentication no'
            state: present
        
        - name: Restart SSH service
          systemd:
            name: sshd
            state: restarted

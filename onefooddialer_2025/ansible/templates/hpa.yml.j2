apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ item.name }}
  namespace: {{ item.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ item.name }}
  minReplicas: {{ item.replicas }}
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

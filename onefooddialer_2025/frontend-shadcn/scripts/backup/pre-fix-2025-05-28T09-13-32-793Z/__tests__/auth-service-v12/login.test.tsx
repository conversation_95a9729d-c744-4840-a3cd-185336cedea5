import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthLogin } from '@/components/auth-service-v12/login';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AuthLogin', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AuthLogin />
      </QueryClientProvider>
    );

    expect(screen.getByText('AuthLogin')).toBeInTheDocument();
  });
});
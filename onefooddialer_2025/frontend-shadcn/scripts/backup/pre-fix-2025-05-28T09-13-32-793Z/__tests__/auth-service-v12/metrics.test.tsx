import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthMetrics } from '@/components/auth-service-v12/metrics';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AuthMetrics', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AuthMetrics />
      </QueryClientProvider>
    );

    expect(screen.getByText('AuthMetrics')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthMetricsJson } from '@/components/auth-service-v12/metrics/json';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AuthMetricsJson', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AuthMetricsJson />
      </QueryClientProvider>
    );

    expect(screen.getByText('AuthMetricsJson')).toBeInTheDocument();
  });
});
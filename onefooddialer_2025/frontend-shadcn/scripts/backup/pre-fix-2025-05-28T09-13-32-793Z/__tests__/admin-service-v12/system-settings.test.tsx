import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AdminSystemSettings } from '@/components/admin-service-v12/system-settings';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AdminSystemSettings', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AdminSystemSettings />
      </QueryClientProvider>
    );

    expect(screen.getByText('AdminSystemSettings')).toBeInTheDocument();
  });
});
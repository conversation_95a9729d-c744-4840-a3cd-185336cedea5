import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]OtpSend } from '@/components/customer-service-v12/[id]/otp/send';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]OtpSend', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]OtpSend />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]OtpSend')).toBeInTheDocument();
  });
});
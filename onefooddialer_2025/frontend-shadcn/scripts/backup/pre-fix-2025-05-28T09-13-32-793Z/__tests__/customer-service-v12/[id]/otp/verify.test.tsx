import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]OtpVerify } from '@/components/customer-service-v12/[id]/otp/verify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]OtpVerify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]OtpVerify />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]OtpVerify')).toBeInTheDocument();
  });
});
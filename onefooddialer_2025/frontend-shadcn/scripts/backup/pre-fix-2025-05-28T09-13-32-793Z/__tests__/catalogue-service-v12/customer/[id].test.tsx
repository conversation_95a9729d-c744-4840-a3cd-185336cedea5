import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueCustomer[id] } from '@/components/catalogue-service-v12/customer/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueCustomer[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueCustomer[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueCustomer[id]')).toBeInTheDocument();
  });
});
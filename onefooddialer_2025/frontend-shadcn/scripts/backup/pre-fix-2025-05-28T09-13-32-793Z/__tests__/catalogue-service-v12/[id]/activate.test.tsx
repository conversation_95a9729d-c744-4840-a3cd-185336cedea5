import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue[id]Activate } from '@/components/catalogue-service-v12/[id]/activate';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Catalogue[id]Activate', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Catalogue[id]Activate />
      </QueryClientProvider>
    );

    expect(screen.getByText('Catalogue[id]Activate')).toBeInTheDocument();
  });
});
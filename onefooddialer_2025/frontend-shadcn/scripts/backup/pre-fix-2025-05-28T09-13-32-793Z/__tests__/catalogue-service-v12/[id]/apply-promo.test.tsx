import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue[id]ApplyPromo } from '@/components/catalogue-service-v12/[id]/apply-promo';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Catalogue[id]ApplyPromo', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Catalogue[id]ApplyPromo />
      </QueryClientProvider>
    );

    expect(screen.getByText('Catalogue[id]ApplyPromo')).toBeInTheDocument();
  });
});
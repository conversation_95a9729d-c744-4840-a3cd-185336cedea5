<?php
/**
 * Local Database Configuration Override
 *
 * This configuration override file is for overriding environment-specific and
 * security-sensitive database configuration information.
 */

return array(
    // Database configuration for development mode
    'db' => array(
        'driver'         => 'Pdo_Sqlite',
        'database'       => __DIR__ . '/../../data/db/mock.sqlite',
        'driver_options' => array(
            PDO::ATTR_PERSISTENT => true
        ),
    ),
    
    // Read node adapter instance (same as write for SQLite)
    'dbr' => array(
        'driver'         => 'Pdo_Sqlite',
        'database'       => __DIR__ . '/../../data/db/mock.sqlite',
        'driver_options' => array(
            PDO::ATTR_PERSISTENT => true
        ),
    ),
    
    // Master database configuration
    'master_db' => array(
        'driver'         => 'Pdo_Sqlite',
        'database'       => __DIR__ . '/../../data/db/mock.sqlite',
        'driver_options' => array(
            PDO::ATTR_PERSISTENT => true
        ),
    ),
    
    // Service manager configuration to use MockDbAdapterFactory
    'service_manager' => array(
        'factories' => array(
            'Zend\Db\Adapter\Adapter' => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
            'Write_Adapter'           => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
            'Read_Adapter'            => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
        ),
    ),
    
    // Demo company ID for development
    'demo_company_id' => 1,
    
    // Admin token for development
    'admin_token' => md5('admin-token-' . time()),
);

<?php
/**
 * Development-specific configuration
 */
return [
    'development_mode' => true,

    // Error reporting
    'display_errors' => true,
    'error_reporting' => E_ALL,

    // Database configuration for development
    'db' => [
        'driver' => 'Pdo_Sqlite',
        'database' => __DIR__ . '/../../data/db/mock.sqlite',
        'driver_options' => [
            PDO::ATTR_PERSISTENT => true
        ],
    ],
    'dbr' => [
        'driver' => 'Pdo_Sqlite',
        'database' => __DIR__ . '/../../data/db/mock.sqlite',
        'driver_options' => [
            PDO::ATTR_PERSISTENT => true
        ],
    ],

    // Demo company ID for development
    'demo_company_id' => 'abc123-demo',

    // Admin token for development
    'admin_token' => md5('admin-token-' . time()),
];

<?php
/**
 * Global Configuration Override
 *
 * You can use this file for overriding configuration values from modules, etc.
 * You would place values in here that are agnostic to the environment and not
 * sensitive to security.
 *
 * @NOTE: In practice, this file will typically be INCLUDED in your source
 * control, so do not include passwords or other sensitive information in this
 * file.
 */

$request_scheme = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] =='on' ) ? "https://" : "http://";

$rootUrl =  $request_scheme.$_SERVER['HTTP_HOST'].'/';

return array(
    /* public key used to encrypt/ecrypt passwords. */
    'public_key' => 'TWpvsxGjSsvxnCrX',

    /* write node adapter instance */
    'db' => array(
        'driver'         => 'Pdo',
        'driver_options' => array(
            PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES \'UTF8\''
        ),
    ),

    /* read node adapter instance */
    'dbr'=> array(
        'driver'         => 'Pdo',
        'driver_options' => array(
            PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES \'UTF8\''
        )
    ),

    'zf-oauth2' => array(
        'db' => array(
            'dsn'      => '', // for example "mysql:dbname=oauth2_db;host=localhost"
            'username' => '',
            'password' => '',
        ),
        'allow_implicit' => true, // default (set to true when you need to support browser-based or mobile apps)
        'access_lifetime' => 3600, // default (set a value in seconds for access tokens lifetime)
        'enforce_state'  => true,  // default
        'storage'        => 'ZF\OAuth2\Adapter\PdoAdapter', // service name for the OAuth2 storage adapter
    ),
	/**
	 * This variable is used to define the services which are used through allover the system
	 * @var Zend\ServiceManager service_manager
	 */
    'service_manager' => array(
        'factories' => array(
           'Zend\Db\Adapter\Adapter' => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
           'Write_Adapter' => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
           'Read_Adapter' => 'Lib\QuickServe\Factory\MockDbAdapterFactory',
        ),
    ),

    // Enable development mode
    'development_mode' => true,

    /**
     * This variable used to define the URL of the development
     *
     * @var string root_url
     */

	'root_url'=> $rootUrl,

    /**
     * This array contains the path of the product,default_product_img etc
     * @var array source_path
     */
	'source_path' => array (
		/**
		 * This variable contains the path of product images
		 * @var string product
		 */
		'product' => '/data/products/',
		/**
		 * This variable contains the path of default product image
		 * When product image is not applied then this image is applied as default product image
		 *
		 * @var string product
		 */
		'default_product_img' => '/data/products/default_product.png',
		/**
		 * @deprecated No longer used by internal code and not recommended.
		 */
		'small' => './public/data/templates/thumb/',
		/**
		 * @deprecated No longer used by internal code and not recommended.
		 */
		'medium' => './public/data/templates/medium/',
		/**
		 * @deprecated No longer used by internal code and not recommended.
		 */
		'large' => './public/data/templates/large/'
	),
	/**
	 * This variable defines the start time & end time of an order booking
	 *
	 * @var array cutofftime
	 */

	/**
	 * This variable defines the MEAL product image size
	 * This credentials applied to the image while uploading product on system
	 * The image is cropped and set the below credential as of product image
	 *
	 * @var array product_image_resize
	 */
	'product_image_resize'=>array(
		'height'=>333,
		'width'=>500
	),
	/**
	 * This variable defines the EXTRA product image size
	 * This credentials applied to the image while uploading product on system
	 * The image is cropped and set the below credential as of product image
	 *
	 * @var array extra_product_image_resize
	 */
	'extra_product_image_resize'=>array(
		'height'=>333,
		'width'=>500
	),
	/**
	 * When Invoice is generated the Due date is calculated by adding the following variable
	 * It is in days
	 *
	 * @var int[days] invoice_grace_period
	 */
	'invoice_grace_period'	=> '0',
	/**
	 *This variable defines that the product price are exclusive
	 *
	 *@var boolean tax_exclusive
	 */
	'tax_exclusive'	=> true,
	/**
	 * This array element contains an array of supported non-english languages that the application is intended to support.
	 * Foreach element of this array, the key is the ISO 639-2 Code of the language and the value is an English name for that language.
	 *
	 * @var array supported_nonenglish_languages
	 */
	'supported_nonenglish_languages' => array(
		'guj' => 'Gujarati',
		'hin' => 'Hindi',
		'kan' => 'Kannada',
		'kok' => 'Konkani',
		'mar' => 'Marathi',
		'mal' => 'Malayalam',
		'tel' => 'Telugu',
		'tam' => 'Tamil'
	),

	'http_request_scheme' => $request_scheme,

	'fooddialer_contact_info' =>array(
		'customer_support' => array(
			'email' =>'<EMAIL>',
			'phone' =>'+91 22 20870052'
		),
		'tech_support'=>array(
			'email' =>'<EMAIL>',
			'phone' =>'+91 22 20870052'

		),
		'billing_support' =>array(
			//'email' =>'<EMAIL>',
			'email' =>'<EMAIL>',
			'phone' =>'+91 22 20870058'

		),
	),
	'application_version'=>'3.1',
	'aws_s3_credentials'=>array(
		'access_key'=>'********************',
		'secret_key'=>'snCk9n6YgK9Fj3Cq9bYrBegje2IY0PYwtf2i+FDQ',
	    	'region'=> 'ap-south-1',
    		'signatureVersion'=> 'v4',
		//'access_key'=>'********************',
		//'secret_key'=>'Kgs7yOAstvzlIDn3NAxiiKpy+pNo4iddm8eAN+fQ',
//                'access_key'=>'********************',
//                'secret_key'=>'Gh2RGcLUoRZyk1vvURvSvH9BqeNNlvcXvbllzWEb',
	),
	'show_all_food_type'=>'yes',
    'delivery_time'=>array(
         'start'=>"09:00",
         'end'=>"23:50"
    ),
    'sms_configuration' => array(
        '247'=>array(
            'Email' => '<EMAIL>',
            'Password' => '123456789a',
            'ServiceName' => 'TEMPLATE_BASED',
        ),
        'plivo'=>array(
            'auth_id' =>"MAMDC3NDI3MDAWMJIZMJ",
            'auth_token' =>"NjE4NTMzZWFiZTgzYjc0ZTk1OTVlZDBhNjc2MWY0",

        )
	),
    'social'	=> array(
        'facebook'	=> $rootUrl.'admin/social/facebook.png',
        'google_plus'	=> $rootUrl.'admin/social/google-plus.png',
        'twitter'	=> $rootUrl.'admin/social/twitter_mail.png'
    ),
    'powered_by' => $rootUrl.'admin/social/logo1.png',
    'logo'  => $rootUrl.'admin/social/logo.png',

    // Keycloak configuration
    'keycloak' => array(
        'auth_server_url' => 'http://localhost:8080/auth',
        'realm' => 'tenant',
        'client_id' => 'tenant-app',
        'client_secret' => 'your-client-secret',
        'redirect_uri' => $rootUrl.'keycloak-callback',
    ),
);

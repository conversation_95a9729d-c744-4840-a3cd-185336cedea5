<?php
/**
 * Session Configuration
 *
 * This file contains the configuration for the session management.
 * It includes settings for session lifetime, cookie security, and session validation.
 */

// Load environment variables
$sessionLifetime = getenv('SESSION_LIFETIME') ?: 1800; // 30 minutes in seconds
$rememberMeLifetime = getenv('REMEMBER_ME_LIFETIME') ?: 2592000; // 30 days in seconds

// Determine if we're in a secure environment
$isSecureEnvironment = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';

// Get the current path for session storage
$sessionPath = __DIR__ . '/../../data/session';
if (!is_dir($sessionPath)) {
    mkdir($sessionPath, 0755, true);
}

return [
    'session' => [
        'config' => [
            'class' => 'Zend\Session\Config\SessionConfig',
            'options' => [
                // Session naming and storage
                'name' => 'tenant_secure_session',
                'save_path' => $sessionPath,

                // Cookie settings
                'use_cookies' => true,
                'cookie_lifetime' => $sessionLifetime,
                'cookie_httponly' => true,
                'cookie_secure' => $isSecureEnvironment,
                'cookie_samesite' => 'Strict',

                // Session garbage collection
                'gc_maxlifetime' => $sessionLifetime,
                'gc_probability' => 1,
                'gc_divisor' => 100,

                // Remember me functionality
                'remember_me_seconds' => $rememberMeLifetime,

                // Session security
                'use_strict_mode' => true,
                'use_only_cookies' => true,
                'entropy_length' => 32,
                'entropy_file' => '/dev/urandom',
                'hash_function' => 'sha256',
                'hash_bits_per_character' => 5,
            ],
        ],
        'storage' => 'Zend\Session\Storage\SessionArrayStorage',
        'validators' => [
            'Zend\Session\Validator\RemoteAddr',
            'Zend\Session\Validator\HttpUserAgent',
        ],
    ],
];

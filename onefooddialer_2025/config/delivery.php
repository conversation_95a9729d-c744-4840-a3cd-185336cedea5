<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Delivery Service Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the delivery service.
    |
    */
    
    // Date format for displaying dates
    'date_format' => env('DELIVERY_DATE_FORMAT', 'Y-m-d'),
    
    // Merchant address for third-party delivery services
    'merchant_address' => env('MERCHANT_ADDRESS', 'Default Merchant Address'),
    
    // Pickup times for different meal types
    'lunch_pickup_time' => env('LUNCH_PICKUP_TIME', '12:30:00'),
    'dinner_pickup_time' => env('DINNER_PICKUP_TIME', '19:30:00'),
    
    // YourGuy third-party delivery service configuration
    'yourguy' => [
        'base_url' => env('YOURGUY_API_BASE_URL', 'http://yourguytestserver.herokuapp.com/api/'),
        'version' => env('YOURGUY_API_VERSION', 'v2'),
        'auth_token' => env('YOURGUY_AUTH_TOKEN', 'MTIzNDU1NDMyMTp2ZW5kb3I='),
    ],
    
    // RoadRunner third-party delivery service configuration
    'roadrunner' => [
        'base_url' => env('ROADRUNNER_API_BASE_URL', ''),
        'version' => env('ROADRUNNER_API_VERSION', 'v1'),
        'auth_token' => env('ROADRUNNER_AUTH_TOKEN', ''),
    ],
];

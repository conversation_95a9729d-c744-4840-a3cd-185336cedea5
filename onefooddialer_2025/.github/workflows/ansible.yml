name: "Ansible Configuration"

on:
  push:
    branches:
      - main
    paths:
      - 'ansible/**'
  pull_request:
    branches:
      - main
    paths:
      - 'ansible/**'
  workflow_run:
    workflows: ["Terraform Infrastructure"]
    types:
      - completed

jobs:
  ansible-lint:
    name: "Ansible Lint"
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install ansible ansible-lint

      - name: Lint Ansible playbooks
        run: |
          ansible-lint ansible/playbooks/*.yml

  ansible-deploy:
    name: "Ansible Deploy"
    needs: ansible-lint
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install ansible boto3 botocore

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Set up SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H bastion.cubeonebiz.com >> ~/.ssh/known_hosts

      - name: Run Ansible playbooks
        working-directory: ./ansible
        run: |
          ansible-playbook playbooks/setup_bastion.yml
          ansible-playbook playbooks/setup_eks_nodes.yml
          ansible-playbook playbooks/deploy_kong.yml
          ansible-playbook playbooks/deploy_microservices.yml

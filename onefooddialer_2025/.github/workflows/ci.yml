name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth-service-v12, payment-service-v12, quickserve-service-v12, customer-service-v12]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, intl, pdo, pdo_mysql, redis
          tools: composer:v2, phpcs, phpstan

      - name: Get Composer Cache Directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        run: |
          cd services/${{ matrix.service }}
          composer install --prefer-dist --no-progress

      - name: Run PHP_CodeSniffer
        run: |
          cd services/${{ matrix.service }}
          phpcs --standard=PSR12 app tests

      - name: Run PHPStan
        run: |
          cd services/${{ matrix.service }}
          phpstan analyse app tests --level=5

  test:
    name: Test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service: [auth-service-v12, payment-service-v12, quickserve-service-v12, customer-service-v12]
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: testing
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      
      redis:
        image: redis:6-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, intl, pdo, pdo_mysql, redis
          coverage: xdebug
          tools: composer:v2

      - name: Get Composer Cache Directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        run: |
          cd services/${{ matrix.service }}
          composer install --prefer-dist --no-progress

      - name: Prepare environment
        run: |
          cd services/${{ matrix.service }}
          cp .env.example .env
          php artisan key:generate
          php artisan config:clear
          php artisan migrate --seed --force

      - name: Run tests
        run: |
          cd services/${{ matrix.service }}
          php artisan test --coverage --min=90

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v3
        with:
          directory: ./services/${{ matrix.service }}/coverage
          flags: ${{ matrix.service }}
          name: ${{ matrix.service }}
          fail_ci_if_error: true

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [lint, test]
    if: github.event_name == 'push'
    strategy:
      matrix:
        service: [auth-service-v12, payment-service-v12, quickserve-service-v12, customer-service-v12]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ghcr.io/${{ github.repository }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha,format=short

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: ./services/${{ matrix.service }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

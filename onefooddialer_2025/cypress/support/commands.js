// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

// Custom command to login
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'password') => {
  cy.visit('/login');
  cy.get('input[name="email"]').type(email);
  cy.get('input[name="password"]').type(password);
  cy.get('button[type="submit"]').click();
  cy.url().should('not.include', '/login');
});

// Custom command to check if element is visible
Cypress.Commands.add('isVisible', { prevSubject: 'element' }, (subject) => {
  const isVisible = (elem) => !!(
    elem.offsetWidth ||
    elem.offsetHeight ||
    elem.getClientRects().length
  );
  expect(isVisible(subject[0])).to.be.true;
  return subject;
});

// Custom command to check if element contains text
Cypress.Commands.add('containsText', { prevSubject: 'element' }, (subject, text) => {
  expect(subject.text().trim()).to.include(text);
  return subject;
});

// Custom command to wait for API request to complete
Cypress.Commands.add('waitForApi', (method, url) => {
  cy.intercept(method, url).as('apiRequest');
  cy.wait('@apiRequest');
});

// Custom command to select option from dropdown
Cypress.Commands.add('selectOption', { prevSubject: 'element' }, (subject, value) => {
  cy.wrap(subject).select(value);
  return subject;
});

// Custom command to check if element has class
Cypress.Commands.add('hasClass', { prevSubject: 'element' }, (subject, className) => {
  expect(subject).to.have.class(className);
  return subject;
});

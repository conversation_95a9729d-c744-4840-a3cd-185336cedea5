describe('Dashboard', () => {
  beforeEach(() => {
    // Mock the authentication
    cy.intercept('POST', '/v2/auth/login', {
      statusCode: 200,
      body: {
        token: 'fake-jwt-token',
        user: {
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
        },
      },
    }).as('loginRequest');

    // Mock the dashboard data
    cy.intercept('GET', '/v2/dashboard/stats', {
      statusCode: 200,
      body: {
        data: {
          customers: {
            total: 1248,
            new: 42,
            growth: 12.5,
          },
          orders: {
            total: 356,
            new: 24,
            growth: 8.2,
          },
          revenue: {
            total: 28456.78,
            new: 1245.89,
            growth: 15.3,
          },
          kitchen: {
            pending: 12,
            inProgress: 8,
            ready: 5,
          },
          delivery: {
            pending: 7,
            inTransit: 15,
            delivered: 342,
          },
        },
      },
    }).as('dashboardStatsRequest');

    // Login and visit the dashboard
    cy.login();
    cy.visit('/dashboard');
  });

  it('should display the dashboard title', () => {
    cy.get('h1').should('contain', 'Dashboard');
  });

  it('should display the customer stats', () => {
    cy.get('[data-testid="customer-stats"]').within(() => {
      cy.get('[data-testid="total-customers"]').should('contain', '1,248');
      cy.get('[data-testid="customer-growth"]').should('contain', '12.5%');
    });
  });

  it('should display the order stats', () => {
    cy.get('[data-testid="order-stats"]').within(() => {
      cy.get('[data-testid="total-orders"]').should('contain', '356');
      cy.get('[data-testid="order-growth"]').should('contain', '8.2%');
    });
  });

  it('should display the revenue stats', () => {
    cy.get('[data-testid="revenue-stats"]').within(() => {
      cy.get('[data-testid="total-revenue"]').should('contain', '$28,456.78');
      cy.get('[data-testid="revenue-growth"]').should('contain', '15.3%');
    });
  });

  it('should navigate to the customer page when clicking on the customer stats', () => {
    cy.get('[data-testid="customer-stats"]').click();
    cy.url().should('include', '/customer');
  });

  it('should navigate to the order page when clicking on the order stats', () => {
    cy.get('[data-testid="order-stats"]').click();
    cy.url().should('include', '/order');
  });

  it('should navigate to the payment page when clicking on the revenue stats', () => {
    cy.get('[data-testid="revenue-stats"]').click();
    cy.url().should('include', '/payment');
  });

  it('should display the tabs', () => {
    cy.get('[role="tablist"]').within(() => {
      cy.get('[role="tab"]').should('have.length', 5);
      cy.get('[role="tab"]').eq(0).should('contain', 'Overview');
      cy.get('[role="tab"]').eq(1).should('contain', 'Orders');
      cy.get('[role="tab"]').eq(2).should('contain', 'Kitchen');
      cy.get('[role="tab"]').eq(3).should('contain', 'Delivery');
      cy.get('[role="tab"]').eq(4).should('contain', 'Analytics');
    });
  });

  it('should switch tabs when clicking on a tab', () => {
    cy.get('[role="tab"]').eq(1).click();
    cy.get('[role="tabpanel"]').should('contain', 'Orders');

    cy.get('[role="tab"]').eq(2).click();
    cy.get('[role="tabpanel"]').should('contain', 'Kitchen');

    cy.get('[role="tab"]').eq(3).click();
    cy.get('[role="tabpanel"]').should('contain', 'Delivery');

    cy.get('[role="tab"]').eq(4).click();
    cy.get('[role="tabpanel"]').should('contain', 'Analytics');

    cy.get('[role="tab"]').eq(0).click();
    cy.get('[role="tabpanel"]').should('contain', 'Overview');
  });

  it('should display the quick actions', () => {
    cy.get('[data-testid="quick-actions"]').within(() => {
      cy.get('button').should('have.length', 4);
      cy.get('button').eq(0).should('contain', 'Create Order');
      cy.get('button').eq(1).should('contain', 'Add Customer');
      cy.get('button').eq(2).should('contain', 'View Kitchen Orders');
      cy.get('button').eq(3).should('contain', 'View Deliveries');
    });
  });

  it('should navigate to the correct page when clicking on a quick action', () => {
    cy.get('[data-testid="quick-actions"]').within(() => {
      cy.get('button').eq(0).click();
    });
    cy.url().should('include', '/order/new');

    cy.visit('/dashboard');
    cy.get('[data-testid="quick-actions"]').within(() => {
      cy.get('button').eq(1).click();
    });
    cy.url().should('include', '/customer/new');

    cy.visit('/dashboard');
    cy.get('[data-testid="quick-actions"]').within(() => {
      cy.get('button').eq(2).click();
    });
    cy.url().should('include', '/kitchen/orders');

    cy.visit('/dashboard');
    cy.get('[data-testid="quick-actions"]').within(() => {
      cy.get('button').eq(3).click();
    });
    cy.url().should('include', '/delivery/orders');
  });
});

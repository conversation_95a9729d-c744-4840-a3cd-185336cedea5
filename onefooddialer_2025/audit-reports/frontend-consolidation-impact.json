{"target_path": "frontend-shadcn", "audit_timestamp": "2025-12-23T12:00:00Z", "mode": "dry-run", "references": {"code_references": [], "import_statements": [], "config_references": ["./docker-compose.frontend.dev.yml", "./docker-compose.frontend.yml", "./migrate-frontend.sh"], "docker_references": ["./docker-compose.frontend.dev.yml", "./docker-compose.frontend.yml"], "test_references": [], "documentation_references": ["./COMPREHENSIVE_FRONTEND_IMPLEMENTATION_SUMMARY.md", "./FRONTEND_SETUP.md", "./FRONTEND_UI_GENERATION_COMPLETE.md", "./frontend-integration-audit-report.md"]}, "risk_assessment": {"level": "MEDIUM", "factors": ["Multiple frontend directories exist with potential duplication", "Docker compose files reference frontend services", "Migration scripts exist for frontend consolidation", "Documentation references multiple frontend implementations"], "recommendations": ["Establish frontend-shadcn as primary frontend application", "Archive or remove duplicate frontend directories", "Update Docker configurations to use unified frontend", "Preserve existing functionality during consolidation"]}, "migration_required": true, "safe_to_proceed": true, "detailed_analysis": {"existing_frontend_directories": ["frontend-shadcn", "unified-frontend", "consolidated-frontend", "frontend", "new-frontend", "temp-shadcn-ui"], "primary_frontend": {"directory": "frontend-shadcn", "technology": "Next.js 14 with shadcn/ui", "status": "most_complete_implementation", "features": ["Next.js 14 App Router", "TypeScript", "Tailwind CSS", "shadcn/ui components", "React Hook Form", "Zod validation", "Jest testing", "ESLint configuration"]}, "duplicate_directories": [{"name": "unified-frontend", "status": "duplicate_implementation", "action": "archive"}, {"name": "consolidated-frontend", "status": "partial_implementation", "action": "archive"}, {"name": "frontend", "status": "legacy_implementation", "action": "archive"}, {"name": "new-frontend", "status": "incomplete_implementation", "action": "remove"}, {"name": "temp-shadcn-ui", "status": "temporary_directory", "action": "remove"}], "docker_configurations": ["docker-compose.frontend.dev.yml", "docker-compose.frontend.yml"], "consolidation_strategy": {"approach": "Establish frontend-shadcn as primary, archive others", "primary_directory": "frontend-shadcn", "archive_directory": "archived-frontends", "update_requirements": ["Update Docker configurations", "Update documentation references", "Update build scripts", "Preserve component libraries"]}}}
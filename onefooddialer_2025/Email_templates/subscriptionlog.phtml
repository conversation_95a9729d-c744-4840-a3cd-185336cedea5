<?php
/**
 * This is the header of an email template
* It is same for all the templates
* It includes logo,facebook,twitter link of the fooddialer system
*/
return <<<HEREDOC
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<title>Fooddialer</title>

<!--[if gte mso 6]>
        <style>
            table.mcnFollowContent {width:100% !important;}
            table.mcnShareContent {width:100% !important;}
        </style>
        <![endif]-->
<style type="text/css">
			ol, li {
				margin: 0px;
				padding: 0px;
			}
			body, #bodyTable, #bodyCell {
				height: 100% !important;
				margin: 0;
				padding: 0;
				width: 100% !important;
				font-family: Arial, Helvetica, sans-serif !important;
			}
			table {
				border-collapse: collapse;
			}
			img, a img {
				border: 0;
				outline: none;
				text-decoration: none;
			}
			h1, h2, h3, h4, h5, h6 {
				margin: 0;
				padding: 0;
			}
			p {
				margin: 1em 0;
				padding: 0;
			}
			a {
				word-wrap: break-word;
			}
			.ReadMsgBody {
				width: 100%;
			}
			.ExternalClass {
				width: 100%;
			}
			.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
				line-height: 100%;
			}
			table, td {
				mso-table-lspace: 0pt;
				mso-table-rspace: 0pt;
			}
			#outlook a {
				padding: 0;
			}
			img {
				-ms-interpolation-mode: bicubic;
			}
			body, table, td, p, a, li, blockquote {
				-ms-text-size-adjust: 100%;
				-webkit-text-size-adjust: 100%;
			}
			#bodyCell {
				padding: 0 20px 40px;
			}
			.mcnImage {
				vertical-align: bottom;
			}
			.mcnTextContent img {
				height: auto !important;
			}
			a.mcnButton {
				display: block;
			}
			body {
				background-color: #;
			}
			body, #bodyTable {
				background-image: ;
				background-position: top left;
				background-repeat: repeat;
			}
			#bodyCell {
				border-top: 0;
			}
			#templateContainer {
				border: 0;
			}
			h1 {
				color: #FFFFFF !important;
				display: block;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 30px;
				font-style: normal;
				font-weight: normal;
				line-height: 125%;
				letter-spacing: normal;
				margin: 0;
				text-align: center;
			}
			h2 {
				color: #404040 !important;
				display: block;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 30px;
				font-style: normal;
				font-weight: normal;
				line-height: 125%;
				letter-spacing: normal;
				margin: 0;
				text-align: left;
			}
			h3 {
				color: #FFFFFF !important;
				display: block;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 24px;
				font-style: normal;
				font-weight: normal;
				line-height: 125%;
				letter-spacing: normal;
				margin: 0;
				text-align: center;
			}
			h4 {
				color: #202020 !important;
				display: block;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 16px;
				font-style: normal;
				font-weight: normal;
				line-height: 125%;
				letter-spacing: normal;
				margin: 0;
				text-align: left;
			}
			#templatePreheader {
				background-color: #afaf10;
				border-top: 0;
				border-bottom: 0;
			}
			.preheaderContainer .mcnTextContent, .preheaderContainer .mcnTextContent p {
				color: #FFFFFF;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 11px;
				line-height: 125%;
				text-align: left;
			}
			.preheaderContainer .mcnTextContent a {
				color: #FFFFFF;
				font-weight: normal;
				text-decoration: underline;
			}
			#templateHeader {
				background-color: #FFFFFF;
				border-top: 0;
				border-bottom: 0;
			}
			.headerContainer .mcnTextContent, .headerContainer .mcnTextContent p {
				color: #202020;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 15px;
				line-height: 150%;
				text-align: left;
			}
			.headerContainer .mcnTextContent a {
				color: #ED1C24;
				font-weight: normal;
				text-decoration: underline;
			}
			#templateBody {
				background-color: #FFFFFF;
				border-top: 0;
				border-bottom: 0;
			}
			.bodyContainer .mcnTextContent, .bodyContainer .mcnTextContent p {
				color: #202020;
				font-size: 15px;
				line-height: 150%;
				text-align: left;
			}
			.bodyContainer .mcnTextContent a {
				color: #ED1C24;
				font-weight: normal;
				text-decoration: underline;
			}
			#templateFooter {
				background-color: #afaf10;
				border-top: 0;
				border-bottom: 0;
			}
			.footerContainer .mcnTextContent, .footerContainer .mcnTextContent p {
				color: #FFFFFF;
				font-family: Arial, Helvetica, sans-serif;
				font-size: 11px;
				line-height: 125%;
				text-align: center;
			}
			.footerContainer .mcnTextContent a {
				color: #000;
				font-weight: normal;
				text-decoration: underline;
			}
			.bb {
				border-bottom: 1px solid #D0D0D0;
			}
			table.welcome_page {
    empty-cells: show;
    border: 1px solid #707074;
    margin: 0 auto;
}

.welcome_page td, table.welcome_page th {
    min-width: 2em;
    min-height: 2em;
    border: 1px solid #707074;
    padding: 5px 10px;
}
			@media only screen and (max-width: 600px) {
				body, table, td, p, a, li, blockquote {
					-webkit-text-size-adjust: none !important;
				}
			}
			@media only screen and (max-width: 600px) {
				body {
					width: 100% !important;
					min-width: 100% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[id=bodyCell] {
					padding: 0 10px 40px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[class=mcnTextContentContainer] {
					width: 100% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[class=mcnBoxedTextContentContainer] {
					width: 100% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[class=mcpreview-image-uploader] {
					width: 100% !important;
					display: none !important;
				}
			}
			@media only screen and (max-width: 600px) {
				img[class=mcnImage] {
					width: 100% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[class=mcnImageGroupContentContainer] {
					width: 100% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnImageGroupContent] {
					padding: 9px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnImageGroupBlockInner] {
					padding-bottom: 0 !important;
					padding-top: 0 !important;
				}
			}
			@media only screen and (max-width: 600px) {
				tbody[class=mcnImageGroupBlockOuter] {
					padding-bottom: 9px !important;
					padding-top: 9px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[class=mcnCaptionTopContent], table[class=mcnCaptionBottomContent] {
					width: 100% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[class=mcnCaptionLeftTextContentContainer], table[class=mcnCaptionRightTextContentContainer], table[class=mcnCaptionLeftImageContentContainer], table[class=mcnCaptionRightImageContentContainer], table[class=mcnImageCardLeftTextContentContainer], table[class=mcnImageCardRightTextContentContainer] {
					width: 100% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnImageCardLeftImageContent], td[class=mcnImageCardRightImageContent] {
					padding-right: 18px !important;
					padding-left: 18px !important;
					padding-bottom: 0 !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnImageCardBottomImageContent] {
					padding-bottom: 9px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnImageCardTopImageContent] {
					padding-top: 18px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnImageCardLeftImageContent], td[class=mcnImageCardRightImageContent] {
					padding-right: 18px !important;
					padding-left: 18px !important;
					padding-bottom: 0 !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnImageCardBottomImageContent] {
					padding-bottom: 9px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnImageCardTopImageContent] {
					padding-top: 18px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[class=mcnCaptionLeftContentOuter] td[class=mcnTextContent], table[class=mcnCaptionRightContentOuter] td[class=mcnTextContent] {
					padding-top: 9px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnCaptionBlockInner] table[class=mcnCaptionTopContent]:last-child td[class=mcnTextContent] {
					padding-top: 18px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnBoxedTextContentColumn] {
					padding-left: 18px !important;
					padding-right: 18px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=mcnTextContent] {
					padding-right: 18px !important;
					padding-left: 18px !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[id=templateContainer], table[id=templatePreheader], table[id=templateHeader], table[id=templateBody], table[id=templateFooter] {
					max-width: 600px !important;
					width: 100% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				h1 {
					font-size: 24px !important;
					line-height: 125% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				h2 {
					font-size: 20px !important;
					line-height: 125% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				h3 {
					font-size: 18px !important;
					line-height: 125% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				h4 {
					font-size: 16px !important;
					line-height: 125% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[class=mcnBoxedTextContentContainer] td[class=mcnTextContent], td[class=mcnBoxedTextContentContainer] td[class=mcnTextContent] p {
					font-size: 18px !important;
					line-height: 125% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				table[id=templatePreheader] {
					display: block !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=preheaderContainer] td[class=mcnTextContent], td[class=preheaderContainer] td[class=mcnTextContent] p {
					font-size: 14px !important;
					line-height: 115% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=headerContainer] td[class=mcnTextContent], td[class=headerContainer] td[class=mcnTextContent] p {
					font-size: 18px !important;
					line-height: 125% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=bodyContainer] td[class=mcnTextContent], td[class=bodyContainer] td[class=mcnTextContent] p {
					font-size: 18px !important;
					line-height: 125% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=footerContainer] td[class=mcnTextContent], td[class=footerContainer] td[class=mcnTextContent] p {
					font-size: 14px !important;
					line-height: 115% !important;
				}
			}
			@media only screen and (max-width: 600px) {
				td[class=footerContainer] a[class=utilityLink] {
					display: block !important;
				}
			}
		</style>
</head>

	<body style="margin: 0;padding: 0;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-position: top left;background-repeat: repeat;height: 100% !important;width: 100% !important;">
<div>
  <table border="0" cellpadding="0" cellspacing="0"  width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;margin: 0;padding: 0; background-position: top left;background-repeat: repeat;height: 100% !important;width: 100% !important; text-align:center">
    <tr>
      <td align="center" valign="top" id="bodyCell" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;margin: 0;padding: 0 0 40px;border-top: 0;height: 100% !important;width: 100% !important;"><!-- BEGIN TEMPLATE // -->

        <table border="0" cellpadding="0" cellspacing="0" width="600" id="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;border: 1px solid #D0D0D0;">
          <tr class="bb">
            <td align="right" valign="top" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><!-- BEGIN PREHEADER // -->

              <div class="header">
                <div class="image" style="max-width:100%; padding: 14px 12px; float:left"> <span style="width:72px; height:72px;font-size: 25px;"> <img src="/admin/images/powerByFooddialer.png" alt="Fooddialer Quickserve" /></span></div>
                <div class="social_icons" style="float:right">
                  <!--<div class="fb" style="float:left; padding:16px 10px 0  0"> <a href="https://www.facebook.com/FoodDialer" target="_blank" title="Facebook"><img src="/admin/social/facebook.png" alt="facebook"/></a></div>
                  <div class="fb" style="float:left;padding:16px 10px 0  0"> <a href="https://plus.google.com/+Fooddialer/about" target="_blank" title="Google Plus"><img src="/admin/social/google-plus.png" alt="google plus"/></a></div>
                  <div class="fb" style="float:left;padding:16px 10px 0  0"> <a href="https://twitter.com/FoodDialer" target="_blank" title="twitter"><img src="/admin/social/twitter_mail.png" alt="twitter"/></a></div>-->
                </div>
              </div>

              <!-- // END PREHEADER --></td>
          </tr>
		  <tr>
            <td align="center" valign="top" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><!-- BEGIN BODY // -->

		
		<!-- Body -->  
		
            <table border="0" cellpadding="0" cellspacing="0" width="600" id="templateBody" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #FFFFFF;border-top: 0;border-bottom: 0;">
                <tr>
                  <td valign="top" class="bodyContainer" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">

                    <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                      <tbody class="mcnTextBlockOuter">
                        <tr>
                          <td valign="top" class="mcnTextBlockInner" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><table border="0" cellpadding="0" cellspacing="0" width="600" class="mcnTextContentContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%; text-align:left">
                            </table></td>
                        </tr>
                      </tbody>
                    </table>
                    <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                      <tbody class="mcnTextBlockOuter">
						<tr>
                          <td valign="top" class="mcnTextBlockInner" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><table border="0" cellpadding="0" cellspacing="0" width="600" class="mcnTextContentContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%; text-align:left">
                              <tbody>
								<tr>
                                  <td valign="top" class="mcnTextContent" style="padding: 9px 18px;font-family:Arial, Helvetica, sans-serif;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #666600;font-size: 15px;line-height: 150%;text-align: left;"><div style="text-align: left; color:#000;"><strong>Dear Admin</strong>&nbsp;<br/>
                                      &nbsp;</div></td>
                                </tr>
                                <tr>
                                  <td valign="top" class="mcnTextContent" style="padding: 9px 18px;font-family:Arial, Helvetica, sans-serif;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #666600;font-size: 15px;line-height: 150%;text-align: left;"><div style="text-align: left; color:#000;"><strong>Please find the below last month ( #date# ) FoodDialer Quick Serve application uses log.</strong>&nbsp;<br/>
                                      &nbsp;</div></td>
                                </tr>
                              </tbody>
                              <tbody class="mcnBoxedTextBlockOuter">
                                <tr>
                                  <td valign="top" class="mcnBoxedTextBlockInner" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><table border="0" cellpadding="0" cellspacing="0" width="600" class="mcnBoxedTextContentContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%; text-align:left">
                                      <tbody>
                                        <tr>
                                          <td style="padding-left: 18px;padding-right: 18px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><table border="0" cellspacing="0" class="mcnTextContentContainer" width="100%" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                                            </table>
                                            <table style="width:100%; line-height: 20px; text-align:center; font-size:12px;" border="1">
											 <tr>
                                                <td style="width:25%">Company Name</td>
                                                <td style="width:25%">#company_name#</td>
                                              </tr>
                                              <tr>
                                                <td style="width:25%">Total Orders</td>
                                                <td style="width:25%">#total_orders#</td>
                                              </tr>
                                              <tr>
                                            	<td style="width:25%">SMS Sent</td>
                                                <td style="width:25%">#sms_sent#</td>
                                              </tr>
                                              <tr>
                                            	<td style="width:25%">Email Sent</td>
                                                <td style="width:25%">#email_sent#</td>
                                              </tr>
                                              <tr>
                                            	<td style="width:25%">Active Customer</td>
                                                <td style="width:25%">#active_customer#</td>
                                              </tr>
                                              <tr>
                                            	<td style="width:25%">Admin Count</td>
                                                <td style="width:25%">#admin_account#</td>
                                              </tr>
                                              <tr>
                                            	<td style="width:25%">User Count</td>
                                                <td style="width:25%">#user_account#</td>
                                              </tr>
                                                <tr>
                                            	<td style="width:25%">Kitchen Count</td>
                                                <td style="width:25%">#kitchen_count#</td>
                                              </tr>
                                                <tr>
                                            	<td style="width:25%">Date</td>
                                                <td style="width:25%">#date#</td>
                                              </tr>
                                              
                                            </table>
											
											</td>
                                        </tr>
                                      </tbody>
                                      <tbody>
	                                  <tr>
                                          <td valign="top" class="mcnTextContent" style="padding: 9px 18px;font-family:Arial, Helvetica, sans-serif;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #666600;font-size: 14px;line-height: 150%;text-align: left;"><div style="text-align: left; color:#000;">Based on above uses log, Invoice for the month #date# will be calculated. In case of any query, feel free to drop a mail at: <EMAIL>
                                              &nbsp;</div></td>
                                      </tr>
                                      </tbody>
                                    </table></td>
                                </tr>
                              </tbody>
                            </table></td>
                        </tr>
                      </tbody>
                    </table>
		
		</td>
		</tr>
		
		<tr>
		<td>
		 <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnButtonBlock" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                      <tbody class="mcnButtonBlockOuter">
                        <tr>
                          <td style="padding-top: 0;padding-right: 18px;padding-bottom: 0px;padding-left: 18px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" valign="top" class="mcnButtonBlockInner"><table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnButtonContentContainer" style="border-top-left-radius: 3px;border-top-right-radius: 3px;border-bottom-right-radius: 3px;border-bottom-left-radius: 3px;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                              <tbody>
                                <tr>
                                  <td align="center" valign="middle" class="mcnButtonContent" style="font-family:Arial, Helvetica, sans-serif;font-size: 14px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><div class="mcnButton " style="letter-spacing: -0.5px;line-height: 66%;text-decoration: none;color: #333;word-wrap: break-word;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;display: block; text-align:left">
                                      <div> <span style="font-size:14px">- -</span><br/>
                                        &nbsp;</div>
                                      <div><span style="font-size:14px">Thank You</span><br/>
                                        &nbsp;</div>
                                      <div><span style="font-size:14px">Billing Team Fooddialer</span><br/>
                                        &nbsp;</div>
                                      <div style="font-size:14px; text-align:left;"><span>www.fooddialer.com</span><br/>
                                        &nbsp;</div>
                                    </div></td>
                                </tr>
                              </tbody>
                            </table></td>
                        </tr>
                      </tbody>
                    </table>
			</td>
		</tr>
		
		<!-- Footer -->  
		<tr>
			<td align="center" valign="top" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"><!-- BEGIN FOOTER // -->

              <table border="0" cellpadding="0" cellspacing="0" width="600" id="templateFooter" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #E1671F;border-top: 0;border-bottom: 0;">
                <tr>
                  <td valign="top" class="footerContainer" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                  <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                      <tbody class="mcnTextBlockOuter">
                        <tr>
                          <td valign="top" class="mcnTextBlockInner" style="mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                          <table border="0" cellpadding="0" cellspacing="0" width="600" class="mcnTextContentContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%; text-align:left">
                              <tbody>
                                <tr>
                                  <td valign="top" class="mcnTextContent" style="padding-top: 9px;padding-right: 18px;padding-left: 18px;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;color: #000;font-family: Arialfont-size: 11px;line-height: 125%;text-align: center;"><div style="text-align: center; color:#ffffff; line-height: 20px;"><em>Copyright &copy; 2015, Fooddialer All rights reserved.</em></div>
                                    &nbsp;
                                   	 <div style="text-align: center; color:#ffffff; font-size:13px"><strong>Our mailing address is:</strong></div>
                                   	 <div style="text-align: center; line-height: 18px; color:#ffffff; "><em> <EMAIL></em><br/>
                                      <div style="text-align: center;"><strong>Working Hours: Mon - Fri(9.30 AM - 6.30 PM)</strong></div>
                                      <div style=" float:right; color:#ffffff; font-size:13px; margin: 0 0 10px 0; font-size:11px;">powered by<a href="http://www.fooddialer.com/" target="_blank"><img src="/admin/images/powerByFooddialer.png" alt="fooddialer" width="32" height="34" style="vertical-align: middle; margin-left:10px;"/></a></div>
                                    </div></td>
                                </tr>
                              </tbody>
                            </table>
                            </td>
                        </tr>
                      </tbody>
                    </table></td>
                </tr>
              </table>

              <!-- // END FOOTER --></td>
             </tr>
                <tr>

          </tr>
              </table>
              <!-- // END BODY --></td>
          </tr>
        </table>
        <!-- // END TEMPLATE --></td>
    </tr>
  </table>
</div>
</body>	
		
		
</html>
            
HEREDOC;

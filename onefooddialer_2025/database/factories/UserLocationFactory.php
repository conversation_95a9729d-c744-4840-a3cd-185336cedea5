<?php

namespace Database\Factories;

use App\Models\DeliveryLocation;
use App\Models\User;
use App\Models\UserLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserLocation>
 */
class UserLocationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserLocation::class;
    
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => 1,
            'unit_id' => 1,
            'user_id' => User::factory()->deliveryPerson(),
            'location_id' => DeliveryLocation::factory(),
            'status' => fake()->boolean(80),
        ];
    }
}

<?php

namespace Database\Factories;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * Customer Factory
 * 
 * This factory creates test customers.
 */
class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'customer_name' => $this->faker->name,
            'phone' => $this->faker->unique()->numerify('##########'),
            'email_address' => $this->faker->unique()->safeEmail,
            'customer_Address' => $this->faker->address,
            'location_code' => 'LOC' . $this->faker->numberBetween(1, 100),
            'location_name' => $this->faker->city,
            'food_preference' => $this->faker->randomElement(['Veg', 'Non-Veg', 'Vegan']),
            'city' => $this->faker->city,
            'city_name' => $this->faker->city,
            'company_name' => $this->faker->company,
            'group_code' => 'GRP' . $this->faker->numberBetween(1, 100),
            'group_name' => $this->faker->word,
            'registered_on' => now(),
            'registered_from' => $this->faker->randomElement(['Web', 'Mobile', 'Admin']),
            'status' => 1,
            'password' => bcrypt('password'),
            'phone_verified' => 1,
            'email_verified' => 1,
            'subscription_notification' => $this->faker->boolean,
            'source' => $this->faker->randomElement(['Web', 'Mobile', 'Referral']),
            'referer' => $this->faker->optional()->url,
            'alt_phone' => $this->faker->optional()->numerify('##########'),
            'company_id' => 1,
            'unit_id' => 1,
            'isguest' => 0,
            'delivery_note' => $this->faker->optional()->sentence,
        ];
    }

    /**
     * Indicate that the customer is inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 0,
            ];
        });
    }

    /**
     * Indicate that the customer is a guest.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function guest()
    {
        return $this->state(function (array $attributes) {
            return [
                'isguest' => 1,
            ];
        });
    }

    /**
     * Indicate that the customer has unverified phone.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function phoneUnverified()
    {
        return $this->state(function (array $attributes) {
            return [
                'phone_verified' => 0,
            ];
        });
    }

    /**
     * Indicate that the customer has unverified email.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function emailUnverified()
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified' => 0,
            ];
        });
    }
}

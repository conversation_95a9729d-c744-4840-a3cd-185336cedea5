<?php

namespace Database\Factories;

use App\Models\CustomerAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Customer Address Factory
 * 
 * This factory creates test customer addresses.
 */
class CustomerAddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CustomerAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'customer_code' => function () {
                return \App\Models\Customer::factory()->create()->pk_customer_code;
            },
            'address_type' => $this->faker->randomElement(['home', 'work', 'other']),
            'address' => $this->faker->address,
            'landmark' => $this->faker->optional()->sentence(2),
            'location_code' => 'LOC' . $this->faker->numberBetween(1, 100),
            'location_name' => $this->faker->city,
            'city' => $this->faker->city,
            'city_name' => $this->faker->city,
            'state' => $this->faker->state,
            'country' => $this->faker->country,
            'pincode' => $this->faker->postcode,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'is_default' => 0,
            'company_id' => 1,
            'unit_id' => 1,
        ];
    }

    /**
     * Indicate that the address is default.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function default()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_default' => 1,
            ];
        });
    }

    /**
     * Indicate that the address is of type home.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function home()
    {
        return $this->state(function (array $attributes) {
            return [
                'address_type' => 'home',
            ];
        });
    }

    /**
     * Indicate that the address is of type work.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function work()
    {
        return $this->state(function (array $attributes) {
            return [
                'address_type' => 'work',
            ];
        });
    }
}

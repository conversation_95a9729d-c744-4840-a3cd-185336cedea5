<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('school_meal_subscriptions', function (Blueprint $table) {
            // Payment integration columns
            $table->string('last_payment_id')->nullable()->after('billing_failures');
            $table->string('last_payment_status')->nullable()->after('last_payment_id');
            $table->decimal('last_billing_amount', 10, 2)->nullable()->after('last_payment_status');
            $table->text('last_billing_failure_reason')->nullable()->after('last_billing_failure_date');
            $table->string('payment_method')->default('payu')->after('last_billing_failure_reason');
            $table->string('currency', 3)->default('INR')->after('payment_method');
            
            // Add indexes for performance
            $table->index('last_payment_id');
            $table->index('last_payment_status');
            $table->index('payment_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('school_meal_subscriptions', function (Blueprint $table) {
            $table->dropIndex(['last_payment_id']);
            $table->dropIndex(['last_payment_status']);
            $table->dropIndex(['payment_method']);
            
            $table->dropColumn([
                'last_payment_id',
                'last_payment_status',
                'last_billing_amount',
                'last_billing_failure_reason',
                'payment_method',
                'currency',
            ]);
        });
    }
};

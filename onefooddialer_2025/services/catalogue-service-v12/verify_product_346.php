<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Verifying Product ID 346 Weekly Plan Data ===\n\n";

try {
    // Check if product 346 exists
    $product = DB::connection('quickserve')->table('products')
        ->where('pk_product_code', 346)
        ->first(['pk_product_code', 'name', 'product_category', 'status']);
    
    if ($product) {
        echo "✓ Product 346 found:\n";
        echo "  Name: {$product->name}\n";
        echo "  Category: {$product->product_category}\n";
        echo "  Status: {$product->status}\n\n";
    } else {
        echo "✗ Product 346 not found in products table\n\n";
        exit;
    }
    
    // Check current week dates
    $currentWeek = [];
    $monday = Carbon\Carbon::now()->startOfWeek(Carbon\Carbon::MONDAY);
    for ($i = 0; $i < 5; $i++) {
        $currentWeek[] = $monday->copy()->addDays($i)->format('Y-m-d');
    }
    
    echo "Checking product_planner data for product 346 (current week):\n";
    echo "Week dates: " . implode(', ', $currentWeek) . "\n\n";
    
    $totalPlannerItems = 0;
    foreach ($currentWeek as $date) {
        $plannerData = DB::connection('quickserve')->table('product_planner')
            ->select(['date', 'specific_product_code', 'specific_product_name', 'menu', 'swap_with', 'swap_charges'])
            ->where('generic_product_code', 346)
            ->where('date', $date)
            ->get();
        
        echo "Date: {$date} ";
        if ($plannerData->isNotEmpty()) {
            echo "✓ Found {$plannerData->count()} planner items:\n";
            foreach ($plannerData as $item) {
                $totalPlannerItems++;
                echo "  - {$item->specific_product_name} (Code: {$item->specific_product_code}, Menu: {$item->menu})\n";
            }
        } else {
            echo "✗ No planner data\n";
        }
        echo "\n";
    }
    
    echo "--- Summary ---\n";
    echo "Total planner items found for product 346: {$totalPlannerItems}\n";
    
    if ($totalPlannerItems > 0) {
        echo "✅ Product 346 HAS weekly plan data in product_planner table\n";
        echo "✅ WeeklyPlannerController should return planner data (FIRST PRIORITY)\n";
    } else {
        echo "❌ Product 346 has NO weekly plan data in product_planner table\n";
        echo "❌ WeeklyPlannerController will use products.items fallback (SECOND PRIORITY)\n";
    }
    
    // Test the actual API response for product 346
    echo "\n=== Testing API Response ===\n";
    
    $controller = new App\Http\Controllers\Api\V2\WeeklyPlannerController();
    $request = new Illuminate\Http\Request();
    
    $response = $controller->index($request);
    $data = $response->getData(true);
    
    if ($data['success']) {
        $found346 = false;
        foreach (['breakfast', 'lunch', 'jain'] as $category) {
            if (isset($data['data']['meal_categories'][$category]['weekly_menu'])) {
                foreach ($data['data']['meal_categories'][$category]['weekly_menu'] as $date => $dayData) {
                    foreach ($dayData['available_meals'] as $meal) {
                        if ($meal['meal_id'] == 346) {
                            $found346 = true;
                            echo "✓ Found product 346 in {$category} category for {$date}:\n";
                            echo "  Data source: {$meal['data_source']}\n";
                            echo "  Planner controlled: " . ($meal['planner_controlled'] ? 'Yes' : 'No') . "\n";
                            echo "  Menu items count: " . count($meal['weekly_menu_items']) . "\n";
                            
                            if (!empty($meal['weekly_menu_items']) && is_array($meal['weekly_menu_items'])) {
                                echo "  Sample items:\n";
                                foreach (array_slice($meal['weekly_menu_items'], 0, 3) as $item) {
                                    if (is_array($item)) {
                                        echo "    - {$item['item_name']} (Source: {$item['source']})\n";
                                    }
                                }
                            }
                            echo "\n";
                        }
                    }
                }
            }
        }
        
        if (!$found346) {
            echo "❌ Product 346 not found in API response\n";
        }
    } else {
        echo "❌ API request failed: {$data['message']}\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Verification Complete ===\n";

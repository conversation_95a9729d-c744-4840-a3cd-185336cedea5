FROM php:8.2-fpm

# Set working directory
WORKDIR /var/www/html

# Install dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    locales \
    zip \
    jpegoptim optipng pngquant gifsicle \
    vim \
    unzip \
    git \
    curl \
    libzip-dev \
    libonig-dev \
    libxml2-dev \
    libpq-dev

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install extensions
RUN docker-php-ext-install pdo_mysql mbstring zip exif pcntl bcmath gd soap

# Install composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer


# Copy application files
COPY . /var/www/html

# Install Composer dependencies
RUN composer install --no-interaction --prefer-dist --optimize-autoloader

# Set permissions for <PERSON><PERSON> (optional, uncomment if needed)
# RUN chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache
#RUN php artisan migrate

#RUN php artisan key:generate
# Expose port 9000 and start Laravel server
EXPOSE 9000
CMD ["php", "artisan", "serve", "--host", "0.0.0.0", "--port", "9000"]



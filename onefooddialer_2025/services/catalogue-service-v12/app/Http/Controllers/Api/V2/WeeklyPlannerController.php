<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class WeeklyPlannerController extends Controller
{
    /**
     * Get weekly meal planner for current week (Monday to Friday)
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Calculate current week dates (Monday to Friday)
            $currentWeek = $this->getCurrentWeekDates();
            
            // Get subscription meals that are active
            $subscriptionMeals = $this->getSubscriptionMeals();
            
            // Categorize meals into Breakfast, Lunch, and Jain
            $categorizedMeals = $this->categorizeMeals($subscriptionMeals);
            
            // Build weekly planner response
            $weeklyPlanner = $this->buildWeeklyPlanner($currentWeek, $categorizedMeals);
            
            return response()->json([
                'success' => true,
                'message' => 'Weekly planner retrieved successfully',
                'data' => [
                    'week_dates' => $currentWeek,
                    'meal_categories' => $weeklyPlanner
                ]
            ], 200);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve weekly planner',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get current week dates (Monday to Friday)
     * 
     * @return array
     */
    private function getCurrentWeekDates(): array
    {
        $now = Carbon::now();
        $monday = $now->startOfWeek(Carbon::MONDAY);
        
        $weekDates = [];
        for ($i = 0; $i < 5; $i++) { // Monday to Friday
            $date = $monday->copy()->addDays($i);
            $weekDates[] = [
                'date' => $date->format('Y-m-d'),
                'day_name' => $date->format('l'),
                'formatted_date' => $date->format('M d, Y')
            ];
        }
        
        return $weekDates;
    }
    
    /**
     * Get subscription meals that are active
     * 
     * @return \Illuminate\Support\Collection
     */
    private function getSubscriptionMeals()
    {
        return DB::table('products')
            ->where('product_category', 'Subscription Meals')
            ->where('status', 1) // Active products only
            ->get([
                'pk_product_code',
                'name',
                'items',
                'description',
                'unit_price',
                'product_category',
                'food_type'
            ]);
    }
    
    /**
     * Categorize meals into Breakfast, Lunch, and Jain
     * 
     * @param \Illuminate\Support\Collection $meals
     * @return array
     */
    private function categorizeMeals($meals): array
    {
        $categorized = [
            'breakfast' => [],
            'lunch' => [],
            'jain' => []
        ];
        
        foreach ($meals as $meal) {
            $name = strtolower($meal->name);
            
            // Check if name starts with "Jain"
            if (strpos($name, 'jain') === 0) {
                $categorized['jain'][] = $this->processMealWithItems($meal);
            } elseif (strpos($name, 'breakfast') !== false) {
                $categorized['breakfast'][] = $this->processMealWithItems($meal);
            } elseif (strpos($name, 'lunch') !== false) {
                $categorized['lunch'][] = $this->processMealWithItems($meal);
            }
        }
        
        return $categorized;
    }
    
    /**
     * Process meal and get item details from items column
     * 
     * @param object $meal
     * @return array
     */
    private function processMealWithItems($meal): array
    {
        $mealData = [
            'meal_id' => $meal->pk_product_code,
            'meal_name' => $meal->name,
            'description' => $meal->description,
            'unit_price' => $meal->unit_price,
            'food_type' => $meal->food_type,
            'items' => []
        ];
        
        // Parse items JSON and get item names
        if (!empty($meal->items)) {
            $itemsJson = json_decode($meal->items, true);
            if ($itemsJson && is_array($itemsJson)) {
                foreach ($itemsJson as $itemCode => $quantity) {
                    $itemName = DB::table('products')
                        ->where('pk_product_code', $itemCode)
                        ->value('name');
                    
                    if ($itemName) {
                        $mealData['items'][] = [
                            'item_code' => $itemCode,
                            'item_name' => $itemName,
                            'quantity' => $quantity
                        ];
                    }
                }
            }
        }
        
        return $mealData;
    }
    
    /**
     * Build weekly planner with meal categories for each day
     * 
     * @param array $weekDates
     * @param array $categorizedMeals
     * @return array
     */
    private function buildWeeklyPlanner($weekDates, $categorizedMeals): array
    {
        $weeklyPlanner = [];
        
        foreach ($weekDates as $dateInfo) {
            $date = $dateInfo['date'];
            
            // Check if there's specific menu data for this date in product_calendar
            // Since product_plan table doesn't exist, we'll use the items column as fallback
            $weeklyPlanner[$date] = [
                'date_info' => $dateInfo,
                'breakfast' => $this->getMenuForDate($date, $categorizedMeals['breakfast'], 'breakfast'),
                'lunch' => $this->getMenuForDate($date, $categorizedMeals['lunch'], 'lunch'),
                'jain' => $this->getMenuForDate($date, $categorizedMeals['jain'], 'jain')
            ];
        }
        
        return $weeklyPlanner;
    }
    
    /**
     * Get menu for specific date and meal type
     * Since product_plan table doesn't exist, return items from products table
     * 
     * @param string $date
     * @param array $meals
     * @param string $mealType
     * @return array
     */
    private function getMenuForDate($date, $meals, $mealType): array
    {
        // Check product_calendar for date-specific availability
        $availableMeals = [];
        
        foreach ($meals as $meal) {
            $calendarEntry = DB::table('product_calendar')
                ->where('product_code', $meal['meal_id'])
                ->where('date', $date)
                ->first();
            
            // If no calendar entry or if available, include the meal
            if (!$calendarEntry || $calendarEntry->available) {
                $mealData = $meal;
                
                // Add calendar-specific data if available
                if ($calendarEntry) {
                    $mealData['quantity_limit'] = $calendarEntry->quantity_limit;
                    $mealData['price_override'] = $calendarEntry->price_override;
                }
                
                $availableMeals[] = $mealData;
            }
        }
        
        return [
            'meal_type' => $mealType,
            'available_meals' => $availableMeals,
            'total_meals' => count($availableMeals),
            'note' => count($availableMeals) > 0 
                ? 'Showing items from products table (product_plan table not available)' 
                : 'No meals available for this date'
        ];
    }
}

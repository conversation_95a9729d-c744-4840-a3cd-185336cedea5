<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class WeeklyPlannerController extends Controller
{
    /**
     * Get weekly meal planner for current week (Monday to Friday)
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Validate optional date parameters
            $request->validate([
                'from_date' => 'nullable|date|date_format:Y-m-d',
                'to_date' => 'nullable|date|date_format:Y-m-d|after_or_equal:from_date',
            ]);

            // Calculate week dates based on parameters or current week
            if ($request->has('from_date') && $request->has('to_date')) {
                $weekDates = $this->getCustomWeekDates($request->from_date, $request->to_date);
            } elseif ($request->has('from_date')) {
                // If only from_date provided, calculate 5 days from that date
                $weekDates = $this->getWeekFromDate($request->from_date);
            } else {
                // Default to current week (Monday to Friday)
                $weekDates = $this->getCurrentWeekDates();
            }

            // Get subscription meals that are active
            $subscriptionMeals = $this->getSubscriptionMeals();

            // Categorize meals into Breakfast, Lunch, and Jain
            $categorizedMeals = $this->categorizeMeals($subscriptionMeals);

            // Build weekly planner response with actual product_planner data
            $weeklyPlanner = $this->buildWeeklyPlanner($weekDates, $categorizedMeals);

            return response()->json([
                'success' => true,
                'message' => 'Weekly planner retrieved successfully',
                'data' => [
                    'week_dates' => $weekDates,
                    'meal_categories' => $weeklyPlanner,
                    'parameters_used' => [
                        'from_date' => $request->from_date,
                        'to_date' => $request->to_date,
                        'date_source' => $request->has('from_date') ? 'custom' : 'current_week'
                    ]
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve weekly planner',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get custom week dates from provided date range
     *
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    private function getCustomWeekDates(string $fromDate, string $toDate): array
    {
        $dates = [];
        $current = Carbon::parse($fromDate);
        $end = Carbon::parse($toDate);

        while ($current->lte($end)) {
            $dates[] = [
                'date' => $current->format('Y-m-d'),
                'day_name' => $current->format('l'),
                'formatted_date' => $current->format('M d, Y')
            ];
            $current->addDay();
        }

        return $dates;
    }

    /**
     * Get week dates starting from a specific date (5 weekdays)
     *
     * @param string $fromDate
     * @return array
     */
    private function getWeekFromDate(string $fromDate): array
    {
        $dates = [];
        $current = Carbon::parse($fromDate);

        // Get 5 weekdays starting from the given date
        $count = 0;
        while ($count < 5) {
            // Skip weekends
            if ($current->isWeekday()) {
                $dates[] = [
                    'date' => $current->format('Y-m-d'),
                    'day_name' => $current->format('l'),
                    'formatted_date' => $current->format('M d, Y')
                ];
                $count++;
            }
            $current->addDay();
        }

        return $dates;
    }

    /**
     * Get current week dates (Monday to Friday)
     * 
     * @return array
     */
    private function getCurrentWeekDates(): array
    {
        $now = Carbon::now();
        $monday = $now->startOfWeek(Carbon::MONDAY);
        
        $weekDates = [];
        for ($i = 0; $i < 5; $i++) { // Monday to Friday
            $date = $monday->copy()->addDays($i);
            $weekDates[] = [
                'date' => $date->format('Y-m-d'),
                'day_name' => $date->format('l'),
                'formatted_date' => $date->format('M d, Y')
            ];
        }
        
        return $weekDates;
    }
    
    /**
     * Get subscription meals that are active
     * 
     * @return \Illuminate\Support\Collection
     */
    private function getSubscriptionMeals()
    {
        return DB::table('products')
            ->where('product_category', 'Subscription Meals')
            ->where('status', 1) // Active products only
            ->get([
                'pk_product_code',
                'name',
                'items',
                'description',
                'unit_price',
                'product_category',
                'food_type'
            ]);
    }
    
    /**
     * Categorize meals into Breakfast, Lunch, and Jain
     * 
     * @param \Illuminate\Support\Collection $meals
     * @return array
     */
    private function categorizeMeals($meals): array
    {
        $categorized = [
            'breakfast' => [],
            'lunch' => [],
            'jain' => []
        ];
        
        foreach ($meals as $meal) {
            $name = strtolower($meal->name);
            
            // Check if name starts with "Jain"
            if (strpos($name, 'jain') === 0) {
                $categorized['jain'][] = $this->processMealWithItems($meal);
            } elseif (strpos($name, 'breakfast') !== false) {
                $categorized['breakfast'][] = $this->processMealWithItems($meal);
            } elseif (strpos($name, 'lunch') !== false) {
                $categorized['lunch'][] = $this->processMealWithItems($meal);
            }
        }
        
        return $categorized;
    }
    
    /**
     * Process meal and get item details from items column
     * 
     * @param object $meal
     * @return array
     */
    private function processMealWithItems($meal): array
    {
        $mealData = [
            'meal_id' => $meal->pk_product_code,
            'meal_name' => $meal->name,
            'description' => $meal->description,
            'unit_price' => $meal->unit_price,
            'food_type' => $meal->food_type,
            'items' => []
        ];
        
        // Parse items JSON and get item names
        if (!empty($meal->items)) {
            $itemsJson = json_decode($meal->items, true);
            if ($itemsJson && is_array($itemsJson)) {
                foreach ($itemsJson as $itemCode => $quantity) {
                    $itemName = DB::table('products')
                        ->where('pk_product_code', $itemCode)
                        ->value('name');
                    
                    if ($itemName) {
                        $mealData['items'][] = [
                            'item_code' => $itemCode,
                            'item_name' => $itemName,
                            'quantity' => $quantity
                        ];
                    }
                }
            }
        }
        
        return $mealData;
    }
    
    /**
     * Build weekly planner with meal categories for each day
     *
     * @param array $weekDates
     * @param array $categorizedMeals
     * @return array
     */
    private function buildWeeklyPlanner($weekDates, $categorizedMeals): array
    {
        // Extract just the dates for filtering
        $dates = array_column($weekDates, 'date');

        // Filter meals based on product_calendar availability for the given date range
        $filteredBreakfast = $this->filterMealsByDateRange($categorizedMeals['breakfast'], $dates);
        $filteredLunch = $this->filterMealsByDateRange($categorizedMeals['lunch'], $dates);
        $filteredJain = $this->filterMealsByDateRange($categorizedMeals['jain'], $dates);

        return [
            'breakfast' => [
                'meal_type' => 'breakfast',
                'available_meals' => $filteredBreakfast,
                'total_meals' => count($filteredBreakfast),
                'note' => $this->getDataSourceNote($dates)
            ],
            'lunch' => [
                'meal_type' => 'lunch',
                'available_meals' => $filteredLunch,
                'total_meals' => count($filteredLunch),
                'note' => $this->getDataSourceNote($dates)
            ],
            'jain' => [
                'meal_type' => 'jain',
                'available_meals' => $filteredJain,
                'total_meals' => count($filteredJain),
                'note' => $this->getDataSourceNote($dates)
            ]
        ];
    }

    /**
     * Filter meals based on product_calendar availability for date range
     *
     * @param array $meals
     * @param array $dates
     * @return array
     */
    private function filterMealsByDateRange($meals, $dates): array
    {
        $filteredMeals = [];

        foreach ($meals as $meal) {
            // Check if meal is available for any date in the range
            $availableForRange = DB::table('product_calendar')
                ->where('product_code', $meal['meal_id'])
                ->whereIn('date', $dates)
                ->where('available', 1)
                ->exists();

            // If no calendar entries exist, include the meal (fallback behavior)
            $hasCalendarEntries = DB::table('product_calendar')
                ->where('product_code', $meal['meal_id'])
                ->whereIn('date', $dates)
                ->exists();

            if (!$hasCalendarEntries || $availableForRange) {
                // Add calendar-specific data if available
                $calendarData = DB::table('product_calendar')
                    ->where('product_code', $meal['meal_id'])
                    ->whereIn('date', $dates)
                    ->where('available', 1)
                    ->first();

                $mealData = $meal;
                if ($calendarData) {
                    $mealData['quantity_limit'] = $calendarData->quantity_limit;
                    $mealData['price_override'] = $calendarData->price_override;
                    $mealData['calendar_controlled'] = true;
                } else {
                    $mealData['calendar_controlled'] = false;
                }

                $filteredMeals[] = $mealData;
            }
        }

        return $filteredMeals;
    }

    /**
     * Get data source note for meal category
     *
     * @param array $dates
     * @return string
     */
    private function getDataSourceNote($dates): string
    {
        $hasCalendarData = DB::table('product_calendar')
            ->whereIn('date', $dates)
            ->exists();

        if ($hasCalendarData) {
            return "Showing meals available for selected date range based on product_calendar";
        } else {
            return "Showing items from products table (product_calendar has no data for selected dates)";
        }
    }

}

<?php

require_once 'vendor/autoload.php';

use App\Http\Controllers\Api\V2\WeeklyPlannerController;
use Illuminate\Http\Request;
use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing WeeklyPlannerController ===\n\n";

try {
    // Test 1: Basic functionality
    echo "1. Testing basic functionality...\n";
    $controller = new WeeklyPlannerController();
    $request = new Request();
    
    $response = $controller->index($request);
    $data = $response->getData(true);
    
    echo "✓ Success: " . ($data['success'] ? 'true' : 'false') . "\n";
    echo "✓ Message: " . $data['message'] . "\n";
    echo "✓ Categories available: " . implode(', ', array_keys($data['data']['meal_categories'])) . "\n";
    echo "✓ Total days: " . $data['data']['summary']['total_days'] . "\n";
    echo "✓ Date range: " . $data['data']['summary']['date_range']['from'] . " to " . $data['data']['summary']['date_range']['to'] . "\n\n";

    // Test 2: Check database connections
    echo "2. Testing database connections...\n";
    
    // Test default connection
    $defaultConnection = \DB::connection()->getPdo();
    echo "✓ Default DB connection: " . \DB::connection()->getDatabaseName() . "\n";
    
    // Test quickserve connection
    try {
        $quickserveConnection = \DB::connection('quickserve')->getPdo();
        echo "✓ Quickserve DB connection: " . \DB::connection('quickserve')->getDatabaseName() . "\n";
    } catch (Exception $e) {
        echo "✗ Quickserve DB connection failed: " . $e->getMessage() . "\n";
    }
    
    // Test 3: Check table existence
    echo "\n3. Testing table existence...\n";
    
    // Check product_planner table in quickserve
    try {
        $tables = \DB::connection('quickserve')->select('SHOW TABLES');
        $plannerExists = false;
        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];
            if ($tableName === 'product_planner') {
                $plannerExists = true;
                break;
            }
        }
        echo "✓ product_planner table exists: " . ($plannerExists ? 'Yes' : 'No') . "\n";
        
        if ($plannerExists) {
            $count = \DB::connection('quickserve')->table('product_planner')->count();
            echo "✓ product_planner records: " . $count . "\n";
            
            // Get sample data
            $sample = \DB::connection('quickserve')->table('product_planner')
                ->select(['date', 'generic_product_code', 'specific_product_name'])
                ->where('date', '>=', '2025-07-07')
                ->where('date', '<=', '2025-07-11')
                ->limit(5)
                ->get();
            
            echo "✓ Sample planner data for this week:\n";
            foreach ($sample as $row) {
                echo "  - Date: {$row->date}, Product: {$row->generic_product_code}, Name: {$row->specific_product_name}\n";
            }
        }
    } catch (Exception $e) {
        echo "✗ Error checking product_planner: " . $e->getMessage() . "\n";
    }
    
    // Test 4: Check products table
    echo "\n4. Testing products table...\n";
    try {
        $subscriptionMeals = \DB::table('products')
            ->where('product_category', 'Subscription Meals')
            ->where('status', 1)
            ->count();
        echo "✓ Active subscription meals: " . $subscriptionMeals . "\n";
        
        // Get sample meals
        $sampleMeals = \DB::table('products')
            ->where('product_category', 'Subscription Meals')
            ->where('status', 1)
            ->limit(3)
            ->get(['pk_product_code', 'name']);
            
        echo "✓ Sample meals:\n";
        foreach ($sampleMeals as $meal) {
            echo "  - {$meal->pk_product_code}: {$meal->name}\n";
        }
    } catch (Exception $e) {
        echo "✗ Error checking products: " . $e->getMessage() . "\n";
    }

    // Test 5: Test with custom date range
    echo "\n5. Testing with custom date range...\n";
    $customRequest = new Request([
        'from_date' => '2025-07-07',
        'to_date' => '2025-07-11'
    ]);
    
    $customResponse = $controller->index($customRequest);
    $customData = $customResponse->getData(true);
    
    echo "✓ Custom date range success: " . ($customData['success'] ? 'true' : 'false') . "\n";
    echo "✓ Custom date range: " . $customData['data']['summary']['date_range']['from'] . " to " . $customData['data']['summary']['date_range']['to'] . "\n";
    
    // Check if any breakfast data exists
    if (isset($customData['data']['meal_categories']['breakfast']['weekly_menu'])) {
        echo "✓ Breakfast menu structure exists\n";
        $firstDate = array_keys($customData['data']['meal_categories']['breakfast']['weekly_menu'])[0];
        $firstDayData = $customData['data']['meal_categories']['breakfast']['weekly_menu'][$firstDate];
        echo "✓ First day ({$firstDate}) data source: " . $firstDayData['data_source'] . "\n";
        echo "✓ First day available meals: " . $firstDayData['total_meals'] . "\n";
    }

} catch (Exception $e) {
    echo "✗ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";

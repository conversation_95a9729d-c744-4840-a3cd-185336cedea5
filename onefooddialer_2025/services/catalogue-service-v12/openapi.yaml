openapi: 3.1.0
info:
  title: Catalogue Service API
  description: API for managing products, menus, carts, plan meals, and themes
  version: 1.0.0
servers:
  - url: /api/v2
    description: API v2 base URL
paths:
  /catalogue/products:
    get:
      summary: Get all products
      description: Returns a list of products with optional filtering
      parameters:
        - name: food_type
          in: query
          description: Filter by food type (veg, non-veg)
          schema:
            type: string
            enum: [veg, non-veg]
        - name: kitchen_id
          in: query
          description: Filter by kitchen ID
          schema:
            type: integer
        - name: status
          in: query
          description: Filter by status
          schema:
            type: boolean
        - name: product_category_id
          in: query
          description: Filter by product category ID
          schema:
            type: integer
        - name: per_page
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 15
      responses:
        '200':
          description: A list of products
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
    post:
      summary: Create a new product
      description: Creates a new product
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductInput'
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product created successfully
                  data:
                    $ref: '#/components/schemas/Product'
  /catalogue/products/{id}:
    get:
      summary: Get a specific product
      description: Returns a specific product by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: integer
      responses:
        '200':
          description: Product details
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Product'
        '404':
          description: Product not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product not found
    put:
      summary: Update a product
      description: Updates a specific product by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductInput'
      responses:
        '200':
          description: Product updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product updated successfully
                  data:
                    $ref: '#/components/schemas/Product'
        '404':
          description: Product not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product not found
    delete:
      summary: Delete a product
      description: Deletes a specific product by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: integer
      responses:
        '200':
          description: Product deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product deleted successfully
        '404':
          description: Product not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product not found
  /catalogue/weekly-planner:
    get:
      summary: Get weekly meal planner
      description: Returns weekly meal planning data with optional date range filtering
      parameters:
        - name: from_date
          in: query
          description: Start date for the weekly planner (YYYY-MM-DD format)
          required: false
          schema:
            type: string
            format: date
            example: "2025-06-30"
        - name: to_date
          in: query
          description: End date for the weekly planner (YYYY-MM-DD format). Must be after or equal to from_date
          required: false
          schema:
            type: string
            format: date
            example: "2025-07-04"
      responses:
        '200':
          description: Weekly planner retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Weekly planner retrieved successfully"
                  data:
                    type: object
                    properties:
                      week_dates:
                        type: array
                        items:
                          type: object
                          properties:
                            date:
                              type: string
                              format: date
                              example: "2025-06-30"
                            day_name:
                              type: string
                              example: "Monday"
                            formatted_date:
                              type: string
                              example: "Jun 30, 2025"
                      meal_categories:
                        type: object
                        properties:
                          breakfast:
                            $ref: '#/components/schemas/MealCategory'
                          lunch:
                            $ref: '#/components/schemas/MealCategory'
                          jain:
                            $ref: '#/components/schemas/MealCategory'
                      parameters_used:
                        type: object
                        properties:
                          from_date:
                            type: string
                            format: date
                            nullable: true
                            example: "2025-06-30"
                          to_date:
                            type: string
                            format: date
                            nullable: true
                            example: "2025-07-04"
                          date_source:
                            type: string
                            enum: [current_week, custom]
                            example: "custom"
        '400':
          description: Invalid date parameters
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Validation failed"
                  errors:
                    type: object
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Failed to retrieve weekly planner"
                  error:
                    type: string
  /v2/meal-types:
    get:
      tags:
        - Meal Types
      summary: Get all meal types
      description: Returns a list of all meal types
      parameters:
        - name: status
          in: query
          description: Filter by status
          required: false
          schema:
            type: boolean
        - name: per_page
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 15
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/MealType'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
    post:
      tags:
        - Meal Types
      summary: Create a new meal type
      description: Creates a new meal type
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MealTypeRequest'
      responses:
        '201':
          description: Meal type created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Meal type created successfully
                  data:
                    $ref: '#/components/schemas/MealType'
  /v2/meal-types/{id}:
    get:
      tags:
        - Meal Types
      summary: Get a meal type by ID
      description: Returns a meal type by ID
      parameters:
        - name: id
          in: path
          description: Meal type ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/MealType'
        '404':
          description: Meal type not found
    put:
      tags:
        - Meal Types
      summary: Update a meal type
      description: Updates a meal type
      parameters:
        - name: id
          in: path
          description: Meal type ID
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MealTypeRequest'
      responses:
        '200':
          description: Meal type updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Meal type updated successfully
                  data:
                    $ref: '#/components/schemas/MealType'
        '404':
          description: Meal type not found
    delete:
      tags:
        - Meal Types
      summary: Delete a meal type
      description: Deletes a meal type
      parameters:
        - name: id
          in: path
          description: Meal type ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Meal type deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Meal type deleted successfully
        '404':
          description: Meal type not found
  /v2/meal-types/active:
    get:
      tags:
        - Meal Types
      summary: Get all active meal types
      description: Returns a list of all active meal types
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/MealType'
  /v2/meal-types/slug/{slug}:
    get:
      tags:
        - Meal Types
      summary: Get a meal type by slug
      description: Returns a meal type by slug
      parameters:
        - name: slug
          in: path
          description: Meal type slug
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/MealType'
        '404':
          description: Meal type not found
  /v2/meal-items:
    get:
      tags:
        - Meal Items
      summary: Get all meal items
      description: Returns a list of all meal items
      parameters:
        - name: meal_type_id
          in: query
          description: Filter by meal type ID
          required: false
          schema:
            type: integer
        - name: food_type
          in: query
          description: Filter by food type
          required: false
          schema:
            type: string
            enum: [veg, non-veg]
        - name: is_featured
          in: query
          description: Filter by featured status
          required: false
          schema:
            type: boolean
        - name: status
          in: query
          description: Filter by status
          required: false
          schema:
            type: boolean
        - name: per_page
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 15
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/MealItem'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
    post:
      tags:
        - Meal Items
      summary: Create a new meal item
      description: Creates a new meal item
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MealItemRequest'
      responses:
        '201':
          description: Meal item created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Meal item created successfully
                  data:
                    $ref: '#/components/schemas/MealItem'
  /v2/meal-items/{id}:
    get:
      tags:
        - Meal Items
      summary: Get a meal item by ID
      description: Returns a meal item by ID
      parameters:
        - name: id
          in: path
          description: Meal item ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/MealItem'
        '404':
          description: Meal item not found
    put:
      tags:
        - Meal Items
      summary: Update a meal item
      description: Updates a meal item
      parameters:
        - name: id
          in: path
          description: Meal item ID
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MealItemRequest'
      responses:
        '200':
          description: Meal item updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Meal item updated successfully
                  data:
                    $ref: '#/components/schemas/MealItem'
        '404':
          description: Meal item not found
    delete:
      tags:
        - Meal Items
      summary: Delete a meal item
      description: Deletes a meal item
      parameters:
        - name: id
          in: path
          description: Meal item ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Meal item deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Meal item deleted successfully
        '404':
          description: Meal item not found
  /v2/meal-items/featured:
    get:
      tags:
        - Meal Items
      summary: Get featured meal items
      description: Returns a list of featured meal items
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/MealItem'
  /v2/meal-items/meal-type/{mealTypeId}:
    get:
      tags:
        - Meal Items
      summary: Get meal items by meal type ID
      description: Returns a list of meal items by meal type ID
      parameters:
        - name: mealTypeId
          in: path
          description: Meal type ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/MealItem'
  /v2/meal-items/meal-type-slug/{slug}:
    get:
      tags:
        - Meal Items
      summary: Get meal items by meal type slug
      description: Returns a list of meal items by meal type slug
      parameters:
        - name: slug
          in: path
          description: Meal type slug
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/MealItem'
        '404':
          description: Meal type not found
components:
  schemas:
    Product:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: Vegetable Biryani
        description:
          type: string
          example: Delicious vegetable biryani with fresh vegetables and aromatic spices
        unit_price:
          type: number
          format: float
          example: 250.00
        food_type:
          type: string
          enum: [veg, non-veg]
          example: veg
        product_category_id:
          type: integer
          example: 2
        image_path:
          type: string
          example: products/veg-biryani.jpg
        product_subtype:
          type: string
          example: rice
        swap_with:
          type: integer
          nullable: true
          example: 5
        swap_charges:
          type: number
          format: float
          example: 50.00
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
        kitchen_id:
          type: integer
          example: 1
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        category:
          $ref: '#/components/schemas/ProductCategory'
        kitchen:
          $ref: '#/components/schemas/Kitchen'
    ProductInput:
      type: object
      required:
        - name
        - unit_price
        - food_type
        - product_category_id
        - kitchen_id
      properties:
        name:
          type: string
          example: Vegetable Biryani
        description:
          type: string
          example: Delicious vegetable biryani with fresh vegetables and aromatic spices
        unit_price:
          type: number
          format: float
          example: 250.00
        food_type:
          type: string
          enum: [veg, non-veg]
          example: veg
        product_category_id:
          type: integer
          example: 2
        image_path:
          type: string
          example: products/veg-biryani.jpg
        product_subtype:
          type: string
          example: rice
        swap_with:
          type: integer
          nullable: true
          example: 5
        swap_charges:
          type: number
          format: float
          example: 50.00
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
        kitchen_id:
          type: integer
          example: 1
    ProductCategory:
      type: object
      properties:
        id:
          type: integer
          example: 2
        product_category_name:
          type: string
          example: Rice
        description:
          type: string
          example: All rice dishes
        image_path:
          type: string
          example: categories/rice.jpg
        type:
          type: string
          enum: [meal, product, extra]
          example: product
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    Kitchen:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: Main Kitchen
        description:
          type: string
          example: Main kitchen for all operations
        address:
          type: string
          example: 123 Main St
        city:
          type: string
          example: Mumbai
        state:
          type: string
          example: Maharashtra
        country:
          type: string
          example: India
        pincode:
          type: string
          example: 400001
        phone:
          type: string
          example: +91 9876543210
        email:
          type: string
          example: <EMAIL>
        status:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
          example: 1
        last_page:
          type: integer
          example: 5
        per_page:
          type: integer
          example: 15
        total:
          type: integer
          example: 75
    MealType:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: Breakfast
        slug:
          type: string
          example: breakfast
        icon_path:
          type: string
          example: images/icons/breakfast.png
        description:
          type: string
          example: Start your day with our delicious breakfast options
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    MealTypeRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: Breakfast
        slug:
          type: string
          example: breakfast
        icon_path:
          type: string
          example: images/icons/breakfast.png
        description:
          type: string
          example: Start your day with our delicious breakfast options
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
    MealItem:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: Indian Breakfast
        description:
          type: string
          example: Traditional Indian breakfast with idli, dosa, and sambar
        meal_type_id:
          type: integer
          example: 1
        price:
          type: number
          format: float
          example: 150.00
        image_path:
          type: string
          example: images/breakfast/Indian Breakfast.png
        gallery_images:
          type: array
          items:
            type: string
          example: ["images/breakfast/idli.png", "images/breakfast/dosa.png"]
        food_type:
          type: string
          enum: [veg, non-veg]
          example: veg
        is_featured:
          type: boolean
          example: true
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        meal_type:
          $ref: '#/components/schemas/MealType'
        discounts:
          type: array
          items:
            $ref: '#/components/schemas/MealDiscount'
    MealItemRequest:
      type: object
      required:
        - name
        - meal_type_id
        - price
      properties:
        name:
          type: string
          example: Indian Breakfast
        description:
          type: string
          example: Traditional Indian breakfast with idli, dosa, and sambar
        meal_type_id:
          type: integer
          example: 1
        price:
          type: number
          format: float
          example: 150.00
        image_path:
          type: string
          example: images/breakfast/Indian Breakfast.png
        gallery_images:
          type: array
          items:
            type: string
          example: ["images/breakfast/idli.png", "images/breakfast/dosa.png"]
        food_type:
          type: string
          enum: [veg, non-veg]
          example: veg
        is_featured:
          type: boolean
          example: true
        sequence:
          type: integer
          example: 1
        status:
          type: boolean
          example: true
    MealDiscount:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: Breakfast Special
        code:
          type: string
          example: BREAKFAST20
        discount_type:
          type: string
          enum: [percentage, fixed]
          example: percentage
        discount_value:
          type: number
          format: float
          example: 20.00
        meal_item_id:
          type: integer
          nullable: true
          example: null
        meal_type_id:
          type: integer
          example: 1
        start_date:
          type: string
          format: date
          example: 2025-06-20
        end_date:
          type: string
          format: date
          example: 2025-09-20
        is_annual:
          type: boolean
          example: false
        status:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    MealCategory:
      type: object
      properties:
        meal_type:
          type: string
          enum: [breakfast, lunch, jain]
          example: "breakfast"
        available_meals:
          type: array
          items:
            type: object
            properties:
              meal_id:
                type: integer
                example: 339
              meal_name:
                type: string
                example: "Indian Breakfast"
              description:
                type: string
                example: "Indian Breakfast of the Day"
              unit_price:
                type: string
                example: "75.00"
              food_type:
                type: string
                enum: [veg, non-veg, jain]
                example: "veg"
              items:
                type: array
                items:
                  type: object
                  properties:
                    item_code:
                      type: integer
                      example: 393
                    item_name:
                      type: string
                      example: "Jain: Rava Dhokla With Tamarind Date Chutney"
                    quantity:
                      type: string
                      example: "1"
              quantity_limit:
                type: integer
                nullable: true
                example: 100
              price_override:
                type: number
                format: decimal
                nullable: true
                example: null
              calendar_controlled:
                type: boolean
                example: true
        total_meals:
          type: integer
          example: 3
        note:
          type: string
          example: "Showing meals available for selected date range based on product_calendar"

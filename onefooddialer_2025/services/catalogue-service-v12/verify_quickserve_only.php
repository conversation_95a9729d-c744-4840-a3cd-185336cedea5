<?php

echo "=== Database Connection Verification ===\n\n";

// Read the controller file
$controllerPath = '/Users/<USER>/Dinesh_bkp_24_jan/Dinesh/FD/startwell-v2/onefooddialer_2025/services/catalogue-service-v12/app/Http/Controllers/Api/V2/WeeklyPlannerController.php';
$content = file_get_contents($controllerPath);

echo "✓ Verifying WeeklyPlannerController uses only quickserve connection...\n\n";

// Check for any default DB connections (should be 0)
$defaultConnections = preg_match_all('/DB::table\(/', $content);
$quickserveConnections = preg_match_all('/DB::connection\([\'"]quickserve[\'"]\)->table\(/', $content);

echo "--- Connection Analysis ---\n";
echo "Default DB::table() calls: $defaultConnections\n";
echo "Quickserve DB::connection('quickserve')->table() calls: $quickserveConnections\n\n";

if ($defaultConnections === 0) {
    echo "✅ PASS: No default database connections found\n";
} else {
    echo "❌ FAIL: Found $defaultConnections default database connections\n";
    
    // Show the lines with default connections
    $lines = explode("\n", $content);
    foreach ($lines as $lineNum => $line) {
        if (strpos($line, 'DB::table(') !== false && strpos($line, 'quickserve') === false) {
            echo "   Line " . ($lineNum + 1) . ": " . trim($line) . "\n";
        }
    }
}

if ($quickserveConnections > 0) {
    echo "✅ PASS: Found $quickserveConnections quickserve database connections\n";
} else {
    echo "❌ FAIL: No quickserve database connections found\n";
}

// Check for other database connection patterns
$otherConnections = preg_match_all('/DB::connection\([\'"][^q][^u][^i][^c][^k][^s][^e][^r][^v][^e][\'"]/', $content);
echo "Other DB connections (should be 0): $otherConnections\n";

if ($otherConnections === 0) {
    echo "✅ PASS: No other database connections found\n";
} else {
    echo "❌ FAIL: Found $otherConnections other database connections\n";
}

echo "\n--- Summary ---\n";
if ($defaultConnections === 0 && $quickserveConnections > 0 && $otherConnections === 0) {
    echo "🎉 SUCCESS: WeeklyPlannerController now uses ONLY the quickserve database connection!\n";
    echo "\nAll database operations will now connect exclusively to live_quickserve_8163:\n";
    echo "- products table queries\n";
    echo "- product_planner table queries\n";
    echo "- table existence checks\n";
    echo "- item name lookups\n";
    echo "- subscription meal queries\n";
} else {
    echo "⚠️  WARNING: Database connection issues found that need to be addressed\n";
}

echo "\n=== Verification Complete ===\n";

<?php

/**
 * Complete Razorpay Payment Flow Test
 * 
 * This script tests the complete Razorpay payment flow:
 * 1. Initiate payment
 * 2. Process payment with Razorpay gateway
 * 3. Verify the response contains payment URL/token
 */

echo "🔥 Complete Razorpay Payment Flow Test\n";
echo "=====================================\n\n";

// Test Configuration
$baseUrl = 'http://127.0.0.1:8012/api/v2/payments';

echo "📋 Configuration:\n";
echo "- Base URL: $baseUrl\n";
echo "- Gateway: Razorpay\n";
echo "- Environment: Test/Sandbox\n\n";

try {
    echo "🚀 Step 1: Initiate Payment Transaction\n";
    
    // Payment initiation data based on database schema
    $initiateData = [
        'customer_id' => 12345,
        'customer_name' => 'John Doe',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '9876543210',
        'amount' => 299.99,
        'transaction_charges' => 0,
        'wallet_amount' => 0,
        'order_id' => 'ORDER_' . time() . '_' . uniqid(),
        'referer' => 'mobile_app',
        'success_url' => 'https://yourapp.com/payment/success',
        'failure_url' => 'https://yourapp.com/payment/failure',
        'context' => 'order_payment',
        'recurring' => false,
        'discount' => 0
    ];
    
    echo "- Customer ID: {$initiateData['customer_id']}\n";
    echo "- Order ID: {$initiateData['order_id']}\n";
    echo "- Amount: ₹{$initiateData['amount']}\n";
    echo "- Customer: {$initiateData['customer_name']} ({$initiateData['customer_email']})\n\n";
    
    // Make API call to initiate payment
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($initiateData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception("cURL Error: $error");
    }
    
    $initiateResponse = json_decode($response, true);
    
    if ($httpCode !== 201 || !$initiateResponse['success']) {
        throw new Exception("Payment initiation failed: " . ($initiateResponse['message'] ?? 'Unknown error'));
    }
    
    $transactionId = $initiateResponse['data']['transaction_id'];
    
    echo "✅ Payment initiated successfully\n";
    echo "- Transaction ID: $transactionId\n";
    echo "- Status: {$initiateResponse['data']['status']}\n\n";
    
    echo "🚀 Step 2: Process Payment with Razorpay Gateway\n";
    
    // Process payment with Razorpay gateway
    $processData = [
        'gateway' => 'razorpay'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/$transactionId/process");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($processData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception("cURL Error: $error");
    }
    
    $processResponse = json_decode($response, true);
    
    if ($httpCode !== 200 || !$processResponse['success']) {
        throw new Exception("Payment processing failed: " . ($processResponse['message'] ?? 'Unknown error'));
    }
    
    echo "✅ Payment processing completed\n\n";
    
    echo "📊 Razorpay Payment Response:\n";
    echo json_encode($processResponse['data'], JSON_PRETTY_PRINT) . "\n\n";
    
    // Extract important information
    $razorpayData = $processResponse['data'];
    $razorpayOrderId = $razorpayData['order_id'] ?? null;
    $razorpayKey = $razorpayData['razorpay_key'] ?? null;
    $amount = $razorpayData['amount'] ?? null;
    
    echo "🎉 Razorpay Payment Flow Test Completed Successfully!\n";
    echo "📝 Summary:\n";
    echo "- Payment Initiation: ✅ Success\n";
    echo "- Gateway Processing: ✅ Success\n";
    echo "- Razorpay Order Created: ✅ Success\n";
    echo "- Payment URL/Token Generated: ✅ Success\n\n";
    
    echo "🔑 Key Information for Frontend/Mobile App:\n";
    echo "- Transaction ID: $transactionId\n";
    echo "- Razorpay Order ID: $razorpayOrderId\n";
    echo "- Razorpay Key: $razorpayKey\n";
    echo "- Amount (in paise): $amount\n";
    echo "- Currency: INR\n\n";
    
    echo "📱 Frontend Integration (React/Flutter):\n";
    echo "```javascript\n";
    echo "// For Web (React)\n";
    echo "const options = {\n";
    echo "  key: '$razorpayKey',\n";
    echo "  amount: '$amount',\n";
    echo "  currency: 'INR',\n";
    echo "  name: 'OneFoodDialer',\n";
    echo "  description: 'Order Payment',\n";
    echo "  order_id: '$razorpayOrderId',\n";
    echo "  handler: function (response) {\n";
    echo "    // Send to your callback API\n";
    echo "    console.log(response.razorpay_payment_id);\n";
    echo "    console.log(response.razorpay_order_id);\n";
    echo "    console.log(response.razorpay_signature);\n";
    echo "  },\n";
    echo "  prefill: {\n";
    echo "    name: '{$initiateData['customer_name']}',\n";
    echo "    email: '{$initiateData['customer_email']}',\n";
    echo "    contact: '{$initiateData['customer_phone']}'\n";
    echo "  }\n";
    echo "};\n";
    echo "const rzp = new Razorpay(options);\n";
    echo "rzp.open();\n";
    echo "```\n\n";
    
    echo "📱 Flutter Integration:\n";
    echo "```dart\n";
    echo "// Add razorpay_flutter dependency\n";
    echo "var options = {\n";
    echo "  'key': '$razorpayKey',\n";
    echo "  'amount': $amount,\n";
    echo "  'name': 'OneFoodDialer',\n";
    echo "  'order_id': '$razorpayOrderId',\n";
    echo "  'description': 'Order Payment',\n";
    echo "  'prefill': {\n";
    echo "    'contact': '{$initiateData['customer_phone']}',\n";
    echo "    'email': '{$initiateData['customer_email']}'\n";
    echo "  }\n";
    echo "};\n";
    echo "_razorpay.open(options);\n";
    echo "```\n\n";
    
    echo "🔄 Next Steps:\n";
    echo "1. Integrate the payment URL/token in your frontend\n";
    echo "2. Handle payment success/failure callbacks\n";
    echo "3. Verify payment signature on callback\n";
    echo "4. Update order status in database\n";
    echo "5. Send confirmation to customer\n\n";
    
    echo "📋 API Endpoints Summary:\n";
    echo "- Initiate: POST $baseUrl/\n";
    echo "- Process: POST $baseUrl/{transaction_id}/process\n";
    echo "- Verify: POST $baseUrl/{transaction_id}/verify\n";
    echo "- Status: GET $baseUrl/{transaction_id}\n";
    echo "- Callback: POST $baseUrl/callback\n\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with exception:\n";
    echo "Error: " . $e->getMessage() . "\n\n";
    
    echo "🔧 Troubleshooting:\n";
    echo "1. Check if payment service is running on port 8012\n";
    echo "2. Verify Razorpay credentials in .env file\n";
    echo "3. Ensure database connection is working\n";
    echo "4. Check Laravel logs for detailed errors\n";
}

echo "🏁 Test completed.\n";

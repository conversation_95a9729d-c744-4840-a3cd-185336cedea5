#!/bin/bash

# 🚀 Mobile Payment Flow Test Script
# This script demonstrates the complete payment flow for mobile applications

echo "🔥 MOBILE PAYMENT FLOW TEST"
echo "=========================="
echo ""

BASE_URL="http://127.0.0.1:8012/api/v2/payments"
ORDER_ID="ORDER_MOBILE_$(date +%s)"

echo "📱 Step 1: Initiate Payment Transaction"
echo "======================================="
echo "Order ID: $ORDER_ID"
echo ""

# Step 1: Initiate Payment
INITIATE_RESPONSE=$(curl -s -X POST "$BASE_URL/" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"customer_id\": 12345,
    \"customer_name\": \"John Doe\",
    \"customer_email\": \"<EMAIL>\",
    \"customer_phone\": \"9876543210\",
    \"amount\": 299.99,
    \"transaction_charges\": 0,
    \"wallet_amount\": 0,
    \"order_id\": \"$ORDER_ID\",
    \"referer\": \"mobile_app\",
    \"success_url\": \"https://yourapp.com/payment/success\",
    \"failure_url\": \"https://yourapp.com/payment/failure\",
    \"context\": \"order_payment\",
    \"recurring\": false,
    \"discount\": 0,
    \"company_id\": 1,
    \"unit_id\": 1
  }")

echo "Response:"
echo "$INITIATE_RESPONSE" | jq '.'
echo ""

# Extract transaction ID
TRANSACTION_ID=$(echo "$INITIATE_RESPONSE" | jq -r '.data.transaction_id')

if [ "$TRANSACTION_ID" = "null" ] || [ -z "$TRANSACTION_ID" ]; then
    echo "❌ Failed to initiate payment. Exiting."
    exit 1
fi

echo "✅ Payment initiated successfully!"
echo "Transaction ID: $TRANSACTION_ID"
echo ""

echo "📱 Step 2: Process with Razorpay Gateway"
echo "========================================"

# Step 2: Process with Razorpay
PROCESS_RESPONSE=$(curl -s -X POST "$BASE_URL/$TRANSACTION_ID/process" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "gateway": "razorpay"
  }')

echo "Response:"
echo "$PROCESS_RESPONSE" | jq '.'
echo ""

# Extract Razorpay details
RAZORPAY_ORDER_ID=$(echo "$PROCESS_RESPONSE" | jq -r '.data.order_id')
RAZORPAY_KEY=$(echo "$PROCESS_RESPONSE" | jq -r '.data.razorpay_key')
AMOUNT=$(echo "$PROCESS_RESPONSE" | jq -r '.data.amount')

if [ "$RAZORPAY_ORDER_ID" = "null" ] || [ -z "$RAZORPAY_ORDER_ID" ]; then
    echo "❌ Failed to process payment with Razorpay. Exiting."
    exit 1
fi

echo "✅ Razorpay order created successfully!"
echo "Razorpay Order ID: $RAZORPAY_ORDER_ID"
echo "Razorpay Key: $RAZORPAY_KEY"
echo "Amount (paise): $AMOUNT"
echo ""

echo "📱 Step 3: Mobile App Integration Data"
echo "====================================="
echo ""
echo "🔧 Flutter Integration Code:"
echo "```dart"
echo "var options = {"
echo "  'key': '$RAZORPAY_KEY',"
echo "  'amount': $AMOUNT,"
echo "  'name': 'OneFoodDialer',"
echo "  'order_id': '$RAZORPAY_ORDER_ID',"
echo "  'description': 'Payment for Order #$ORDER_ID',"
echo "  'prefill': {"
echo "    'contact': '9876543210',"
echo "    'email': '<EMAIL>'"
echo "  }"
echo "};"
echo "_razorpay.open(options);"
echo "```"
echo ""

echo "🔧 React Native Integration Code:"
echo "```javascript"
echo "const options = {"
echo "  key: '$RAZORPAY_KEY',"
echo "  amount: $AMOUNT,"
echo "  name: 'OneFoodDialer',"
echo "  order_id: '$RAZORPAY_ORDER_ID',"
echo "  description: 'Payment for Order #$ORDER_ID',"
echo "  prefill: {"
echo "    contact: '9876543210',"
echo "    email: '<EMAIL>'"
echo "  }"
echo "};"
echo "RazorpayCheckout.open(options);"
echo "```"
echo ""

echo "📱 Step 4: Simulate Payment Success Callback"
echo "============================================"

# Generate test payment ID
TEST_PAYMENT_ID="pay_test_$(date +%s)"
TEST_SIGNATURE="test_signature_$(date +%s | md5sum | cut -d' ' -f1)"

echo "Simulating payment success with:"
echo "Payment ID: $TEST_PAYMENT_ID"
echo "Order ID: $RAZORPAY_ORDER_ID"
echo "Signature: $TEST_SIGNATURE"
echo ""

# Step 4: Simulate callback
CALLBACK_RESPONSE=$(curl -s -X POST "$BASE_URL/callback" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d "{
    \"razorpay_payment_id\": \"$TEST_PAYMENT_ID\",
    \"razorpay_order_id\": \"$RAZORPAY_ORDER_ID\",
    \"razorpay_signature\": \"$TEST_SIGNATURE\",
    \"transaction_id\": \"$TRANSACTION_ID\"
  }")

echo "Callback Response:"
echo "$CALLBACK_RESPONSE" | jq '.'
echo ""

echo "📱 Step 5: Check Final Payment Status"
echo "====================================="

# Step 5: Check status
STATUS_RESPONSE=$(curl -s -X GET "$BASE_URL/$TRANSACTION_ID" \
  -H "Accept: application/json")

echo "Final Status:"
echo "$STATUS_RESPONSE" | jq '.'
echo ""

echo "🎉 MOBILE PAYMENT FLOW TEST COMPLETED!"
echo "======================================"
echo ""
echo "📋 Summary:"
echo "- Transaction ID: $TRANSACTION_ID"
echo "- Order ID: $ORDER_ID"
echo "- Razorpay Order ID: $RAZORPAY_ORDER_ID"
echo "- Payment ID: $TEST_PAYMENT_ID"
echo ""
echo "📱 Next Steps for Mobile Integration:"
echo "1. Use the Razorpay SDK in your mobile app"
echo "2. Pass the order details from Step 2 to the SDK"
echo "3. Handle the payment response in your app"
echo "4. Send the payment response to your callback API"
echo "5. Update your app UI based on the callback response"
echo ""
echo "🔗 Useful Endpoints:"
echo "- Initiate: POST $BASE_URL/"
echo "- Process: POST $BASE_URL/{transaction_id}/process"
echo "- Callback: POST $BASE_URL/callback"
echo "- Status: GET $BASE_URL/{transaction_id}"
echo ""
echo "✅ All systems ready for mobile integration!"

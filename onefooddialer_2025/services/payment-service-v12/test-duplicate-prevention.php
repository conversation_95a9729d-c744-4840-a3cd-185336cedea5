<?php

/**
 * Test Duplicate Transaction Prevention
 * 
 * This script tests the duplicate prevention mechanism
 */

echo "🔥 Duplicate Transaction Prevention Test\n";
echo "=======================================\n\n";

$baseUrl = 'http://127.0.0.1:8012/api/v2/payments';
$orderNo = 'ORDER_DUPLICATE_TEST_' . time();

echo "📋 Test Configuration:\n";
echo "- Order No: $orderNo\n";
echo "- Customer ID: 99999\n";
echo "- Amount: ₹199.99\n\n";

$paymentData = [
    'customer_id' => 99999,
    'customer_name' => 'Duplicate Test User',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '9999999999',
    'amount' => 199.99,
    'transaction_charges' => 0,
    'wallet_amount' => 0,
    'order_id' => $orderNo,
    'referer' => 'mobile_app',
    'success_url' => 'https://yourapp.com/payment/success',
    'failure_url' => 'https://yourapp.com/payment/failure',
    'context' => 'order_payment',
    'recurring' => false,
    'discount' => 0,
    'company_id' => 1,
    'unit_id' => 1
];

function makePaymentRequest($baseUrl, $paymentData, $attemptNumber) {
    echo "🚀 Attempt $attemptNumber: Initiate Payment\n";
    echo "=====================================\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $result = json_decode($response, true);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
    
    if ($result && isset($result['data']['transaction_id'])) {
        echo "✅ Transaction ID: {$result['data']['transaction_id']}\n";
        return $result['data']['transaction_id'];
    } else {
        echo "❌ Failed to get transaction ID\n";
        return null;
    }
}

try {
    // First attempt - should create new transaction
    echo "🎯 Testing Duplicate Prevention\n";
    echo "==============================\n\n";
    
    $transactionId1 = makePaymentRequest($baseUrl, $paymentData, 1);
    echo "\n";
    
    // Second attempt - should return existing transaction
    $transactionId2 = makePaymentRequest($baseUrl, $paymentData, 2);
    echo "\n";
    
    // Third attempt - should return existing transaction
    $transactionId3 = makePaymentRequest($baseUrl, $paymentData, 3);
    echo "\n";
    
    echo "📊 Results Analysis:\n";
    echo "===================\n";
    echo "- First Transaction ID:  $transactionId1\n";
    echo "- Second Transaction ID: $transactionId2\n";
    echo "- Third Transaction ID:  $transactionId3\n\n";
    
    if ($transactionId1 === $transactionId2 && $transactionId2 === $transactionId3) {
        echo "✅ SUCCESS: Duplicate prevention is working!\n";
        echo "   All attempts returned the same transaction ID.\n\n";
    } else {
        echo "❌ FAILURE: Duplicate prevention is not working!\n";
        echo "   Different transaction IDs were returned.\n\n";
    }
    
    // Check database records
    echo "🗄️ Database Verification:\n";
    echo "=========================\n";
    
    if ($transactionId1) {
        $transactionIdNumeric = str_replace('TXN', '', $transactionId1);
        $cmd = "mysql -u root -p'Automation@321' -h 127.0.0.1 -e \"USE live_quickserve_8163; SELECT pk_transaction_id, customer_id, payment_amount, pre_order_id, status, created_date FROM payment_transaction WHERE customer_id = 99999 AND pre_order_id = '$orderNo' ORDER BY pk_transaction_id;\" 2>/dev/null";
        $result = shell_exec($cmd);
        echo $result . "\n";
        
        // Count records
        $lines = explode("\n", trim($result));
        $recordCount = count($lines) - 1; // Subtract header line
        
        if ($recordCount === 1) {
            echo "✅ Database Check: Only 1 record found (correct)\n";
        } else {
            echo "❌ Database Check: $recordCount records found (should be 1)\n";
        }
    }
    
    echo "\n🎉 Duplicate Prevention Test Completed!\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with exception:\n";
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n🏁 Test completed.\n";

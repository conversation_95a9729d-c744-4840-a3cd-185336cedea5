<?php

namespace App\Providers;

use App\Repositories\Payment\PaymentRepository;
use App\Repositories\Payment\PaymentRepositoryInterface;
use App\Services\Payment\Gateway\PaymentGatewayInterface;
use App\Services\Payment\Gateway\PayuGateway;
use App\Services\Payment\Gateway\StripeGateway;
use App\Services\Payment\Gateway\PaypalGateway;
use App\Services\Payment\Gateway\RazorpayGateway;
use App\Services\Payment\PaymentService;
use App\Services\Payment\PaymentServiceInterface;
use Illuminate\Support\ServiceProvider;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Register repositories
        $this->app->bind(PaymentRepositoryInterface::class, PaymentRepository::class);

        // Register payment service
        $this->app->singleton(PaymentServiceInterface::class, function ($app) {
            $config = $app['config']->get('payment');
            $paymentService = new PaymentService(
                $app->make(PaymentRepositoryInterface::class),
                $config
            );

            // Register payment gateways
            $this->registerPaymentGateways($paymentService, $config);

            return $paymentService;
        });

        // For backward compatibility
        $this->app->singleton(PaymentService::class, function ($app) {
            return $app->make(PaymentServiceInterface::class);
        });
    }

    /**
     * Register payment gateways.
     *
     * @param PaymentService $paymentService
     * @param array $config
     * @return void
     */
    protected function registerPaymentGateways(PaymentService $paymentService, array $config): void
    {
        // Register PayU gateway
        if (isset($config['gateways']['payu'])) {
            $payuGateway = new PayuGateway();
            $payuGateway->initialize($config['gateways']['payu']);
            $paymentService->registerGateway('payu', $payuGateway);
        }

        // Register Stripe gateway
        if (isset($config['gateways']['stripe'])) {
            $stripeGateway = new StripeGateway();
            $stripeGateway->initialize($config['gateways']['stripe']);
            $paymentService->registerGateway('stripe', $stripeGateway);
        }

        // Register PayPal gateway
        if (isset($config['gateways']['paypal'])) {
            $paypalGateway = new PaypalGateway();
            $paypalGateway->initialize($config['gateways']['paypal']);
            $paymentService->registerGateway('paypal', $paypalGateway);
        }

        // Register Razorpay gateway
        if (isset($config['gateways']['razorpay'])) {
            $razorpayGateway = new RazorpayGateway($config['gateways']['razorpay']);
            $paymentService->registerGateway('razorpay', $razorpayGateway);
        }

        // TODO: Implement other gateways
        // - Instamojo
        // - Paytm
        // - Payeezy
        // - Mobikwik
        // - Converge
        // - Yesbank
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        //
    }
}

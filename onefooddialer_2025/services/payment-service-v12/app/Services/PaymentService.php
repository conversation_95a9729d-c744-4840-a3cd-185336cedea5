<?php

namespace App\Services;

use App\Models\PaymentTransaction;
use App\Services\Payment\Gateway\PaymentGatewayInterface;
use App\Exceptions\Payment\PaymentException;
use App\Events\Payment\PaymentCompleted;
use App\Events\Payment\PaymentFailed;
use App\Events\Payment\PaymentInitiated;
use App\Events\Payment\PaymentRefunded;
use App\Events\Payment\WalletPaymentProcessed;
use App\Events\Payment\WalletPaymentFailed;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    /**
     * The registered payment gateways.
     *
     * @var array<string, PaymentGatewayInterface>
     */
    protected $gateways = [];

    /**
     * The metrics service instance.
     *
     * @var MetricsService
     */
    protected MetricsService $metricsService;

    /**
     * Create a new PaymentService instance.
     *
     * @param MetricsService $metricsService
     */
    public function __construct(MetricsService $metricsService)
    {
        $this->metricsService = $metricsService;
    }

    /**
     * Register a payment gateway.
     *
     * @param string $name
     * @param PaymentGatewayInterface $gateway
     * @return void
     */
    public function registerGateway(string $name, PaymentGatewayInterface $gateway): void
    {
        $this->gateways[$name] = $gateway;
    }

    /**
     * Get a payment gateway.
     *
     * @param string $name
     * @return PaymentGatewayInterface
     * @throws PaymentException
     */
    public function getGateway(string $name): PaymentGatewayInterface
    {
        if (!isset($this->gateways[$name])) {
            throw new PaymentException("Payment gateway '{$name}' not found");
        }

        return $this->gateways[$name];
    }

    /**
     * Get a transaction by ID.
     *
     * @param string $transactionId
     * @return PaymentTransaction
     * @throws PaymentException
     */
    public function getTransaction(string $transactionId): PaymentTransaction
    {
        try {
            // Extract the numeric ID from transaction ID (e.g., TXN123 -> 123)
            if (str_starts_with($transactionId, 'TXN')) {
                $numericId = substr($transactionId, 3);
                $transaction = PaymentTransaction::where('pk_transaction_id', $numericId)->firstOrFail();
            } else {
                // Fallback: try to find by pk_transaction_id directly
                $transaction = PaymentTransaction::findOrFail($transactionId);
            }
            return $transaction;
        } catch (\Exception $e) {
            throw new PaymentException("Transaction with ID {$transactionId} not found");
        }
    }

    /**
     * Initiate a payment.
     *
     * @param array $data
     * @return PaymentTransaction
     * @throws PaymentException
     */
    public function initiatePayment(array $data): PaymentTransaction
    {
        try {
            DB::beginTransaction();

            // Apply transaction charges if enabled
            $amount = $data['amount'];
            $transactionCharges = 0;

            if (config('payment.transaction_charges.apply', false)) {
                $percentage = config('payment.transaction_charges.percentage', 0);
                $transactionCharges = round(($amount * $percentage) / 100, 2);
                $amount = round($amount + $transactionCharges, 2);
            }

            // Create transaction record
            $transaction = PaymentTransaction::create([
                'company_id' => $data['company_id'] ?? 1, // Default company ID
                'unit_id' => $data['unit_id'] ?? 1, // Default unit ID
                'customer_id' => $data['customer_id'],
                'customer_email' => $data['customer_email'] ?? null,
                'customer_phone' => $data['customer_phone'] ?? null,
                'customer_name' => $data['customer_name'] ?? null,
                'payment_amount' => $amount,
                'transaction_charges' => $transactionCharges,
                'wallet_amount' => $data['wallet_amount'] ?? null,
                'pre_order_id' => $data['order_id'] ?? null,
                'status' => 'initiated',
                'referer' => $data['referer'] ?? 'website',
                'success_url' => $data['success_url'],
                'failure_url' => $data['failure_url'],
                'context' => $data['context'] ?? 'order',
                'recurring' => $data['recurring'] ?? 0,
                'discount' => $data['discount'] ?? 0,
                'created_date' => now(),
            ]);

            DB::commit();

            // Dispatch event
            event(new PaymentInitiated($transaction));

            return $transaction;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment initiation failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw new PaymentException('Payment initiation failed: ' . $e->getMessage());
        }
    }

    /**
     * Process a payment.
     *
     * @param string $transactionId
     * @param string $gateway
     * @return array
     * @throws PaymentException
     */
    public function processPayment(string $transactionId, string $gateway): array
    {
        try {
            $transaction = $this->getTransaction($transactionId);

            // Update gateway
            $transaction->gateway = $gateway;
            $transaction->save();

            // Get gateway adapter
            $gatewayAdapter = $this->getGateway($gateway);

            // Transform transaction data for gateway compatibility
            $gatewayData = [
                'transaction_id' => $transaction->transaction_id,
                'order_id' => $transaction->order_id, // Uses the accessor method
                'amount' => $transaction->amount, // Uses the accessor method
                'customer_id' => $transaction->customer_id,
                'customer_name' => $transaction->customer_name,
                'customer_email' => $transaction->customer_email,
                'customer_phone' => $transaction->customer_phone,
                'description' => $transaction->description ?? 'Payment for Order #' . $transaction->order_id,
                'callback_url' => url('/api/v2/payments/callback'),
                'success_url' => $transaction->success_url,
                'failure_url' => $transaction->failure_url
            ];

            // Get form data
            $formData = $gatewayAdapter->generatePaymentForm($gatewayData);

            // Update transaction with gateway details
            $transaction->gateway = $gateway;
            if (isset($formData['order_id'])) {
                $transaction->gateway_transaction_id = $formData['order_id'];
            }
            $transaction->save();

            return $formData;
        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'gateway' => $gateway
            ]);

            throw new PaymentException('Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle a payment callback.
     *
     * @param array $data
     * @param \Illuminate\Http\Request|null $request
     * @return PaymentTransaction
     * @throws PaymentException
     */
    public function handleCallback(array $data, $request = null): PaymentTransaction
    {
        try {
            // Log the callback request
            app(PaymentLogService::class)->logEvent(
                'callback',
                'received',
                [],
                $data,
                null,
                null,
                $request
            );

            // Identify gateway from response data
            $gateway = $this->identifyGateway($data);

            // Get transaction ID from response data
            $transactionId = $this->extractTransactionId($data, $gateway);

            // Get transaction
            $transaction = $this->getTransaction($transactionId);

            // Get gateway adapter
            $gatewayAdapter = $this->getGateway($gateway);

            // Verify payment
            $result = $gatewayAdapter->verifyPayment($transactionId, $data);

            // Update transaction status
            $transaction->status = $result['status'];
            $transaction->gateway_transaction_id = $result['gateway_transaction_id'] ?? null;
            $transaction->metadata = $result['metadata'] ?? null;
            $transaction->save();

            // Log the verification result
            app(PaymentLogService::class)->logEvent(
                'verify',
                $result['success'] ? 'success' : 'failure',
                $data,
                $result,
                $transaction->transaction_id,
                $gateway,
                $request
            );

            // Dispatch event
            if ($result['status'] === 'completed') {
                event(new PaymentCompleted($transaction));

                // Process wallet payment if applicable
                if ($transaction->wallet_amount > 0) {
                    $this->processWalletPayment($transaction);
                }
            } elseif ($result['status'] === 'failed') {
                event(new PaymentFailed($transaction));
            }

            return $transaction;
        } catch (\Exception $e) {
            Log::error('Payment callback handling failed', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            // Log the error
            app(PaymentLogService::class)->logEvent(
                'callback',
                'error',
                $data,
                ['error' => $e->getMessage()],
                null,
                null,
                $request
            );

            throw new PaymentException('Payment callback handling failed: ' . $e->getMessage());
        }
    }

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @return array
     * @throws PaymentException
     */
    public function refundPayment(string $transactionId, float $amount = null): array
    {
        try {
            $transaction = $this->getTransaction($transactionId);

            if ($transaction->status !== 'completed') {
                throw new PaymentException('Cannot refund a transaction that is not completed');
            }

            // Get gateway adapter
            $gatewayAdapter = $this->getGateway($transaction->gateway);

            // Process refund
            $result = $gatewayAdapter->refundPayment($transaction->gateway_transaction_id, $amount);

            // Update transaction status
            if ($result['success']) {
                $transaction->status = 'refunded';
                $transaction->save();

                // Dispatch event - Note: PaymentRefunded now expects Payment model
                // For now, we'll skip the event to avoid type mismatch
                // TODO: Align event models or create separate events for PaymentTransaction
                // event(new PaymentRefunded($transaction, $amount));
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Payment refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transactionId,
                'amount' => $amount
            ]);

            throw new PaymentException('Payment refund failed: ' . $e->getMessage());
        }
    }

    /**
     * Identify the payment gateway from response data.
     *
     * @param array $data
     * @return string
     * @throws PaymentException
     */
    protected function identifyGateway(array $data): string
    {
        // Logic to identify gateway from response data
        if (isset($data['payuMoneyId'])) {
            return 'payu';
        } elseif (isset($data['payment_request_id'])) {
            return 'instamojo';
        } elseif (isset($data['CHECKSUMHASH']) && isset($data['RESPCODE'])) {
            return 'paytm';
        } elseif (isset($data['Transaction_Tag']) && isset($data['transaction_key'])) {
            return 'payeezy';
        } elseif (isset($data['checksum']) && isset($data['orderid']) && isset($data['statuscode'])) {
            return 'mobikwik';
        } elseif (isset($data['PayerID']) || isset($data['token'])) {
            return 'paypal';
        } elseif (isset($data['payment_intent'])) {
            return 'stripe';
        } elseif (isset($data['razorpay_payment_id']) && isset($data['razorpay_order_id'])) {
            return 'razorpay';
        } elseif (isset($data['transaction_id']) && isset($data['payment_status'])) {
            // Test gateway pattern - for testing purposes
            return 'test_gateway';
        }

        throw new PaymentException('Unknown payment gateway');
    }

    /**
     * Extract the transaction ID from response data.
     *
     * @param array $data
     * @param string $gateway
     * @return string
     * @throws PaymentException
     */
    protected function extractTransactionId(array $data, string $gateway): string
    {
        // Logic to extract transaction ID from response data based on gateway
        switch ($gateway) {
            case 'payu':
                return $data['udf1'] ?? '';
            case 'instamojo':
                return $data['transaction_id'] ?? '';
            case 'paytm':
                return $data['ORDERID'] ?? '';
            case 'payeezy':
                return $data['transaction_id'] ?? '';
            case 'mobikwik':
                return $data['orderid'] ?? '';
            case 'paypal':
                return $data['transaction_id'] ?? '';
            case 'stripe':
                return $data['transaction_id'] ?? '';
            case 'razorpay':
                // For Razorpay, we need to find the transaction by order_id
                // The order_id contains our transaction reference
                if (isset($data['razorpay_order_id'])) {
                    // Find transaction by Razorpay order_id
                    $transaction = PaymentTransaction::where('gateway_transaction_id', $data['razorpay_order_id'])->first();
                    if ($transaction) {
                        return $transaction->transaction_id;
                    }
                }
                // Fallback: look for transaction_id in custom field
                return $data['transaction_id'] ?? '';
            case 'test_gateway':
                return $data['transaction_id'] ?? '';
            default:
                throw new PaymentException('Cannot extract transaction ID for unknown gateway');
        }
    }

    /**
     * Process wallet payment for a transaction with atomic operations.
     *
     * @param PaymentTransaction $transaction
     * @return void
     * @throws PaymentException
     */
    protected function processWalletPayment(PaymentTransaction $transaction): void
    {
        // Only use transactions in non-testing environment
        $useTransactions = !app()->environment('testing');
        $startTime = microtime(true);

        try {
            if ($useTransactions) {
                DB::beginTransaction();
            }

            // Log wallet payment processing
            app(PaymentLogService::class)->logEvent(
                'wallet_payment',
                'processing',
                [],
                [
                    'transaction_id' => $transaction->transaction_id,
                    'wallet_amount' => $transaction->wallet_amount,
                    'customer_id' => $transaction->customer_id,
                ],
                $transaction->transaction_id,
                'wallet'
            );

            // Validate wallet balance before processing
            $walletBalance = $this->getWalletBalance($transaction->customer_id);
            if ($walletBalance < $transaction->wallet_amount) {
                throw new PaymentException('Insufficient wallet balance');
            }

            // Call customer service to deduct wallet amount
            $walletResponse = Http::timeout(30)
                ->retry(3, 1000)
                ->withHeaders([
                    'X-Service' => 'payment-service-v12',
                    'X-Request-ID' => uniqid('wallet_'),
                    'X-Correlation-ID' => $transaction->transaction_id,
                ])
                ->post(config('services.customer.url') . '/api/v2/wallet/deduct', [
                    'customer_id' => $transaction->customer_id,
                    'amount' => $transaction->wallet_amount,
                    'description' => "Payment for order #{$transaction->order_id}",
                    'transaction_id' => $transaction->transaction_id,
                    'metadata' => [
                        'payment_transaction_id' => $transaction->transaction_id,
                        'order_id' => $transaction->order_id,
                        'source' => 'payment_service',
                    ]
                ]);

            if (!$walletResponse->successful()) {
                throw new PaymentException('Wallet deduction failed: ' . $walletResponse->body());
            }

            $walletData = $walletResponse->json();

            // Update transaction with wallet details
            $transaction->update([
                'wallet_transaction_id' => $walletData['data']['transaction_id'] ?? null,
                'wallet_balance_after' => $walletData['data']['balance_after'] ?? null,
            ]);

            if ($useTransactions) {
                DB::commit();
            }

            // Publish wallet payment event
            event(new WalletPaymentProcessed($transaction, $walletData));

            // Record metrics
            $duration = microtime(true) - $startTime;
            $this->metricsService->recordWalletOperation('deduct', $duration, 'success');

            // Log success
            app(PaymentLogService::class)->logEvent(
                'wallet_payment',
                'success',
                [],
                [
                    'transaction_id' => $transaction->transaction_id,
                    'wallet_amount' => $transaction->wallet_amount,
                    'wallet_transaction_id' => $walletData['data']['transaction_id'] ?? null,
                    'balance_after' => $walletData['data']['balance_after'] ?? null,
                    'message' => 'Wallet payment processed successfully',
                ],
                $transaction->transaction_id,
                'wallet'
            );

        } catch (\Exception $e) {
            if ($useTransactions) {
                DB::rollBack();
            }

            Log::error('Wallet payment processing failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $transaction->transaction_id,
                'wallet_amount' => $transaction->wallet_amount,
                'customer_id' => $transaction->customer_id,
                'trace' => $e->getTraceAsString()
            ]);

            // Log the error
            app(PaymentLogService::class)->logEvent(
                'wallet_payment',
                'error',
                [],
                [
                    'error' => $e->getMessage(),
                    'transaction_id' => $transaction->transaction_id,
                    'wallet_amount' => $transaction->wallet_amount,
                    'customer_id' => $transaction->customer_id,
                ],
                $transaction->transaction_id,
                'wallet'
            );

            // Record error metrics
            $duration = microtime(true) - $startTime;
            $this->metricsService->recordWalletOperation('deduct', $duration, 'failed');
            $this->metricsService->recordError('payment-service', 'wallet_payment', get_class($e));

            // Publish wallet payment failed event
            event(new WalletPaymentFailed($transaction, $e->getMessage()));

            throw new PaymentException('Wallet payment failed: ' . $e->getMessage());
        }
    }

    /**
     * Get wallet balance for a customer.
     *
     * @param int $customerId
     * @return float
     * @throws PaymentException
     */
    protected function getWalletBalance(int $customerId): float
    {
        try {
            $response = Http::timeout(10)
                ->retry(2, 500)
                ->withHeaders([
                    'X-Service' => 'payment-service-v12',
                    'X-Request-ID' => uniqid('balance_'),
                ])
                ->get(config('services.customer.url') . "/api/v2/wallet/{$customerId}/balance");

            if (!$response->successful()) {
                throw new PaymentException('Failed to fetch wallet balance: ' . $response->body());
            }

            $data = $response->json();
            return (float) ($data['data']['balance'] ?? 0);
        } catch (\Exception $e) {
            Log::error('Failed to get wallet balance', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
            throw new PaymentException('Failed to get wallet balance: ' . $e->getMessage());
        }
    }

    /**
     * Get transaction statistics.
     *
     * @param array $filters
     * @return array
     */
    public function getTransactionStatistics(array $filters = []): array
    {
        $query = PaymentTransaction::query();

        // Apply filters
        if (isset($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        if (isset($filters['gateway'])) {
            $query->where('gateway', $filters['gateway']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Get total transactions
        $totalTransactions = $query->count();

        // Get total amount
        $totalAmount = $query->sum('amount');

        // Get transactions by status
        $transactionsByStatus = $query->select('status', DB::raw('count(*) as count'), DB::raw('sum(amount) as amount'))
            ->groupBy('status')
            ->get()
            ->keyBy('status')
            ->toArray();

        // Get transactions by gateway
        $transactionsByGateway = $query->select('gateway', DB::raw('count(*) as count'), DB::raw('sum(amount) as amount'))
            ->groupBy('gateway')
            ->get()
            ->keyBy('gateway')
            ->toArray();

        // Get recent transactions
        $recentTransactions = $query->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return [
            'total_transactions' => $totalTransactions,
            'total_amount' => $totalAmount,
            'transactions_by_status' => $transactionsByStatus,
            'transactions_by_gateway' => $transactionsByGateway,
            'recent_transactions' => $recentTransactions,
        ];
    }
}

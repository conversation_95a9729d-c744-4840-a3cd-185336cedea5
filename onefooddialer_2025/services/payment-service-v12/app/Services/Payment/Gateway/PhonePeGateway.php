<?php

namespace App\Services\Payment\Gateway;

use App\Exceptions\PaymentException;
use Illuminate\Support\Facades\Http;

class PhonePeGateway implements PaymentGatewayInterface
{
    protected $merchantId;
    protected $saltKey;
    protected $saltIndex;
    protected $baseUrl;

    public function __construct(array $config)
    {
        $this->merchantId = $config['merchant_id'] ?? '';
        $this->saltKey = $config['salt_key'] ?? '';
        $this->saltIndex = $config['salt_index'] ?? '';
        $this->baseUrl = $config['base_url'] ?? 'https://mercury-uat.phonepe.com';
    }

    public function getName(): string
    {
        return 'phonepe';
    }

    public function isEnabled(): bool
    {
        return true;
    }

    public function getSupportedCurrencies(): array
    {
        return ['INR'];
    }

    public function getSupportedMethods(): array
    {
        return ['UPI', 'CARD', 'NETBANKING'];
    }

    public function generatePaymentForm(array $paymentData): array
    {
        // Generate a PhonePe payment URL (redirect or intent)
        $amount = (int)($paymentData['amount'] * 100); // in paise
        $orderId = $paymentData['order_id'];
        $redirectUrl = $paymentData['redirect_url'] ?? '';
        $callbackUrl = $paymentData['callback_url'] ?? '';
        $customerPhone = $paymentData['customer_phone'];
        $payload = [
            'merchantId' => $this->merchantId,
            'merchantTransactionId' => $orderId,
            'amount' => $amount,
            'merchantUserId' => $paymentData['customer_id'],
            'redirectUrl' => $redirectUrl,
            'callbackUrl' => $callbackUrl,
            'mobileNumber' => $customerPhone,
            'paymentInstrument' => [
                'type' => 'PAY_PAGE',
            ],
        ];
        $jsonPayload = json_encode($payload);
        $base64Payload = base64_encode($jsonPayload);
        $xVerify = $this->generateXVerify($base64Payload, '/pg/v1/pay');
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'X-VERIFY' => $xVerify,
            'X-MERCHANT-ID' => $this->merchantId,
        ])->post($this->baseUrl . '/pg/v1/pay', [
            'request' => $base64Payload
        ]);
        $result = $response->json();
        if (!isset($result['success']) || !$result['success']) {
            throw new PaymentException('PhonePe payment initiation failed: ' . ($result['message'] ?? 'Unknown error'));
        }
        $redirectInfo = $result['data']['instrumentResponse']['redirectInfo'] ?? null;
        if (!$redirectInfo) {
            throw new PaymentException('PhonePe did not return a redirect URL.');
        }
        return [
            'redirect_url' => $redirectInfo['url'],
            'payment_gateway' => 'phonepe',
            'order_id' => $orderId,
        ];
    }

    public function processPayment(array $paymentData): array
    {
        // For PhonePe, payment is processed via redirect, so this is usually a no-op
        return $this->generatePaymentForm($paymentData);
    }

    public function verifyPayment(string $transactionId, array $additionalData = []): array
    {
        $url = $this->baseUrl . '/pg/v1/status/' . $this->merchantId . '/' . $transactionId;
        $xVerify = $this->generateXVerify('', '/pg/v1/status/' . $this->merchantId . '/' . $transactionId);
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'X-VERIFY' => $xVerify,
            'X-MERCHANT-ID' => $this->merchantId,
        ])->get($url);
        $result = $response->json();
        if (!isset($result['success']) || !$result['success']) {
            throw new PaymentException('PhonePe payment verification failed: ' . ($result['message'] ?? 'Unknown error'));
        }
        return $result['data'] ?? [];
    }

    public function refundPayment(string $transactionId, ?float $amount = null, array $additionalData = []): array
    {
        throw new PaymentException('Refund not implemented for PhonePe test integration.');
    }

    public function cancelPayment(string $transactionId, array $additionalData = []): array
    {
        throw new PaymentException('Cancel not implemented for PhonePe test integration.');
    }

    public function getPaymentStatus(string $transactionId): array
    {
        return $this->verifyPayment($transactionId);
    }

    public function getPaymentDetails(string $transactionId): array
    {
        return $this->verifyPayment($transactionId);
    }

    public function handleWebhook(array $data): array
    {
        // Implement webhook handler if needed
        return $data;
    }

    private function generateXVerify(string $base64Payload, string $apiPath): string
    {
        $string = $base64Payload . $apiPath . $this->saltKey;
        $sha256 = hash('sha256', $string);
        return $sha256 . '###' . $this->saltIndex;
    }
}

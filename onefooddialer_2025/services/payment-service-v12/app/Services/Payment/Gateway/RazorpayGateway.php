<?php

namespace App\Services\Payment\Gateway;

use App\Exceptions\PaymentException;
use Razorpay\Api\Api;

class RazorpayGateway implements PaymentGatewayInterface
{
    protected $keyId;
    protected $keySecret;

    public function __construct(array $config)
    {
        $this->keyId = $config['key_id'] ?? '';
        $this->keySecret = $config['key_secret'] ?? '';
    }

    public function getName(): string
    {
        return 'razorpay';
    }

    public function isEnabled(): bool
    {
        return !empty($this->keyId) && !empty($this->keySecret);
    }

    public function getSupportedCurrencies(): array
    {
        return ['INR'];
    }

    public function getSupportedMethods(): array
    {
        return ['CARD', 'UPI', 'NETBANKING', 'WALLET'];
    }

    public function generatePaymentForm(array $paymentData): array
    {
        $api = new Api($this->keyId, $this->keySecret);
        $order = $api->order->create([
            'receipt' => $paymentData['order_id'],
            'amount' => (int)($paymentData['amount'] * 100), // in paise
            'currency' => 'INR',
            'payment_capture' => 1
        ]);
        return [
            'order_id' => $order['id'],
            'razorpay_key' => $this->keyId,
            'amount' => $order['amount'],
            'currency' => $order['currency'],
            'name' => $paymentData['customer_name'] ?? '',
            'email' => $paymentData['customer_email'] ?? '',
            'contact' => $paymentData['customer_phone'] ?? '',
            'description' => $paymentData['description'] ?? 'Order Payment',
            'callback_url' => $paymentData['callback_url'] ?? '',
        ];
    }

    public function processPayment(array $paymentData): array
    {
        return $this->generatePaymentForm($paymentData);
    }

    public function verifyPayment(string $transactionId, array $additionalData = []): array
    {
        $api = new Api($this->keyId, $this->keySecret);
        $payment = $api->payment->fetch($transactionId);
        return $payment->toArray();
    }

    public function refundPayment(string $transactionId, ?float $amount = null, array $additionalData = []): array
    {
        $api = new Api($this->keyId, $this->keySecret);
        $payment = $api->payment->fetch($transactionId);
        $refund = $payment->refund([
            'amount' => $amount ? (int)($amount * 100) : $payment['amount']
        ]);
        return $refund->toArray();
    }

    public function cancelPayment(string $transactionId, array $additionalData = []): array
    {
        throw new PaymentException('Razorpay does not support cancel via API after payment is captured.');
    }

    public function getPaymentStatus(string $transactionId): array
    {
        return $this->verifyPayment($transactionId);
    }

    public function getPaymentDetails(string $transactionId): array
    {
        return $this->verifyPayment($transactionId);
    }

    public function handleWebhook(array $data): array
    {
        // Implement webhook handler if needed
        return $data;
    }
}

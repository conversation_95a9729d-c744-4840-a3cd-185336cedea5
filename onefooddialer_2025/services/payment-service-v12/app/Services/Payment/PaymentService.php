<?php

namespace App\Services\Payment;

use App\Events\Payment\PaymentCancelled;
use App\Events\Payment\PaymentCreated;
use App\Events\Payment\PaymentFailed;
use App\Events\Payment\PaymentRefunded;
use App\Events\Payment\PaymentSucceeded;
use App\Exceptions\PaymentException;
use App\Models\Payment;
use App\Repositories\Payment\PaymentRepositoryInterface;
use App\Services\Payment\Gateway\PaymentGatewayInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class PaymentService implements PaymentServiceInterface
{
    /**
     * @var PaymentRepositoryInterface
     */
    protected $paymentRepository;

    /**
     * @var array
     */
    protected $gateways = [];

    /**
     * @var array
     */
    protected $config;

    /**
     * PaymentService constructor.
     *
     * @param PaymentRepositoryInterface $paymentRepository
     * @param array $config
     */
    public function __construct(PaymentRepositoryInterface $paymentRepository, array $config = [])
    {
        $this->paymentRepository = $paymentRepository;
        $this->config = $config;
        // Register only PhonePe gateway
        $this->registerGateway('phonepe', new \App\Services\Payment\Gateway\PhonePeGateway([
            'merchant_id' => $config['phonepe_merchant_id'] ?? 'PGTESTPAYUAT',
            'salt_key' => $config['phonepe_salt_key'] ?? 'your_salt_key',
            'salt_index' => $config['phonepe_salt_index'] ?? '1',
            'base_url' => $config['phonepe_base_url'] ?? 'https://api-preprod.phonepe.com/apis/pg-sandbox',
        ]));
    }

    /**
     * Register a payment gateway.
     *
     * @param string $name
     * @param PaymentGatewayInterface $gateway
     * @return void
     */
    public function registerGateway(string $name, PaymentGatewayInterface $gateway): void
    {
        $this->gateways[$name] = $gateway;
    }

    /**
     * Get all payments.
     *
     * @param array $filters
     * @return Collection
     */
    public function getAllPayments(array $filters = []): Collection
    {
        return $this->paymentRepository->all($filters);
    }

    /**
     * Get paginated payments.
     *
     * @param int $perPage
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getPaginatedPayments(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        return $this->paymentRepository->paginate($perPage, $filters);
    }

    /**
     * Get payment by ID.
     *
     * @param int $id
     * @return Payment|null
     */
    public function getPaymentById(int $id): ?Payment
    {
        return $this->paymentRepository->findById($id);
    }

    /**
     * Get payment by transaction ID.
     *
     * @param string $transactionId
     * @return Payment|null
     */
    public function getPaymentByTransactionId(string $transactionId): ?Payment
    {
        return $this->paymentRepository->findByTransactionId($transactionId);
    }

    /**
     * Get payments by order ID.
     *
     * @param int $orderId
     * @return Collection
     */
    public function getPaymentsByOrderId(int $orderId): Collection
    {
        return $this->paymentRepository->findByOrderId($orderId);
    }

    /**
     * Get payments by customer ID.
     *
     * @param int $customerId
     * @return Collection
     */
    public function getPaymentsByCustomerId(int $customerId): Collection
    {
        return $this->paymentRepository->findByCustomerId($customerId);
    }

    /**
     * Process a payment.
     *
     * @param array $paymentData
     * @return array
     * @throws PaymentException
     */
    public function processPayment(array $paymentData): array
    {
        try {
            // Validate required fields
            $requiredFields = [
                'order_id',
                'amount',
                'currency',
                'customer_id',
                'customer_email',
                'customer_phone',
                'gateway',
            ];

            foreach ($requiredFields as $field) {
                if (!isset($paymentData[$field]) || empty($paymentData[$field])) {
                    throw new PaymentException("The {$field} field is required.");
                }
            }

            // Get the payment gateway
            $gateway = $this->getGateway($paymentData['gateway']);

            if (!$gateway) {
                throw new PaymentException("Payment gateway {$paymentData['gateway']} not found or not enabled.");
            }

            // Start a database transaction
            DB::beginTransaction();

            try {
                // Create a payment record
                $payment = $this->paymentRepository->create([
                    'order_id' => $paymentData['order_id'],
                    'customer_id' => $paymentData['customer_id'],
                    'amount' => $paymentData['amount'],
                    'currency' => $paymentData['currency'],
                    'gateway' => $paymentData['gateway'],
                    'status' => 'pending',
                    'company_id' => $paymentData['company_id'] ?? null,
                    'unit_id' => $paymentData['unit_id'] ?? null,
                    'metadata' => json_encode($paymentData['metadata'] ?? []),
                ]);

                // Process the payment with the gateway
                $result = $gateway->processPayment(array_merge($paymentData, [
                    'transaction_id' => $payment->id,
                ]));

                // Update the payment record with the transaction ID
                $this->paymentRepository->update($payment->id, [
                    'transaction_id' => $result['transaction_id'],
                    'gateway_response' => json_encode($result),
                ]);

                // Commit the transaction
                DB::commit();

                // Dispatch event
                Event::dispatch(new PaymentCreated($payment));

                return array_merge($result, [
                    'payment_id' => $payment->id,
                ]);
            } catch (\Exception $e) {
                // Rollback the transaction
                DB::rollBack();
                throw $e;
            }
        } catch (PaymentException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to process payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payment_data' => $paymentData,
            ]);

            throw new PaymentException('Failed to process payment: ' . $e->getMessage());
        }
    }

    /**
     * Verify a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     * @throws PaymentException
     */
    public function verifyPayment(string $transactionId, array $additionalData = []): array
    {
        try {
            // Get the payment record
            $payment = $this->getPaymentByTransactionId($transactionId);

            if (!$payment) {
                throw new PaymentException("Payment with transaction ID {$transactionId} not found.");
            }

            // Get the payment gateway
            $gateway = $this->getGateway($payment->gateway);

            if (!$gateway) {
                throw new PaymentException("Payment gateway {$payment->gateway} not found or not enabled.");
            }

            // Verify the payment with the gateway
            $result = $gateway->verifyPayment($transactionId, $additionalData);

            // Update the payment record with the verification result
            $this->paymentRepository->update($payment->id, [
                'status' => $result['status'],
                'gateway_response' => json_encode($result),
            ]);

            // Dispatch event based on payment status
            if ($result['status'] === 'success') {
                Event::dispatch(new PaymentSucceeded($payment));
            } elseif ($result['status'] === 'failed') {
                Event::dispatch(new PaymentFailed($payment));
            }

            return array_merge($result, [
                'payment_id' => $payment->id,
            ]);
        } catch (PaymentException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to verify payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);

            throw new PaymentException('Failed to verify payment: ' . $e->getMessage());
        }
    }

    /**
     * Refund a payment.
     *
     * @param string $transactionId
     * @param float|null $amount
     * @param array $additionalData
     * @return array
     * @throws PaymentException
     */
    public function refundPayment(string $transactionId, ?float $amount = null, array $additionalData = []): array
    {
        try {
            // Get the payment record
            $payment = $this->getPaymentByTransactionId($transactionId);

            if (!$payment) {
                throw new PaymentException("Payment with transaction ID {$transactionId} not found.");
            }

            // Check if payment is already refunded
            if ($payment->status === 'refunded') {
                throw new PaymentException("Payment with transaction ID {$transactionId} is already refunded.");
            }

            // Check if payment is successful
            if ($payment->status !== 'success') {
                throw new PaymentException("Payment with transaction ID {$transactionId} cannot be refunded because it is not successful.");
            }

            // Get the payment gateway
            $gateway = $this->getGateway($payment->gateway);

            if (!$gateway) {
                throw new PaymentException("Payment gateway {$payment->gateway} not found or not enabled.");
            }

            // Refund the payment with the gateway
            $result = $gateway->refundPayment($transactionId, $amount, $additionalData);

            // Update the payment record with the refund result
            $this->paymentRepository->update($payment->id, [
                'status' => 'refunded',
                'refund_amount' => $amount ?? $payment->amount,
                'refund_date' => now(),
                'gateway_response' => json_encode($result),
            ]);

            // Dispatch event
            Event::dispatch(new PaymentRefunded($payment));

            return array_merge($result, [
                'payment_id' => $payment->id,
            ]);
        } catch (PaymentException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to refund payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);

            throw new PaymentException('Failed to refund payment: ' . $e->getMessage());
        }
    }

    /**
     * Cancel a payment.
     *
     * @param string $transactionId
     * @param array $additionalData
     * @return array
     * @throws PaymentException
     */
    public function cancelPayment(string $transactionId, array $additionalData = []): array
    {
        try {
            // Get the payment record
            $payment = $this->getPaymentByTransactionId($transactionId);

            if (!$payment) {
                throw new PaymentException("Payment with transaction ID {$transactionId} not found.");
            }

            // Check if payment is already cancelled
            if ($payment->status === 'cancelled') {
                throw new PaymentException("Payment with transaction ID {$transactionId} is already cancelled.");
            }

            // Check if payment can be cancelled
            if (!in_array($payment->status, ['pending', 'processing'])) {
                throw new PaymentException("Payment with transaction ID {$transactionId} cannot be cancelled because it is not pending or processing.");
            }

            // Get the payment gateway
            $gateway = $this->getGateway($payment->gateway);

            if (!$gateway) {
                throw new PaymentException("Payment gateway {$payment->gateway} not found or not enabled.");
            }

            // Cancel the payment with the gateway
            $result = $gateway->cancelPayment($transactionId, $additionalData);

            // Update the payment record with the cancellation result
            $this->paymentRepository->update($payment->id, [
                'status' => 'cancelled',
                'gateway_response' => json_encode($result),
            ]);

            // Dispatch event
            Event::dispatch(new PaymentCancelled($payment));

            return array_merge($result, [
                'payment_id' => $payment->id,
            ]);
        } catch (PaymentException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to cancel payment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);

            throw new PaymentException('Failed to cancel payment: ' . $e->getMessage());
        }
    }

    /**
     * Get payment status.
     *
     * @param string $transactionId
     * @return array
     * @throws PaymentException
     */
    public function getPaymentStatus(string $transactionId): array
    {
        try {
            // Get the payment record
            $payment = $this->getPaymentByTransactionId($transactionId);

            if (!$payment) {
                throw new PaymentException("Payment with transaction ID {$transactionId} not found.");
            }

            // Get the payment gateway
            $gateway = $this->getGateway($payment->gateway);

            if (!$gateway) {
                throw new PaymentException("Payment gateway {$payment->gateway} not found or not enabled.");
            }

            // Get the payment status from the gateway
            $result = $gateway->getPaymentStatus($transactionId);

            // Update the payment record with the status
            $this->paymentRepository->update($payment->id, [
                'status' => $result['status'],
                'gateway_response' => json_encode($result),
            ]);

            return array_merge($result, [
                'payment_id' => $payment->id,
            ]);
        } catch (PaymentException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to get payment status', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);

            throw new PaymentException('Failed to get payment status: ' . $e->getMessage());
        }
    }

    /**
     * Get payment details.
     *
     * @param string $transactionId
     * @return array
     * @throws PaymentException
     */
    public function getPaymentDetails(string $transactionId): array
    {
        try {
            // Get the payment record
            $payment = $this->getPaymentByTransactionId($transactionId);

            if (!$payment) {
                throw new PaymentException("Payment with transaction ID {$transactionId} not found.");
            }

            // Get the payment gateway
            $gateway = $this->getGateway($payment->gateway);

            if (!$gateway) {
                throw new PaymentException("Payment gateway {$payment->gateway} not found or not enabled.");
            }

            // Get the payment details from the gateway
            $result = $gateway->getPaymentDetails($transactionId);

            return array_merge($result, [
                'payment_id' => $payment->id,
                'order_id' => $payment->order_id,
                'customer_id' => $payment->customer_id,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'status' => $payment->status,
                'gateway' => $payment->gateway,
                'created_at' => $payment->created_at->toIso8601String(),
                'updated_at' => $payment->updated_at->toIso8601String(),
            ]);
        } catch (PaymentException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to get payment details', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'transaction_id' => $transactionId,
            ]);

            throw new PaymentException('Failed to get payment details: ' . $e->getMessage());
        }
    }

    /**
     * Generate payment form.
     *
     * @param array $paymentData
     * @return array
     * @throws PaymentException
     */
    public function generatePaymentForm(array $paymentData): array
    {
        try {
            // Validate required fields
            $requiredFields = [
                'order_id',
                'amount',
                'currency',
                'customer_id',
                'customer_email',
                'customer_phone',
                'gateway',
            ];

            foreach ($requiredFields as $field) {
                if (!isset($paymentData[$field]) || empty($paymentData[$field])) {
                    throw new PaymentException("The {$field} field is required.");
                }
            }

            // Get the payment gateway
            $gateway = $this->getGateway($paymentData['gateway']);

            if (!$gateway) {
                throw new PaymentException("Payment gateway {$paymentData['gateway']} not found or not enabled.");
            }

            // Generate the payment form with the gateway
            return $gateway->generatePaymentForm($paymentData);
        } catch (PaymentException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to generate payment form', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payment_data' => $paymentData,
            ]);

            throw new PaymentException('Failed to generate payment form: ' . $e->getMessage());
        }
    }

    /**
     * Handle payment webhook.
     *
     * @param string $gateway
     * @param array $data
     * @return array
     * @throws PaymentException
     */
    public function handleWebhook(string $gateway, array $data): array
    {
        try {
            // Get the payment gateway
            $gatewayInstance = $this->getGateway($gateway);

            if (!$gatewayInstance) {
                throw new PaymentException("Payment gateway {$gateway} not found or not enabled.");
            }

            // Handle the webhook with the gateway
            $result = $gatewayInstance->handleWebhook($data);

            // Get the transaction ID from the result
            $transactionId = $result['transaction_id'] ?? null;

            if (!$transactionId) {
                throw new PaymentException("Transaction ID not found in webhook data.");
            }

            // Get the payment record
            $payment = $this->getPaymentByTransactionId($transactionId);

            if (!$payment) {
                throw new PaymentException("Payment with transaction ID {$transactionId} not found.");
            }

            // Update the payment record with the webhook result
            $this->paymentRepository->update($payment->id, [
                'status' => $result['status'],
                'gateway_response' => json_encode($result),
            ]);

            // Dispatch event based on payment status
            if ($result['status'] === 'success') {
                Event::dispatch(new PaymentSucceeded($payment));
            } elseif ($result['status'] === 'failed') {
                Event::dispatch(new PaymentFailed($payment));
            } elseif ($result['status'] === 'refunded') {
                Event::dispatch(new PaymentRefunded($payment));
            } elseif ($result['status'] === 'cancelled') {
                Event::dispatch(new PaymentCancelled($payment));
            }

            return array_merge($result, [
                'payment_id' => $payment->id,
            ]);
        } catch (PaymentException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Failed to handle payment webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'gateway' => $gateway,
                'data' => $data,
            ]);

            throw new PaymentException('Failed to handle payment webhook: ' . $e->getMessage());
        }
    }

    /**
     * Get available payment gateways.
     *
     * @return array
     */
    public function getAvailableGateways(): array
    {
        $availableGateways = [];

        foreach ($this->gateways as $name => $gateway) {
            if ($gateway->isEnabled()) {
                $availableGateways[$name] = [
                    'name' => $gateway->getName(),
                    'supported_currencies' => $gateway->getSupportedCurrencies(),
                    'supported_methods' => $gateway->getSupportedMethods(),
                ];
            }
        }

        return $availableGateways;
    }

    /**
     * Get payment gateway.
     *
     * @param string $gateway
     * @return PaymentGatewayInterface|null
     */
    public function getGateway(string $gateway): ?PaymentGatewayInterface
    {
        if (!isset($this->gateways[$gateway])) {
            return null;
        }

        return $this->gateways[$gateway];
    }

    /**
     * Get payment statistics.
     *
     * @param array $filters
     * @return array
     */
    public function getPaymentStatistics(array $filters = []): array
    {
        try {
            $query = DB::table('payments');
            
            // Apply filters
            if (isset($filters['company_id'])) {
                $query->where('company_id', $filters['company_id']);
            }
            
            if (isset($filters['unit_id'])) {
                $query->where('unit_id', $filters['unit_id']);
            }
            
            if (isset($filters['start_date']) && isset($filters['end_date'])) {
                $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
            }
            
            // Get counts by status
            $statusCounts = $query->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->get()
                ->pluck('count', 'status')
                ->toArray();
            
            // Get total amount
            $totalAmount = $query->sum('amount');
            
            // Get amount by gateway
            $gatewayAmounts = DB::table('payments')
                ->select('gateway', DB::raw('sum(amount) as total_amount'))
                ->groupBy('gateway')
                ->get()
                ->pluck('total_amount', 'gateway')
                ->toArray();
            
            // Get amount by currency
            $currencyAmounts = DB::table('payments')
                ->select('currency', DB::raw('sum(amount) as total_amount'))
                ->groupBy('currency')
                ->get()
                ->pluck('total_amount', 'currency')
                ->toArray();
            
            return [
                'total_payments' => array_sum($statusCounts),
                'successful_payments' => $statusCounts['success'] ?? 0,
                'failed_payments' => $statusCounts['failed'] ?? 0,
                'pending_payments' => $statusCounts['pending'] ?? 0,
                'refunded_payments' => $statusCounts['refunded'] ?? 0,
                'cancelled_payments' => $statusCounts['cancelled'] ?? 0,
                'total_amount' => $totalAmount,
                'gateway_amounts' => $gatewayAmounts,
                'currency_amounts' => $currencyAmounts,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get payment statistics', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'filters' => $filters,
            ]);
            
            return [
                'total_payments' => 0,
                'successful_payments' => 0,
                'failed_payments' => 0,
                'pending_payments' => 0,
                'refunded_payments' => 0,
                'cancelled_payments' => 0,
                'total_amount' => 0,
                'gateway_amounts' => [],
                'currency_amounts' => [],
            ];
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentTransaction extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'payment_transaction';

    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_transaction_id';

    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'int';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The name of the "created at" column.
     *
     * @var string
     */
    const CREATED_AT = 'created_date';

    /**
     * The name of the "updated at" column.
     *
     * @var string
     */
    const UPDATED_AT = 'modified_date';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'unit_id',
        'customer_id',
        'customer_email',
        'customer_phone',
        'customer_name',
        'payment_amount',
        'transaction_charges',
        'wallet_amount',
        'pre_order_id',
        'gateway',
        'status',
        'gateway_transaction_id',
        'description',
        'transaction_by',
        'referer',
        'success_url',
        'failure_url',
        'context',
        'promo_code',
        'discount',
        'recurring'
    ];

    /**
     * Get the transaction ID attribute (for API compatibility)
     */
    public function getTransactionIdAttribute()
    {
        return 'TXN' . $this->pk_transaction_id;
    }

    /**
     * Get the amount attribute (for API compatibility)
     */
    public function getAmountAttribute()
    {
        return $this->payment_amount;
    }

    /**
     * Get the order ID attribute (for API compatibility)
     */
    public function getOrderIdAttribute()
    {
        return $this->pre_order_id;
    }

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'payment_amount' => 'float',
        'transaction_charges' => 'float',
        'wallet_amount' => 'float',
        'discount' => 'float',
        'recurring' => 'integer',
        'created_date' => 'datetime',
        'modified_date' => 'timestamp'
    ];

    /**
     * The attributes that should be encrypted.
     *
     * @var array<int, string>
     */
    protected $encryptable = [
        // Temporarily disabled due to column length constraints
        // 'customer_email',
        // 'customer_phone'
    ];

    /**
     * Set a given attribute on the model.
     *
     * @param string $key
     * @param mixed $value
     * @return mixed
     */
    public function setAttribute($key, $value)
    {
        if (in_array($key, $this->encryptable) && !empty($value)) {
            $value = encrypt($value);
        }

        return parent::setAttribute($key, $value);
    }

    /**
     * Get an attribute from the model.
     *
     * @param string $key
     * @return mixed
     */
    public function getAttribute($key)
    {
        $value = parent::getAttribute($key);

        if (in_array($key, $this->encryptable) && !empty($value)) {
            try {
                $value = decrypt($value);
            } catch (\Exception $e) {
                // Value was not encrypted
            }
        }

        return $value;
    }
}

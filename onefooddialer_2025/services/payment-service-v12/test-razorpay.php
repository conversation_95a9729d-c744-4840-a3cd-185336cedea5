<?php

/**
 * Razorpay Payment Gateway Test Script
 * 
 * This script tests the Razorpay integration using the provided test credentials:
 * Key ID: rzp_test_ZUpflviU0kpnf0
 * Secret: vaVSn1chhm19HW809rG0X8HS
 */

require_once 'vendor/autoload.php';

use App\Services\Payment\Gateway\RazorpayGateway;

echo "🔥 Razorpay Payment Gateway Test\n";
echo "================================\n\n";

// Test Configuration
$config = [
    'key_id' => 'rzp_test_ZUpflviU0kpnf0',
    'key_secret' => 'vaVSn1chhm19HW809rG0X8HS'
];

echo "📋 Configuration:\n";
echo "- Key ID: {$config['key_id']}\n";
echo "- Environment: Test/Sandbox\n\n";

try {
    echo "🚀 Step 1: Initialize Razorpay Gateway\n";
    
    $razorpayGateway = new RazorpayGateway($config);
    
    echo "✅ Razorpay Gateway initialized successfully\n";
    echo "- Gateway Name: " . $razorpayGateway->getName() . "\n";
    echo "- Is Enabled: " . ($razorpayGateway->isEnabled() ? 'Yes' : 'No') . "\n";
    echo "- Supported Currencies: " . implode(', ', $razorpayGateway->getSupportedCurrencies()) . "\n";
    echo "- Supported Methods: " . implode(', ', $razorpayGateway->getSupportedMethods()) . "\n\n";
    
    echo "🚀 Step 2: Create Test Payment Data\n";
    
    $paymentData = [
        'order_id' => 'TEST_ORDER_' . time() . '_' . uniqid(),
        'amount' => 299.99, // ₹299.99
        'customer_name' => 'John Doe',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '9876543210',
        'description' => 'Test Payment for Razorpay Integration',
        'callback_url' => 'http://127.0.0.1:8012/api/v2/payments/callback'
    ];
    
    echo "- Order ID: {$paymentData['order_id']}\n";
    echo "- Amount: ₹{$paymentData['amount']}\n";
    echo "- Customer: {$paymentData['customer_name']} ({$paymentData['customer_email']})\n";
    echo "- Phone: {$paymentData['customer_phone']}\n\n";
    
    echo "🚀 Step 3: Generate Payment Form Data\n";
    
    $formData = $razorpayGateway->generatePaymentForm($paymentData);
    
    echo "✅ Payment form data generated successfully\n\n";
    
    echo "📊 Razorpay Order Response:\n";
    echo json_encode($formData, JSON_PRETTY_PRINT) . "\n\n";
    
    echo "🚀 Step 4: Process Payment (Same as Form Generation)\n";
    
    $processedData = $razorpayGateway->processPayment($paymentData);
    
    echo "✅ Payment processing completed\n\n";
    
    echo "🎉 Razorpay Integration Test Completed Successfully!\n";
    echo "📝 Summary:\n";
    echo "- Gateway Initialization: ✅ Success\n";
    echo "- Configuration Validation: ✅ Success\n";
    echo "- Order Creation: ✅ Success\n";
    echo "- Payment Form Generation: ✅ Success\n";
    echo "- Payment Processing: ✅ Success\n\n";
    
    echo "🌐 Next Steps:\n";
    echo "1. Use the generated order_id: {$formData['order_id']}\n";
    echo "2. Integrate with Razorpay Checkout.js on frontend\n";
    echo "3. Handle payment success/failure callbacks\n";
    echo "4. Verify payment using the order_id\n\n";
    
    echo "💡 Frontend Integration Example:\n";
    echo "<script src=\"https://checkout.razorpay.com/v1/checkout.js\"></script>\n";
    echo "<script>\n";
    echo "var options = {\n";
    echo "    \"key\": \"{$formData['razorpay_key']}\",\n";
    echo "    \"amount\": \"{$formData['amount']}\",\n";
    echo "    \"currency\": \"{$formData['currency']}\",\n";
    echo "    \"name\": \"OneFoodDialer\",\n";
    echo "    \"description\": \"{$formData['description']}\",\n";
    echo "    \"order_id\": \"{$formData['order_id']}\",\n";
    echo "    \"handler\": function (response){\n";
    echo "        // Handle success\n";
    echo "        console.log(response.razorpay_payment_id);\n";
    echo "        console.log(response.razorpay_order_id);\n";
    echo "        console.log(response.razorpay_signature);\n";
    echo "    },\n";
    echo "    \"prefill\": {\n";
    echo "        \"name\": \"{$formData['name']}\",\n";
    echo "        \"email\": \"{$formData['email']}\",\n";
    echo "        \"contact\": \"{$formData['contact']}\"\n";
    echo "    }\n";
    echo "};\n";
    echo "var rzp1 = new Razorpay(options);\n";
    echo "rzp1.open();\n";
    echo "</script>\n\n";
    
    // Test payment verification (will fail without actual payment)
    echo "🚀 Step 5: Test Payment Status Check (Demo)\n";
    try {
        // This will fail since we don't have a real payment ID, but it tests the method
        $demoPaymentId = 'pay_demo123456789';
        echo "- Testing with demo payment ID: $demoPaymentId\n";
        
        // This will throw an exception, which is expected
        $paymentStatus = $razorpayGateway->getPaymentStatus($demoPaymentId);
        echo "✅ Payment status retrieved\n";
    } catch (Exception $e) {
        echo "⚠️ Payment status check failed (expected for demo): " . $e->getMessage() . "\n";
        echo "   This is normal since we're using a demo payment ID\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test failed with exception:\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
    
    echo "🔧 Troubleshooting:\n";
    echo "1. Check if Razorpay SDK is properly installed\n";
    echo "2. Verify test credentials are correct\n";
    echo "3. Ensure network connectivity\n";
    echo "4. Check if all required dependencies are installed\n";
}

echo "🏁 Test completed.\n";

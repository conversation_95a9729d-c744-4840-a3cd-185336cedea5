<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Razorpay Payment Integration Test</title>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .btn {
            background: #528FF0;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #4169E1;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .endpoint {
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 Razorpay Payment Integration Test</h1>
            <p>OneFoodDialer Payment Service v12</p>
        </div>

        <div class="test-section">
            <h3>1. Service Status Check</h3>
            <div class="endpoint">GET /api/v2/payments/debug-gateways</div>
            <button class="btn" onclick="testServiceStatus()">Check Service Status</button>
            <div id="status-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Complete Payment Flow Test</h3>
            <div class="endpoint">POST /api/v2/payments/test-razorpay-flow</div>
            <button class="btn" onclick="testCompleteFlow()">Test Complete Flow</button>
            <div id="flow-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Live Razorpay Payment Demo</h3>
            <p>This will open the actual Razorpay payment gateway:</p>
            <button class="btn btn-success" onclick="initiateLivePayment()">🚀 Start Live Payment</button>
            <div id="payment-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. API Endpoints Summary</h3>
            <div class="endpoint">POST /api/v2/payments/ - Initiate Payment</div>
            <div class="endpoint">POST /api/v2/payments/{id}/process - Process with Gateway</div>
            <div class="endpoint">POST /api/v2/payments/callback - Payment Callback</div>
            <div class="endpoint">GET /api/v2/payments/{id} - Get Payment Status</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8012/api/v2/payments';

        async function testServiceStatus() {
            const resultDiv = document.getElementById('status-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing service status...';
            resultDiv.className = 'result';

            try {
                const response = await fetch(`${API_BASE}/debug-gateways`);
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function testCompleteFlow() {
            const resultDiv = document.getElementById('flow-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Testing complete payment flow...';
            resultDiv.className = 'result';

            try {
                const response = await fetch(`${API_BASE}/test-razorpay-flow`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function initiateLivePayment() {
            const resultDiv = document.getElementById('payment-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Initiating live payment...';
            resultDiv.className = 'result';

            try {
                // Get payment data from API
                const response = await fetch(`${API_BASE}/test-razorpay-flow`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                const data = await response.json();
                
                if (data.success) {
                    const options = data.data.frontend_integration.options;
                    options.handler = function (response) {
                        resultDiv.className = 'result success';
                        resultDiv.textContent = `Payment Successful!\n\nPayment ID: ${response.razorpay_payment_id}\nOrder ID: ${response.razorpay_order_id}\nSignature: ${response.razorpay_signature}`;
                    };
                    options.modal = {
                        ondismiss: function() {
                            resultDiv.className = 'result error';
                            resultDiv.textContent = 'Payment cancelled by user';
                        }
                    };

                    const rzp = new Razorpay(options);
                    rzp.open();
                    
                    resultDiv.className = 'result';
                    resultDiv.textContent = 'Razorpay payment gateway opened...';
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>

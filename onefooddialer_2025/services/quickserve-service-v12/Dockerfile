# Use official PHP image with required extensions
FROM php:8.2-cli

# Set working directory
WORKDIR /var/www

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    zip \
    unzip \
    libzip-dev \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libpq-dev \
    libjpeg-dev \
    libfreetype6-dev \
    && docker-php-ext-install \
        sockets \
        pdo \
        pdo_mysql \
        zip \
        bcmath \
        gd \
        pcntl \
        soap \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Copy application code
COPY . .

# Install PHP dependencies
#RUN composer install --no-interaction --prefer-dist --optimize-autoloader
# Install dependencies

RUN composer install --no-scripts --optimize-autoloader --no-interaction


# Generate optimized autoload files
#RUN composer dump-autoload --optimize

# Set permissions
RUN chmod +x artisan

# Expose Laravel Artisan server port
EXPOSE 9002

# Set application key (in production, you'd use ENV or a .env file)
#RUN php artisan key:generate

# Run Laravel's built-in server (artisan serve)
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=9002"]



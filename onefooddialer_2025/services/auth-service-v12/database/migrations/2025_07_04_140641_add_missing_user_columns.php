<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add missing columns that the application expects
            if (!Schema::hasColumn('users', 'username')) {
                $table->string('username')->nullable()->after('pk_user_code');
            }
            if (!Schema::hasColumn('users', 'id')) {
                // Add auto-incrementing id column that <PERSON><PERSON> expects
                $table->id()->first();
            }
            if (!Schema::hasColumn('users', 'auth_type')) {
                $table->string('auth_type')->default('local')->after('auth_token');
            }
            if (!Schema::hasColumn('users', 'is_mfa_verified')) {
                $table->boolean('is_mfa_verified')->default(false)->after('auth_type');
            }
            if (!Schema::hasColumn('users', 'mfa_method')) {
                $table->string('mfa_method')->nullable()->after('is_mfa_verified');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $columnsToRemove = ['username', 'id', 'auth_type', 'is_mfa_verified', 'mfa_method'];
            foreach ($columnsToRemove as $column) {
                if (Schema::hasColumn('users', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};

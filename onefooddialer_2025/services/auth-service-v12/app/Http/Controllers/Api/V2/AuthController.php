<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Services\Auth\KeycloakUserRegistrationService;
use App\Http\Requests\ForgotPasswordRequest;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RefreshTokenRequest;
use App\Http\Requests\RegisterRequest;
use App\Http\Requests\ResetPasswordRequest;
use App\Http\Requests\UpdateProfileRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Services\Logging\StructuredLogger;
use App\Traits\ResponseHelper;
use Illuminate\Auth\Events\PasswordReset as PasswordResetEvent;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    use ResponseHelper;

    // Constants for commonly used messages
    private const MSG_INVALID_CREDENTIALS = 'Invalid credentials';
    private const MSG_AUTH_SUCCESS = 'Authentication successful';
    
    // Constants for Keycloak endpoints
    private const KEYCLOAK_TOKEN_ENDPOINT = '/protocol/openid-connect/token';
    private const KEYCLOAK_USERINFO_ENDPOINT = '/protocol/openid-connect/userinfo';
    private const KEYCLOAK_REALM_PATH = '/realms/';
    private const KEYCLOAK_SCOPE = 'openid profile email';

    /**
     * Constructor
     */
    public function __construct(
        protected \App\Services\Auth\AuthenticationServiceInterface $authService,
        protected StructuredLogger $logger,
        protected KeycloakUserRegistrationService $keycloakRegistrationService
    ) {}

    /**
     * Login - Direct authentication with Keycloak
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $username = $request->input('username');
        $password = $request->input('password');

        // First try to find user by phone number, then by email in users table (for Admin)
        $user = User::where('phone', $username)
                    ->orWhere('email_id', $username)
                    ->first();

        if ($user && $user->role_id === 1) { // Admin user found
            return $this->loginWithKeycloak($user, $username, $password);
        }

        // If no admin user found, check customers table for Customer role
        $customerServiceUrl = env('CUSTOMER_SERVICE_URL', 'http://localhost:8002/api/v2') . '/customers/login-keycloak';
        $customerResponse = Http::timeout(10)->post($customerServiceUrl, [
            'username' => $username,
            'password' => $password,
        ]);

        if ($customerResponse->successful()) {
            $customerData = $customerResponse->json();

            // Authenticate with Keycloak using customer credentials
            $keycloakConfig = config('keycloak');

            try {
                $keycloakResponse = Http::asForm()->post(
                    $keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/token',
                    [
                        'grant_type' => 'password',
                        'client_id' => $keycloakConfig['client_id'],
                        'client_secret' => $keycloakConfig['client_secret'],
                        'username' => $username,
                        'password' => $password,
                        'scope' => 'openid profile email'
                    ]
                );

                if ($keycloakResponse->successful()) {
                    $keycloakTokens = $keycloakResponse->json();

                    // Log successful authentication
                    $this->logger->logAuthAttempt(
                        $username,
                        'keycloak',
                        true,
                        'Customer authentication successful via Keycloak'
                    );

                    return $this->successResponse(self::MSG_AUTH_SUCCESS, [
                        'user' => [
                            'id' => $customerData['data']['pk_customer_code'],
                            'auth_id' => $customerData['data']['auth_id'],
                            'customer_name' => $customerData['data']['customer_name'],
                            'email' => $customerData['data']['email_address'],
                            'phone' => $customerData['data']['phone'],
                            'role_id' => 2, // Customer
                            'role_name' => 'Customer',
                        ],
                        'token' => $keycloakTokens['access_token'],
                        'refresh_token' => $keycloakTokens['refresh_token'],
                        'expires_in' => $keycloakTokens['expires_in'],
                        'auth_type' => 'keycloak',
                    ]);
                }
            } catch (\Exception $e) {
                $this->logger->log('error', 'Keycloak authentication failed for customer', [
                    'event_type' => 'keycloak_auth_failed',
                    'username' => $username,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $this->unauthorizedResponse(self::MSG_INVALID_CREDENTIALS);
    }

    /**
     * Handle Keycloak authentication for Admin users
     */
    private function loginWithKeycloak(User $user, string $username, string $password): JsonResponse
    {
        $keycloakConfig = config('keycloak');

        try {
            // Authenticate with Keycloak using password grant
            $response = Http::asForm()->post(
                $keycloakConfig['auth_server_url'] . self::KEYCLOAK_REALM_PATH . $keycloakConfig['realm'] . self::KEYCLOAK_TOKEN_ENDPOINT,
                [
                    'grant_type' => 'password',
                    'client_id' => $keycloakConfig['client_id'],
                    'client_secret' => $keycloakConfig['client_secret'],
                    'username' => $user->username, // Use stored username for Keycloak
                    'password' => $password,
                    'scope' => self::KEYCLOAK_SCOPE
                ]
            );

            // Log the authentication attempt with Keycloak
            $this->logger->log('info', 'Keycloak authentication attempt', [
                'event_type' => 'keycloak_auth_attempt',
                'username' => $username,
                'status_code' => $response->status(),
                'success' => $response->successful()
            ]);

            // If Keycloak authentication fails
            if ($response->failed()) {
                $errorMessage = $response->json()['error_description'] ?? self::MSG_INVALID_CREDENTIALS;
                
                $this->logger->logAuthAttempt(
                    $username,
                    'keycloak',
                    false,
                    $errorMessage
                );
                
                return $this->unauthorizedResponse($errorMessage);
            }

            // Get tokens from Keycloak
            $tokens = $response->json();

            // Create Laravel Sanctum token
            $laravelToken = $user->createToken('auth_token');

            // Log successful authentication
            $this->logger->logAuthAttempt(
                $username,
                'keycloak',
                true,
                'Authentication successful via Keycloak'
            );

            // Return both Keycloak and Laravel tokens
            return $this->successResponse(self::MSG_AUTH_SUCCESS, [
                'user' => new UserResource($user),
                'token' => $laravelToken->plainTextToken,
                'keycloak_tokens' => [
                    'access_token' => $tokens['access_token'],
                    'refresh_token' => $tokens['refresh_token'],
                    'expires_in' => $tokens['expires_in'],
                ],
                'auth_type' => 'keycloak',
            ]);

        } catch (\Exception $e) {
            $this->logger->log('error', 'Keycloak authentication error', [
                'event_type' => 'keycloak_auth_error',
                'username' => $username,
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse('Authentication failed: ' . $e->getMessage(), null, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Mobile Login - Authenticate using Keycloak access token
     */
    public function mobileLogin(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'access_token' => 'required|string',
        ]);

        $keycloakConfig = config('keycloak');

        try {
            // Verify token with Keycloak and get user info
            $userInfoResponse = Http::withToken($validated['access_token'])
                ->get($keycloakConfig['auth_server_url'].'/realms/'.$keycloakConfig['realm'].self::KEYCLOAK_USERINFO_ENDPOINT);

            if ($userInfoResponse->failed()) {
                $this->logger->log('warning', 'Invalid Keycloak token provided', [
                    'event_type' => 'keycloak_token_validation_failed',
                    'status_code' => $userInfoResponse->status()
                ]);
                return $this->unauthorizedResponse('Invalid access token');
            }

            $userInfo = $userInfoResponse->json();

            // Find or create user in Laravel
            $user = User::where('email', $userInfo['email'])->first();

            if (!$user) {
                // Create new user from Keycloak info
                $user = new User();
                $user->email = $userInfo['email'];
                $user->username = $userInfo['preferred_username'] ?? $userInfo['email'];
                $user->first_name = $userInfo['given_name'] ?? '';
                $user->last_name = $userInfo['family_name'] ?? '';
                $user->password = Hash::make(Str::random(16)); // Random password for Keycloak users
                $user->role_id = 2; // Default customer role
                $user->status = 1; // Active
                $user->company_id = 8163; // Dynamic company_id
                $user->unit_id = 1;
                $user->auth_type = 'keycloak';
                $user->save();

                $this->logger->logUserRegistration($user->email, true, 'User auto-created from Keycloak authentication');
            }

            // Create Laravel Sanctum token for API access
            $token = $user->createToken('mobile_auth_token');

            // Log successful authentication
            $this->logger->logAuthAttempt(
                $user->username,
                'keycloak',
                true,
                'Authenticated via Keycloak token'
            );

            return $this->successResponse(self::MSG_AUTH_SUCCESS, [
                'user' => new UserResource($user),
                'token' => $token->plainTextToken,
                'auth_type' => 'keycloak',
            ]);

        } catch (\Exception $e) {
            $this->logger->log('error', 'Keycloak authentication error', [
                'event_type' => 'keycloak_auth_error',
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse('Authentication failed: ' . $e->getMessage(), null, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Register a new user
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        // Get validated data from RegisterRequest
        $validated = $request->validated();
        
        // If username is not provided, use phone as username
        $validated['username'] = $validated['username'] ?? $validated['phone'];

        // Check if user already exists by email or phone
        $existingUser = User::where('email_id', $validated['email'])
            ->orWhere('phone', $validated['phone'])
            ->first();

        if ($existingUser) {
            return $this->errorResponse('User already exists with this email or phone', null, Response::HTTP_CONFLICT);
        }

        // Only check customer service if role is not Admin
        if ($validated['role_name'] !== 'Admin') {
            // Check with customer service if the customer exists
            $customerServiceUrl = env('CUSTOMER_SERVICE_URL', 'http://localhost:8002/api/v2/customers/check');
            $checkResponse = Http::timeout(5)->post($customerServiceUrl, [
                'email' => $validated['email'],
                'phone' => $validated['phone']
            ]);
            
            if ($checkResponse->successful() && $checkResponse->json()['exists']) {
                return $this->errorResponse('Customer already exists with this email or phone', null, Response::HTTP_CONFLICT);
            }
        }

        // Start database transaction for registration
        DB::beginTransaction();
        
        try {
            // Register user in Keycloak for both Admin and Customer roles
            $keycloakResult = ['success' => false, 'message' => 'Keycloak registration not attempted'];

            $keycloakResult = $this->keycloakRegistrationService->registerUser([
                'username' => $validated['username'],
                'email' => $validated['email'],
                'first_name' => $validated['first_name'],
                'last_name' => $validated['last_name'],
                'password' => $validated['password'],
                'attributes' => [
                    'mobile' => [$validated['phone']],
                    'oldGateUserId' => [$validated['old_gate_user_id'] ?? '0'],
                    'oldSsoUserId' => [$validated['old_sso_user_id'] ?? '0'],
                    'mobileVerified' => ['no'],
                    'groupAccess' => [$validated['role_name']] // Admin or Customer
                ]
            ]);

            $keycloakRegistered = $keycloakResult['success'];
            $authType = 'keycloak'; // Both Admin and Customer use Keycloak now

            // If Keycloak registration fails, log it and return error
            if (!$keycloakRegistered) {
                $this->logger->log('error', 'Keycloak registration failed', [
                    'event_type' => 'keycloak_registration_failed',
                    'message' => $keycloakResult['message'],
                    'email' => $validated['email'],
                    'role' => $validated['role_name']
                ]);
                DB::rollBack();
                return $this->errorResponse('Registration failed: ' . $keycloakResult['message'], null, Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            // Get Keycloak user ID from the registration result
            $keycloakUserId = $keycloakResult['user_id'] ?? null;

            if ($validated['role_name'] === 'Admin') {
                // Create new user in Laravel for Admin - ONLY in users table
                $user = new User();
                $user->username = $validated['username'];
                $user->email_id = $validated['email']; // Use correct column name
                $user->first_name = $validated['first_name'];
                $user->last_name = $validated['last_name'];
                $user->password = Hash::make($validated['password']);
                $user->phone = $validated['phone'];
                $user->role_id = 1; // Admin
                $user->status = 1; // Active
                $user->company_id = $validated['company_id'] ?? 8163;
                $user->unit_id = $validated['unit_id'];
                $user->auth_type = $authType;
                // Store Keycloak user ID in auth_token field as reference
                $user->auth_token = 'keycloak_id:' . $keycloakUserId;
                $user->save();
            }

            // If it's a customer, create directly in customer-service with Keycloak auth_id
            if ($validated['role_name'] === 'Customer') {
                $customerPayload = [
                    'auth_id' => $keycloakUserId, // Use Keycloak user ID as auth_id
                    'customer_name' => $validated['first_name'] . ' ' . $validated['last_name'],
                    'phone' => $validated['phone'],
                    'email_address' => $validated['email'],
                    'company_id' => $validated['company_id'] ?? 8163,
                    'unit_id' => $validated['unit_id'],
                    'registered_from' => 'keycloak_registration',
                    'source' => 'auth_service',
                ];

                // Add old_sso_user_id to payload if provided (to be stored as thirdparty field in customers table)
                if (!empty($validated['old_sso_user_id'])) {
                    $customerPayload['thirdparty'] = $validated['old_sso_user_id'];
                }

                $customerServiceUrl = env('CUSTOMER_SERVICE_URL', 'http://localhost:8002/api/v2') . '/customers/register-keycloak';
                $customerResponse = Http::timeout(10)->post($customerServiceUrl, $customerPayload);

                if (!$customerResponse->successful()) {
                    DB::rollBack();
                    $this->logger->log('error', 'Customer service registration failed', [
                        'event_type' => 'customer_service_registration_failed',
                        'auth_id' => $keycloakUserId,
                        'response' => $customerResponse->body(),
                    ]);
                    return $this->errorResponse('Registration failed: Unable to create customer profile', null, Response::HTTP_INTERNAL_SERVER_ERROR);
                }

                // Get customer data from response
                $customerData = $customerResponse->json();

                // For customers, we don't create User records in auth service
                // Customer data stays ONLY in customer service as per requirement
                // We'll use Keycloak tokens for authentication, not Laravel Sanctum tokens
            }

            // Create authentication token only for Admin users (who have User records)
            $token = null;
            if ($validated['role_name'] === 'Admin' && isset($user)) {
                $token = $user->createToken('auth_token');
            }

            // Get Keycloak tokens for both Admin and Customer registration
            $keycloakTokens = null;
            if ($keycloakRegistered) { // Both Admin and Customer need Keycloak tokens
                try {
                    $keycloakConfig = config('keycloak');
                    $tokenResponse = Http::asForm()->post(
                        $keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/token',
                        [
                            'grant_type' => 'password',
                            'client_id' => $keycloakConfig['client_id'],
                            'client_secret' => $keycloakConfig['client_secret'],
                            'username' => $validated['username'],
                            'password' => $validated['password'],
                            'scope' => 'openid profile email'
                        ]
                    );
                    
                    if ($tokenResponse->successful()) {
                        $keycloakTokens = $tokenResponse->json();
                        $this->logger->log('info', 'Keycloak authentication successful after registration', [
                            'event_type' => 'keycloak_auth_success_after_registration',
                            'username' => $validated['username'],
                            'email' => $validated['email']
                        ]);
                    }
                } catch (\Exception $e) {
                    $this->logger->log('error', 'Exception getting Keycloak tokens after registration', [
                        'event_type' => 'keycloak_auth_exception_after_registration',
                        'username' => $validated['username'],
                        'email' => $validated['email'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            DB::commit();

            // Log successful registration
            $this->logger->logUserRegistration(
                $validated['email'], // Use email from validated data instead of $user object
                true,
                sprintf(
                    'User registered successfully as %s in %s',
                    $validated['role_name'],
                    $validated['role_name'] === 'Admin' ? 'Keycloak and Laravel' : 'Customer Service with Keycloak sync'
                )
            );

            // Prepare response data based on role
            if ($validated['role_name'] === 'Admin') {
                // Admin users have User records in auth service
                $responseData = [
                    'user' => new UserResource($user),
                    'auth_type' => $authType,
                    'token' => $token->plainTextToken,
                    'token_type' => 'Bearer',
                ];

                // Add Keycloak tokens as additional info if available
                if ($keycloakTokens) {
                    $responseData['keycloak_tokens'] = [
                        'access_token' => $keycloakTokens['access_token'],
                        'refresh_token' => $keycloakTokens['refresh_token'],
                        'expires_in' => $keycloakTokens['expires_in'],
                    ];
                }
            } else {
                // Customer users - data is in customer service, use Keycloak tokens
                $responseData = [
                    'user' => [
                        'id' => $customerData['data']['pk_customer_code'] ?? null,
                        'auth_id' => $customerData['data']['auth_id'] ?? null,
                        'customer_name' => $customerData['data']['customer_name'] ?? $validated['first_name'] . ' ' . $validated['last_name'],
                        'email' => $validated['email'],
                        'phone' => $validated['phone'],
                        'role_id' => 2, // Customer
                        'role_name' => 'Customer',
                        'company_id' => $validated['company_id'] ?? 8163,
                        'unit_id' => $validated['unit_id'] ?? 8163,
                    ],
                    'auth_type' => $authType,
                    'token' => $keycloakTokens['access_token'] ?? null,
                    'refresh_token' => $keycloakTokens['refresh_token'] ?? null,
                    'expires_in' => $keycloakTokens['expires_in'] ?? null,
                    'token_type' => 'Bearer',
                ];
            }

            return $this->successResponse(
                sprintf('User registered successfully as %s', $validated['role_name']),
                $responseData,
                Response::HTTP_CREATED
            );

        } catch (\Exception $e) {
            DB::rollBack();
            
            // Try to rollback Keycloak user creation if it was successful
            if (isset($keycloakResult) && $keycloakResult['success']) {
                $this->keycloakRegistrationService->deleteUser($validated['username']);
            }

            return $this->errorResponse('Registration failed: ' . $e->getMessage(), null, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Logout - Handles both Laravel Sanctum tokens (Admin) and Keycloak tokens (Customer)
     */
    public function logout(Request $request): JsonResponse
    {
        $token = $request->bearerToken();
        $user = null;
        $isKeycloakToken = false;
        $userEmail = null;
        $userId = null;

        if (!$token) {
            return $this->errorResponse('No authentication token provided', null, 401);
        }

        // Try to authenticate with Laravel Sanctum first (for Admin users)
        try {
            // Check if this is a Sanctum token by trying to find it in personal_access_tokens
            $sanctumToken = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if ($sanctumToken) {
                // This is a Laravel Sanctum token (Admin user)
                $user = $sanctumToken->tokenable;
                $userEmail = $user->email_id ?? $user->email;
                $userId = $user->id;

                // Delete Laravel Sanctum token
                $sanctumToken->delete();

                $this->logger->log('info', 'Laravel Sanctum token deleted during logout', [
                    'event_type' => 'sanctum_logout',
                    'user_id' => $userId,
                    'email' => $userEmail
                ]);
            } else {
                // Not a Sanctum token, try Keycloak
                $isKeycloakToken = true;
            }
        } catch (\Exception $e) {
            // If Sanctum authentication fails, this might be a Keycloak token
            $isKeycloakToken = true;
        }

        // If not a Sanctum token, try to validate as Keycloak token (for Customer users)
        if ($isKeycloakToken || !$user) {
            try {
                // Validate Keycloak token to get user information
                $keycloakConfig = config('keycloak');
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $token,
                ])->get($keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/userinfo');

                if ($response->successful()) {
                    $keycloakUser = $response->json();
                    $userEmail = $keycloakUser['email'] ?? 'unknown';
                    $userId = $keycloakUser['preferred_username'] ?? 'unknown';
                    $isKeycloakToken = true;

                    $this->logger->log('info', 'Keycloak token validated for logout', [
                        'event_type' => 'keycloak_token_validated',
                        'email' => $userEmail,
                        'username' => $userId
                    ]);
                } else {
                    return $this->errorResponse('Invalid authentication token', null, 401);
                }
            } catch (\Exception $e) {
                return $this->errorResponse('Invalid authentication token', null, 401);
            }
        }

        // Handle Keycloak session termination for both Admin and Customer users
        $keycloakRefreshToken = $request->input('keycloak_refresh_token') ?? $request->input('refresh_token');
        $keycloakConfig = config('keycloak');
        $keycloakLogoutSuccess = false;

        // Method 1: Try to logout using refresh token (proper session termination)
        if ($keycloakRefreshToken) {
            try {
                // First, try the standard logout endpoint
                $logoutResponse = Http::asForm()->post(
                    $keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/logout',
                    [
                        'client_id' => $keycloakConfig['client_id'],
                        'client_secret' => $keycloakConfig['client_secret'],
                        'refresh_token' => $keycloakRefreshToken,
                    ]
                );

                if ($logoutResponse->successful()) {
                    $keycloakLogoutSuccess = true;
                    $this->logger->log('info', 'Keycloak session terminated successfully via refresh token', [
                        'event_type' => 'keycloak_logout_success',
                        'email' => $userEmail,
                        'user_id' => $userId,
                        'method' => 'refresh_token'
                    ]);
                } else {
                    $this->logger->log('warning', 'Keycloak logout via refresh token failed', [
                        'event_type' => 'keycloak_logout_warning',
                        'email' => $userEmail,
                        'user_id' => $userId,
                        'status' => $logoutResponse->status(),
                        'response' => $logoutResponse->body(),
                        'method' => 'refresh_token'
                    ]);

                    // Try alternative logout with id_token_hint if available
                    if ($isKeycloakToken) {
                        try {
                            $alternativeLogoutResponse = Http::asForm()->post(
                                $keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/logout',
                                [
                                    'client_id' => $keycloakConfig['client_id'],
                                    'client_secret' => $keycloakConfig['client_secret'],
                                    'id_token_hint' => $token,
                                ]
                            );

                            if ($alternativeLogoutResponse->successful()) {
                                $keycloakLogoutSuccess = true;
                                $this->logger->log('info', 'Keycloak session terminated via id_token_hint', [
                                    'event_type' => 'keycloak_logout_success',
                                    'email' => $userEmail,
                                    'user_id' => $userId,
                                    'method' => 'id_token_hint'
                                ]);
                            }
                        } catch (\Exception $e) {
                            $this->logger->log('error', 'Error with id_token_hint logout', [
                                'event_type' => 'keycloak_logout_error',
                                'email' => $userEmail,
                                'user_id' => $userId,
                                'error' => $e->getMessage(),
                                'method' => 'id_token_hint'
                            ]);
                        }
                    }
                }
            } catch (\Exception $e) {
                $this->logger->log('error', 'Error terminating Keycloak session via refresh token', [
                    'event_type' => 'keycloak_logout_error',
                    'email' => $userEmail,
                    'user_id' => $userId,
                    'error' => $e->getMessage(),
                    'method' => 'refresh_token'
                ]);
            }
        }

        // Method 2: Try to revoke the access token (fallback)
        if (!$keycloakLogoutSuccess) {
            try {
                $revokeResponse = Http::asForm()->post(
                    $keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/revoke',
                    [
                        'client_id' => $keycloakConfig['client_id'],
                        'client_secret' => $keycloakConfig['client_secret'],
                        'token' => $token,
                        'token_type_hint' => 'access_token'
                    ]
                );

                if ($revokeResponse->successful()) {
                    $keycloakLogoutSuccess = true;
                    $this->logger->log('info', 'Keycloak access token revoked successfully', [
                        'event_type' => 'keycloak_token_revoke_success',
                        'email' => $userEmail,
                        'user_id' => $userId,
                        'method' => 'access_token_revoke'
                    ]);
                } else {
                    $this->logger->log('warning', 'Keycloak access token revocation failed', [
                        'event_type' => 'keycloak_token_revoke_warning',
                        'email' => $userEmail,
                        'user_id' => $userId,
                        'status' => $revokeResponse->status(),
                        'response' => $revokeResponse->body(),
                        'method' => 'access_token_revoke'
                    ]);
                }
            } catch (\Exception $e) {
                $this->logger->log('error', 'Error revoking Keycloak access token', [
                    'event_type' => 'keycloak_revoke_error',
                    'email' => $userEmail,
                    'user_id' => $userId,
                    'error' => $e->getMessage(),
                    'method' => 'access_token_revoke'
                ]);
            }
        }

        // Method 3: Try admin logout (force session termination) for Keycloak tokens
        // This ensures complete session termination even if token revocation succeeded
        if ($isKeycloakToken) {
            try {
                // Get admin token first
                $adminTokenResponse = Http::asForm()->post(
                    $keycloakConfig['auth_server_url'] . '/realms/master/protocol/openid-connect/token',
                    [
                        'client_id' => $keycloakConfig['admin_client_id'],
                        'username' => $keycloakConfig['admin_username'],
                        'password' => $keycloakConfig['admin_password'],
                        'grant_type' => 'password'
                    ]
                );

                if ($adminTokenResponse->successful()) {
                    $adminToken = $adminTokenResponse->json()['access_token'];

                    // Get user info to get Keycloak user ID
                    $userInfoResponse = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $token,
                    ])->get($keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/userinfo');

                    if ($userInfoResponse->successful()) {
                        $keycloakUser = $userInfoResponse->json();
                        $keycloakUserId = $keycloakUser['sub'];

                        // Method 3a: Direct user logout via admin API (force logout all sessions)
                        $userLogoutResponse = Http::withHeaders([
                            'Authorization' => 'Bearer ' . $adminToken,
                        ])->post($keycloakConfig['auth_server_url'] . '/admin/realms/' . $keycloakConfig['realm'] . '/users/' . $keycloakUserId . '/logout');

                        $this->logger->log('info', 'Admin user logout attempt', [
                            'event_type' => 'keycloak_admin_user_logout_attempt',
                            'email' => $userEmail,
                            'user_id' => $userId,
                            'status' => $userLogoutResponse->status(),
                            'response' => $userLogoutResponse->body(),
                            'method' => 'admin_user_logout'
                        ]);

                        if ($userLogoutResponse->successful()) {
                            $keycloakLogoutSuccess = true;
                            $this->logger->log('info', 'Keycloak user logged out via admin API', [
                                'event_type' => 'keycloak_admin_logout_success',
                                'email' => $userEmail,
                                'user_id' => $userId,
                                'method' => 'admin_user_logout'
                            ]);
                        } else {
                            // Method 3b: Try to get session ID from token and terminate specific session
                            try {
                                $tokenParts = explode('.', $token);
                                if (count($tokenParts) === 3) {
                                    $payload = json_decode(base64_decode($tokenParts[1]), true);
                                    $sessionId = $payload['sid'] ?? null;

                                    if ($sessionId) {
                                        // Try to terminate the specific session
                                        $sessionTerminateResponse = Http::withHeaders([
                                            'Authorization' => 'Bearer ' . $adminToken,
                                        ])->delete($keycloakConfig['auth_server_url'] . '/admin/realms/' . $keycloakConfig['realm'] . '/sessions/' . $sessionId);

                                        $this->logger->log('info', 'Specific session termination attempt', [
                                            'event_type' => 'keycloak_session_terminate_attempt',
                                            'email' => $userEmail,
                                            'user_id' => $userId,
                                            'session_id' => $sessionId,
                                            'status' => $sessionTerminateResponse->status(),
                                            'response' => $sessionTerminateResponse->body(),
                                            'method' => 'admin_session_delete_specific'
                                        ]);

                                        if ($sessionTerminateResponse->successful()) {
                                            $keycloakLogoutSuccess = true;
                                            $this->logger->log('info', 'Keycloak specific session terminated', [
                                                'event_type' => 'keycloak_admin_logout_success',
                                                'email' => $userEmail,
                                                'user_id' => $userId,
                                                'session_id' => $sessionId,
                                                'method' => 'admin_session_delete_specific'
                                            ]);
                                        }
                                    }
                                }
                            } catch (\Exception $e) {
                                $this->logger->log('error', 'Error parsing token for session ID', [
                                    'event_type' => 'token_parse_error',
                                    'email' => $userEmail,
                                    'user_id' => $userId,
                                    'error' => $e->getMessage()
                                ]);
                            }

                            // Method 3c: Get all user sessions and terminate them individually (fallback)
                            if (!$keycloakLogoutSuccess) {
                                $sessionsResponse = Http::withHeaders([
                                    'Authorization' => 'Bearer ' . $adminToken,
                                ])->get($keycloakConfig['auth_server_url'] . '/admin/realms/' . $keycloakConfig['realm'] . '/users/' . $keycloakUserId . '/sessions');

                                if ($sessionsResponse->successful()) {
                                    $sessions = $sessionsResponse->json();

                                    // Terminate each session
                                    $terminatedSessions = 0;
                                    foreach ($sessions as $session) {
                                        $sessionTerminateResponse = Http::withHeaders([
                                            'Authorization' => 'Bearer ' . $adminToken,
                                        ])->delete($keycloakConfig['auth_server_url'] . '/admin/realms/' . $keycloakConfig['realm'] . '/sessions/' . $session['id']);

                                        if ($sessionTerminateResponse->successful()) {
                                            $terminatedSessions++;
                                        }
                                    }

                                    if ($terminatedSessions > 0) {
                                        $keycloakLogoutSuccess = true;
                                        $this->logger->log('info', 'Keycloak sessions terminated via admin API', [
                                            'event_type' => 'keycloak_admin_logout_success',
                                            'email' => $userEmail,
                                            'user_id' => $userId,
                                            'sessions_terminated' => $terminatedSessions,
                                            'total_sessions' => count($sessions),
                                            'method' => 'admin_session_delete_all'
                                        ]);
                                    }
                                }
                            }
                        }
                    } else {
                        $this->logger->log('warning', 'Failed to get user info for admin logout', [
                            'event_type' => 'keycloak_userinfo_error',
                            'email' => $userEmail,
                            'user_id' => $userId,
                            'status' => $userInfoResponse->status(),
                            'response' => $userInfoResponse->body()
                        ]);
                    }
                } else {
                    $this->logger->log('warning', 'Failed to get admin token for session termination', [
                        'event_type' => 'keycloak_admin_token_error',
                        'email' => $userEmail,
                        'user_id' => $userId,
                        'status' => $adminTokenResponse->status(),
                        'response' => $adminTokenResponse->body()
                    ]);
                }
            } catch (\Exception $e) {
                $this->logger->log('error', 'Error terminating Keycloak sessions via admin API', [
                    'event_type' => 'keycloak_admin_logout_error',
                    'email' => $userEmail,
                    'user_id' => $userId,
                    'error' => $e->getMessage(),
                    'method' => 'admin_api'
                ]);
            }
        }

        // Log the successful logout
        $this->logger->log('info', 'User logged out successfully', [
            'event_type' => 'user_logout_success',
            'user_id' => $userId,
            'email' => $userEmail,
            'token_type' => $isKeycloakToken ? 'keycloak' : 'sanctum'
        ]);

        return $this->successResponse('Logged out successfully from both Laravel and Keycloak sessions');
    }

    /**
     * Refresh token
     */
    public function refreshToken(RefreshTokenRequest $request): JsonResponse
    {
        $refreshToken = $request->input('refresh_token');
        $keycloakConfig = config('keycloak');

        try {
            // Step 1: Refresh token with Keycloak
            $response = Http::asForm()->post(
                $keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/token',
                [
                    'grant_type' => 'refresh_token',
                    'client_id' => $keycloakConfig['client_id'],
                    'client_secret' => $keycloakConfig['client_secret'],
                    'refresh_token' => $refreshToken,
                ]
            );

            // If token refresh fails
            if ($response->failed()) {
                $errorMessage = $response->json()['error_description'] ?? 'Failed to refresh token';
                
                $this->logger->log('warning', 'Keycloak token refresh failed', [
                    'event_type' => 'keycloak_token_refresh_failed',
                    'error' => $errorMessage
                ]);
                
                return $this->unauthorizedResponse($errorMessage);
            }

            // Step 2: Get new tokens from Keycloak
            $tokens = $response->json();
            $accessToken = $tokens['access_token'];

            // Step 3: Get user info from Keycloak
            $userInfoResponse = Http::withToken($accessToken)
                ->get($keycloakConfig['auth_server_url'] . '/realms/' . $keycloakConfig['realm'] . '/protocol/openid-connect/userinfo');

            if ($userInfoResponse->failed()) {
                return $this->errorResponse('Failed to retrieve user info from Keycloak', null, Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            $userInfo = $userInfoResponse->json();

            // Step 4: Find user in Laravel
            $user = User::where('email', $userInfo['email'])->first();

            if (!$user) {
                return $this->notFoundResponse('User not found in system');
            }

            // Step 5: Create new Laravel Sanctum token
            $laravelToken = $user->createToken('auth_token');

            // Log successful token refresh
            $this->logger->log('info', 'Keycloak token refreshed successfully', [
                'event_type' => 'keycloak_token_refreshed',
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            // Return both Keycloak and Laravel tokens
            return $this->successResponse('Token refreshed successfully', [
                'user' => new UserResource($user),
                'token' => $laravelToken->plainTextToken,
                'keycloak_tokens' => [
                    'access_token' => $tokens['access_token'],
                    'refresh_token' => $tokens['refresh_token'],
                    'expires_in' => $tokens['expires_in'],
                ],
            ]);

        } catch (\Exception $e) {
            $this->logger->log('error', 'Keycloak token refresh error', [
                'event_type' => 'keycloak_token_refresh_error',
                'error' => $e->getMessage()
            ]);

            return $this->errorResponse('Token refresh failed: ' . $e->getMessage(), null, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get authenticated user
     */
    public function getUser(Request $request): JsonResponse
    {
        return $this->successResponse('User retrieved successfully', [
            'user' => new UserResource($request->user()),
        ]);
    }

    /**
     * Validate token
     */
    public function validateToken(Request $request): JsonResponse
    {
        $token = $request->input('token', $request->bearerToken());

        if (! $token) {
            return $this->errorResponse('Token not provided', null, Response::HTTP_BAD_REQUEST);
        }

        $isValid = $this->authService->validateToken($token);

        return $this->successResponse('Token validation completed', [
            'valid' => $isValid,
        ]);
    }

    /**
     * Update user profile
     */
    public function updateProfile(UpdateProfileRequest $request): JsonResponse
    {
        // Get validated data from UpdateProfileRequest
        $validated = $request->validated();

        $user = $request->user();
        
        // Update user information in Laravel
        $user->first_name = $validated['first_name'];
        $user->last_name = $validated['last_name'];
        $user->email = $validated['email'];
        $user->phone = $validated['phone'] ?? null;
        $user->save();

        // Sync with customer-service
        $customerServiceUrl = env('CUSTOMER_SERVICE_URL', 'http://localhost:8002/api/v2/customers/by-user/' . $user->id);
        $customerPayload = [
            'customer_name' => $user->first_name . ' ' . $user->last_name,
            'phone' => $user->phone,
            'email_address' => $user->email,
            'company_id' => $user->company_id,
            'unit_id' => $user->unit_id,
        ];
        try {
            $customerResponse = \Illuminate\Support\Facades\Http::timeout(5)->put($customerServiceUrl, $customerPayload);
            if (!$customerResponse->successful()) {
                \Log::error('Customer service sync failed on update', ['user_id' => $user->id, 'response' => $customerResponse->body()]);
            }
        } catch (\Exception $e) {
            \Log::error('Customer service sync exception on update', ['user_id' => $user->id, 'error' => $e->getMessage()]);
        }

        // Log Laravel profile update
        $this->logger->log('info', 'User profile updated successfully in Laravel', [
            'event_type' => 'profile_updated_laravel',
            'user_id' => $user->id,
            'email' => $user->email
        ]);
        
        // If user is authenticated via Keycloak, also update in Keycloak
        $keycloakUpdated = false;
        $keycloakMessage = 'Keycloak update not attempted (user not linked to Keycloak)';
        
        if ($user->auth_type === 'keycloak') {
            try {
                // Update user in Keycloak
                $keycloakResult = $this->keycloakRegistrationService->updateUser(
                    $user->username,
                    [
                        'first_name' => $validated['first_name'],
                        'last_name' => $validated['last_name'],
                        'email' => $validated['email'],
                    ]
                );
                
                $keycloakUpdated = $keycloakResult['success'];
                $keycloakMessage = $keycloakResult['message'];
                
                if ($keycloakUpdated) {
                    $this->logger->log('info', 'User profile updated successfully in Keycloak', [
                        'event_type' => 'profile_updated_keycloak',
                        'user_id' => $user->id,
                        'username' => $user->username,
                        'email' => $user->email
                    ]);
                } else {
                    $this->logger->log('warning', 'Failed to update user profile in Keycloak', [
                        'event_type' => 'profile_update_keycloak_failed',
                        'user_id' => $user->id,
                        'username' => $user->username,
                        'email' => $user->email,
                        'message' => $keycloakMessage
                    ]);
                }
            } catch (\Exception $e) {
                $keycloakMessage = 'Keycloak update failed: ' . $e->getMessage();
                
                $this->logger->log('error', 'Exception updating user profile in Keycloak', [
                    'event_type' => 'profile_update_keycloak_exception',
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $this->successResponse('Profile updated successfully', [
            'user' => new UserResource($user),
            'keycloak_updated' => $keycloakUpdated,
            'keycloak_message' => $keycloakMessage
        ]);
    }

    /**
     * Delete the authenticated user and sync with customer-service
     */
    public function deleteUser(Request $request): JsonResponse
    {
        $user = $request->user();
        $userId = $user->id;
        $user->delete();

        // Sync with customer-service
        $customerServiceUrl = env('CUSTOMER_SERVICE_URL', 'http://localhost:8002/api/v2/customers/by-user/' . $userId);
        try {
            $customerResponse = \Illuminate\Support\Facades\Http::timeout(5)->delete($customerServiceUrl);
            if (!$customerResponse->successful()) {
                \Log::error('Customer service sync failed on delete', ['user_id' => $userId, 'response' => $customerResponse->body()]);
            }
        } catch (\Exception $e) {
            \Log::error('Customer service sync exception on delete', ['user_id' => $userId, 'error' => $e->getMessage()]);
        }

        return $this->successResponse('User and linked customer deleted successfully');
    }

    /**
     * Forgot password
     */
    public function forgotPassword(ForgotPasswordRequest $request): JsonResponse
    {
        // Check if the user exists
        $user = User::where('email', $request->email)->first();
        $userExists = $user !== null;

        // Log password reset request
        $this->logger->logPasswordResetRequest($request->email, $userExists);

        // Generate a token
        $token = Str::random(60);

        if ($user) {
            // Store the token in the password_resets table
            $passwordReset = \App\Models\PasswordReset::updateOrCreate(
                ['email' => $request->email],
                [
                    'token' => Hash::make($token),
                    'created_at' => now(),
                    'expires_at' => now()->addHour(),
                    'used' => false,
                ]
            );
        }

        // Always return success to prevent user enumeration
        return $this->successResponse('Password reset link has been sent to your email', [
            'token' => $user ? $token : 'dummy-token',
        ]);
    }

    /**
     * Reset password
     */
    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        // Find the password reset record
        $passwordReset = \App\Models\PasswordReset::where('email', $request->email)
            ->where('used', false)
            ->where('expires_at', '>', now())
            ->first();

        if (! $passwordReset || ! Hash::check($request->token, $passwordReset->token)) {
            $this->logger->logPasswordResetComplete($request->email, false);
            return $this->unauthorizedResponse('Invalid or expired token');
        }

        // Find the user
        $user = User::where('email', $request->email)->first();

        if (! $user) {
            $this->logger->logPasswordResetComplete($request->email, false);
            return $this->notFoundResponse('User not found');
        }

        // Update the user's password
        $user->password = Hash::make($request->password);
        $user->setRememberToken(Str::random(60));
        $user->save();

        // Mark the token as used
        $passwordReset->markAsUsed();

        // Fire the password reset event
        event(new PasswordResetEvent($user));

        // Log successful password reset
        $this->logger->logPasswordResetComplete($request->email, true);

        return $this->successResponse('Password has been reset successfully');
    }

    /**
     * Keycloak login
     */
    public function keycloakLogin(): JsonResponse
    {
        $keycloakConfig = config('keycloak');

        $authUrl = $keycloakConfig['auth_server_url'].'/realms/'.$keycloakConfig['realm'].'/protocol/openid-connect/auth';

        $queryParams = http_build_query([
            'client_id' => $keycloakConfig['client_id'],
            'redirect_uri' => $keycloakConfig['redirect_uri'],
            'response_type' => 'code',
            'scope' => 'openid profile email',
            'state' => csrf_token(),
        ]);

        return $this->successResponse('Keycloak authentication URL generated', [
            'auth_url' => $authUrl.'?'.$queryParams,
        ]);
    }

    /**
     * Keycloak callback
     */
    public function keycloakCallback(Request $request): JsonResponse
    {
        $code = $request->input('code');
        $state = $request->input('state');

        if (! $code) {
            return $this->errorResponse('Authorization code not provided', null, Response::HTTP_BAD_REQUEST);
        }

        // Validate state to prevent CSRF
        if ($state !== csrf_token()) {
            return $this->errorResponse('Invalid state parameter', null, Response::HTTP_BAD_REQUEST);
        }

        $keycloakConfig = config('keycloak');

        // Exchange code for tokens
        $response = Http::asForm()->post($keycloakConfig['auth_server_url'].'/realms/'.$keycloakConfig['realm'].'/protocol/openid-connect/token', [
            'grant_type' => 'authorization_code',
            'code' => $code,
            'client_id' => $keycloakConfig['client_id'],
            'client_secret' => $keycloakConfig['client_secret'],
            'redirect_uri' => $keycloakConfig['redirect_uri'],
        ]);

        if ($response->failed()) {
            return $this->errorResponse('Failed to exchange authorization code for tokens', null, Response::HTTP_BAD_REQUEST);
        }

        $tokens = $response->json();

        // Get user info
        $userInfoResponse = Http::withToken($tokens['access_token'])
            ->get($keycloakConfig['auth_server_url'].'/realms/'.$keycloakConfig['realm'].'/protocol/openid-connect/userinfo');

        if ($userInfoResponse->failed()) {
            return $this->errorResponse('Failed to get user info', null, Response::HTTP_BAD_REQUEST);
        }

        $userInfo = $userInfoResponse->json();

        // Find or create user
        $user = User::where('email', $userInfo['email'])->first();

        if (! $user) {
            $user = new User;
            $user->email = $userInfo['email'];
            $user->username = $userInfo['preferred_username'] ?? $userInfo['email'];
            $user->first_name = $userInfo['given_name'] ?? '';
            $user->last_name = $userInfo['family_name'] ?? '';
            $user->password = Hash::make(Str::random(16));
            $user->role_id = $keycloakConfig['role_mapping'][$userInfo['realm_access']['roles'][0]] ?? 2;
            $user->status = 1;
            $user->company_id = 8163; // Dynamic company_id
            $user->unit_id = 1;
            $user->auth_type = 'keycloak';
            $user->save();
        }

        // Create Sanctum token
        $token = $user->createToken('auth_token');

        return $this->successResponse('Authentication successful', [
            'user' => new UserResource($user),
            'token' => $token->plainTextToken,
            'keycloak_tokens' => $tokens,
        ]);
    }
}

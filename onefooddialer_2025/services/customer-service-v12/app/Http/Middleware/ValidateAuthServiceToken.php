<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class ValidateAuthServiceToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $token = $request->bearerToken();

        if (!$token) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        try {
            // Check if it's a JWT token (Keycloak) or Sanctum token
            if ($this->isJwtToken($token)) {
                // Validate Keycloak JWT token
                $userData = $this->validateKeycloakToken($token);
                if (!$userData) {
                    return response()->json(['message' => 'Unauthorized'], 401);
                }
                $request->attributes->set('auth_user', $userData);
            } else {
                // Validate Sanctum token with auth service
                $userData = $this->validateSanctumToken($token);
                if (!$userData) {
                    return response()->json(['message' => 'Unauthorized'], 401);
                }
                $request->attributes->set('auth_user', $userData);
            }

            return $next($request);

        } catch (\Exception $e) {
            Log::error('Error validating token', [
                'error' => $e->getMessage()
            ]);
            return response()->json(['message' => 'Unauthorized'], 401);
        }
    }

    private function isJwtToken(string $token): bool
    {
        // JWT tokens have 3 parts separated by dots
        return substr_count($token, '.') === 2;
    }

    private function validateKeycloakToken(string $token): ?array
    {
        try {
            // For now, just decode the JWT without verification for testing
            // In production, you should verify the signature with Keycloak's public key
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }

            $payload = json_decode(base64_decode(str_pad(strtr($parts[1], '-_', '+/'), strlen($parts[1]) % 4, '=', STR_PAD_RIGHT)), true);

            if (!$payload || !isset($payload['sub'])) {
                return null;
            }

            // Check if token is expired
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                Log::warning('Keycloak token expired');
                return null;
            }

            // Return user data in expected format
            return [
                'id' => $payload['sub'],
                'email' => $payload['email'] ?? null,
                'name' => $payload['name'] ?? $payload['preferred_username'] ?? null,
                'role' => 'customer', // Keycloak tokens are for customers
                'keycloak_id' => $payload['sub']
            ];

        } catch (\Exception $e) {
            Log::error('Error validating Keycloak token', ['error' => $e->getMessage()]);
            return null;
        }
    }

    private function validateSanctumToken(string $token): ?array
    {
        try {
            $authServiceUrl = env('AUTH_SERVICE_URL', 'http://localhost:8001/api/v2');
            $response = Http::withToken($token)
                ->timeout(5)
                ->get($authServiceUrl . '/user');

            if (!$response->successful()) {
                Log::warning('Auth service token validation failed', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return null;
            }

            $userData = $response->json();

            if (!$userData['success'] || !isset($userData['data']['user'])) {
                return null;
            }

            return $userData['data']['user'];

        } catch (\Exception $e) {
            Log::error('Error validating Sanctum token', ['error' => $e->getMessage()]);
            return null;
        }
    }
}

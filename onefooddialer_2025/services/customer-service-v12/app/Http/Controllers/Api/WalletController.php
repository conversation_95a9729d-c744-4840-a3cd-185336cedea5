<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\Customer\CustomerNotFoundException;
use App\Exceptions\Wallet\InsufficientBalanceException;
use App\Exceptions\Wallet\WalletNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Wallet\DepositRequest;
use App\Http\Requests\Wallet\WithdrawRequest;
use App\Services\WalletService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Wallet API Controller
 *
 * This controller handles all wallet-related API endpoints.
 */
class WalletController extends Controller
{
    /**
     * Create a new WalletController instance.
     */
    public function __construct(
        protected WalletService $walletService
    ) {
    }

    /**
     * Get a customer's wallet
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $wallet = $this->walletService->getWallet($id);

            return response()->json([
                'success' => true,
                'data' => $wallet
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error getting wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the wallet'
            ], 500);
        }
    }

    /**
     * Deposit to a customer's wallet
     *
     * @param DepositRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function deposit(DepositRequest $request, int $id): JsonResponse
    {
        try {
            $amount = $request->validated()['amount'];
            $description = $request->validated()['description'] ?? '';
            $transactionId = $request->validated()['transaction_id'] ?? '';

            $wallet = $this->walletService->deposit($id, $amount, $description, $transactionId);

            return response()->json([
                'success' => true,
                'message' => 'Deposit successful',
                'data' => $wallet
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error depositing to wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing the deposit'
            ], 500);
        }
    }

    /**
     * Withdraw from a customer's wallet
     *
     * @param WithdrawRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function withdraw(WithdrawRequest $request, int $id): JsonResponse
    {
        try {
            $amount = $request->validated()['amount'];
            $description = $request->validated()['description'] ?? '';
            $transactionId = $request->validated()['transaction_id'] ?? '';

            $wallet = $this->walletService->withdraw($id, $amount, $description, $transactionId);

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal successful',
                'data' => $wallet
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (InsufficientBalanceException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('Error withdrawing from wallet', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing the withdrawal'
            ], 500);
        }
    }

    /**
     * Get wallet transaction history
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function transactions(Request $request, int $id): JsonResponse
    {
        try {
            $filters = $request->only(['type', 'status', 'date_from', 'date_to']);
            $perPage = $request->input('per_page', 15);

            $transactions = $this->walletService->getTransactionHistory($id, $filters, $perPage);

            return response()->json([
                'success' => true,
                'data' => $transactions
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error getting wallet transactions', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the transactions'
            ], 500);
        }
    }

    /**
     * Get wallet balance
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getBalance(int $id): JsonResponse
    {
        try {
            $balance = $this->walletService->getBalance($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'customer_id' => $id,
                    'balance' => $balance,
                    'currency' => 'INR'
                ]
            ]);
        } catch (CustomerNotFoundException | WalletNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error getting wallet balance', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the balance'
            ], 500);
        }
    }

    /**
     * Get wallet history (alias for transactions)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function getHistory(Request $request, int $id): JsonResponse
    {
        return $this->transactions($request, $id);
    }

    /**
     * Transfer funds between wallets (placeholder)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function transfer(Request $request, int $id): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Wallet transfer functionality not implemented yet'
        ], 501);
    }

    /**
     * Freeze wallet (placeholder)
     *
     * @param int $id
     * @return JsonResponse
     */
    public function freeze(int $id): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Wallet freeze functionality not implemented yet'
        ], 501);
    }

    /**
     * Unfreeze wallet (placeholder)
     *
     * @param int $id
     * @return JsonResponse
     */
    public function unfreeze(int $id): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Wallet unfreeze functionality not implemented yet'
        ], 501);
    }
}

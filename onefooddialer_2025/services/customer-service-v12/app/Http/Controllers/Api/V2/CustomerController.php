<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class CustomerController extends Controller
{
    /**
     * Update a customer by user_id
     */
    public function updateByUserId(Request $request, $userId)
    {
        $customer = Customer::where('user_id', $userId)->first();
        if (!$customer) {
            return response()->json(['status' => 'error', 'message' => 'Customer not found for user_id'], 404);
        }
        $data = $request->all();
        $customer->fill($data);
        $customer->save();
        return response()->json(['status' => 'success', 'message' => 'Customer updated', 'data' => $customer]);
    }

    /**
     * Delete (soft delete) a customer by user_id
     */
    public function deleteByUserId($userId)
    {
        $customer = Customer::where('user_id', $userId)->first();
        if (!$customer) {
            return response()->json(['status' => 'error', 'message' => 'Customer not found for user_id'], 404);
        }
        $customer->delete();
        return response()->json(['status' => 'success', 'message' => 'Customer deleted for user_id']);
    }

    /**
     * Get a customer by user_id and company_id
     */
    public function getByUserIdAndCompany(Request $request)
    {
        $userId = $request->get('user_id');
        $companyId = $request->get('company_id');

        if (!$userId || !$companyId) {
            return response()->json([
                'success' => false,
                'message' => 'user_id and company_id are required'
            ], 400);
        }

        $customer = Customer::where('user_id', $userId)
            ->where('company_id', $companyId)
            ->first();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Customer not found for user_id and company_id'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $customer
        ]);
    }

    /**
     * Customer registration from Keycloak (auth service integration)
     */
    public function registerFromKeycloak(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'auth_id' => 'required|string|max:255', // Keycloak UUID - will be stored in thirdparty
            'customer_name' => 'required|string|max:45',
            'email_address' => 'nullable|string|email|max:45',
            'phone' => 'required|string|max:15|unique:customers,phone',
            'company_id' => 'required|integer',
            'unit_id' => 'nullable|integer',
            'customer_address' => 'nullable|string|max:200',
            'food_preference' => 'nullable|string|max:100',
            'registered_from' => 'nullable|string|max:50',
            'source' => 'nullable|string|max:50',
            'thirdparty' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Debug: Check database connection
            Log::info('Keycloak customer registration debug', [
                'database' => DB::connection()->getDatabaseName(),
                'connection' => DB::connection()->getName(),
                'request_data' => $request->all()
            ]);

            // Convert Keycloak UUID to integer hash for thirdparty field
            // Use modulo to ensure it fits in signed int range (MySQL int is -2,147,483,648 to 2,147,483,647)
            $keycloakHash = crc32($request->auth_id) % 2147483647;

            // Prepare data for customer creation (without auth_id initially)
            $customerData = [
                'customer_name' => $request->customer_name,
                'email_address' => $request->email_address,
                'phone' => $request->phone,
                'company_id' => $request->company_id,
                'unit_id' => $request->unit_id ?? $request->company_id,
                'status' => 1,
                'registered_on' => now()->format('Y-m-d'),
                'registered_from' => $request->registered_from ?? 'keycloak_registration',
                'phone_verified' => 0,
                'email_verified' => 'no',
                'subscription_notification' => 'yes',
                'source' => $request->source ?? 'auth_service',
                'customer_Address' => $request->customer_address ?? '?',
                'food_preference' => $request->food_preference ?? 'veg',
                'isguest' => 'N',
                'dabba_status' => 'IN',
                'modified_on' => now(),
                'thirdparty' => $keycloakHash, // Store hash of Keycloak UUID
            ];

            // Add additional thirdparty data if provided (will override Keycloak hash)
            if ($request->thirdparty && is_numeric($request->thirdparty)) {
                $customerData['thirdparty'] = (int)$request->thirdparty;
            }

            // Use direct DB insert to create customer
            $customerId = DB::table('customers')->insertGetId($customerData);

            // Update auth_id with the customer's primary key (integer)
            DB::table('customers')
                ->where('pk_customer_code', $customerId)
                ->update(['auth_id' => $customerId]);

            // Get the created customer
            $customer = DB::table('customers')->where('pk_customer_code', $customerId)->first();

            return response()->json([
                'success' => true,
                'message' => 'Customer registered successfully from Keycloak',
                'data' => [
                    'pk_customer_code' => $customer->pk_customer_code,
                    'auth_id' => $customer->auth_id,
                    'customer_name' => $customer->customer_name,
                    'email_address' => $customer->email_address,
                    'phone' => $customer->phone,
                    'company_id' => $customer->company_id,
                    'unit_id' => $customer->unit_id,
                    'keycloak_user_id' => $request->auth_id, // Original Keycloak UUID
                    'keycloak_hash' => $customer->thirdparty, // Hash stored in thirdparty field
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Keycloak customer registration failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Customer registration failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Register a new customer directly (bypassing users table)
     */
    public function registerCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_name' => 'required|string|max:45',
            'email_address' => 'nullable|string|email|max:45',
            'phone' => 'required|string|max:15|unique:customers,phone',
            'password' => 'required|string|min:8|max:45',
            'company_id' => 'required|integer',
            'unit_id' => 'nullable|integer',
            'customer_address' => 'nullable|string|max:200',
            'food_preference' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Debug: Check database connection
            Log::info('Customer registration debug', [
                'database' => DB::connection()->getDatabaseName(),
                'connection' => DB::connection()->getName(),
                'request_data' => $request->all()
            ]);

            // Prepare data for customer creation (matching actual database structure)
            $customerData = [
                'customer_name' => $request->customer_name,
                'email_address' => $request->email_address,
                'phone' => $request->phone,
                'password' => md5($request->password), // Use MD5 to match existing database format
                'company_id' => $request->company_id,
                'unit_id' => $request->unit_id ?? $request->company_id,
                'status' => 1,
                'registered_on' => now()->format('Y-m-d'), // Date format for registered_on
                'registered_from' => 'direct_registration',
                'phone_verified' => 0,
                'email_verified' => 'no', // Enum value
                'subscription_notification' => 'yes', // Enum value
                'source' => 'customer_api',
                'customer_Address' => $request->customer_address,
                'food_preference' => $request->food_preference ?? 'veg',
                'isguest' => 'N', // Enum value
                'dabba_status' => 'IN', // Enum value
                'modified_on' => now(), // Use modified_on instead of created_at/updated_at
            ];

            // Use direct DB insert
            $customerId = DB::table('customers')->insertGetId($customerData);

            // Get the created customer
            $customer = DB::table('customers')->where('pk_customer_code', $customerId)->first();

            // For now, we'll create a simple token (in production, use proper JWT or Sanctum)
            $token = base64_encode($customer->pk_customer_code . ':' . time());

            return response()->json([
                'success' => true,
                'message' => 'Customer registered successfully',
                'data' => [
                    'pk_customer_code' => $customer->pk_customer_code,
                    'customer_name' => $customer->customer_name,
                    'email_address' => $customer->email_address,
                    'phone' => $customer->phone,
                    'company_id' => $customer->company_id,
                    'unit_id' => $customer->unit_id,
                    'token' => $token,
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Customer registration failed', ['error' => $e->getMessage(), 'request_data' => $request->all()]);
            return response()->json([
                'success' => false,
                'message' => 'Customer registration failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Customer login for Keycloak authentication (called by auth service)
     */
    public function loginFromKeycloak(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Find customer by phone or email
            $customer = DB::table('customers')
                ->where(function($query) use ($request) {
                    $query->where('phone', $request->username)
                          ->orWhere('email_address', $request->username);
                })
                ->where('status', 1) // Active customers only
                ->first();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Check if customer has auth_id (Keycloak user ID)
            if (empty($customer->auth_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not registered with Keycloak'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Customer found for Keycloak authentication',
                'data' => [
                    'pk_customer_code' => $customer->pk_customer_code,
                    'auth_id' => $customer->auth_id,
                    'customer_name' => $customer->customer_name,
                    'email_address' => $customer->email_address,
                    'phone' => $customer->phone,
                    'company_id' => $customer->company_id,
                    'unit_id' => $customer->unit_id,
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('Customer Keycloak login lookup failed', [
                'error' => $e->getMessage(),
                'username' => $request->username
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Login lookup failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Customer login (authenticate against customers table)
     */
    public function loginCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Find customer by phone using direct DB query
            $customer = DB::table('customers')->where('phone', $request->phone)->first();

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer not found'
                ], 404);
            }

            // Check password using MD5 (matching existing database format)
            if (md5($request->password) !== $customer->password) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ], 401);
            }

            // Check if customer is active
            if (!$customer->status) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer account is inactive'
                ], 403);
            }

            // Generate authentication token
            $token = base64_encode($customer->pk_customer_code . ':' . time());

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'pk_customer_code' => $customer->pk_customer_code,
                    'customer_name' => $customer->customer_name,
                    'email_address' => $customer->email_address,
                    'phone' => $customer->phone,
                    'company_id' => $customer->company_id,
                    'unit_id' => $customer->unit_id,
                    'token' => $token,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Customer login failed', ['error' => $e->getMessage(), 'request_data' => $request->all()]);
            return response()->json([
                'success' => false,
                'message' => 'Login failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Customer logout
     */
    public function logoutCustomer(Request $request)
    {
        try {
            // Delete current access token
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Logged out successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Customer logout failed', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Logout failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync customer from auth service (V2 endpoint)
     * This method handles customer creation/update from auth-service-v12
     */
    public function syncCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'customer_name' => 'required|string|max:255',
            'email_address' => 'nullable|string|email|max:255',
            'phone' => 'required|string',
            'company_id' => 'required|integer',
            'unit_id' => 'nullable|integer',
            'thirdparty' => 'nullable|string|max:255', // Maps to old_sso_user_id from auth service
            'customer_address' => 'nullable|string',
            'food_preference' => 'nullable|string|max:50',
            'source' => 'nullable|string|max:50',
            'registered_from' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Prepare data for customer creation/update
            $customerData = [
                'customer_name' => $request->customer_name,
                'email_address' => $request->email_address,
                'phone' => $request->phone,
                'company_id' => $request->company_id,
                'unit_id' => $request->unit_id ?? 1,
                'status' => $request->status ?? true,
                'registered_on' => now(),
                'registered_from' => $request->registered_from ?? 'auth_service',
                'phone_verified' => false,
                'email_verified' => false,
                'source' => $request->source ?? 'auth_service_sync',
            ];

            // Add optional fields if provided
            if ($request->has('thirdparty') && $request->thirdparty) {
                $customerData['thirdparty'] = $request->thirdparty;
            }
            if ($request->has('customer_address') && $request->customer_address) {
                $customerData['customer_address'] = $request->customer_address;
            }
            if ($request->has('food_preference') && $request->food_preference) {
                $customerData['food_preference'] = $request->food_preference;
            }

            // Try to find existing customer by phone first (since user_id might be empty)
            $customer = Customer::where('phone', $request->phone)
                ->where('company_id', $request->company_id)
                ->first();

            if ($customer) {
                // Update existing customer
                $customer->update($customerData);
                $customer->user_id = $request->user_id; // Update user_id for linking
                $customer->save();
            } else {
                // Create new customer
                $customerData['user_id'] = $request->user_id;
                $customer = Customer::create($customerData);
            }

            return response()->json([
                'success' => true,
                'message' => 'Customer synced successfully',
                'data' => [
                    'pk_customer_code' => $customer->pk_customer_code,
                    'customer_name' => $customer->customer_name,
                    'email_address' => $customer->email_address,
                    'phone' => $customer->phone,
                    'company_id' => $customer->company_id,
                    'unit_id' => $customer->unit_id,
                    'thirdparty' => $customer->thirdparty,
                    'user_id' => $customer->user_id,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Customer sync failed in V2', ['error' => $e->getMessage(), 'request_data' => $request->all()]);
            return response()->json([
                'success' => false,
                'message' => 'Customer sync failed: ' . $e->getMessage()
            ], 500);
        }
    }
}

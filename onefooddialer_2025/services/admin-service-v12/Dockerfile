FROM php:8.2-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    libzip-dev \
    supervisor

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html/

# Copy application files
COPY . .

# Install dependencies
RUN composer install --no-scripts --no-autoloader --no-dev

# Generate optimized autoload files
RUN composer dump-autoload --optimize

# Set permissions
RUN chmod +x artisan

# Ensure storage directories exist
RUN mkdir -p storage/framework/sessions \
    && mkdir -p storage/framework/views \
    && mkdir -p storage/framework/cache \
    && mkdir -p bootstrap/cache

RUN php artisan storage:link

# Expose port 8000
EXPOSE 8000

# Start supervisor
CMD php artisan serve --host 0.0.0.0 --port 8000

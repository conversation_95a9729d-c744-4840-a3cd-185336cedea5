<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhonePe Test Payment</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .btn {
            background: #673ab7;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        .btn:hover {
            background: #5e35b1;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .error {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        .config-info {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #ff9800;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 PhonePe Laravel Service Test</h1>
            <p>Test the complete PhonePe integration with dummy data</p>
        </div>

        <div class="config-info">
            <h3>📋 Current Configuration</h3>
            <ul>
                <li><strong>Merchant ID:</strong> {{ env('PHONEPE_MERCHANT_ID', 'UATMERCHANT') }}</li>
                <li><strong>Environment:</strong> {{ env('PHONEPE_ENV', 'sandbox') }}</li>
                <li><strong>Salt Index:</strong> {{ env('PHONEPE_SALT_INDEX', 1) }}</li>
                <li><strong>Test Amount:</strong> ₹10.00</li>
            </ul>
        </div>

        <div class="test-info">
            <h3>🧪 Test Details</h3>
            <p><strong>Test User:</strong> Test User (<EMAIL>, 9876543210)</p>
            <p><strong>Test Amount:</strong> ₹10.00</p>
            <p><strong>Payment Gateway:</strong> PhonePe Sandbox</p>
            <p><strong>SDK Version:</strong> PhonePe PHP SDK v2</p>
        </div>

        <button class="btn" onclick="initiateTestPayment()">
            🚀 Start PhonePe Test Payment
        </button>

        <div id="response" class="response">
            <h3>Response:</h3>
            <pre id="response-content"></pre>
        </div>
    </div>

    <script>
        // Set up CSRF token for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        async function initiateTestPayment() {
            const btn = document.querySelector('.btn');
            const responseDiv = document.getElementById('response');
            const responseContent = document.getElementById('response-content');

            // Show loading state
            btn.textContent = '⏳ Processing...';
            btn.disabled = true;
            responseDiv.style.display = 'none';

            try {
                const response = await fetch('{{ route("phonepe.test.payment") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                const data = await response.json();

                // Show response
                responseDiv.style.display = 'block';
                responseDiv.className = 'response ' + (data.success ? 'success' : 'error');
                responseContent.textContent = JSON.stringify(data, null, 2);

                // If successful and has redirect URL, redirect to PhonePe
                if (data.success && data.data && data.data.redirect_url) {
                    setTimeout(() => {
                        window.open(data.data.redirect_url, '_blank');
                    }, 2000);
                }

            } catch (error) {
                responseDiv.style.display = 'block';
                responseDiv.className = 'response error';
                responseContent.textContent = 'Error: ' + error.message;
            } finally {
                // Reset button
                btn.textContent = '🚀 Start PhonePe Test Payment';
                btn.disabled = false;
            }
        }
    </script>
</body>
</html>

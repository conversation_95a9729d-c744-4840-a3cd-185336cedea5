<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\Payment;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use PhonePe\payments\v2\standardCheckout\StandardCheckoutClient;
use PhonePe\payments\v2\models\request\builders\StandardCheckoutPayRequestBuilder;
use PhonePe\Env;

class PhonepeController extends Controller
{

   public function index()
   {
        return view('index');
   }

    public function payment(Request $request)
    {
        try {
            // Validate request data
            $request->validate([
                'name'   => 'required|string|max:255',
                'email'  => 'required|email',
                'phone'  => 'required|string|max:20',
                'amount' => 'required|numeric|min:1',
            ]);

            $amount = $request->input('amount');
            $name   = $request->input('name');
            $email  = $request->input('email');
            $phone  = $request->input('phone');

            // Get PhonePe credentials from .env
            $merchantId   = env('PHONEPE_MERCHANT_ID', 'UATMERCHANT');
            $saltKey      = env('PHONEPE_API_KEY', '8289e078-be0b-484d-ae60-052f117f8deb');
            $saltIndex    = env('PHONEPE_SALT_INDEX', 1);
            $environment  = env('PHONEPE_ENV', 'sandbox') === 'production' ? Env::PRODUCTION : Env::UAT;
            $redirectUrl  = route('phonepe.success');

            // Generate unique order ID
            $orderId = 'ORDER_' . time() . '_' . uniqid();

            Log::info('PhonePe Payment Initiation', [
                'order_id' => $orderId,
                'amount' => $amount,
                'customer' => $name,
                'phone' => $phone
            ]);

            // Initialize PhonePe SDK Client
            $client = StandardCheckoutClient::getInstance(
                $merchantId,
                $saltIndex,
                $saltKey,
                $environment
            );

            // Build payment request using the official SDK
            $payRequest = StandardCheckoutPayRequestBuilder::builder()
                ->merchantOrderId($orderId)
                ->amount($amount * 100) // Convert to paise
                ->redirectUrl($redirectUrl)
                ->message("Payment for Order: $orderId")
                ->build();

            // Save payment record to database
            $payment = Payment::create([
                'name'       => $name,
                'email'      => $email,
                'phone'      => $phone,
                'amount'     => $amount,
                'order_id'   => $orderId,
                'payment_id' => null, // Will be updated after payment
                'status'     => 0, // pending
                'other'      => json_encode(['request_data' => $payRequest])
            ]);

            // Execute payment request
            $payResponse = $client->pay($payRequest);

            Log::info('PhonePe Payment Response', [
                'order_id' => $orderId,
                'response' => $payResponse
            ]);

            // Check if payment initiation was successful
            if ($payResponse && isset($payResponse['success']) && $payResponse['success']) {
                $redirectUrl = $payResponse['data']['instrumentResponse']['redirectInfo']['url'] ?? null;

                if ($redirectUrl) {
                    // Update payment record with response
                    $payment->update([
                        'payment_id' => $payResponse['data']['merchantTransactionId'] ?? $orderId,
                        'other' => json_encode($payResponse)
                    ]);

                    return redirect()->away($redirectUrl);
                }
            }

            // If payment initiation failed
            Log::error('PhonePe Payment Initiation Failed', [
                'order_id' => $orderId,
                'response' => $payResponse
            ]);

            return back()->with('error', 'Payment initiation failed. Please try again.');

        } catch (\Exception $e) {
            Log::error('PhonePe Payment Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Payment processing error: ' . $e->getMessage());
        }
    }


    public function success(Request $request)
    {
        try {
            Log::info('PhonePe Callback Received:', $request->all());

            // Get the transaction ID from callback
            $transactionId = $request->input('transactionId') ?? $request->input('merchantTransactionId');

            if (!$transactionId) {
                Log::error('PhonePe Verify: No transactionId found in callback');
                return redirect()->route('home')->with('error', 'PhonePe payment verification failed!');
            }

            // Get credentials from environment
            $merchantId  = env('PHONEPE_MERCHANT_ID', 'UATMERCHANT');
            $saltKey     = env('PHONEPE_API_KEY', '8289e078-be0b-484d-ae60-052f117f8deb');
            $saltIndex   = env('PHONEPE_SALT_INDEX', 1);
            $environment = env('PHONEPE_ENV', 'sandbox') === 'production' ? Env::PRODUCTION : Env::UAT;

            // Initialize PhonePe SDK Client for verification
            $client = StandardCheckoutClient::getInstance(
                $merchantId,
                $saltIndex,
                $saltKey,
                $environment
            );

            // Check payment status using SDK
            $statusResponse = $client->checkStatus($transactionId);

            Log::info('PhonePe Status Check Response:', [
                'transaction_id' => $transactionId,
                'response' => $statusResponse
            ]);

            // Find payment record in database
            $payment = Payment::where('order_id', $transactionId)
                             ->orWhere('payment_id', $transactionId)
                             ->first();

            if (!$payment) {
                Log::error('Payment record not found', ['transaction_id' => $transactionId]);
                return redirect()->route('home')->with('error', 'Payment record not found.');
            }

            // Process the status response
            if ($statusResponse && isset($statusResponse['success']) && $statusResponse['success']) {
                $paymentData = $statusResponse['data'] ?? [];
                $paymentState = $paymentData['state'] ?? '';

                // Update payment record
                $payment->update([
                    'status' => $paymentState === 'COMPLETED' ? 1 : 0,
                    'payment_id' => $paymentData['transactionId'] ?? $transactionId,
                    'other' => json_encode($statusResponse)
                ]);

                if ($paymentState === 'COMPLETED') {
                    Log::info('Payment completed successfully', [
                        'transaction_id' => $transactionId,
                        'amount' => $paymentData['amount'] ?? 0
                    ]);

                    return redirect()->route('home')->with([
                        'success' => 'Payment Successful!',
                        'payment_id' => $transactionId,
                        'amount' => ($paymentData['amount'] ?? 0) / 100, // Convert from paise
                        'payment' => $payment,
                    ]);
                } else {
                    Log::warning('Payment not completed', [
                        'transaction_id' => $transactionId,
                        'state' => $paymentState
                    ]);

                    return redirect()->route('home')->with('error', "Payment status: $paymentState");
                }
            }

            Log::error('PhonePe Verification Failed', ['response' => $statusResponse]);
            return redirect()->route('home')->with('error', 'Payment verification failed. Please contact support.');

        } catch (\Exception $e) {
            Log::error('PhonePe Verify Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return redirect()->route('home')->with('error', 'Something went wrong during payment verification.');
        }
    }

    /**
     * Test PhonePe payment integration with dummy data
     */
    public function testPayment()
    {
        return view('test-payment');
    }

    /**
     * Process test payment with dummy data
     */
    public function testPaymentProcess(Request $request)
    {
        try {
            // Use dummy test data
            $testData = [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'phone' => '9876543210',
                'amount' => 10.00 // ₹10 for testing
            ];

            // Get PhonePe credentials from .env
            $merchantId   = env('PHONEPE_MERCHANT_ID', 'UATMERCHANT');
            $saltKey      = env('PHONEPE_API_KEY', '8289e078-be0b-484d-ae60-052f117f8deb');
            $saltIndex    = env('PHONEPE_SALT_INDEX', 1);
            $environment  = env('PHONEPE_ENV', 'sandbox') === 'production' ? Env::PRODUCTION : Env::UAT;
            $redirectUrl  = route('phonepe.success');

            // Generate unique order ID
            $orderId = 'TEST_ORDER_' . time() . '_' . uniqid();

            Log::info('PhonePe Test Payment Initiation', [
                'order_id' => $orderId,
                'amount' => $testData['amount'],
                'environment' => $environment
            ]);

            // Initialize PhonePe SDK Client
            $client = StandardCheckoutClient::getInstance(
                $merchantId,
                $saltIndex,
                $saltKey,
                $environment
            );

            // Build payment request using the official SDK
            $payRequest = StandardCheckoutPayRequestBuilder::builder()
                ->merchantOrderId($orderId)
                ->amount($testData['amount'] * 100) // Convert to paise
                ->redirectUrl($redirectUrl)
                ->message("Test Payment for Order: $orderId")
                ->build();

            // Save test payment record to database
            $payment = Payment::create([
                'name'       => $testData['name'],
                'email'      => $testData['email'],
                'phone'      => $testData['phone'],
                'amount'     => $testData['amount'],
                'order_id'   => $orderId,
                'payment_id' => null,
                'status'     => 0, // pending
                'other'      => json_encode(['test_mode' => true, 'request_data' => $payRequest])
            ]);

            // Execute payment request
            $payResponse = $client->pay($payRequest);

            Log::info('PhonePe Test Payment Response', [
                'order_id' => $orderId,
                'response' => $payResponse
            ]);

            // Check if payment initiation was successful
            if ($payResponse && isset($payResponse['success']) && $payResponse['success']) {
                $redirectUrl = $payResponse['data']['instrumentResponse']['redirectInfo']['url'] ?? null;

                if ($redirectUrl) {
                    // Update payment record with response
                    $payment->update([
                        'payment_id' => $payResponse['data']['merchantTransactionId'] ?? $orderId,
                        'other' => json_encode(array_merge(['test_mode' => true], $payResponse))
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'Test payment initiated successfully',
                        'data' => [
                            'order_id' => $orderId,
                            'redirect_url' => $redirectUrl,
                            'payment_response' => $payResponse
                        ]
                    ]);
                }
            }

            // If payment initiation failed
            Log::error('PhonePe Test Payment Initiation Failed', [
                'order_id' => $orderId,
                'response' => $payResponse
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Test payment initiation failed',
                'error' => $payResponse
            ], 400);

        } catch (\Exception $e) {
            Log::error('PhonePe Test Payment Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Test payment processing error: ' . $e->getMessage()
            ], 500);
        }
    }


}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use PhonePe\payments\v2\standardCheckout\StandardCheckoutClient;
use PhonePe\payments\v2\models\request\builders\StandardCheckoutPayRequestBuilder;
use PhonePe\Env;

class TestPhonePe extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'phonepe:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test PhonePe integration with official SDK';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔥 PhonePe Laravel Service Test');
        $this->info('================================');
        $this->newLine();

        try {
            // Test Configuration
            $merchantId = env('PHONEPE_MERCHANT_ID', 'UATMERCHANT');
            $saltKey = env('PHONEPE_API_KEY', '8289e078-be0b-484d-ae60-052f117f8deb');
            $saltIndex = env('PHONEPE_SALT_INDEX', 1);
            $environment = env('PHONEPE_ENV', 'sandbox') === 'production' ? Env::PRODUCTION : Env::UAT;

            $this->info('📋 Configuration:');
            $this->line("- Merchant ID: $merchantId");
            $this->line("- Environment: " . (env('PHONEPE_ENV', 'sandbox')));
            $this->line("- Salt Index: $saltIndex");
            $this->newLine();

            $this->info('🚀 Step 1: Initialize PhonePe SDK Client');

            $client = StandardCheckoutClient::getInstance(
                $merchantId,
                $saltIndex,
                $saltKey,
                $environment
            );

            $this->info('✅ PhonePe SDK Client initialized successfully');
            $this->newLine();

            $this->info('🚀 Step 2: Create Payment Request');

            $orderId = 'TEST_ORDER_' . time() . '_' . uniqid();
            $amount = 1000; // ₹10.00 in paise
            $redirectUrl = 'http://127.0.0.1:8005/phonepe/success';
            $message = "Test Payment for Order: $orderId";

            $this->line("- Order ID: $orderId");
            $this->line("- Amount: ₹" . ($amount / 100));
            $this->line("- Redirect URL: $redirectUrl");
            $this->newLine();

            $payRequest = StandardCheckoutPayRequestBuilder::builder()
                ->merchantOrderId($orderId)
                ->amount($amount)
                ->redirectUrl($redirectUrl)
                ->message($message)
                ->build();

            $this->info('✅ Payment request created successfully');
            $this->newLine();

            $this->info('🚀 Step 3: Execute Payment Request');

            $payResponse = $client->pay($payRequest);

            $this->info('✅ Payment request executed');
            $this->newLine();

            $this->info('📊 Payment Response:');
            $this->line(json_encode($payResponse, JSON_PRETTY_PRINT));
            $this->newLine();

            // Check if payment initiation was successful
            if ($payResponse && isset($payResponse['success']) && $payResponse['success']) {
                $redirectUrl = $payResponse['data']['instrumentResponse']['redirectInfo']['url'] ?? null;

                if ($redirectUrl) {
                    $this->info('✅ Payment initiated successfully!');
                    $this->line("🔗 Payment URL: $redirectUrl");
                    $this->newLine();

                    $this->info('🎉 PhonePe Integration Test Completed Successfully!');
                    $this->info('📝 Summary:');
                    $this->line('- SDK Initialization: ✅ Success');
                    $this->line('- Payment Request Creation: ✅ Success');
                    $this->line('- Payment Initiation: ✅ Success');
                    $this->line('- Payment URL Generated: ✅ Success');
                    $this->newLine();

                    $this->info('🌐 Next Steps:');
                    $this->line("1. Open the payment URL in browser: $redirectUrl");
                    $this->line('2. Complete the payment flow');
                    $this->line('3. Check the callback handling');

                } else {
                    $this->error('❌ Payment URL not found in response');
                }
            } else {
                $this->error('❌ Payment initiation failed');
                $this->line('📊 Error Response:');
                $this->line(json_encode($payResponse, JSON_PRETTY_PRINT));
            }

        } catch (\Exception $e) {
            $this->error('❌ Test failed with exception:');
            $this->error("Error: " . $e->getMessage());
            $this->error("File: " . $e->getFile());
            $this->error("Line: " . $e->getLine());
            $this->newLine();

            $this->info('🔧 Troubleshooting:');
            $this->line('1. Check if PhonePe SDK is properly installed');
            $this->line('2. Verify merchant credentials');
            $this->line('3. Ensure network connectivity');
            $this->line('4. Check Laravel logs for detailed errors');
        }

        $this->info('🏁 Test completed.');
        return 0;
    }
}

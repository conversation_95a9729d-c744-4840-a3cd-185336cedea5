<?php

/**
 * PhonePe Laravel Service Test Script
 * 
 * This script tests the PhonePe integration following the official documentation:
 * https://developer.phonepe.com/v1/reference/php-sdk-introduction-standard-checkout
 */

require_once 'vendor/autoload.php';

use PhonePe\payments\v2\standardcheckout\StandardCheckoutClient;
use PhonePe\payments\v2\standardcheckout\request\StandardCheckoutPayRequestBuilder;
use PhonePe\Env;

echo "🔥 PhonePe Laravel Service Test\n";
echo "================================\n\n";

// Test Configuration
$merchantId = 'UATMERCHANT';
$saltKey = '8289e078-be0b-484d-ae60-052f117f8deb';
$saltIndex = 1;
$environment = Env::UAT; // Use Env::PRODUCTION for live environment

echo "📋 Configuration:\n";
echo "- Merchant ID: $merchantId\n";
echo "- Environment: UAT (Sandbox)\n";
echo "- Salt Index: $saltIndex\n\n";

try {
    echo "🚀 Step 1: Initialize PhonePe SDK Client\n";
    
    $client = StandardCheckoutClient::getInstance(
        $merchantId,
        $saltIndex,
        $saltKey,
        $environment
    );
    
    echo "✅ PhonePe SDK Client initialized successfully\n\n";
    
    echo "🚀 Step 2: Create Payment Request\n";
    
    $orderId = 'TEST_ORDER_' . time() . '_' . uniqid();
    $amount = 1000; // ₹10.00 in paise
    $redirectUrl = 'http://127.0.0.1:8005/phonepe/success';
    $message = "Test Payment for Order: $orderId";
    
    echo "- Order ID: $orderId\n";
    echo "- Amount: ₹" . ($amount / 100) . "\n";
    echo "- Redirect URL: $redirectUrl\n\n";
    
    $payRequest = StandardCheckoutPayRequestBuilder::builder()
        ->merchantOrderId($orderId)
        ->amount($amount)
        ->redirectUrl($redirectUrl)
        ->message($message)
        ->build();
    
    echo "✅ Payment request created successfully\n\n";
    
    echo "🚀 Step 3: Execute Payment Request\n";
    
    $payResponse = $client->pay($payRequest);
    
    echo "✅ Payment request executed\n\n";
    
    echo "📊 Payment Response:\n";
    echo json_encode($payResponse, JSON_PRETTY_PRINT) . "\n\n";
    
    // Check if payment initiation was successful
    if ($payResponse && isset($payResponse['success']) && $payResponse['success']) {
        $redirectUrl = $payResponse['data']['instrumentResponse']['redirectInfo']['url'] ?? null;
        
        if ($redirectUrl) {
            echo "✅ Payment initiated successfully!\n";
            echo "🔗 Payment URL: $redirectUrl\n\n";
            
            echo "🚀 Step 4: Test Payment Status Check\n";
            
            // Test status check
            $transactionId = $payResponse['data']['merchantTransactionId'] ?? $orderId;
            
            try {
                $statusResponse = $client->checkStatus($transactionId);
                echo "✅ Status check executed\n";
                echo "📊 Status Response:\n";
                echo json_encode($statusResponse, JSON_PRETTY_PRINT) . "\n\n";
            } catch (Exception $e) {
                echo "⚠️ Status check failed (expected for new transaction): " . $e->getMessage() . "\n\n";
            }
            
            echo "🎉 PhonePe Integration Test Completed Successfully!\n";
            echo "📝 Summary:\n";
            echo "- SDK Initialization: ✅ Success\n";
            echo "- Payment Request Creation: ✅ Success\n";
            echo "- Payment Initiation: ✅ Success\n";
            echo "- Payment URL Generated: ✅ Success\n";
            echo "- Status Check API: ✅ Available\n\n";
            
            echo "🌐 Next Steps:\n";
            echo "1. Open the payment URL in browser: $redirectUrl\n";
            echo "2. Complete the payment flow\n";
            echo "3. Check the callback handling at: $redirectUrl\n";
            echo "4. Test the Laravel web interface at: http://127.0.0.1:8005/phonepe/test\n\n";
            
        } else {
            echo "❌ Payment URL not found in response\n";
        }
    } else {
        echo "❌ Payment initiation failed\n";
        echo "📊 Error Response:\n";
        echo json_encode($payResponse, JSON_PRETTY_PRINT) . "\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test failed with exception:\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
    
    echo "🔧 Troubleshooting:\n";
    echo "1. Check if PhonePe SDK is properly installed\n";
    echo "2. Verify merchant credentials\n";
    echo "3. Ensure network connectivity\n";
    echo "4. Check Laravel logs for detailed errors\n";
}

echo "🏁 Test completed.\n";

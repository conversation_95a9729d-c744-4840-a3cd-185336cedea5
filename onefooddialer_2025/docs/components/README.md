# Component Documentation

This section provides documentation for the UI components used in the QuickServe frontend application.

## Table of Contents

- [UI Components](ui-components.md)
- [Layout Components](layout-components.md)
- [Microfrontend Components](microfrontend-components.md)
- [Form Components](form-components.md)
- [Data Display Components](data-display-components.md)
- [Feedback Components](feedback-components.md)
- [Navigation Components](navigation-components.md)

## Overview

The QuickServe frontend uses a component-based architecture with the following types of components:

- **UI Components**: Reusable UI components from the Shadcn UI library
- **Layout Components**: Components for page layout and structure
- **Microfrontend Components**: Components specific to each microfrontend
- **Form Components**: Components for form inputs and validation
- **Data Display Components**: Components for displaying data
- **Feedback Components**: Components for user feedback
- **Navigation Components**: Components for navigation

## Component Structure

Components are organized in the following directory structure:

```
src/components/
├── ui/                # Shadcn UI components
├── layout/            # Layout components
├── microfrontends/    # Microfrontend-specific components
│   ├── customer/      # Customer components
│   ├── payment/       # Payment components
│   ├── order/         # Order components
│   ├── kitchen/       # Kitchen components
│   └── delivery/      # Delivery components
├── forms/             # Form components
├── data-display/      # Data display components
├── feedback/          # Feedback components
└── navigation/        # Navigation components
```

## Component Guidelines

### 1. Component Naming

Components should be named using PascalCase and should be descriptive of their purpose.

```tsx
// Good
function CustomerList() { ... }

// Bad
function customerList() { ... }
function list() { ... }
```

### 2. Component Structure

Components should follow a consistent structure:

```tsx
// Imports
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from '@/lib/i18n/use-translations';
import { Button } from '@/components/ui/button';

// Types
interface CustomerListProps {
  initialPage?: number;
  perPage?: number;
}

// Component
export function CustomerList({ initialPage = 1, perPage = 10 }: CustomerListProps) {
  // Hooks
  const router = useRouter();
  const { t } = useTranslations();
  
  // State
  const [page, setPage] = useState(initialPage);
  
  // Effects
  useEffect(() => {
    // Effect logic
  }, [page]);
  
  // Event handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };
  
  // Render
  return (
    <div>
      {/* Component JSX */}
    </div>
  );
}
```

### 3. Props

Props should be typed using TypeScript interfaces and should have default values where appropriate.

```tsx
interface ButtonProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

export function Button({
  variant = 'default',
  size = 'md',
  disabled = false,
  children,
  onClick,
}: ButtonProps) {
  // Component logic
}
```

### 4. State Management

Components should use React hooks for state management. Complex state should be moved to Zustand stores.

```tsx
// Local state
const [isOpen, setIsOpen] = useState(false);

// Store state
const { customers, isLoading, error, fetchCustomers } = useCustomerStore();
```

### 5. Side Effects

Side effects should be handled using the `useEffect` hook.

```tsx
useEffect(() => {
  fetchCustomers({ page, per_page: perPage });
}, [fetchCustomers, page, perPage]);
```

### 6. Event Handlers

Event handlers should be defined as functions within the component.

```tsx
const handleSubmit = (e: React.FormEvent) => {
  e.preventDefault();
  // Handle form submission
};
```

### 7. Conditional Rendering

Conditional rendering should use the ternary operator or logical AND operator.

```tsx
// Ternary operator
{isLoading ? <Spinner /> : <CustomerTable customers={customers} />}

// Logical AND operator
{isLoading && <Spinner />}
```

### 8. Lists

Lists should use the `key` prop with a unique identifier.

```tsx
{customers.map((customer) => (
  <CustomerRow key={customer.id} customer={customer} />
))}
```

### 9. Styling

Components should use Tailwind CSS for styling.

```tsx
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow">
  <h2 className="text-xl font-bold">Customers</h2>
  <Button>Add Customer</Button>
</div>
```

### 10. Accessibility

Components should be accessible and should use appropriate ARIA attributes.

```tsx
<button
  aria-label="Close"
  aria-expanded={isOpen}
  onClick={() => setIsOpen(false)}
>
  <XIcon />
</button>
```

## Component Testing

Components should be tested using Jest and React Testing Library. Tests should be placed in a `__tests__` directory next to the component.

```
src/components/microfrontends/customer/
├── customer-list.tsx
├── __tests__/
│   └── customer-list.test.tsx
```

Example test:

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { CustomerList } from '../customer-list';
import { useCustomerStore } from '@/lib/store/customer-store';

// Mock the store
jest.mock('@/lib/store/customer-store');

describe('CustomerList', () => {
  beforeEach(() => {
    (useCustomerStore as jest.Mock).mockReturnValue({
      customers: [],
      isLoading: false,
      error: null,
      fetchCustomers: jest.fn(),
    });
  });

  it('renders the customer list', () => {
    render(<CustomerList />);
    expect(screen.getByText('Customers')).toBeInTheDocument();
  });

  it('shows loading state when isLoading is true', () => {
    (useCustomerStore as jest.Mock).mockReturnValue({
      customers: [],
      isLoading: true,
      error: null,
      fetchCustomers: jest.fn(),
    });

    render(<CustomerList />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows error message when there is an error', () => {
    (useCustomerStore as jest.Mock).mockReturnValue({
      customers: [],
      isLoading: false,
      error: 'Failed to fetch customers',
      fetchCustomers: jest.fn(),
    });

    render(<CustomerList />);
    expect(screen.getByText('Failed to fetch customers')).toBeInTheDocument();
  });

  it('calls fetchCustomers on mount', () => {
    const fetchCustomers = jest.fn();
    (useCustomerStore as jest.Mock).mockReturnValue({
      customers: [],
      isLoading: false,
      error: null,
      fetchCustomers,
    });

    render(<CustomerList />);
    expect(fetchCustomers).toHaveBeenCalledWith({ page: 1, per_page: 10 });
  });
});
```

## Further Reading

For more detailed information about specific components, please refer to the individual documentation files linked in the Table of Contents.

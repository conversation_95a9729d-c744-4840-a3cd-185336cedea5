# Frontend Integration Guide

## Overview

This guide provides comprehensive instructions for integrating Next.js microfrontends with the Laravel 12 microservices architecture through Kong API Gateway.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Authentication Flow](#authentication-flow)
3. [API Client Setup](#api-client-setup)
4. [Service Integration](#service-integration)
5. [Error Handling](#error-handling)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)

## Architecture Overview

### Microservices Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js       │    │   Kong API      │    │   Laravel 12    │
│   Frontend      │───▶│   Gateway       │───▶│   Microservices │
│   (Port 3000)   │    │   (Port 8000)   │    │   (Ports 8001+) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Service Endpoints

| Service | Port | Base Path | Description |
|---------|------|-----------|-------------|
| Kong Gateway | 8000 | `/v2/*` | API Gateway and routing |
| Auth Service | 8001 | `/v2/auth` | Authentication and authorization |
| Customer Service | 8002 | `/v2/customers` | Customer management |
| Payment Service | 8003 | `/v2/payments` | Payment processing |
| QuickServe Service | 8004 | `/v2/orders` | Order management |
| Kitchen Service | 8005 | `/v2/kitchens` | Kitchen operations |
| Delivery Service | 8006 | `/v2/delivery` | Delivery tracking |
| Analytics Service | 8007 | `/v2/analytics` | Analytics and reporting |

## Authentication Flow

### 1. JWT Token-Based Authentication

All API requests require a valid JWT token in the Authorization header:

```typescript
headers: {
  'Authorization': `Bearer ${accessToken}`,
  'Content-Type': 'application/json'
}
```

### 2. Token Refresh Flow

```typescript
// Example token refresh implementation
async function refreshToken(refreshToken: string): Promise<TokenResponse> {
  const response = await fetch('http://localhost:8000/v2/auth/refresh-token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      refresh_token: refreshToken
    })
  });

  if (!response.ok) {
    throw new Error('Token refresh failed');
  }

  return response.json();
}
```

### 3. MFA Integration

```typescript
// Request MFA OTP
async function requestMfaOtp(method: 'email' | 'sms'): Promise<void> {
  const response = await fetch('http://localhost:8000/v2/auth/mfa/request', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ method })
  });

  if (!response.ok) {
    throw new Error('MFA OTP request failed');
  }
}

// Verify MFA OTP
async function verifyMfaOtp(otp: string): Promise<void> {
  const response = await fetch('http://localhost:8000/v2/auth/mfa/verify', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ otp })
  });

  if (!response.ok) {
    throw new Error('MFA OTP verification failed');
  }
}
```

## API Client Setup

### 1. Base API Client

```typescript
// lib/api-client.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

class ApiClient {
  private client: AxiosInstance;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor(baseURL: string = 'http://localhost:8000') {
    this.client = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await this.refreshAccessToken();
            return this.client(originalRequest);
          } catch (refreshError) {
            this.logout();
            throw refreshError;
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.client.post('/v2/auth/refresh-token', {
      refresh_token: this.refreshToken
    });

    const { access_token, refresh_token } = response.data.data;
    this.setTokens(access_token, refresh_token);
  }

  public setTokens(accessToken: string, refreshToken: string): void {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    
    // Store in localStorage for persistence
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
  }

  public logout(): void {
    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  public async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  public async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

### 2. Service-Specific Clients

```typescript
// services/auth-service.ts
import { apiClient } from '../lib/api-client';

export interface LoginRequest {
  identifier: string;
  password: string;
  remember_me?: boolean;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
  };
}

export class AuthService {
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return apiClient.post<LoginResponse>('/v2/auth/login', credentials);
  }

  async logout(): Promise<void> {
    await apiClient.post('/v2/auth/logout');
    apiClient.logout();
  }

  async getUser(): Promise<User> {
    const response = await apiClient.get<{ data: User }>('/v2/auth/user');
    return response.data;
  }

  async requestMfaOtp(method: 'email' | 'sms'): Promise<void> {
    await apiClient.post('/v2/auth/mfa/request', { method });
  }

  async verifyMfaOtp(otp: string): Promise<void> {
    await apiClient.post('/v2/auth/mfa/verify', { otp });
  }
}

export const authService = new AuthService();
```

## Service Integration

### 1. Customer Service Integration

```typescript
// services/customer-service.ts
import { apiClient } from '../lib/api-client';

export interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  customer_code: string;
  status: 'active' | 'inactive' | 'suspended';
  created_at: string;
  updated_at: string;
}

export class CustomerService {
  async getCustomers(page: number = 1, perPage: number = 15): Promise<PaginatedResponse<Customer>> {
    return apiClient.get<PaginatedResponse<Customer>>(`/v2/customers?page=${page}&per_page=${perPage}`);
  }

  async getCustomer(id: number): Promise<Customer> {
    const response = await apiClient.get<{ data: Customer }>(`/v2/customers/${id}`);
    return response.data;
  }

  async createCustomer(customer: CreateCustomerRequest): Promise<Customer> {
    const response = await apiClient.post<{ data: Customer }>('/v2/customers', customer);
    return response.data;
  }

  async updateCustomer(id: number, customer: UpdateCustomerRequest): Promise<Customer> {
    const response = await apiClient.put<{ data: Customer }>(`/v2/customers/${id}`, customer);
    return response.data;
  }

  async deleteCustomer(id: number): Promise<void> {
    await apiClient.delete(`/v2/customers/${id}`);
  }

  async searchCustomers(query: string): Promise<Customer[]> {
    const response = await apiClient.get<{ data: { data: Customer[] } }>(`/v2/customers/search?query=${encodeURIComponent(query)}`);
    return response.data.data;
  }

  async lookupCustomer(criteria: { phone?: string; email?: string; code?: string }): Promise<Customer | null> {
    const response = await apiClient.post<{ data: Customer | null; found: boolean }>('/v2/customers/lookup', criteria);
    return response.data;
  }
}

export const customerService = new CustomerService();
```

### 2. Order Service Integration

```typescript
// services/order-service.ts
import { apiClient } from '../lib/api-client';

export interface Order {
  id: number;
  customer_id: number;
  status: string;
  total_amount: number;
  items: OrderItem[];
  created_at: string;
  updated_at: string;
}

export class OrderService {
  async getOrders(page: number = 1): Promise<PaginatedResponse<Order>> {
    return apiClient.get<PaginatedResponse<Order>>(`/v2/orders?page=${page}`);
  }

  async getOrder(id: number): Promise<Order> {
    const response = await apiClient.get<{ data: Order }>(`/v2/orders/${id}`);
    return response.data;
  }

  async createOrder(order: CreateOrderRequest): Promise<Order> {
    const response = await apiClient.post<{ data: Order }>('/v2/orders', order);
    return response.data;
  }

  async updateOrderStatus(id: number, status: string): Promise<Order> {
    const response = await apiClient.put<{ data: Order }>(`/v2/orders/${id}/status`, { status });
    return response.data;
  }

  async assignOrder(id: number, assigneeId: number): Promise<void> {
    await apiClient.post(`/v2/orders/${id}/assign`, { assignee_id: assigneeId });
  }

  async startPreparation(id: number): Promise<void> {
    await apiClient.post(`/v2/orders/${id}/start-preparation`);
  }

  async markReady(id: number): Promise<void> {
    await apiClient.post(`/v2/orders/${id}/ready`);
  }

  async completeOrder(id: number): Promise<void> {
    await apiClient.post(`/v2/orders/${id}/complete`);
  }
}

export const orderService = new OrderService();
```

## Error Handling

### 1. Standardized Error Response

All services return errors in a consistent format:

```typescript
interface ApiError {
  success: false;
  message: string;
  error_code?: string;
  errors?: Record<string, string[]>; // For validation errors
}
```

### 2. Error Handling Utility

```typescript
// utils/error-handler.ts
export class ApiError extends Error {
  public statusCode: number;
  public errorCode?: string;
  public validationErrors?: Record<string, string[]>;

  constructor(
    message: string,
    statusCode: number,
    errorCode?: string,
    validationErrors?: Record<string, string[]>
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.validationErrors = validationErrors;
  }
}

export function handleApiError(error: any): ApiError {
  if (error.response) {
    const { status, data } = error.response;
    return new ApiError(
      data.message || 'An error occurred',
      status,
      data.error_code,
      data.errors
    );
  }

  if (error.request) {
    return new ApiError('Network error - please check your connection', 0);
  }

  return new ApiError(error.message || 'An unexpected error occurred', 0);
}
```

### 3. React Error Boundary

```typescript
// components/ErrorBoundary.tsx
import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('API Error:', error, errorInfo);
    // Log to monitoring service
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>{this.state.error?.message}</p>
          <button onClick={() => this.setState({ hasError: false })}>
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## Best Practices

### 1. Request/Response Patterns

- Always use TypeScript interfaces for request/response types
- Implement proper error handling for all API calls
- Use consistent naming conventions across services
- Implement request/response logging for debugging

### 2. Performance Optimization

```typescript
// Implement request caching
import { QueryClient, useQuery } from '@tanstack/react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

// Use in components
function CustomerList() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['customers'],
    queryFn: () => customerService.getCustomers(),
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {data?.data.map(customer => (
        <div key={customer.id}>{customer.name}</div>
      ))}
    </div>
  );
}
```

### 3. Security Considerations

- Never store sensitive data in localStorage
- Implement proper CSRF protection
- Use HTTPS in production
- Validate all user inputs
- Implement rate limiting on the frontend

## Troubleshooting

### Common Issues

1. **401 Unauthorized Errors**
   - Check if access token is valid
   - Verify token refresh logic
   - Ensure proper Authorization header format

2. **CORS Issues**
   - Verify Kong CORS configuration
   - Check allowed origins and methods
   - Ensure preflight requests are handled

3. **Network Timeouts**
   - Increase timeout values for slow operations
   - Implement retry logic with exponential backoff
   - Check network connectivity

4. **Validation Errors**
   - Review API documentation for required fields
   - Implement client-side validation
   - Handle server validation errors gracefully

### Debug Tools

```typescript
// Enable debug logging
if (process.env.NODE_ENV === 'development') {
  apiClient.interceptors.request.use(request => {
    console.log('API Request:', request);
    return request;
  });

  apiClient.interceptors.response.use(
    response => {
      console.log('API Response:', response);
      return response;
    },
    error => {
      console.error('API Error:', error);
      return Promise.reject(error);
    }
  );
}
```

For more detailed information, refer to the [OpenAPI Documentation](./swagger-ui/index.html) and individual service documentation.

# OneFoodDialer 2025 - Theme System Quick Start Guide

## **🚀 Quick Start**

This guide helps developers quickly understand and work with the OneFoodDialer 2025 theme system.

## **📦 What's Included**

- ✅ **8 Pre-built Themes** (Default, Blue, Green, Amber, Scaled variants, Mono, School Tiffin)
- ✅ **TypeScript Integration** (Full type safety)
- ✅ **Setup Wizard Integration** (Step 3 of 8)
- ✅ **Real-time Preview** (Instant theme switching)
- ✅ **Dark Mode Support** (All themes)
- ✅ **Responsive Design** (Mobile & desktop)

## **⚡ 5-Minute Setup**

### **1. Use Existing Themes**

```tsx
import { useThemeConfig } from '@/components/active-theme';

function MyComponent() {
  const { activeTheme, setActiveTheme } = useThemeConfig();
  
  return (
    <div>
      <p>Current theme: {activeTheme}</p>
      <button onClick={() => setActiveTheme('school-tiffin')}>
        Switch to School Tiffin Theme
      </button>
    </div>
  );
}
```

### **2. Add Theme Selector**

```tsx
import { ThemeSelector } from '@/components/theme-selector';

function Settings() {
  return (
    <div>
      <h2>Choose Theme</h2>
      <ThemeSelector />
    </div>
  );
}
```

### **3. Use Theme-Aware Styling**

```css
.my-component {
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: 1px solid var(--border);
}

/* School Tiffin specific styling */
.theme-school-tiffin .my-component {
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}
```

## **🎨 Available Themes**

| **Theme** | **Use Case** | **Primary Color** |
|-----------|--------------|-------------------|
| `default` | General purpose | Neutral Gray |
| `blue` | Corporate | Blue |
| `green` | Eco-friendly | Lime Green |
| `amber` | Warm/welcoming | Amber |
| `school-tiffin` | **School meals** | **Healthy Green** |
| `default-scaled` | Large screens | Neutral Gray |
| `blue-scaled` | Large screens | Blue |
| `mono-scaled` | Developers | Neutral Gray |

## **🏫 School Tiffin Theme**

### **When to Use**
- School meal services
- Child-focused applications
- Parent-facing interfaces
- Educational food programs

### **Key Features**
```css
/* Colors */
--primary: #4CAF50;      /* Healthy Green */
--secondary: #FFC107;    /* Energetic Yellow */
--accent: #FF5722;       /* Warm Orange */
--background: #FFF8E1;   /* Light Cream */

/* Typography */
--font-sans: 'Poppins', 'Helvetica', 'Arial';

/* Special Components */
.school-tiffin-card     /* Gradient cards */
.school-tiffin-button   /* Interactive buttons */
```

### **Usage Example**
```tsx
function SchoolDashboard() {
  const { setActiveTheme } = useThemeConfig();
  
  useEffect(() => {
    // Auto-apply School Tiffin theme for school tenants
    if (tenantType === 'school') {
      setActiveTheme('school-tiffin');
    }
  }, [tenantType]);

  return (
    <div className="school-tiffin-card">
      <h1>School Meal Dashboard</h1>
      <button className="school-tiffin-button">
        Order Lunch
      </button>
    </div>
  );
}
```

## **🛠️ Common Tasks**

### **Get Current Theme**
```tsx
const { activeTheme } = useThemeConfig();
console.log('Current theme:', activeTheme); // 'school-tiffin'
```

### **Switch Theme Programmatically**
```tsx
const { setActiveTheme } = useThemeConfig();
setActiveTheme('school-tiffin');
```

### **Get Theme Information**
```tsx
import { getThemeByName, AvailableThemes } from '@/themes';

const schoolTheme = getThemeByName('school-tiffin');
console.log(schoolTheme?.displayName); // 'School Tiffin'
console.log(schoolTheme?.colors.primary); // '#4CAF50'

// Or access directly
const allThemes = AvailableThemes;
```

### **Check if Theme is Active**
```tsx
const { activeTheme } = useThemeConfig();
const isSchoolTheme = activeTheme === 'school-tiffin';

return (
  <div className={isSchoolTheme ? 'school-layout' : 'default-layout'}>
    {/* Content */}
  </div>
);
```

## **🎯 Setup Wizard Integration**

### **Theme Selection Step**
The theme selection is automatically included in the setup wizard as Step 3:

```
1. Company Profile
2. System Settings
3. Theme Selection ← Automatically included
4. Payment Gateways
5. Menu Setup
6. Subscription Plan
7. Team Invitations
8. Complete Setup
```

### **Access Theme Selection Page**
```
URL: /admin-service-v12/setupThemeSelection
```

### **Customize Theme Selection**
```tsx
import { ThemeSelectionForm } from '@/components/setup-wizard/theme-selection-form';

function CustomSetup() {
  const handleThemeSelected = (theme: string) => {
    console.log('Theme selected:', theme);
    // Custom logic here
  };

  return (
    <ThemeSelectionForm
      onSuccess={() => router.push('/next-step')}
      onBack={() => router.back()}
      onCancel={() => router.push('/dashboard')}
    />
  );
}
```

## **🔧 Customization**

### **Override Theme Colors**
```css
/* Custom CSS for specific components */
.theme-school-tiffin .my-custom-component {
  --primary: #YOUR_CUSTOM_COLOR;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}
```

### **Add Custom Theme Classes**
```css
/* Add to theme.css */
.theme-school-tiffin .meal-card {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  border: 2px solid #4CAF50;
  border-radius: 0.5rem;
  transition: transform 0.2s ease;
}

.theme-school-tiffin .meal-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}
```

## **📱 Responsive Considerations**

### **Mobile-First Approach**
```css
.theme-school-tiffin .mobile-menu {
  /* Mobile styles */
  padding: 1rem;
}

@media (min-width: 768px) {
  .theme-school-tiffin .mobile-menu {
    /* Tablet styles */
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-school-tiffin .mobile-menu {
    /* Desktop styles */
    padding: 2rem;
  }
}
```

## **🧪 Testing Themes**

### **Test Theme Switching**
```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ActiveThemeProvider } from '@/components/active-theme';

test('switches to School Tiffin theme', () => {
  render(
    <ActiveThemeProvider>
      <ThemeSelector onSelect={jest.fn()} />
    </ActiveThemeProvider>
  );
  
  fireEvent.click(screen.getByText('School Tiffin'));
  expect(document.body.classList.contains('theme-school-tiffin')).toBe(true);
});
```

## **⚠️ Common Pitfalls**

### **1. Theme Not Applying**
```tsx
// ❌ Wrong - Missing theme provider
function App() {
  return <MyComponent />;
}

// ✅ Correct - Wrap with theme provider
function App() {
  return (
    <ActiveThemeProvider>
      <MyComponent />
    </ActiveThemeProvider>
  );
}
```

### **2. CSS Variables Not Working**
```css
/* ❌ Wrong - Hard-coded colors */
.my-component {
  background-color: #4CAF50;
}

/* ✅ Correct - Use CSS variables */
.my-component {
  background-color: var(--primary);
}
```

### **3. Theme Not Persisting**
```tsx
// ✅ Theme persistence is handled automatically
// The ActiveThemeProvider saves to cookies
// No additional code needed
```

## **📚 Additional Resources**

- **Full Documentation**: `docs/theme-system-documentation.md`
- **Theme Audit Report**: `docs/theme-audit-report.md`
- **Component Examples**: `src/components/setup-wizard/`
- **CSS Reference**: `src/app/theme.css`
- **TypeScript Types**: `src/themes/index.ts`

## **🆘 Need Help?**

### **Common Questions**

**Q: How do I add a new theme?**
A: See the "Adding a New Theme" section in the full documentation.

**Q: Can I customize the School Tiffin theme?**
A: Yes, override CSS variables or add custom classes.

**Q: How do I test themes?**
A: Use the theme selector in development or the setup wizard.

**Q: Are themes accessible?**
A: Yes, all themes meet WCAG accessibility standards.

### **Support**
- Check the full documentation first
- Look at existing component examples
- Test in the setup wizard
- Ask the development team for complex customizations

---

**Quick Start Guide Version**: 1.0  
**For**: OneFoodDialer 2025 Theme System  
**Last Updated**: December 2024

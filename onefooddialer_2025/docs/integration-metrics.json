{"metrics": {"totalLaravelRoutes": 584, "totalFrontendCalls": 214, "connectedEndpoints": 24, "frontendUnboundCalls": 158, "backendOrphanedRoutes": 556, "integrationCoverage": 4.1, "lastUpdated": "2025-05-23T05:31:21.747Z"}, "recentImplementations": [{"endpoint": "POST /v2/auth/register", "service": "auth-service-v12", "status": "completed", "implementedDate": "2025-05-22", "responseTime": "5-8ms", "testCoverage": "100%", "frontends": ["unified-frontend", "frontend-shadcn"]}], "nextPriorities": [{"ticketId": "FE-UNBOUND-014", "endpoint": "POST /v2/auth/forgot-password", "priority": "Critical", "estimatedEffort": "Low", "businessImpact": "Critical - Password recovery"}, {"ticketId": "FE-UNBOUND-015", "endpoint": "POST /v2/auth/reset-password", "priority": "Critical", "estimatedEffort": "Low", "businessImpact": "Critical - Password recovery"}, {"ticketId": "FE-UNBOUND-016", "endpoint": "POST /v2/auth/verify-email", "priority": "Critical", "estimatedEffort": "Medium", "businessImpact": "Critical - Email verification"}, {"ticketId": "FE-UNBOUND-020", "endpoint": "GET /v2/auth/user", "priority": "Critical", "estimatedEffort": "Low", "businessImpact": "Critical - User profile"}], "serviceBreakdown": [{"service": "Authentication", "totalRoutes": 47, "connected": 8, "coverage": 17, "priority": "Critical"}, {"service": "Customer Management", "totalRoutes": 89, "connected": 3, "coverage": 3.4, "priority": "High"}, {"service": "Order Processing", "totalRoutes": 156, "connected": 4, "coverage": 2.6, "priority": "High"}, {"service": "Payment Processing", "totalRoutes": 78, "connected": 2, "coverage": 2.6, "priority": "High"}, {"service": "Kitchen Operations", "totalRoutes": 45, "connected": 2, "coverage": 4.4, "priority": "Medium"}, {"service": "Delivery Management", "totalRoutes": 67, "connected": 3, "coverage": 4.5, "priority": "Medium"}, {"service": "Analytics & Reporting", "totalRoutes": 52, "connected": 1, "coverage": 1.9, "priority": "Medium"}, {"service": "Administrative", "totalRoutes": 34, "connected": 1, "coverage": 2.9, "priority": "Low"}, {"service": "Notification System", "totalRoutes": 16, "connected": 0, "coverage": 0, "priority": "Low"}], "generatedAt": "2025-05-23T05:31:22.100Z"}
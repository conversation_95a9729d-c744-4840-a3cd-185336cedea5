<?php

namespace App\Repositories\Interfaces;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Interface ProductRepositoryInterface
 * 
 * This interface defines the contract for product repository operations.
 */
interface ProductRepositoryInterface
{
    /**
     * Get all products with optional filtering.
     *
     * @param  int  $perPage
     * @param  int|null  $categoryId
     * @param  string|null  $search
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAllProducts(int $perPage = 15, ?int $categoryId = null, ?string $search = null): LengthAwarePaginator;

    /**
     * Find a product by ID.
     *
     * @param  int  $id
     * @return \App\Models\Product|null
     */
    public function findById(int $id): ?Product;

    /**
     * Create a new product.
     *
     * @param  array  $data
     * @return \App\Models\Product
     */
    public function create(array $data): Product;

    /**
     * Update an existing product.
     *
     * @param  \App\Models\Product  $product
     * @param  array  $data
     * @return \App\Models\Product
     */
    public function update(Product $product, array $data): Product;

    /**
     * Delete a product.
     *
     * @param  \App\Models\Product  $product
     * @return bool
     */
    public function delete(Product $product): bool;

    /**
     * Get products by category.
     *
     * @param  int  $categoryId
     * @param  int  $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getProductsByCategory(int $categoryId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Search products by name or description.
     *
     * @param  string  $query
     * @param  int  $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function searchProducts(string $query, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get featured products.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFeaturedProducts(int $limit = 10): Collection;

    /**
     * Get related products.
     *
     * @param  \App\Models\Product  $product
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRelatedProducts(Product $product, int $limit = 4): Collection;

    /**
     * Get products by IDs.
     *
     * @param  array  $ids
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProductsByIds(array $ids): Collection;

    /**
     * Get products by price range.
     *
     * @param  float  $minPrice
     * @param  float  $maxPrice
     * @param  int  $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getProductsByPriceRange(float $minPrice, float $maxPrice, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get latest products.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLatestProducts(int $limit = 10): Collection;

    /**
     * Get popular products based on order count.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPopularProducts(int $limit = 10): Collection;
}

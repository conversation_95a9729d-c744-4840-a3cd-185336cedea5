# OneFoodDialer 2025 - Next.js Frontend Developer Guide

## Overview

This comprehensive guide provides step-by-step instructions for Next.js developers working on the OneFoodDialer 2025 frontend application. The guide covers microfrontend architecture, component development, API integration, and deployment best practices.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Architecture Overview](#architecture-overview)
3. [Development Environment Setup](#development-environment-setup)
4. [Microfrontend Architecture](#microfrontend-architecture)
5. [Component Development](#component-development)
6. [API Integration](#api-integration)
7. [Authentication Integration](#authentication-integration)
8. [TypeScript Implementation](#typescript-implementation)
9. [Testing Strategy](#testing-strategy)
10. [Build Optimization](#build-optimization)
11. [Deployment Process](#deployment-process)
12. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Software
- **Node.js**: 18.0 or higher
- **npm**: 9.0+ or **yarn**: 1.22+
- **Git**: Latest version
- **Docker**: 20.10+ (for local development)
- **VS Code**: Recommended IDE

### Required Knowledge
- Next.js 15 with App Router
- React 18+ with hooks
- TypeScript fundamentals
- Tailwind CSS
- API integration patterns
- Testing with Jest and React Testing Library

### Development Tools Setup

```bash
# Install Node.js 18+ (using nvm)
nvm install 18
nvm use 18

# Verify installations
node --version  # Should be 18.0+
npm --version   # Should be 9.0+

# Install global tools
npm install -g @storybook/cli
npm install -g typescript
npm install -g eslint

# Verify tools
tsc --version
eslint --version
```

## Architecture Overview

### Frontend Structure

The OneFoodDialer 2025 frontend follows a microfrontend architecture pattern:

```
frontend-shadcn/
├── src/
│   ├── app/
│   │   ├── (microfrontend-v2)/          # Microfrontend pages
│   │   │   ├── auth/                    # Authentication pages
│   │   │   ├── customer/                # Customer management
│   │   │   ├── payment/                 # Payment processing
│   │   │   ├── quickserve/              # Core business logic
│   │   │   ├── kitchen/                 # Kitchen operations
│   │   │   ├── delivery/                # Delivery management
│   │   │   ├── analytics/               # Analytics & reporting
│   │   │   ├── admin/                   # Admin operations
│   │   │   ├── notification/            # Notifications
│   │   │   ├── catalogue/               # Product catalog
│   │   │   ├── subscription/            # Subscription management
│   │   │   ├── meal/                    # Meal planning
│   │   │   └── misscall/                # Missed call handling
│   │   ├── globals.css                  # Global styles
│   │   ├── layout.tsx                   # Root layout
│   │   └── page.tsx                     # Home page
│   ├── components/
│   │   ├── ui/                          # shadcn/ui components
│   │   ├── microfrontends/              # Microfrontend components
│   │   ├── common/                      # Shared components
│   │   └── layouts/                     # Layout components
│   ├── hooks/                           # Custom React hooks
│   ├── services/                        # API service modules
│   ├── types/                           # TypeScript type definitions
│   ├── utils/                           # Utility functions
│   └── lib/                             # Library configurations
├── public/                              # Static assets
├── .storybook/                          # Storybook configuration
├── tests/                               # Test files
├── docs/                                # Documentation
└── package.json                         # Dependencies
```

### Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript 5.0+
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: React Query 5 + Zustand
- **Forms**: React Hook Form + Zod validation
- **Testing**: Jest + React Testing Library + Storybook
- **Icons**: Lucide React
- **Authentication**: Keycloak integration
- **API Client**: Axios with React Query

## Development Environment Setup

### 1. Clone and Setup Repository

```bash
# Clone the repository
git clone https://gitrepo.futurescapetech.com/developers/onefooddialer_2025.git
cd onefooddialer_2025

# Navigate to frontend directory
cd frontend-shadcn

# Install dependencies
npm install

# Copy environment file
cp .env.example .env.local
```

### 2. Environment Configuration

Update `.env.local`:

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/v2
NEXT_PUBLIC_API_TIMEOUT=10000

# Keycloak Configuration
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080/auth/realms/demo
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=oneapp
NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI=http://localhost:3000/auth/callback

# Application Configuration
NEXT_PUBLIC_APP_NAME=OneFoodDialer 2025
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_DARK_MODE=true

# Development Tools
NEXT_PUBLIC_ENABLE_DEVTOOLS=true
NEXT_PUBLIC_ENABLE_STORYBOOK=true
```

### 3. Start Development Server

```bash
# Start the development server
npm run dev

# Start with specific port
npm run dev -- --port 3001

# Start with turbo mode
npm run dev:turbo
```

### 4. Verify Setup

```bash
# Check if server is running
curl http://localhost:3000

# Run health check
npm run health-check

# Validate environment
npm run validate-env
```

## Microfrontend Architecture

### 1. Microfrontend Structure

Each microfrontend follows a consistent structure:

```
src/app/(microfrontend-v2)/[service]/
├── page.tsx                    # Main page component
├── layout.tsx                  # Service-specific layout
├── loading.tsx                 # Loading component
├── error.tsx                   # Error boundary
├── not-found.tsx              # 404 page
├── components/                 # Service-specific components
│   ├── [ComponentName]/
│   │   ├── index.tsx
│   │   ├── [ComponentName].tsx
│   │   ├── [ComponentName].test.tsx
│   │   ├── [ComponentName].stories.tsx
│   │   └── types.ts
├── hooks/                      # Service-specific hooks
├── types/                      # Service-specific types
└── utils/                      # Service-specific utilities
```

### 2. MicrofrontendLayout Component

Create a shared layout for all microfrontends:

```tsx
// src/components/layouts/MicrofrontendLayout.tsx
import React from 'react';
import { Sidebar } from '@/components/common/Sidebar';
import { Header } from '@/components/common/Header';
import { Breadcrumbs } from '@/components/common/Breadcrumbs';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';

interface MicrofrontendLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  breadcrumbs?: Array<{ label: string; href?: string }>;
  actions?: React.ReactNode;
}

export function MicrofrontendLayout({
  children,
  title,
  description,
  breadcrumbs,
  actions
}: MicrofrontendLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6">
          <div className="mb-6">
            {breadcrumbs && <Breadcrumbs items={breadcrumbs} />}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                {description && (
                  <p className="mt-1 text-sm text-gray-600">{description}</p>
                )}
              </div>
              {actions && <div className="flex gap-2">{actions}</div>}
            </div>
          </div>
          <ErrorBoundary>
            {children}
          </ErrorBoundary>
        </main>
      </div>
    </div>
  );
}
```

### 3. Service Page Implementation

Example implementation for QuickServe service:

```tsx
// src/app/(microfrontend-v2)/quickserve/page.tsx
import { MicrofrontendLayout } from '@/components/layouts/MicrofrontendLayout';
import { OrdersOverview } from './components/OrdersOverview';
import { QuickActions } from './components/QuickActions';
import { RecentActivity } from './components/RecentActivity';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

export default function QuickServePage() {
  const breadcrumbs = [
    { label: 'Dashboard', href: '/' },
    { label: 'QuickServe' }
  ];

  const actions = (
    <Button>
      <Plus className="w-4 h-4 mr-2" />
      New Order
    </Button>
  );

  return (
    <MicrofrontendLayout
      title="QuickServe Dashboard"
      description="Manage orders, track performance, and monitor operations"
      breadcrumbs={breadcrumbs}
      actions={actions}
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <OrdersOverview />
        </div>
        <div className="space-y-6">
          <QuickActions />
          <RecentActivity />
        </div>
      </div>
    </MicrofrontendLayout>
  );
}
```

### 4. Service Layout Configuration

```tsx
// src/app/(microfrontend-v2)/quickserve/layout.tsx
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'QuickServe - OneFoodDialer 2025',
  description: 'Manage orders and track performance',
};

export default function QuickServeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="quickserve-layout">
      {children}
    </div>
  );
}
```

## Component Development

### 1. Component Structure with shadcn/ui

Follow the established pattern for creating components:

```tsx
// src/components/microfrontends/quickserve/OrderCard/OrderCard.tsx
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, MapPin, User } from 'lucide-react';
import { Order } from '@/types/quickserve';
import { formatCurrency, formatTime } from '@/utils/format';

interface OrderCardProps {
  order: Order;
  onViewDetails: (orderId: string) => void;
  onUpdateStatus: (orderId: string, status: string) => void;
}

export function OrderCard({ order, onViewDetails, onUpdateStatus }: OrderCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'preparing': return 'bg-orange-100 text-orange-800';
      case 'ready': return 'bg-green-100 text-green-800';
      case 'delivered': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Order #{order.id}</CardTitle>
          <Badge className={getStatusColor(order.status)}>
            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center text-sm text-gray-600">
            <User className="w-4 h-4 mr-2" />
            {order.customer.name}
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <MapPin className="w-4 h-4 mr-2" />
            {order.deliveryAddress}
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Clock className="w-4 h-4 mr-2" />
            {formatTime(order.estimatedDelivery)}
          </div>
          <div className="flex items-center justify-between pt-3 border-t">
            <span className="font-semibold text-lg">
              {formatCurrency(order.totalAmount)}
            </span>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails(order.id)}
              >
                View Details
              </Button>
              {order.status === 'pending' && (
                <Button
                  size="sm"
                  onClick={() => onUpdateStatus(order.id, 'confirmed')}
                >
                  Confirm
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

### 2. Component Testing

```tsx
// src/components/microfrontends/quickserve/OrderCard/OrderCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { OrderCard } from './OrderCard';
import { mockOrder } from '@/tests/mocks/quickserve';

describe('OrderCard', () => {
  const mockProps = {
    order: mockOrder,
    onViewDetails: jest.fn(),
    onUpdateStatus: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders order information correctly', () => {
    render(<OrderCard {...mockProps} />);

    expect(screen.getByText(`Order #${mockOrder.id}`)).toBeInTheDocument();
    expect(screen.getByText(mockOrder.customer.name)).toBeInTheDocument();
    expect(screen.getByText(mockOrder.deliveryAddress)).toBeInTheDocument();
  });

  it('calls onViewDetails when View Details button is clicked', () => {
    render(<OrderCard {...mockProps} />);

    fireEvent.click(screen.getByText('View Details'));
    expect(mockProps.onViewDetails).toHaveBeenCalledWith(mockOrder.id);
  });

  it('shows confirm button for pending orders', () => {
    const pendingOrder = { ...mockOrder, status: 'pending' };
    render(<OrderCard {...mockProps} order={pendingOrder} />);

    expect(screen.getByText('Confirm')).toBeInTheDocument();
  });

  it('calls onUpdateStatus when Confirm button is clicked', () => {
    const pendingOrder = { ...mockOrder, status: 'pending' };
    render(<OrderCard {...mockProps} order={pendingOrder} />);

    fireEvent.click(screen.getByText('Confirm'));
    expect(mockProps.onUpdateStatus).toHaveBeenCalledWith(mockOrder.id, 'confirmed');
  });
});
```

### 3. Storybook Stories

```tsx
// src/components/microfrontends/quickserve/OrderCard/OrderCard.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { OrderCard } from './OrderCard';
import { mockOrder } from '@/tests/mocks/quickserve';

const meta: Meta<typeof OrderCard> = {
  title: 'Microfrontends/QuickServe/OrderCard',
  component: OrderCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onViewDetails: { action: 'view details clicked' },
    onUpdateStatus: { action: 'status updated' },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Pending: Story = {
  args: {
    order: { ...mockOrder, status: 'pending' },
  },
};

export const Confirmed: Story = {
  args: {
    order: { ...mockOrder, status: 'confirmed' },
  },
};

export const Preparing: Story = {
  args: {
    order: { ...mockOrder, status: 'preparing' },
  },
};

export const Ready: Story = {
  args: {
    order: { ...mockOrder, status: 'ready' },
  },
};

export const Delivered: Story = {
  args: {
    order: { ...mockOrder, status: 'delivered' },
  },
};

export const Cancelled: Story = {
  args: {
    order: { ...mockOrder, status: 'cancelled' },
  },
};
```

## API Integration

### 1. Service Layer Architecture

Create service modules for each microservice:

```tsx
// src/services/quickserve-service.ts
import { apiClient } from '@/lib/api-client';
import { Order, CreateOrderRequest, UpdateOrderRequest } from '@/types/quickserve';

export class QuickServeService {
  private baseUrl = '/quickserve';

  async getOrders(params?: {
    page?: number;
    limit?: number;
    status?: string;
    customerId?: string;
  }): Promise<{ data: Order[]; meta: any }> {
    const response = await apiClient.get(`${this.baseUrl}/orders`, { params });
    return response.data;
  }

  async getOrder(id: string): Promise<Order> {
    const response = await apiClient.get(`${this.baseUrl}/orders/${id}`);
    return response.data.data;
  }

  async createOrder(data: CreateOrderRequest): Promise<Order> {
    const response = await apiClient.post(`${this.baseUrl}/orders`, data);
    return response.data.data;
  }

  async updateOrder(id: string, data: UpdateOrderRequest): Promise<Order> {
    const response = await apiClient.patch(`${this.baseUrl}/orders/${id}`, data);
    return response.data.data;
  }

  async updateOrderStatus(id: string, status: string): Promise<Order> {
    const response = await apiClient.patch(`${this.baseUrl}/orders/${id}/status`, { status });
    return response.data.data;
  }

  async deleteOrder(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/orders/${id}`);
  }

  async getOrderStats(): Promise<any> {
    const response = await apiClient.get(`${this.baseUrl}/stats`);
    return response.data.data;
  }
}

export const quickServeService = new QuickServeService();
```

### 2. React Query Hooks

```tsx
// src/hooks/useQuickServe.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { quickServeService } from '@/services/quickserve-service';
import { Order, CreateOrderRequest, UpdateOrderRequest } from '@/types/quickserve';
import { toast } from '@/hooks/use-toast';

export const QUICKSERVE_QUERY_KEYS = {
  orders: ['quickserve', 'orders'] as const,
  order: (id: string) => ['quickserve', 'orders', id] as const,
  stats: ['quickserve', 'stats'] as const,
};

export function useOrders(params?: {
  page?: number;
  limit?: number;
  status?: string;
  customerId?: string;
}) {
  return useQuery({
    queryKey: [...QUICKSERVE_QUERY_KEYS.orders, params],
    queryFn: () => quickServeService.getOrders(params),
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });
}

export function useOrder(id: string) {
  return useQuery({
    queryKey: QUICKSERVE_QUERY_KEYS.order(id),
    queryFn: () => quickServeService.getOrder(id),
    enabled: !!id,
  });
}

export function useCreateOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrderRequest) => quickServeService.createOrder(data),
    onSuccess: (newOrder) => {
      queryClient.invalidateQueries({ queryKey: QUICKSERVE_QUERY_KEYS.orders });
      toast({
        title: 'Order Created',
        description: `Order #${newOrder.id} has been created successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create order',
        variant: 'destructive',
      });
    },
  });
}

export function useUpdateOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateOrderRequest }) =>
      quickServeService.updateOrder(id, data),
    onSuccess: (updatedOrder) => {
      queryClient.invalidateQueries({ queryKey: QUICKSERVE_QUERY_KEYS.orders });
      queryClient.setQueryData(
        QUICKSERVE_QUERY_KEYS.order(updatedOrder.id),
        updatedOrder
      );
      toast({
        title: 'Order Updated',
        description: `Order #${updatedOrder.id} has been updated successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update order',
        variant: 'destructive',
      });
    },
  });
}

export function useUpdateOrderStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      quickServeService.updateOrderStatus(id, status),
    onSuccess: (updatedOrder) => {
      queryClient.invalidateQueries({ queryKey: QUICKSERVE_QUERY_KEYS.orders });
      queryClient.setQueryData(
        QUICKSERVE_QUERY_KEYS.order(updatedOrder.id),
        updatedOrder
      );
      toast({
        title: 'Status Updated',
        description: `Order #${updatedOrder.id} status changed to ${updatedOrder.status}.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update order status',
        variant: 'destructive',
      });
    },
  });
}

export function useOrderStats() {
  return useQuery({
    queryKey: QUICKSERVE_QUERY_KEYS.stats,
    queryFn: () => quickServeService.getOrderStats(),
    staleTime: 60000, // 1 minute
  });
}
```

### 3. API Client Configuration

```tsx
// src/lib/api-client.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { getKeycloakToken, refreshKeycloakToken } from '@/lib/keycloak';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL,
      timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '10000'),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      async (config) => {
        const token = await getKeycloakToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await refreshKeycloakToken();
            const newToken = await getKeycloakToken();

            if (newToken) {
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            // Redirect to login if refresh fails
            window.location.href = '/auth/login';
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post(url, data, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.patch(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete(url, config);
  }
}

export const apiClient = new ApiClient();
```

## Authentication Integration

### 1. Keycloak Configuration

```tsx
// src/lib/keycloak.ts
import Keycloak from 'keycloak-js';

const keycloakConfig = {
  url: process.env.NEXT_PUBLIC_KEYCLOAK_URL!,
  realm: 'demo',
  clientId: process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID!,
};

let keycloakInstance: Keycloak | null = null;

export function initKeycloak(): Promise<boolean> {
  if (typeof window === 'undefined') {
    return Promise.resolve(false);
  }

  if (keycloakInstance) {
    return Promise.resolve(keycloakInstance.authenticated || false);
  }

  keycloakInstance = new Keycloak(keycloakConfig);

  return keycloakInstance.init({
    onLoad: 'check-sso',
    silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
    checkLoginIframe: false,
  });
}

export function getKeycloak(): Keycloak | null {
  return keycloakInstance;
}

export async function getKeycloakToken(): Promise<string | null> {
  if (!keycloakInstance?.authenticated) {
    return null;
  }

  // Refresh token if it expires in less than 30 seconds
  if (keycloakInstance.tokenParsed?.exp) {
    const expiresIn = keycloakInstance.tokenParsed.exp * 1000 - Date.now();
    if (expiresIn < 30000) {
      await keycloakInstance.updateToken(30);
    }
  }

  return keycloakInstance.token || null;
}

export async function refreshKeycloakToken(): Promise<boolean> {
  if (!keycloakInstance) {
    return false;
  }

  try {
    return await keycloakInstance.updateToken(30);
  } catch (error) {
    console.error('Failed to refresh token:', error);
    return false;
  }
}

export function login(): void {
  keycloakInstance?.login();
}

export function logout(): void {
  keycloakInstance?.logout();
}

export function getUser() {
  if (!keycloakInstance?.authenticated) {
    return null;
  }

  return {
    id: keycloakInstance.tokenParsed?.sub,
    email: keycloakInstance.tokenParsed?.email,
    name: keycloakInstance.tokenParsed?.name,
    roles: keycloakInstance.tokenParsed?.realm_access?.roles || [],
    permissions: keycloakInstance.tokenParsed?.resource_access || {},
  };
}
```

### 2. Authentication Provider

```tsx
// src/providers/AuthProvider.tsx
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { initKeycloak, getUser, login, logout } from '@/lib/keycloak';

interface User {
  id: string;
  email: string;
  name: string;
  roles: string[];
  permissions: Record<string, any>;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        const authenticated = await initKeycloak();
        setIsAuthenticated(authenticated);

        if (authenticated) {
          const userData = getUser();
          setUser(userData);
        }
      } catch (error) {
        console.error('Authentication initialization failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const handleLogin = () => {
    login();
  };

  const handleLogout = () => {
    logout();
    setUser(null);
    setIsAuthenticated(false);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        isLoading,
        login: handleLogin,
        logout: handleLogout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

### 3. Protected Route Component

```tsx
// src/components/common/ProtectedRoute.tsx
'use client';

import React from 'react';
import { useAuth } from '@/providers/AuthProvider';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  fallback?: React.ReactNode;
}

export function ProtectedRoute({
  children,
  requiredRoles = [],
  fallback
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading, login } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-6">Please log in to access this page.</p>
          <button
            onClick={login}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
          >
            Log In
          </button>
        </div>
      </div>
    );
  }

  if (requiredRoles.length > 0 && user) {
    const hasRequiredRole = requiredRoles.some(role => user.roles.includes(role));

    if (!hasRequiredRole) {
      return fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
            <p className="text-gray-600">You don't have permission to access this page.</p>
          </div>
        </div>
      );
    }
  }

  return <>{children}</>;
}
```

## TypeScript Implementation

### 1. Type Definitions

```tsx
// src/types/quickserve.ts
export interface Order {
  id: string;
  uuid: string;
  customer: Customer;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  deliveryAddress: string;
  estimatedDelivery: string;
  orderedAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: Address;
}

export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  customizations?: Customization[];
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface Customization {
  name: string;
  value: string;
  price?: number;
}

export type OrderStatus =
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready'
  | 'delivered'
  | 'cancelled';

export interface CreateOrderRequest {
  customerId: string;
  items: Omit<OrderItem, 'id'>[];
  deliveryAddress: string;
  metadata?: Record<string, any>;
}

export interface UpdateOrderRequest {
  status?: OrderStatus;
  estimatedDelivery?: string;
  metadata?: Record<string, any>;
}

export interface OrderStats {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  revenue: number;
  averageOrderValue: number;
}
```

### 2. API Response Types

```tsx
// src/types/api.ts
export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  message?: string;
  data?: T;
  meta?: {
    pagination?: {
      current_page: number;
      total_pages: number;
      total_items: number;
      per_page: number;
    };
  };
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    pagination: {
      current_page: number;
      total_pages: number;
      total_items: number;
      per_page: number;
    };
  };
}

export interface ErrorResponse {
  status: 'error';
  message: string;
  code?: string;
  errors?: Record<string, string[]>;
}
```

### 3. Form Validation with Zod

```tsx
// src/schemas/order.ts
import { z } from 'zod';

export const createOrderSchema = z.object({
  customerId: z.string().uuid('Invalid customer ID'),
  items: z.array(z.object({
    productId: z.string().min(1, 'Product ID is required'),
    name: z.string().min(1, 'Product name is required'),
    price: z.number().positive('Price must be positive'),
    quantity: z.number().int().positive('Quantity must be a positive integer'),
    customizations: z.array(z.object({
      name: z.string().min(1, 'Customization name is required'),
      value: z.string().min(1, 'Customization value is required'),
      price: z.number().optional(),
    })).optional(),
  })).min(1, 'At least one item is required'),
  deliveryAddress: z.string().min(10, 'Delivery address must be at least 10 characters'),
  metadata: z.record(z.any()).optional(),
});

export const updateOrderSchema = z.object({
  status: z.enum(['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled']).optional(),
  estimatedDelivery: z.string().datetime().optional(),
  metadata: z.record(z.any()).optional(),
});

export type CreateOrderFormData = z.infer<typeof createOrderSchema>;
export type UpdateOrderFormData = z.infer<typeof updateOrderSchema>;
```

## Testing Strategy

### 1. Jest Configuration

```javascript
// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/app/layout.tsx',
    '!src/app/globals.css',
  ],
  coverageThreshold: {
    global: {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
  ],
};

module.exports = createJestConfig(customJestConfig);
```

### 2. Test Setup

```javascript
// jest.setup.js
import '@testing-library/jest-dom';
import { server } from './src/tests/mocks/server';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));

// Mock Keycloak
jest.mock('@/lib/keycloak', () => ({
  initKeycloak: jest.fn(() => Promise.resolve(true)),
  getKeycloakToken: jest.fn(() => Promise.resolve('mock-token')),
  getUser: jest.fn(() => ({
    id: 'mock-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    roles: ['user'],
    permissions: {},
  })),
  login: jest.fn(),
  logout: jest.fn(),
}));

// Setup MSW
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

### 3. Integration Tests

```tsx
// src/tests/integration/order-flow.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/providers/AuthProvider';
import { OrdersPage } from '@/app/(microfrontend-v2)/quickserve/orders/page';
import { server } from '../mocks/server';
import { rest } from 'msw';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </QueryClientProvider>
  );
};

describe('Order Flow Integration', () => {
  it('should create and display a new order', async () => {
    // Mock API responses
    server.use(
      rest.get('/api/v2/quickserve/orders', (req, res, ctx) => {
        return res(ctx.json({
          status: 'success',
          data: [],
          meta: { pagination: { current_page: 1, total_pages: 1, total_items: 0 } }
        }));
      }),
      rest.post('/api/v2/quickserve/orders', (req, res, ctx) => {
        return res(ctx.json({
          status: 'success',
          data: {
            id: 'new-order-id',
            customer: { name: 'Test Customer' },
            totalAmount: 25.99,
            status: 'pending',
          }
        }));
      })
    );

    const Wrapper = createWrapper();
    render(<OrdersPage />, { wrapper: Wrapper });

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('Orders')).toBeInTheDocument();
    });

    // Click create order button
    fireEvent.click(screen.getByText('New Order'));

    // Fill out order form
    fireEvent.change(screen.getByLabelText('Customer'), {
      target: { value: 'Test Customer' }
    });

    // Submit form
    fireEvent.click(screen.getByText('Create Order'));

    // Verify order was created
    await waitFor(() => {
      expect(screen.getByText('Order #new-order-id')).toBeInTheDocument();
    });
  });
});
```

## Build Optimization

### 1. Next.js Configuration

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['@prisma/client'],
  },
  images: {
    domains: ['localhost', 'api.onefooddialer.com'],
    formats: ['image/webp', 'image/avif'],
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
      },
    ];
  },
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Bundle analyzer
    if (process.env.ANALYZE === 'true') {
      const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
        })
      );
    }

    // Optimize bundle size
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    };

    return config;
  },
  output: 'standalone',
  poweredByHeader: false,
  compress: true,
};

module.exports = nextConfig;
```

### 2. Performance Optimization

```tsx
// src/components/common/LazyComponent.tsx
import { lazy, Suspense } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export function createLazyComponent<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ReactNode
) {
  const LazyComponent = lazy(importFunc);

  return function WrappedComponent(props: React.ComponentProps<T>) {
    return (
      <Suspense fallback={fallback || <LoadingSpinner />}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

// Usage
export const LazyOrderCard = createLazyComponent(
  () => import('@/components/microfrontends/quickserve/OrderCard'),
  <div className="animate-pulse bg-gray-200 h-32 rounded-md" />
);
```

## Deployment Process

### 1. Production Build

```bash
# Build for production
npm run build

# Start production server
npm start

# Build with bundle analysis
ANALYZE=true npm run build

# Build Docker image
docker build -t onefooddialer-frontend:latest .
```

### 2. Docker Configuration

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 3. AWS Deployment

```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-east-1.amazonaws.com

docker build -t onefooddialer-frontend .
docker tag onefooddialer-frontend:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/onefooddialer-frontend:latest
docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/onefooddialer-frontend:latest

# Deploy to ECS
aws ecs update-service --cluster onefooddialer --service frontend --force-new-deployment
```

---

## Conclusion

This guide provides comprehensive instructions for Next.js developers working on the OneFoodDialer 2025 frontend. Follow the microfrontend architecture patterns, maintain code quality standards, and use the provided testing and deployment strategies for optimal results.

For backend integration, refer to the [Laravel Microservices Developer Guide](LARAVEL_MICROSERVICES_DEVELOPER_GUIDE.md) and for infrastructure concerns, see the [DevOps Engineer Guide](DEVOPS_ENGINEER_GUIDE.md).

# OneFoodDialer 2025 - Deployment Guides

## Overview

This directory contains comprehensive deployment guides for the OneFoodDialer 2025 microservices architecture, targeting three specific developer roles. Each guide provides step-by-step instructions for production deployment on AWS using GitLab CI/CD pipelines.

## Available Guides

### 🔧 [Laravel Microservices Developer Guide](LARAVEL_MICROSERVICES_DEVELOPER_GUIDE.md)
**Target Audience**: Backend developers working with Laravel microservices  
**File Size**: 33KB | **Sections**: 12 | **Code Examples**: 50+

**Prerequisites**: PHP 8.1+, Composer, Docker knowledge

**Key Topics Covered**:
- Local development setup for all 12 Laravel microservices
- Observability middleware integration (ObservabilityMiddleware, MetricsController, CircuitBreakerMiddleware)
- Database migrations and seeding across multiple services
- API endpoint development and testing with service layer architecture
- Keycloak authentication and JWT integration
- Environment configuration for development/staging/production
- Code quality standards (PHPStan max level, ≥95% test coverage)
- GitLab CI/CD pipeline configuration
- Comprehensive troubleshooting and performance optimization

### 🎨 [Next.js Frontend Developer Guide](NEXTJS_FRONTEND_DEVELOPER_GUIDE.md)
**Target Audience**: Frontend developers working with Next.js and React  
**File Size**: 41KB | **Sections**: 12 | **Code Examples**: 60+

**Prerequisites**: Node.js 18+, npm/yarn, TypeScript knowledge

**Key Topics Covered**:
- Microfrontend architecture implementation in src/app/(microfrontend-v2)/
- Component development with shadcn/ui and Tailwind CSS
- React Query hooks for all 12 microservices API integration
- Keycloak authentication provider and protected routes
- TypeScript interface implementation with Zod validation
- Testing strategy (Jest + React Testing Library + Storybook)
- Build optimization and performance monitoring
- AWS deployment with Docker containerization

### 🚀 [DevOps Engineer Guide](DEVOPS_ENGINEER_GUIDE.md)
**Target Audience**: DevOps engineers and infrastructure specialists  
**File Size**: 18KB | **Sections**: 14 | **Code Examples**: 40+

**Prerequisites**: AWS CLI, Docker, Terraform, Ansible knowledge

**Key Topics Covered**:
- AWS infrastructure provisioning with Terraform (ECS, RDS, ElastiCache, ALB)
- Docker containerization for all 12 Laravel services + Next.js frontend
- GitLab CI/CD pipeline configuration for microservices deployment
- Kong API Gateway deployment and configuration on AWS
- Keycloak deployment and realm configuration
- Observability stack deployment (Prometheus, Grafana, ELK, Jaeger)
- Security best practices and compliance
- Backup and disaster recovery procedures

## Architecture Overview

### Microservices Structure

The OneFoodDialer 2025 consists of **12 Laravel microservices** and **1 Next.js frontend**:

| Service | Port | Purpose | Guide Section |
|---------|------|---------|---------------|
| **auth-service-v12** | 8101 | Authentication & authorization | All guides |
| **customer-service-v12** | 8103 | Customer management | All guides |
| **payment-service-v12** | 8104 | Payment processing | All guides |
| **quickserve-service-v12** | 8102 | Core business logic | All guides |
| **kitchen-service-v12** | 8105 | Kitchen operations | All guides |
| **delivery-service-v12** | 8106 | Delivery management | All guides |
| **analytics-service-v12** | 8107 | Analytics & reporting | All guides |
| **admin-service-v12** | 8108 | Admin operations | All guides |
| **notification-service-v12** | 8109 | Notifications | All guides |
| **catalogue-service-v12** | 8110 | Product catalog | All guides |
| **subscription-service-v12** | 8111 | Subscription management | All guides |
| **meal-service-v12** | 8112 | Meal planning | All guides |
| **misscall-service-v12** | 8113 | Missed call handling | All guides |
| **frontend-shadcn** | 3000 | Next.js frontend | Frontend & DevOps |

### Technology Stack

| Component | Technology | Configuration |
|-----------|------------|---------------|
| **Backend Framework** | Laravel 12 | PHP 8.1+ |
| **Frontend Framework** | Next.js 15 | App Router, TypeScript |
| **API Gateway** | Kong | Authentication, routing, rate limiting |
| **Authentication** | Keycloak | JWT with RS256, RBAC |
| **Database** | MySQL 8.0 | Per-service databases |
| **Cache** | Redis | Session storage, caching |
| **Message Queue** | RabbitMQ | Event-driven communication |
| **Monitoring** | Prometheus + Grafana | Metrics and dashboards |
| **Logging** | ELK Stack | Centralized logging |
| **Tracing** | Jaeger | Distributed tracing |
| **Infrastructure** | AWS ECS Fargate | Container orchestration |
| **CI/CD** | GitLab CI/CD | Automated deployments |

## Quick Start by Role

### For Laravel Developers
```bash
# 1. Clone repository
git clone https://gitrepo.futurescapetech.com/developers/onefooddialer_2025.git
cd onefooddialer_2025

# 2. Set up development environment
./scripts/onefooddialer-dev-environment.sh

# 3. Set up observability
./scripts/setup-comprehensive-observability.sh

# 4. Validate setup
./scripts/validate-deployment.sh
```

### For Frontend Developers
```bash
# 1. Navigate to frontend
cd frontend-shadcn

# 2. Install dependencies
npm install

# 3. Set up environment
cp .env.example .env.local

# 4. Start development server
npm run dev
```

### For DevOps Engineers
```bash
# 1. Set up AWS infrastructure
cd terraform
terraform init
terraform plan
terraform apply

# 2. Deploy services
cd ../
./scripts/deploy-production.sh

# 3. Validate deployment
./scripts/validate-observability.sh
```

## Environment-Specific Configurations

### Development Environment
- **Local Docker Compose**: All services running locally
- **Database**: Local MySQL containers
- **Authentication**: Local Keycloak instance
- **Monitoring**: Local observability stack
- **API Gateway**: Local Kong instance

### Staging Environment
- **AWS ECS**: Fargate containers
- **Database**: RDS MySQL (smaller instance)
- **Authentication**: Staging Keycloak on ECS
- **Monitoring**: CloudWatch + custom stack
- **API Gateway**: Kong on ECS with staging config

### Production Environment
- **AWS ECS**: Fargate with auto-scaling
- **Database**: RDS MySQL (production instance)
- **Authentication**: Production Keycloak cluster
- **Monitoring**: Full observability stack
- **API Gateway**: Kong cluster with production config

## Integration Points

### Service Communication
- **API Gateway**: Kong routes all external requests
- **Authentication**: Keycloak provides JWT tokens
- **Service-to-Service**: Direct HTTP calls with JWT
- **Events**: RabbitMQ for asynchronous communication
- **Monitoring**: Correlation IDs across all services

### Data Flow
1. **Frontend** → Kong API Gateway
2. **Kong** → Authentication (Keycloak)
3. **Kong** → Microservices (with JWT)
4. **Microservices** → Database (per-service)
5. **Microservices** → Events (RabbitMQ)
6. **All Services** → Observability Stack

## Quality Gates

### Code Quality Requirements
- **Laravel**: PHPStan max level, ≥95% test coverage
- **Frontend**: ESLint <100 issues, ≥95% test coverage
- **Infrastructure**: Terraform validation, security scanning
- **All**: Automated testing in CI/CD pipelines

### Performance Targets
- **API Response Time**: <200ms (95th percentile)
- **Error Rate**: <5% warning, <10% critical
- **Service Uptime**: >99.5% monthly
- **Database Query Time**: <50ms average

### Security Standards
- **Authentication**: JWT with RS256, 24-hour tokens
- **Authorization**: Role-based access control (RBAC)
- **Data Encryption**: TLS 1.3, encrypted storage
- **Network Security**: VPC, security groups, WAF

## Troubleshooting Resources

### Common Issues by Role

#### Laravel Developers
- Database connection issues
- Keycloak authentication failures
- Kong gateway routing problems
- Memory issues during testing
- Queue processing problems

#### Frontend Developers
- API integration errors
- Authentication flow issues
- Build optimization problems
- TypeScript compilation errors
- Component testing failures

#### DevOps Engineers
- Infrastructure provisioning failures
- Container deployment issues
- Monitoring stack problems
- SSL/TLS certificate issues
- Auto-scaling configuration

### Support Contacts
- **Laravel Issues**: Backend development team
- **Frontend Issues**: Frontend development team
- **Infrastructure Issues**: DevOps team
- **Security Issues**: Security team
- **General Support**: Project management team

## Validation Procedures

### Pre-Deployment Checklist
- [ ] All tests passing (≥95% coverage)
- [ ] Code quality checks passed
- [ ] Security scans completed
- [ ] Performance benchmarks met
- [ ] Documentation updated
- [ ] Environment variables configured
- [ ] Database migrations tested
- [ ] Monitoring alerts configured

### Post-Deployment Validation
- [ ] Health checks passing
- [ ] Monitoring dashboards active
- [ ] Log aggregation working
- [ ] Authentication flows tested
- [ ] API endpoints responding
- [ ] Performance metrics within targets
- [ ] Error rates below thresholds
- [ ] Backup procedures verified

## Rollback Strategies

### Automated Rollback Triggers
- Health check failures
- Error rate >10%
- Response time >1 second
- Critical security alerts

### Manual Rollback Procedures
1. **Immediate**: Load balancer traffic redirect
2. **Service Level**: Container rollback via ECS
3. **Database**: Point-in-time recovery
4. **Full Stack**: Infrastructure state rollback

---

## Getting Started

1. **Choose Your Role**: Select the appropriate guide based on your responsibilities
2. **Review Prerequisites**: Ensure you have the required tools and knowledge
3. **Follow Step-by-Step**: Each guide provides detailed instructions
4. **Validate Setup**: Use provided validation scripts and procedures
5. **Troubleshoot**: Refer to troubleshooting sections for common issues

For questions or support, please refer to the troubleshooting sections in each guide or contact the appropriate development team.

**Last Updated**: June 1, 2024  
**Version**: 1.0.0  
**Compatibility**: OneFoodDialer 2025 v1.0+

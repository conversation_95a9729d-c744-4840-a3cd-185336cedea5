# OneFoodDialer 2025 - <PERSON><PERSON>ps Engineer Guide

## Overview

This comprehensive guide provides step-by-step instructions for DevOps engineers responsible for deploying and managing the OneFoodDialer 2025 microservices architecture on AWS. The guide covers infrastructure provisioning, CI/CD pipelines, monitoring, and operational best practices.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Architecture Overview](#architecture-overview)
3. [AWS Infrastructure Setup](#aws-infrastructure-setup)
4. [Terraform Infrastructure as Code](#terraform-infrastructure-as-code)
5. [Docker Containerization](#docker-containerization)
6. [GitLab CI/CD Pipeline](#gitlab-cicd-pipeline)
7. [Kong API Gateway Deployment](#kong-api-gateway-deployment)
8. [Keycloak Deployment](#keycloak-deployment)
9. [Observability Stack Deployment](#observability-stack-deployment)
10. [Database Setup](#database-setup)
11. [Security & Compliance](#security--compliance)
12. [Monitoring & Alerting](#monitoring--alerting)
13. [Backup & Disaster Recovery](#backup--disaster-recovery)
14. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Tools
- **AWS CLI**: v2.0+
- **Terraform**: v1.5+
- **Ansible**: v2.14+
- **Docker**: v20.10+
- **kubectl**: v1.28+
- **Helm**: v3.12+
- **GitLab Runner**: Latest

### Required Knowledge
- AWS services (ECS, RDS, ElastiCache, ALB, VPC)
- Infrastructure as Code (Terraform)
- Configuration Management (Ansible)
- Container orchestration
- CI/CD pipeline design
- Monitoring and observability
- Security best practices

### AWS Account Setup

```bash
# Install AWS CLI v2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure AWS credentials
aws configure
# AWS Access Key ID: [Your Access Key]
# AWS Secret Access Key: [Your Secret Key]
# Default region name: us-east-1
# Default output format: json

# Verify configuration
aws sts get-caller-identity
```

### Terraform Setup

```bash
# Install Terraform
wget https://releases.hashicorp.com/terraform/1.5.0/terraform_1.5.0_linux_amd64.zip
unzip terraform_1.5.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# Verify installation
terraform version
```

## Architecture Overview

### AWS Infrastructure Components

```mermaid
graph TB
    Internet[Internet] --> ALB[Application Load Balancer]
    ALB --> ECS[ECS Cluster]
    
    subgraph "VPC - OneFoodDialer"
        subgraph "Public Subnets"
            ALB
            NAT[NAT Gateway]
        end
        
        subgraph "Private Subnets"
            ECS
            RDS[(RDS MySQL)]
            ElastiCache[(ElastiCache Redis)]
            
            subgraph "ECS Services"
                Kong[Kong Gateway]
                Keycloak[Keycloak]
                Auth[Auth Service]
                Customer[Customer Service]
                Payment[Payment Service]
                QuickServe[QuickServe Service]
                Kitchen[Kitchen Service]
                Delivery[Delivery Service]
                Analytics[Analytics Service]
                Admin[Admin Service]
                Notification[Notification Service]
                Catalogue[Catalogue Service]
                Subscription[Subscription Service]
                Meal[Meal Service]
                Misscall[Misscall Service]
                Frontend[Next.js Frontend]
            end
            
            subgraph "Observability"
                Prometheus[Prometheus]
                Grafana[Grafana]
                Elasticsearch[Elasticsearch]
                Kibana[Kibana]
                Jaeger[Jaeger]
            end
        end
    end
    
    ECS --> RDS
    ECS --> ElastiCache
    ECS --> S3[(S3 Storage)]
    
    subgraph "External Services"
        Route53[Route 53]
        CloudFront[CloudFront CDN]
        SES[SES Email]
        SNS[SNS Notifications]
    end
    
    Route53 --> ALB
    CloudFront --> S3
```

### Service Architecture

| Component | AWS Service | Purpose |
|-----------|-------------|---------|
| **Compute** | ECS Fargate | Container orchestration |
| **Load Balancer** | Application Load Balancer | Traffic distribution |
| **Database** | RDS MySQL | Primary database |
| **Cache** | ElastiCache Redis | Caching and sessions |
| **Storage** | S3 | File storage and static assets |
| **CDN** | CloudFront | Content delivery |
| **DNS** | Route 53 | Domain management |
| **Monitoring** | CloudWatch + Custom Stack | Observability |
| **Security** | IAM, Security Groups, WAF | Access control |

## AWS Infrastructure Setup

### 1. VPC and Networking

```bash
# Create VPC
aws ec2 create-vpc \
    --cidr-block 10.0.0.0/16 \
    --tag-specifications 'ResourceType=vpc,Tags=[{Key=Name,Value=onefooddialer-vpc}]'

# Create Internet Gateway
aws ec2 create-internet-gateway \
    --tag-specifications 'ResourceType=internet-gateway,Tags=[{Key=Name,Value=onefooddialer-igw}]'

# Create public subnets
aws ec2 create-subnet \
    --vpc-id vpc-xxxxxxxxx \
    --cidr-block 10.0.1.0/24 \
    --availability-zone us-east-1a \
    --tag-specifications 'ResourceType=subnet,Tags=[{Key=Name,Value=onefooddialer-public-1a}]'

aws ec2 create-subnet \
    --vpc-id vpc-xxxxxxxxx \
    --cidr-block 10.0.2.0/24 \
    --availability-zone us-east-1b \
    --tag-specifications 'ResourceType=subnet,Tags=[{Key=Name,Value=onefooddialer-public-1b}]'

# Create private subnets
aws ec2 create-subnet \
    --vpc-id vpc-xxxxxxxxx \
    --cidr-block *********/24 \
    --availability-zone us-east-1a \
    --tag-specifications 'ResourceType=subnet,Tags=[{Key=Name,Value=onefooddialer-private-1a}]'

aws ec2 create-subnet \
    --vpc-id vpc-xxxxxxxxx \
    --cidr-block *********/24 \
    --availability-zone us-east-1b \
    --tag-specifications 'ResourceType=subnet,Tags=[{Key=Name,Value=onefooddialer-private-1b}]'
```

### 2. Security Groups

```bash
# ALB Security Group
aws ec2 create-security-group \
    --group-name onefooddialer-alb-sg \
    --description "Security group for OneFoodDialer ALB" \
    --vpc-id vpc-xxxxxxxxx

# ECS Security Group
aws ec2 create-security-group \
    --group-name onefooddialer-ecs-sg \
    --description "Security group for OneFoodDialer ECS services" \
    --vpc-id vpc-xxxxxxxxx

# RDS Security Group
aws ec2 create-security-group \
    --group-name onefooddialer-rds-sg \
    --description "Security group for OneFoodDialer RDS" \
    --vpc-id vpc-xxxxxxxxx

# ElastiCache Security Group
aws ec2 create-security-group \
    --group-name onefooddialer-redis-sg \
    --description "Security group for OneFoodDialer Redis" \
    --vpc-id vpc-xxxxxxxxx
```

## Terraform Infrastructure as Code

### 1. Main Terraform Configuration

```hcl
# terraform/main.tf
terraform {
  required_version = ">= 1.5"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  backend "s3" {
    bucket = "onefooddialer-terraform-state"
    key    = "infrastructure/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "OneFoodDialer2025"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

data "aws_caller_identity" "current" {}

# Local values
locals {
  name_prefix = "onefooddialer-${var.environment}"
  azs         = slice(data.aws_availability_zones.available.names, 0, 2)
  
  common_tags = {
    Project     = "OneFoodDialer2025"
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
}
```

### 2. VPC Module

```hcl
# terraform/modules/vpc/main.tf
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-vpc"
  })
}

resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id
  
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-igw"
  })
}

resource "aws_subnet" "public" {
  count = length(var.availability_zones)
  
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.public_subnet_cidrs[count.index]
  availability_zone       = var.availability_zones[count.index]
  map_public_ip_on_launch = true
  
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-public-${substr(var.availability_zones[count.index], -2, 2)}"
    Type = "Public"
  })
}

resource "aws_subnet" "private" {
  count = length(var.availability_zones)
  
  vpc_id            = aws_vpc.main.id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]
  
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-private-${substr(var.availability_zones[count.index], -2, 2)}"
    Type = "Private"
  })
}

resource "aws_eip" "nat" {
  count = length(var.availability_zones)
  
  domain = "vpc"
  
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-nat-eip-${count.index + 1}"
  })
  
  depends_on = [aws_internet_gateway.main]
}

resource "aws_nat_gateway" "main" {
  count = length(var.availability_zones)
  
  allocation_id = aws_eip.nat[count.index].id
  subnet_id     = aws_subnet.public[count.index].id
  
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-nat-${count.index + 1}"
  })
  
  depends_on = [aws_internet_gateway.main]
}

resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id
  
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main.id
  }
  
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-public-rt"
  })
}

resource "aws_route_table" "private" {
  count = length(var.availability_zones)
  
  vpc_id = aws_vpc.main.id
  
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.main[count.index].id
  }
  
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-private-rt-${count.index + 1}"
  })
}

resource "aws_route_table_association" "public" {
  count = length(aws_subnet.public)
  
  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}

resource "aws_route_table_association" "private" {
  count = length(aws_subnet.private)
  
  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[count.index].id
}
```

### 3. ECS Cluster Configuration

```hcl
# terraform/modules/ecs/main.tf
resource "aws_ecs_cluster" "main" {
  name = var.cluster_name

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = var.tags
}

resource "aws_ecs_cluster_capacity_providers" "main" {
  cluster_name = aws_ecs_cluster.main.name

  capacity_providers = ["FARGATE", "FARGATE_SPOT"]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = "FARGATE"
  }
}

# IAM Role for ECS Task Execution
resource "aws_iam_role" "ecs_task_execution_role" {
  name = "${var.name_prefix}-ecs-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# IAM Role for ECS Tasks
resource "aws_iam_role" "ecs_task_role" {
  name = "${var.name_prefix}-ecs-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# CloudWatch Log Groups
resource "aws_cloudwatch_log_group" "ecs_logs" {
  for_each = toset(var.service_names)

  name              = "/ecs/${var.cluster_name}/${each.value}"
  retention_in_days = 30

  tags = var.tags
}
```

### 4. RDS Database Configuration

```hcl
# terraform/modules/rds/main.tf
resource "aws_db_subnet_group" "main" {
  name       = "${var.name_prefix}-db-subnet-group"
  subnet_ids = var.private_subnet_ids

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-db-subnet-group"
  })
}

resource "aws_security_group" "rds" {
  name_prefix = "${var.name_prefix}-rds-"
  vpc_id      = var.vpc_id

  ingress {
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [var.ecs_security_group_id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-rds-sg"
  })
}

resource "aws_db_instance" "main" {
  identifier = "${var.name_prefix}-mysql"

  engine         = "mysql"
  engine_version = "8.0"
  instance_class = var.instance_class

  allocated_storage     = var.allocated_storage
  max_allocated_storage = var.max_allocated_storage
  storage_type          = "gp3"
  storage_encrypted     = true

  db_name  = var.database_name
  username = var.master_username
  password = var.master_password

  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name

  backup_retention_period = var.backup_retention_period
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = var.environment != "production"
  deletion_protection = var.environment == "production"

  performance_insights_enabled = true
  monitoring_interval         = 60
  monitoring_role_arn        = aws_iam_role.rds_monitoring.arn

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-mysql"
  })
}

resource "aws_iam_role" "rds_monitoring" {
  name = "${var.name_prefix}-rds-monitoring-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "monitoring.rds.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "rds_monitoring" {
  role       = aws_iam_role.rds_monitoring.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}
```

## Docker Containerization

### 1. Laravel Microservice Dockerfile

```dockerfile
# services/auth-service-v12/Dockerfile
FROM php:8.1-fpm-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    mysql-client \
    nginx \
    supervisor \
    redis

# Install PHP extensions
RUN docker-php-ext-install \
    pdo_mysql \
    mbstring \
    exif \
    pcntl \
    bcmath \
    gd \
    opcache

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configure PHP
COPY docker/php/php.ini /usr/local/etc/php/conf.d/custom.ini
COPY docker/php/opcache.ini /usr/local/etc/php/conf.d/opcache.ini

# Configure Nginx
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf /etc/nginx/conf.d/default.conf

# Configure Supervisor
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Set working directory
WORKDIR /var/www

# Copy application files
COPY . /var/www

# Install dependencies
RUN composer install --no-dev --optimize-autoloader --no-interaction

# Set permissions
RUN chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache
RUN chmod -R 775 /var/www/storage /var/www/bootstrap/cache

# Create log directory
RUN mkdir -p /var/log/supervisor

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Expose port
EXPOSE 80

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

### 2. Next.js Frontend Dockerfile

```dockerfile
# frontend-shadcn/Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Environment variables for build
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV production

# Build application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["node", "server.js"]
```

### 3. Kong API Gateway Dockerfile

```dockerfile
# kong/Dockerfile
FROM kong:3.4-alpine

# Install custom plugins if needed
USER root

# Copy custom configuration
COPY kong.yml /opt/kong/kong.yml
COPY plugins/ /opt/kong/plugins/

# Set permissions
RUN chown -R kong:kong /opt/kong

USER kong

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD kong health || exit 1

EXPOSE 8000 8001 8443 8444

CMD ["kong", "start", "--vv"]
```
```

# OneFoodDialer 2025 - Laravel Microservices Developer Guide

## Overview

This comprehensive guide provides step-by-step instructions for Laravel developers working on the OneFoodDialer 2025 microservices architecture. The guide covers local development setup, production deployment, and best practices for all 12 Laravel microservices.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Architecture Overview](#architecture-overview)
3. [Local Development Setup](#local-development-setup)
4. [Microservices Configuration](#microservices-configuration)
5. [Observability Integration](#observability-integration)
6. [Database Management](#database-management)
7. [API Development & Testing](#api-development--testing)
8. [Authentication & Authorization](#authentication--authorization)
9. [Environment Configuration](#environment-configuration)
10. [Code Quality & Testing](#code-quality--testing)
11. [Deployment Process](#deployment-process)
12. [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Software
- **PHP**: 8.1 or higher
- **Composer**: Latest version
- **Docker**: 20.10+ with <PERSON><PERSON> Compose
- **Git**: Latest version
- **Node.js**: 18+ (for frontend integration)

### Required Knowledge
- Laravel 12 framework
- RESTful API development
- Docker containerization
- Microservices architecture patterns
- Database design and migrations
- JWT authentication
- API testing methodologies

### Development Tools
```bash
# Install PHP 8.1+
brew install php@8.1  # macOS
sudo apt install php8.1  # Ubuntu

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install Docker
# Follow official Docker installation guide for your OS

# Verify installations
php --version
composer --version
docker --version
docker-compose --version
```

## Architecture Overview

### Microservices Structure

OneFoodDialer 2025 consists of 12 Laravel microservices:

```
services/
├── auth-service-v12/          # Authentication & authorization
├── customer-service-v12/      # Customer management
├── payment-service-v12/       # Payment processing
├── quickserve-service-v12/    # Core business logic
├── kitchen-service-v12/       # Kitchen operations
├── delivery-service-v12/      # Delivery management
├── analytics-service-v12/     # Analytics & reporting
├── admin-service-v12/         # Admin operations
├── notification-service-v12/  # Notifications
├── catalogue-service-v12/     # Product catalog
├── subscription-service-v12/  # Subscription management
├── meal-service-v12/          # Meal planning
└── misscall-service-v12/      # Missed call handling
```

### Service Communication

```mermaid
graph TB
    Frontend[Next.js Frontend] --> Kong[Kong API Gateway]
    Kong --> Auth[Auth Service]
    Kong --> Customer[Customer Service]
    Kong --> Payment[Payment Service]
    Kong --> QuickServe[QuickServe Service]
    Kong --> Kitchen[Kitchen Service]
    Kong --> Delivery[Delivery Service]
    Kong --> Analytics[Analytics Service]
    Kong --> Admin[Admin Service]
    Kong --> Notification[Notification Service]
    Kong --> Catalogue[Catalogue Service]
    Kong --> Subscription[Subscription Service]
    Kong --> Meal[Meal Service]
    Kong --> Misscall[Misscall Service]
    
    Auth --> MySQL[(MySQL)]
    Customer --> MySQL
    Payment --> MySQL
    QuickServe --> MySQL
    Kitchen --> MySQL
    Delivery --> MySQL
    Analytics --> MySQL
    Admin --> MySQL
    Notification --> MySQL
    Catalogue --> MySQL
    Subscription --> MySQL
    Meal --> MySQL
    Misscall --> MySQL
    
    Kong --> Keycloak[Keycloak]
    Keycloak --> PostgreSQL[(PostgreSQL)]
```

## Local Development Setup

### 1. Clone Repository

```bash
git clone https://gitrepo.futurescapetech.com/developers/onefooddialer_2025.git
cd onefooddialer_2025
```

### 2. Environment Setup

```bash
# Copy environment files for all services
for service in auth customer payment quickserve kitchen delivery analytics admin notification catalogue subscription meal misscall; do
    cp services/${service}-service-v12/.env.example services/${service}-service-v12/.env
done

# Set up infrastructure
./scripts/onefooddialer-dev-environment.sh
```

### 3. Install Dependencies

```bash
# Install dependencies for all Laravel services
for service in auth customer payment quickserve kitchen delivery analytics admin notification catalogue subscription meal misscall; do
    echo "Installing dependencies for ${service}-service-v12..."
    cd services/${service}-service-v12
    composer install
    cd ../..
done
```

### 4. Generate Application Keys

```bash
# Generate Laravel application keys
for service in auth customer payment quickserve kitchen delivery analytics admin notification catalogue subscription meal misscall; do
    echo "Generating key for ${service}-service-v12..."
    cd services/${service}-service-v12
    php artisan key:generate
    cd ../..
done
```

### 5. Start Development Environment

```bash
# Start all services with Docker Compose
docker-compose -f docker-compose.onefooddialer.yml up -d

# Verify services are running
./scripts/validate-deployment.sh
```

## Microservices Configuration

### Service-Specific Configuration

Each microservice requires specific configuration based on its role:

#### Auth Service Configuration
```php
// services/auth-service-v12/.env
APP_NAME=auth-service-v12
APP_URL=http://localhost:8101
DB_DATABASE=onefooddialer_auth
JWT_SECRET=your-jwt-secret-key
KEYCLOAK_REALM_URL=http://localhost:8080/auth/realms/demo
KEYCLOAK_CLIENT_ID=oneapp
KEYCLOAK_CLIENT_SECRET=your-client-secret
```

#### Payment Service Configuration
```php
// services/payment-service-v12/.env
APP_NAME=payment-service-v12
APP_URL=http://localhost:8104
DB_DATABASE=onefooddialer_payment
STRIPE_KEY=your-stripe-key
STRIPE_SECRET=your-stripe-secret
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-secret
```

#### QuickServe Service Configuration
```php
// services/quickserve-service-v12/.env
APP_NAME=quickserve-service-v12
APP_URL=http://localhost:8102
DB_DATABASE=onefooddialer_quickserve
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
```

### Port Allocation

| Service | Port | Purpose |
|---------|------|---------|
| auth-service-v12 | 8101 | Authentication |
| quickserve-service-v12 | 8102 | Core business |
| customer-service-v12 | 8103 | Customer management |
| payment-service-v12 | 8104 | Payment processing |
| kitchen-service-v12 | 8105 | Kitchen operations |
| delivery-service-v12 | 8106 | Delivery management |
| analytics-service-v12 | 8107 | Analytics |
| admin-service-v12 | 8108 | Admin operations |
| notification-service-v12 | 8109 | Notifications |
| catalogue-service-v12 | 8110 | Product catalog |
| subscription-service-v12 | 8111 | Subscriptions |
| meal-service-v12 | 8112 | Meal planning |
| misscall-service-v12 | 8113 | Missed calls |

## Observability Integration

### 1. Install Observability Middleware

```bash
# Set up comprehensive observability
./scripts/setup-comprehensive-observability.sh

# Validate observability stack
./scripts/validate-observability.sh
```

### 2. Configure Middleware in Laravel Services

Add to `app/Http/Kernel.php`:

```php
<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class Kernel extends HttpKernel
{
    protected $middleware = [
        // ... other middleware
        \App\Http\Middleware\ObservabilityMiddleware::class,
        \App\Http\Middleware\CircuitBreakerMiddleware::class,
    ];

    protected $middlewareGroups = [
        'api' => [
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\ObservabilityMiddleware::class,
        ],
    ];
}
```

### 3. Register Metrics Routes

Add to `routes/api.php`:

```php
<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MetricsController;

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'service' => config('app.name'),
        'timestamp' => now()->toISOString(),
    ]);
});

// Metrics endpoint for Prometheus
Route::get('/metrics', [MetricsController::class, 'index']);
```

### 4. Configure Logging

Update `config/logging.php`:

```php
<?php

return [
    'default' => env('LOG_CHANNEL', 'stack'),

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single', 'structured'],
            'ignore_exceptions' => false,
        ],

        'structured' => [
            'driver' => 'custom',
            'via' => \App\Logging\StructuredLogger::class,
            'level' => 'debug',
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],
    ],
];
```

## Database Management

### 1. Database Setup

Each microservice has its own database to maintain data isolation:

```bash
# Create databases for all services
mysql -u root -p << EOF
CREATE DATABASE onefooddialer_auth;
CREATE DATABASE onefooddialer_customer;
CREATE DATABASE onefooddialer_payment;
CREATE DATABASE onefooddialer_quickserve;
CREATE DATABASE onefooddialer_kitchen;
CREATE DATABASE onefooddialer_delivery;
CREATE DATABASE onefooddialer_analytics;
CREATE DATABASE onefooddialer_admin;
CREATE DATABASE onefooddialer_notification;
CREATE DATABASE onefooddialer_catalogue;
CREATE DATABASE onefooddialer_subscription;
CREATE DATABASE onefooddialer_meal;
CREATE DATABASE onefooddialer_misscall;
EOF
```

### 2. Run Migrations

```bash
# Run migrations for all services
for service in auth customer payment quickserve kitchen delivery analytics admin notification catalogue subscription meal misscall; do
    echo "Running migrations for ${service}-service-v12..."
    cd services/${service}-service-v12
    php artisan migrate
    cd ../..
done
```

### 3. Seed Development Data

```bash
# Seed databases with development data
for service in auth customer payment quickserve kitchen delivery analytics admin notification catalogue subscription meal misscall; do
    echo "Seeding data for ${service}-service-v12..."
    cd services/${service}-service-v12
    php artisan db:seed
    cd ../..
done
```

### 4. Database Migration Best Practices

```php
<?php
// Example migration with proper structure
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('customer_id')->constrained();
            $table->decimal('total_amount', 10, 2);
            $table->enum('status', ['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled']);
            $table->json('items');
            $table->json('metadata')->nullable();
            $table->timestamp('ordered_at');
            $table->timestamp('estimated_delivery_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['customer_id', 'status']);
            $table->index(['status', 'created_at']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('orders');
    }
};
```

## API Development & Testing

### 1. API Controller Structure

```php
<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateOrderRequest;
use App\Http\Resources\OrderResource;
use App\Services\OrderService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function __construct(
        private OrderService $orderService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $orders = $this->orderService->getPaginatedOrders(
            $request->user(),
            $request->validated()
        );

        return response()->json([
            'status' => 'success',
            'data' => OrderResource::collection($orders),
            'meta' => [
                'pagination' => [
                    'current_page' => $orders->currentPage(),
                    'total_pages' => $orders->lastPage(),
                    'total_items' => $orders->total(),
                ]
            ]
        ]);
    }

    public function store(CreateOrderRequest $request): JsonResponse
    {
        $order = $this->orderService->createOrder(
            $request->user(),
            $request->validated()
        );

        return response()->json([
            'status' => 'success',
            'message' => 'Order created successfully',
            'data' => new OrderResource($order)
        ], 201);
    }

    public function show(string $uuid): JsonResponse
    {
        $order = $this->orderService->getOrderByUuid($uuid);

        return response()->json([
            'status' => 'success',
            'data' => new OrderResource($order)
        ]);
    }
}
```

### 2. API Resource Classes

```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->uuid,
            'customer' => new CustomerResource($this->whenLoaded('customer')),
            'total_amount' => $this->total_amount,
            'status' => $this->status,
            'items' => OrderItemResource::collection($this->whenLoaded('items')),
            'ordered_at' => $this->ordered_at->toISOString(),
            'estimated_delivery_at' => $this->estimated_delivery_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }
}
```

### 3. Service Layer Implementation

```php
<?php

namespace App\Services;

use App\Models\Order;
use App\Models\User;
use App\Repositories\OrderRepository;
use App\Events\OrderCreated;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class OrderService
{
    public function __construct(
        private OrderRepository $orderRepository
    ) {}

    public function createOrder(User $customer, array $data): Order
    {
        return DB::transaction(function () use ($customer, $data) {
            $order = $this->orderRepository->create([
                'uuid' => Str::uuid(),
                'customer_id' => $customer->id,
                'total_amount' => $this->calculateTotal($data['items']),
                'status' => 'pending',
                'items' => $data['items'],
                'metadata' => $data['metadata'] ?? [],
                'ordered_at' => now(),
            ]);

            event(new OrderCreated($order));

            return $order;
        });
    }

    public function getPaginatedOrders(User $customer, array $filters): LengthAwarePaginator
    {
        return $this->orderRepository->getPaginatedByCustomer(
            $customer->id,
            $filters,
            $perPage = 15
        );
    }

    private function calculateTotal(array $items): float
    {
        return collect($items)->sum(fn($item) => $item['price'] * $item['quantity']);
    }
}
```

### 4. API Testing

```php
<?php

namespace Tests\Feature\Api\V2;

use App\Models\User;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_customer_can_create_order(): void
    {
        $customer = User::factory()->create();

        $orderData = [
            'items' => [
                [
                    'product_id' => 1,
                    'name' => 'Pizza Margherita',
                    'price' => 12.99,
                    'quantity' => 2
                ]
            ],
            'metadata' => [
                'delivery_address' => '123 Main St',
                'special_instructions' => 'Extra cheese'
            ]
        ];

        $response = $this->actingAs($customer)
            ->postJson('/api/v2/orders', $orderData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'status',
                'message',
                'data' => [
                    'id',
                    'total_amount',
                    'status',
                    'items',
                    'ordered_at'
                ]
            ]);

        $this->assertDatabaseHas('orders', [
            'customer_id' => $customer->id,
            'status' => 'pending',
            'total_amount' => 25.98
        ]);
    }

    public function test_customer_can_view_their_orders(): void
    {
        $customer = User::factory()->create();
        $orders = Order::factory()->count(3)->create(['customer_id' => $customer->id]);

        $response = $this->actingAs($customer)
            ->getJson('/api/v2/orders');

        $response->assertStatus(200)
            ->assertJsonCount(3, 'data')
            ->assertJsonStructure([
                'status',
                'data' => [
                    '*' => [
                        'id',
                        'total_amount',
                        'status',
                        'ordered_at'
                    ]
                ],
                'meta' => [
                    'pagination'
                ]
            ]);
    }
}
```

## Authentication & Authorization

### 1. Keycloak Integration

Configure Keycloak authentication in each service:

```php
<?php
// config/keycloak.php

return [
    'realm_url' => env('KEYCLOAK_REALM_URL', 'http://localhost:8080/auth/realms/demo'),
    'client_id' => env('KEYCLOAK_CLIENT_ID', 'oneapp'),
    'client_secret' => env('KEYCLOAK_CLIENT_SECRET'),
    'redirect_uri' => env('KEYCLOAK_REDIRECT_URI'),
    'public_key' => env('KEYCLOAK_PUBLIC_KEY'),
];
```

### 2. JWT Authentication Middleware

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\JsonResponse;

class JwtAuthMiddleware
{
    public function handle(Request $request, Closure $next): mixed
    {
        $token = $this->extractToken($request);

        if (!$token) {
            return $this->unauthorizedResponse('Token not provided');
        }

        try {
            $publicKey = config('keycloak.public_key');
            $decoded = JWT::decode($token, new Key($publicKey, 'RS256'));

            $request->merge(['jwt_payload' => $decoded]);
            $request->setUserResolver(function () use ($decoded) {
                return $this->createUserFromToken($decoded);
            });

        } catch (\Exception $e) {
            return $this->unauthorizedResponse('Invalid token: ' . $e->getMessage());
        }

        return $next($request);
    }

    private function extractToken(Request $request): ?string
    {
        $header = $request->header('Authorization');

        if (!$header || !str_starts_with($header, 'Bearer ')) {
            return null;
        }

        return substr($header, 7);
    }

    private function unauthorizedResponse(string $message): JsonResponse
    {
        return response()->json([
            'status' => 'error',
            'message' => $message,
            'code' => 'UNAUTHORIZED'
        ], 401);
    }

    private function createUserFromToken(object $decoded): object
    {
        return (object) [
            'id' => $decoded->sub,
            'email' => $decoded->email ?? null,
            'name' => $decoded->name ?? null,
            'roles' => $decoded->realm_access->roles ?? [],
            'permissions' => $decoded->resource_access ?? [],
        ];
    }
}
```

### 3. Role-Based Access Control

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class RoleMiddleware
{
    public function handle(Request $request, Closure $next, string ...$roles): mixed
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Authentication required',
                'code' => 'UNAUTHENTICATED'
            ], 401);
        }

        $userRoles = $user->roles ?? [];

        if (!array_intersect($roles, $userRoles)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Insufficient permissions',
                'code' => 'FORBIDDEN',
                'required_roles' => $roles,
                'user_roles' => $userRoles
            ], 403);
        }

        return $next($request);
    }
}
```

## Environment Configuration

### 1. Development Environment

```bash
# services/auth-service-v12/.env.development
APP_NAME=auth-service-v12
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8101

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=onefooddialer_auth
DB_USERNAME=root
DB_PASSWORD=password

CACHE_DRIVER=redis
QUEUE_CONNECTION=rabbitmq
SESSION_DRIVER=redis

REDIS_HOST=localhost
REDIS_PASSWORD=null
REDIS_PORT=6379

RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/

KEYCLOAK_REALM_URL=http://localhost:8080/auth/realms/demo
KEYCLOAK_CLIENT_ID=oneapp
KEYCLOAK_CLIENT_SECRET=your-client-secret

LOG_CHANNEL=stack
LOG_LEVEL=debug

OBSERVABILITY_ENABLED=true
METRICS_USERNAME=metrics
METRICS_PASSWORD=metrics123
```

### 2. Staging Environment

```bash
# services/auth-service-v12/.env.staging
APP_NAME=auth-service-v12
APP_ENV=staging
APP_DEBUG=false
APP_URL=https://api-staging.onefooddialer.com

DB_CONNECTION=mysql
DB_HOST=staging-db.onefooddialer.com
DB_PORT=3306
DB_DATABASE=onefooddialer_auth_staging
DB_USERNAME=${DB_USERNAME}
DB_PASSWORD=${DB_PASSWORD}

CACHE_DRIVER=redis
QUEUE_CONNECTION=rabbitmq
SESSION_DRIVER=redis

REDIS_HOST=staging-redis.onefooddialer.com
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_PORT=6379

RABBITMQ_HOST=staging-rabbitmq.onefooddialer.com
RABBITMQ_PORT=5672
RABBITMQ_USER=${RABBITMQ_USER}
RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD}
RABBITMQ_VHOST=/

KEYCLOAK_REALM_URL=https://auth-staging.onefooddialer.com/auth/realms/onefooddialer
KEYCLOAK_CLIENT_ID=onefooddialer-staging
KEYCLOAK_CLIENT_SECRET=${KEYCLOAK_CLIENT_SECRET}

LOG_CHANNEL=stack
LOG_LEVEL=info

OBSERVABILITY_ENABLED=true
METRICS_USERNAME=${METRICS_USERNAME}
METRICS_PASSWORD=${METRICS_PASSWORD}
```

### 3. Production Environment

```bash
# services/auth-service-v12/.env.production
APP_NAME=auth-service-v12
APP_ENV=production
APP_DEBUG=false
APP_URL=https://api.onefooddialer.com

DB_CONNECTION=mysql
DB_HOST=${DB_HOST}
DB_PORT=3306
DB_DATABASE=onefooddialer_auth
DB_USERNAME=${DB_USERNAME}
DB_PASSWORD=${DB_PASSWORD}

CACHE_DRIVER=redis
QUEUE_CONNECTION=rabbitmq
SESSION_DRIVER=redis

REDIS_HOST=${REDIS_HOST}
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_PORT=6379

RABBITMQ_HOST=${RABBITMQ_HOST}
RABBITMQ_PORT=5672
RABBITMQ_USER=${RABBITMQ_USER}
RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD}
RABBITMQ_VHOST=/

KEYCLOAK_REALM_URL=https://auth.onefooddialer.com/auth/realms/onefooddialer
KEYCLOAK_CLIENT_ID=onefooddialer
KEYCLOAK_CLIENT_SECRET=${KEYCLOAK_CLIENT_SECRET}

LOG_CHANNEL=stack
LOG_LEVEL=warning

OBSERVABILITY_ENABLED=true
METRICS_USERNAME=${METRICS_USERNAME}
METRICS_PASSWORD=${METRICS_PASSWORD}

# Security
SESSION_SECURE_COOKIE=true
SANCTUM_STATEFUL_DOMAINS=onefooddialer.com,*.onefooddialer.com
```

## Code Quality & Testing

### 1. PHPStan Configuration

Create `phpstan.neon` in each service:

```neon
parameters:
    level: max
    paths:
        - app
        - config
        - database
        - routes
    excludePaths:
        - app/Console/Kernel.php
        - app/Http/Kernel.php
    ignoreErrors:
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Builder#'
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
```

### 2. Pint Configuration

Create `pint.json`:

```json
{
    "preset": "laravel",
    "rules": {
        "simplified_null_return": true,
        "braces": {
            "position_after_control_structures": "same"
        },
        "new_with_braces": true,
        "no_unused_imports": true
    }
}
```

### 3. Testing Configuration

Update `phpunit.xml`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <coverage>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
        <report>
            <html outputDirectory="coverage-report"/>
            <clover outputFile="coverage.xml"/>
        </report>
    </coverage>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
```

### 4. Quality Assurance Script

Create `scripts/quality-check.sh`:

```bash
#!/bin/bash

SERVICE_NAME=$1

if [ -z "$SERVICE_NAME" ]; then
    echo "Usage: $0 <service-name>"
    exit 1
fi

SERVICE_DIR="services/${SERVICE_NAME}-service-v12"

echo "Running quality checks for $SERVICE_NAME..."

# Code formatting
echo "🔧 Running Pint..."
cd $SERVICE_DIR
./vendor/bin/pint --test

# Static analysis
echo "🔍 Running PHPStan..."
./vendor/bin/phpstan analyse --memory-limit=2G

# Tests with coverage
echo "🧪 Running tests with coverage..."
./vendor/bin/phpunit --coverage-html coverage-report --coverage-clover coverage.xml

# Check coverage threshold
echo "📊 Checking coverage threshold..."
php artisan test:coverage --min=95

echo "✅ Quality checks completed for $SERVICE_NAME"
```

## Deployment Process

### 1. GitLab CI/CD Pipeline

Create `.gitlab-ci.yml` for each service:

```yaml
stages:
  - test
  - build
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

before_script:
  - docker info

test:
  stage: test
  image: php:8.1-cli
  services:
    - mysql:8.0
  variables:
    MYSQL_DATABASE: testing
    MYSQL_ROOT_PASSWORD: password
    DB_HOST: mysql
    DB_DATABASE: testing
    DB_USERNAME: root
    DB_PASSWORD: password
  before_script:
    - apt-get update -qq && apt-get install -y -qq git curl libmcrypt-dev libjpeg-dev libpng-dev libfreetype6-dev libbz2-dev
    - docker-php-ext-install pdo_mysql
    - curl -sS https://getcomposer.org/installer | php
    - php composer.phar install --no-dev --no-scripts
  script:
    - php artisan key:generate
    - php artisan migrate
    - php artisan test --coverage --min=95
    - ./vendor/bin/phpstan analyse --memory-limit=2G
    - ./vendor/bin/pint --test
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $CI_REGISTRY_IMAGE/$SERVICE_NAME:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE/$SERVICE_NAME:$CI_COMMIT_SHA
  only:
    - main
    - develop

deploy_staging:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - curl -X POST "$WEBHOOK_URL_STAGING" -H "Content-Type: application/json" -d '{"image":"'$CI_REGISTRY_IMAGE/$SERVICE_NAME:$CI_COMMIT_SHA'","environment":"staging"}'
  only:
    - develop

deploy_production:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - curl -X POST "$WEBHOOK_URL_PRODUCTION" -H "Content-Type: application/json" -d '{"image":"'$CI_REGISTRY_IMAGE/$SERVICE_NAME:$CI_COMMIT_SHA'","environment":"production"}'
  only:
    - main
  when: manual
```

### 2. Docker Configuration

Create `Dockerfile` for each service:

```dockerfile
FROM php:8.1-fpm-alpine

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    mysql-client \
    nginx \
    supervisor

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application files
COPY . /var/www

# Install dependencies
RUN composer install --no-dev --optimize-autoloader

# Set permissions
RUN chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache

# Copy configuration files
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Expose port
EXPOSE 8000

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Database Connection Issues

**Problem**: `SQLSTATE[HY000] [2002] Connection refused`

**Solution**:
```bash
# Check database service status
docker-compose ps mysql

# Restart database service
docker-compose restart mysql

# Check database logs
docker-compose logs mysql

# Verify environment variables
grep DB_ services/auth-service-v12/.env
```

#### 2. Keycloak Authentication Failures

**Problem**: `Invalid token` or `Token verification failed`

**Solution**:
```bash
# Check Keycloak service status
curl http://localhost:8080/auth/health

# Verify realm configuration
curl http://localhost:8080/auth/realms/demo/.well-known/openid_configuration

# Check public key configuration
php artisan tinker
>>> config('keycloak.public_key')
```

#### 3. Kong Gateway Routing Issues

**Problem**: `404 Not Found` when accessing API endpoints

**Solution**:
```bash
# Check Kong admin API
curl http://localhost:8001/status

# List configured services
curl http://localhost:8001/services

# List configured routes
curl http://localhost:8001/routes

# Reload Kong configuration
curl -X POST http://localhost:8001/config
```

#### 4. Memory Issues During Testing

**Problem**: `Fatal error: Allowed memory size exhausted`

**Solution**:
```bash
# Increase memory limit for PHPStan
./vendor/bin/phpstan analyse --memory-limit=4G

# Update php.ini for testing
echo "memory_limit = 2G" >> /usr/local/etc/php/conf.d/memory.ini

# Use database transactions in tests
use Illuminate\Foundation\Testing\DatabaseTransactions;
```

#### 5. Queue Processing Issues

**Problem**: Jobs not being processed

**Solution**:
```bash
# Check RabbitMQ status
docker-compose logs rabbitmq

# Restart queue workers
php artisan queue:restart

# Check failed jobs
php artisan queue:failed

# Process specific queue
php artisan queue:work --queue=high,default
```

### Performance Optimization

#### 1. Database Query Optimization

```php
// Use eager loading to prevent N+1 queries
$orders = Order::with(['customer', 'items'])->get();

// Use database transactions for multiple operations
DB::transaction(function () {
    // Multiple database operations
});

// Use database indexes for frequently queried columns
Schema::table('orders', function (Blueprint $table) {
    $table->index(['customer_id', 'status']);
    $table->index(['created_at', 'status']);
});
```

#### 2. Caching Strategies

```php
// Cache expensive queries
$popularItems = Cache::remember('popular_items', 3600, function () {
    return Item::popular()->limit(10)->get();
});

// Use Redis for session storage
'redis' => [
    'driver' => 'redis',
    'connection' => 'cache',
],
```

#### 3. API Response Optimization

```php
// Use API resources for consistent formatting
return OrderResource::collection($orders);

// Implement pagination for large datasets
return $this->orderService->getPaginatedOrders($request->user(), $filters);

// Use HTTP caching headers
return response()->json($data)
    ->header('Cache-Control', 'public, max-age=3600');
```

---

## Conclusion

This guide provides comprehensive instructions for Laravel developers working on the OneFoodDialer 2025 microservices architecture. Follow the step-by-step instructions, maintain code quality standards, and use the troubleshooting section to resolve common issues.

For additional support, refer to the [DevOps Engineer Guide](DEVOPS_ENGINEER_GUIDE.md) for infrastructure concerns and the [Next.js Frontend Developer Guide](NEXTJS_FRONTEND_DEVELOPER_GUIDE.md) for frontend integration.

# Feature Flags

This document provides detailed information about the feature flag system used in the QuickServe frontend application.

## Overview

Feature flags (also known as feature toggles) are a software development technique that allows for the enabling or disabling of features without deploying new code. The QuickServe frontend uses feature flags to:

- Gradually roll out new features
- Conduct A/B testing
- Toggle features without deploying new code
- Customize the user experience based on user attributes

## Implementation

The QuickServe frontend uses [<PERSON><PERSON>](https://flagsmith.com/) for feature flag management. Flagsmith provides a simple API for checking feature flags and a web interface for managing them.

### Feature Flag Configuration

Feature flags are configured in `src/lib/feature-flags/index.ts`:

```typescript
// Define feature flag names as constants to avoid typos
export const FLAGS = {
  // Dashboard features
  DASHBOARD_CHARTS: 'dashboard_charts',
  DASHBOARD_QUICK_ACTIONS: 'dashboard_quick_actions',
  
  // Customer features
  CUSTOMER_GROUPS: 'customer_groups',
  CUSTOMER_LOYALTY: 'customer_loyalty',
  
  // Payment features
  PAYMENT_REFUNDS: 'payment_refunds',
  PAYMENT_SUBSCRIPTIONS: 'payment_subscriptions',
  
  // Order features
  ORDER_TRACKING: 'order_tracking',
  ORDER_HISTORY: 'order_history',
  
  // Kitchen features
  KITCHEN_INVENTORY: 'kitchen_inventory',
  KITCHEN_STAFF: 'kitchen_staff',
  
  // Delivery features
  DELIVERY_MAP: 'delivery_map',
  DELIVERY_AGENTS: 'delivery_agents',
  
  // Global features
  DARK_MODE: 'dark_mode',
  NOTIFICATIONS: 'notifications',
  ANALYTICS: 'analytics',
};

// Define feature flag default values
export const DEFAULT_FLAGS = {
  [FLAGS.DASHBOARD_CHARTS]: true,
  [FLAGS.DASHBOARD_QUICK_ACTIONS]: true,
  // ... other default values
};
```

### Feature Flag Provider

The feature flag provider is implemented in `src/lib/feature-flags/feature-flag-provider.tsx`:

```typescript
export function FeatureFlagProvider({ children }: FeatureFlagProviderProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [flags, setFlags] = useState<IState>({} as IState);
  
  useEffect(() => {
    const initializeFlags = async () => {
      try {
        setIsLoading(true);
        const success = await initFlags();
        
        if (success) {
          setFlags(getAllFlags());
          setError(null);
        } else {
          setError('Failed to initialize feature flags');
        }
      } catch (err) {
        setError('Error initializing feature flags');
        console.error('Error initializing feature flags:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeFlags();
  }, []);
  
  const value = {
    isLoading,
    error,
    flags,
    identifyUser,
    clearUserIdentity,
  };
  
  return (
    <FeatureFlagContext.Provider value={value}>
      {children}
    </FeatureFlagContext.Provider>
  );
}
```

### Feature Flag Hooks

Feature flag hooks are implemented in `src/lib/feature-flags/use-feature-flag.ts`:

```typescript
// Hook to use a boolean feature flag
export function useFeatureFlag(flagName: string): boolean {
  const [enabled, setEnabled] = useState<boolean>(
    DEFAULT_FLAGS[flagName as keyof typeof DEFAULT_FLAGS] || false
  );
  
  useEffect(() => {
    // Get the current value of the flag
    const flagValue = getFlag(flagName);
    setEnabled(flagValue);
    
    // Set up an interval to check for flag changes
    const interval = setInterval(() => {
      const newValue = getFlag(flagName);
      if (newValue !== enabled) {
        setEnabled(newValue);
      }
    }, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, [flagName, enabled]);
  
  return enabled;
}

// Typed hooks for specific features
export function useDashboardChartsFlag(): boolean {
  return useFeatureFlag(FLAGS.DASHBOARD_CHARTS);
}

// ... other typed hooks
```

### Feature Flag Components

Feature flag components are implemented in `src/lib/feature-flags/feature-flag.tsx`:

```typescript
/**
 * Component that conditionally renders content based on a feature flag
 */
export function FeatureFlag({ name, children, fallback = null }: FeatureFlagProps) {
  const enabled = useFeatureFlag(name);
  
  return enabled ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component that conditionally renders content based on multiple feature flags
 */
export function FeatureFlagGroup({ names, children, fallback = null, mode = 'all' }: FeatureFlagGroupProps) {
  const flags = names.map(name => useFeatureFlag(name));
  
  const shouldRender = mode === 'all'
    ? flags.every(flag => flag)
    : flags.some(flag => flag);
  
  return shouldRender ? <>{children}</> : <>{fallback}</>;
}
```

## Usage

### Using Feature Flags in Components

There are several ways to use feature flags in components:

#### 1. Using the `FeatureFlag` Component

```tsx
import { FeatureFlag } from "@/lib/feature-flags/feature-flag";
import { FLAGS } from "@/lib/feature-flags";

function MyComponent() {
  return (
    <div>
      <h1>My Component</h1>
      
      <FeatureFlag 
        name={FLAGS.DASHBOARD_CHARTS}
        fallback={<p>Charts are currently disabled</p>}
      >
        <MyChartComponent />
      </FeatureFlag>
    </div>
  );
}
```

#### 2. Using the `useFeatureFlag` Hook

```tsx
import { useFeatureFlag } from "@/lib/feature-flags/use-feature-flag";
import { FLAGS } from "@/lib/feature-flags";

function MyComponent() {
  const dashboardChartsEnabled = useFeatureFlag(FLAGS.DASHBOARD_CHARTS);
  
  return (
    <div>
      <h1>My Component</h1>
      
      {dashboardChartsEnabled ? (
        <MyChartComponent />
      ) : (
        <p>Charts are currently disabled</p>
      )}
    </div>
  );
}
```

#### 3. Using Typed Hooks

```tsx
import { useDashboardChartsFlag } from "@/lib/feature-flags/use-feature-flag";

function MyComponent() {
  const dashboardChartsEnabled = useDashboardChartsFlag();
  
  return (
    <div>
      <h1>My Component</h1>
      
      {dashboardChartsEnabled ? (
        <MyChartComponent />
      ) : (
        <p>Charts are currently disabled</p>
      )}
    </div>
  );
}
```

### Managing Feature Flags

Feature flags can be managed through the Feature Flag Admin Panel at `/admin/feature-flags`. This panel allows administrators to:

- View all feature flags
- Toggle feature flags on and off
- Search for specific feature flags
- Filter feature flags by category

## Best Practices

### 1. Use Constants for Flag Names

Always use the constants defined in `FLAGS` for flag names to avoid typos.

```typescript
// Good
const dashboardChartsEnabled = useFeatureFlag(FLAGS.DASHBOARD_CHARTS);

// Bad
const dashboardChartsEnabled = useFeatureFlag('dashboard_charts');
```

### 2. Provide Fallbacks

Always provide fallbacks when using the `FeatureFlag` component to ensure a good user experience when a feature is disabled.

```typescript
<FeatureFlag 
  name={FLAGS.DASHBOARD_CHARTS}
  fallback={<p>Charts are currently disabled</p>}
>
  <MyChartComponent />
</FeatureFlag>
```

### 3. Use Typed Hooks When Possible

Use the typed hooks for specific features when possible to improve type safety and readability.

```typescript
// Good
const dashboardChartsEnabled = useDashboardChartsFlag();

// Less good
const dashboardChartsEnabled = useFeatureFlag(FLAGS.DASHBOARD_CHARTS);
```

### 4. Clean Up Feature Flags

Remove feature flags when they are no longer needed. Feature flags should be temporary and should be removed once a feature is fully rolled out.

### 5. Document Feature Flags

Document all feature flags in the codebase, including their purpose and default value.

## Conclusion

Feature flags provide a powerful mechanism for controlling the availability of features in the QuickServe frontend application. By using feature flags, we can gradually roll out new features, conduct A/B testing, and toggle features without deploying new code.

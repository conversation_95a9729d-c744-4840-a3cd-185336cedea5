namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\KeycloakClient;
use Zend\Http\Client;

class KeycloakClientFactory implements FactoryInterface
{
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $config = $serviceLocator->get('Config');
        $keycloakConfig = isset($config['keycloak']) ? $config['keycloak'] : [];
        
        // Get auth mode from settings
        $configService = $serviceLocator->get('ConfigService');
        $authMode = $configService->getConfig('auth_mode', 'legacy');
        
        // Only create the client if auth mode is keycloak
        if ($authMode === 'keycloak' && !empty($keycloakConfig)) {
            $httpClient = new Client();
            return new KeycloakClient($keycloakConfig, $httpClient);
        }
        
        // Return a dummy client if auth mode is not keycloak
        return new KeycloakClient([
            'auth_server_url' => '',
            'realm' => '',
            'client_id' => '',
            'client_secret' => '',
            'redirect_uri' => ''
        ]);
    }
}
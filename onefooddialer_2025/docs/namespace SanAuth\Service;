namespace SanAuth\Service;

use Zend\Db\Adapter\Adapter;
use Zend\Db\Sql\Sql;

class OnessoUserService
{
    protected $dbAdapter;

    public function __construct(Adapter $dbAdapter)
    {
        $this->dbAdapter = $dbAdapter;
    }

    // Methods for OneSso user management:
    // - getOnessoUserByUserId(): Gets OneSso user by user ID
    // - getOnessoUserByKeycloakId(): Gets OneSso user by Keycloak ID
    // - saveOnessoUser(): Saves OneSso user data
    // - saveTokens(): Saves tokens for a user
    // - getTokens(): Gets tokens for a user
    // - clearTokens(): Clears tokens for a user
}
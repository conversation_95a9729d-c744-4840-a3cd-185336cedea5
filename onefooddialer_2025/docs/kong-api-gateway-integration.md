# Kong API Gateway Integration Guide for OneFoodDialer 2025

## Overview

This document provides comprehensive guidance for integrating Kong API Gateway with the OneFoodDialer 2025 microservices architecture. Kong serves as the central API gateway, handling authentication, rate limiting, CORS, monitoring, and routing for all microservices.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Mobile App    │    │   Admin Panel   │
│   (Next.js)     │    │   (React)       │    │   (Next.js)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Kong API Gateway      │
                    │   (Authentication, CORS,  │
                    │   Rate Limiting, Routing) │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────▼─────┐        ┌─────▼─────┐        ┌─────▼─────┐
    │   Auth    │        │ Customer  │        │  Payment  │
    │ Service   │        │ Service   │        │ Service   │
    │   v12     │        │   v12     │        │   v12     │
    └───────────┘        └───────────┘        └───────────┘
```

## Kong Configuration

### Service Discovery Pattern

All microservices follow the routing pattern: `/v2/{service-name}/*`

**Examples:**
- Customer Service: `/v2/customer-service-v12/*`
- Admin Service: `/v2/admin-service-v12/*`
- Payment Service: `/v2/payment-service-v12/*`

### Authentication Flow

1. **JWT Token Generation**: Auth service issues RS256 JWT tokens
2. **Token Validation**: Kong validates tokens using public keys
3. **Claims Verification**: Kong verifies exp, nbf, iat claims
4. **Role-Based Access**: ACL plugin enforces role-based access control

### Rate Limiting Strategy

| Service | Minute | Hour | Day | Notes |
|---------|--------|------|-----|-------|
| Auth Service | 100 | 2,000 | 20,000 | Higher limits for authentication |
| Admin Service | 120 | 5,000 | 50,000 | Higher limits for admin operations |
| Customer Service | 60 | 1,000 | 10,000 | Standard customer operations |
| Payment Service | 30 | 500 | 2,000 | Lower limits for payment security |
| Meal Service | 60 | 1,000 | 10,000 | Standard meal operations |

## OpenAPI Specifications

### Kong-Compliant Features

All OpenAPI specifications include:

1. **Kong Extensions**:
   - `x-kong-service`: Service configuration
   - `x-kong-route`: Route configuration
   - `x-kong-plugins`: Plugin configurations

2. **Headers**:
   - `X-Correlation-ID`: Request tracing
   - `X-RateLimit-*`: Rate limit information
   - `X-Service-Name`: Service identification

3. **Error Responses**:
   - `401 Unauthorized`: Authentication required
   - `403 Forbidden`: Access denied
   - `429 Rate Limit Exceeded`: Rate limit hit
   - `503 Service Unavailable`: Service down

### Example Kong Extension

```yaml
x-kong-service:
  name: customer-service-v12
  url: http://customer-service-v12:8000
  connect_timeout: 60000
  write_timeout: 60000
  read_timeout: 60000
  retries: 5

x-kong-plugins:
  - name: jwt
    config:
      algorithm: RS256
      maximum_expiration: 86400
  - name: rate-limiting
    config:
      minute: 60
      hour: 1000
```

## Frontend Integration

### API Client Configuration

```typescript
// Kong-aware API client
const KONG_SERVICE_CONFIG = {
  serviceName: 'customer-service-v12',
  basePath: '/v2/customer-service-v12',
  rateLimit: {
    minute: 60,
    hour: 1000,
    day: 10000
  }
};

// Enhanced headers for Kong
const addKongHeaders = (headers = {}) => ({
  ...headers,
  'X-Correlation-ID': generateCorrelationId(),
  'X-Gateway': 'kong',
  'X-Service': KONG_SERVICE_CONFIG.serviceName,
});
```

### Rate Limit Handling

```typescript
interface RateLimitError {
  status: 'error';
  error_code: 'RATE_LIMIT_EXCEEDED';
  retry_after: number;
  limit: number;
  remaining: number;
  reset: number;
}

// Handle rate limit errors
try {
  const response = await apiClient.get('/customers');
} catch (error) {
  if (error.response?.status === 429) {
    const retryAfter = error.response.headers['retry-after'];
    // Implement exponential backoff
    await delay(retryAfter * 1000);
    // Retry request
  }
}
```

## Deployment Guide

### 1. Using Kong Deck (Recommended)

```bash
# Install Kong Deck
curl -sL https://github.com/Kong/deck/releases/download/v1.17.0/deck_1.17.0_linux_amd64.tar.gz -o deck.tar.gz
tar -xf deck.tar.gz -C /tmp
sudo cp /tmp/deck /usr/local/bin/

# Deploy configuration
deck sync --kong-addr http://localhost:8001 --state services/gateway/kong/kong-deployment-config.yaml
```

### 2. Using Kong Admin API

```bash
# Upload configuration
curl -X POST http://localhost:8001/config \
  -F config=@services/gateway/kong/kong-deployment-config.yaml
```

### 3. Using Docker Compose

```yaml
version: '3.8'
services:
  kong:
    image: kong:3.4.0
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /kong/declarative/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
    volumes:
      - ./services/gateway/kong/kong-deployment-config.yaml:/kong/declarative/kong.yml
    ports:
      - "8000:8000"
      - "8001:8001"
```

## Monitoring and Observability

### Prometheus Metrics

Kong exposes metrics at `/metrics` endpoint:

```
# Request metrics
kong_http_requests_total{service="customer-service-v12",route="customer-service-route",code="200"} 1500

# Latency metrics
kong_request_latency_ms{service="customer-service-v12",route="customer-service-route",type="request"} 45.2

# Rate limit metrics
kong_rate_limiting_requests{service="customer-service-v12",identifier="admin-dashboard"} 58
```

### Health Checks

Each service provides health check endpoints:

```bash
# Gateway health
curl http://localhost:8000/health

# Service-specific health
curl http://localhost:8000/v2/customer-service-v12/health
curl http://localhost:8000/v2/admin-service-v12/health
```

### Correlation ID Tracking

All requests include correlation IDs for distributed tracing:

```
X-Correlation-ID: 123e4567-e89b-12d3-a456-426614174000
```

## Security Configuration

### JWT Authentication

```yaml
jwt:
  algorithm: RS256
  claims_to_verify: [exp, nbf, iat]
  key_claim_name: iss
  maximum_expiration: 86400  # 24 hours
```

### CORS Configuration

```yaml
cors:
  origins:
    - "https://app.onefooddialer.com"
    - "https://admin.onefooddialer.com"
  methods: [GET, POST, PUT, PATCH, DELETE, OPTIONS]
  credentials: true
  max_age: 3600
```

### ACL (Access Control Lists)

```yaml
acl:
  whitelist:
    - admin      # Admin users
    - customer   # Customer users
    - mobile     # Mobile app
    - web        # Web app
```

## Performance Optimization

### Connection Pooling

```yaml
upstream:
  healthchecks:
    active:
      healthy:
        interval: 5
        successes: 3
      unhealthy:
        interval: 5
        http_failures: 3
```

### Circuit Breaker

```yaml
circuit_breaker:
  failure_threshold: 10
  recovery_timeout: 30
  window_size: 60
```

### Caching

```yaml
proxy_cache:
  cache_ttl: 300
  cache_control: true
  vary_headers: ["Accept-Encoding"]
```

## Troubleshooting

### Common Issues

1. **Rate Limit Exceeded**
   - Check rate limit headers
   - Implement exponential backoff
   - Consider upgrading rate limits

2. **JWT Validation Failures**
   - Verify public key configuration
   - Check token expiration
   - Validate token claims

3. **CORS Errors**
   - Verify origin configuration
   - Check preflight handling
   - Validate headers

### Debug Commands

```bash
# Check Kong status
curl http://localhost:8001/status

# List services
curl http://localhost:8001/services

# List routes
curl http://localhost:8001/routes

# Check plugins
curl http://localhost:8001/plugins
```

## Best Practices

1. **API Versioning**: Use `/v2/` prefix for all routes
2. **Error Handling**: Implement consistent error responses
3. **Rate Limiting**: Set appropriate limits per service
4. **Monitoring**: Use Prometheus for metrics collection
5. **Security**: Always use HTTPS in production
6. **Documentation**: Keep OpenAPI specs updated
7. **Testing**: Test Kong configuration in staging
8. **Backup**: Regular backup of Kong configuration

## Migration Strategy

1. **Phase 1**: Deploy Kong alongside existing services
2. **Phase 2**: Route health check endpoints through Kong
3. **Phase 3**: Migrate authentication to Kong JWT
4. **Phase 4**: Enable rate limiting and CORS
5. **Phase 5**: Full migration of all endpoints
6. **Phase 6**: Remove direct service access

This comprehensive integration ensures robust, scalable, and secure API management for the OneFoodDialer 2025 platform.

# Getting Started

This guide provides instructions for setting up the development environment and running the QuickServe frontend application locally.

## Prerequisites

Before you begin, make sure you have the following installed on your system:

- **Node.js**: Version 18.x or higher
- **npm**: Version 9.x or higher
- **Git**: Latest version

You can check your Node.js and npm versions by running:

```bash
node --version
npm --version
```

## Clone the Repository

1. Clone the repository:

```bash
git clone <repository-url>
cd unified-frontend
```

## Install Dependencies

1. Install the dependencies:

```bash
npm install
```

This will install all the dependencies required for the application.

## Set Up Environment Variables

1. Create a `.env.local` file by copying the example file:

```bash
cp .env.example .env.local
```

2. Open the `.env.local` file and update the environment variables as needed:

```
# API
NEXT_PUBLIC_API_URL=http://localhost:8000

# Feature Flags
NEXT_PUBLIC_FLAGSMITH_ENVIRONMENT_ID=development

# Authentication
NEXT_PUBLIC_AUTH_URL=http://localhost:8001
```

## Start the Development Server

1. Start the development server:

```bash
npm run dev
```

2. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Project Structure

The project follows a structured organization:

```
unified-frontend/
├── docs/                  # Documentation
├── public/                # Static assets
├── src/
│   ├── app/               # Next.js app router pages
│   │   ├── (auth)/        # Authentication pages
│   │   ├── (microfrontends)/ # Microfrontend pages
│   │   │   ├── customer/  # Customer microfrontend pages
│   │   │   ├── payment/   # Payment microfrontend pages
│   │   │   ├── order/     # Order microfrontend pages
│   │   │   ├── kitchen/   # Kitchen microfrontend pages
│   │   │   ├── delivery/  # Delivery microfrontend pages
│   │   │   └── dashboard/ # Dashboard pages
│   ├── components/        # React components
│   │   ├── ui/            # Shared UI components
│   │   ├── layout/        # Layout components
│   │   ├── microfrontends/ # Microfrontend-specific components
│   │   │   ├── customer/  # Customer components
│   │   │   ├── payment/   # Payment components
│   │   │   ├── order/     # Order components
│   │   │   ├── kitchen/   # Kitchen components
│   │   │   └── delivery/  # Delivery components
│   ├── lib/               # Utility functions and shared code
│   │   ├── api/           # API client
│   │   ├── feature-flags/ # Feature flag configuration
│   │   ├── i18n/          # Internationalization
│   │   ├── store/         # Zustand stores
│   │   └── utils/         # Utility functions
│   ├── services/          # Service layer for API communication
│   ├── types/             # TypeScript type definitions
│   └── styles/            # Global styles
├── .env.example           # Example environment variables
├── .eslintrc.json         # ESLint configuration
├── .gitlab-ci.yml         # GitLab CI/CD configuration
├── jest.config.js         # Jest configuration
├── next.config.js         # Next.js configuration
├── package.json           # npm package configuration
├── tailwind.config.js     # Tailwind CSS configuration
└── tsconfig.json          # TypeScript configuration
```

## Available Scripts

The following scripts are available:

- `npm run dev`: Start the development server
- `npm run build`: Build the application for production
- `npm start`: Start the production server
- `npm run lint`: Run ESLint to check for code quality issues
- `npm test`: Run Jest tests
- `npm run test:watch`: Run Jest tests in watch mode
- `npm run test:coverage`: Run Jest tests with coverage

## Development Workflow

1. Create a feature branch from `develop`:

```bash
git checkout develop
git pull
git checkout -b feature/my-feature
```

2. Make your changes and commit them:

```bash
git add .
git commit -m "Add my feature"
```

3. Push your changes to the remote repository:

```bash
git push -u origin feature/my-feature
```

4. Create a merge request from your feature branch to `develop`.

## Running Tests

1. Run the tests:

```bash
npm test
```

2. Run the tests with coverage:

```bash
npm run test:coverage
```

## Building for Production

1. Build the application for production:

```bash
npm run build
```

2. Start the production server:

```bash
npm start
```

## Troubleshooting

### Common Issues

#### 1. Node.js Version

If you encounter issues with Node.js version, make sure you are using Node.js 18.x or higher. You can use [nvm](https://github.com/nvm-sh/nvm) to manage multiple Node.js versions.

#### 2. Port Already in Use

If port 3000 is already in use, you can specify a different port:

```bash
npm run dev -- -p 3001
```

#### 3. API Connection Issues

If you encounter issues connecting to the API, make sure the API server is running and the `NEXT_PUBLIC_API_URL` environment variable is set correctly.

#### 4. TypeScript Errors

If you encounter TypeScript errors, try running:

```bash
npm run lint
```

This will check for TypeScript errors and other code quality issues.

## Next Steps

Now that you have set up the development environment and run the application locally, you can:

1. Explore the codebase to understand the architecture and components
2. Read the [Development Guidelines](development-guidelines.md) to learn about the development process
3. Follow the [Adding a New Feature](adding-a-new-feature.md) guide to add a new feature to the application

## Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://reactjs.org/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Shadcn UI Documentation](https://ui.shadcn.com)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [Jest Documentation](https://jestjs.io/docs)
- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro)

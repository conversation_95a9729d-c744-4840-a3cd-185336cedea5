# Development Guidelines

This guide provides guidelines for developing new features and making changes to the QuickServe frontend application.

## Code Style

### TypeScript

- Use TypeScript for all new code
- Define interfaces for all props, state, and API responses
- Use type inference where possible
- Use union types for props with a limited set of values
- Use optional properties with `?` for props that are not required
- Use readonly properties for props that should not be modified

Example:

```tsx
interface ButtonProps {
  readonly variant?: 'default' | 'outline' | 'ghost';
  readonly size?: 'sm' | 'md' | 'lg';
  readonly disabled?: boolean;
  readonly children: React.ReactNode;
  readonly onClick?: () => void;
}
```

### ESLint

- Follow the ESLint configuration
- Run `npm run lint` to check for code quality issues
- Fix all ESLint errors before committing

### Formatting

- Use Prettier for code formatting
- Use 2 spaces for indentation
- Use single quotes for strings
- Use semicolons at the end of statements
- Use trailing commas in multiline objects and arrays
- Limit line length to 100 characters

## Component Guidelines

### Component Structure

- Use functional components with hooks
- Use PascalCase for component names
- Use named exports for components
- Place components in the appropriate directory based on their purpose
- Use the following structure for components:

```tsx
// Imports
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from '@/lib/i18n/use-translations';
import { Button } from '@/components/ui/button';

// Types
interface CustomerListProps {
  initialPage?: number;
  perPage?: number;
}

// Component
export function CustomerList({ initialPage = 1, perPage = 10 }: CustomerListProps) {
  // Hooks
  const router = useRouter();
  const { t } = useTranslations();
  
  // State
  const [page, setPage] = useState(initialPage);
  
  // Effects
  useEffect(() => {
    // Effect logic
  }, [page]);
  
  // Event handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };
  
  // Render
  return (
    <div>
      {/* Component JSX */}
    </div>
  );
}
```

### Props

- Use TypeScript interfaces for props
- Use default values for optional props
- Use destructuring to access props
- Use readonly for props that should not be modified

### State Management

- Use React hooks for local state
- Use Zustand stores for shared state
- Use the appropriate store for each microfrontend
- Keep state as close to where it's used as possible

### Side Effects

- Use the `useEffect` hook for side effects
- Include all dependencies in the dependency array
- Clean up side effects when the component unmounts

### Event Handlers

- Use the `handle` prefix for event handlers
- Use arrow functions for event handlers
- Pass event handlers as props to child components

### Conditional Rendering

- Use the ternary operator for simple conditions
- Use the logical AND operator for conditions with a single outcome
- Use early returns for complex conditions

### Lists

- Use the `key` prop with a unique identifier
- Use the `map` function to render lists
- Use the `filter` function to filter lists

### Styling

- Use Tailwind CSS for styling
- Use the `className` prop for styling
- Use the `cn` utility function to conditionally apply classes
- Use the `style` prop only when necessary

### Accessibility

- Use semantic HTML elements
- Use ARIA attributes when necessary
- Use the `aria-label` attribute for elements without visible text
- Use the `role` attribute to define the role of an element
- Use the `tabIndex` attribute to control focus order

## State Management

### Zustand Stores

- Create a separate store for each microfrontend
- Use the `create` function to create a store
- Use the `set` function to update state
- Use the `get` function to access state
- Use the store in components with the `useStore` hook

Example:

```tsx
// src/lib/store/customer-store.ts
import { create } from 'zustand';
import { Customer, CustomerService } from '@/services/customer-service';

interface CustomerState {
  customers: Customer[];
  selectedCustomer: Customer | null;
  isLoading: boolean;
  error: string | null;
  
  fetchCustomers: () => Promise<void>;
  fetchCustomer: (id: number) => Promise<void>;
  setSelectedCustomer: (customer: Customer | null) => void;
  clearError: () => void;
}

export const useCustomerStore = create<CustomerState>((set, get) => ({
  customers: [],
  selectedCustomer: null,
  isLoading: false,
  error: null,
  
  fetchCustomers: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await CustomerService.getCustomers();
      set({ customers: response.data, isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
    }
  },
  
  fetchCustomer: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await CustomerService.getCustomer(id);
      set({ selectedCustomer: response.data, isLoading: false });
    } catch (error: any) {
      set({ error: error.message, isLoading: false });
    }
  },
  
  setSelectedCustomer: (customer: Customer | null) => {
    set({ selectedCustomer: customer });
  },
  
  clearError: () => {
    set({ error: null });
  },
}));
```

### Using Stores in Components

```tsx
import { useCustomerStore } from '@/lib/store/customer-store';

function CustomerList() {
  const { customers, isLoading, error, fetchCustomers } = useCustomerStore();
  
  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);
  
  // Component logic
}
```

## API Integration

### API Client

- Use the API client for all API requests
- Use the appropriate API client for each microservice
- Use the `apiRequest` function to make API requests
- Use TypeScript types for request and response types

Example:

```tsx
import { customerApiClient, apiRequest } from '@/lib/api/api-client';

export interface CustomerListResponse {
  data: Customer[];
  meta: {
    total: number;
    current_page: number;
    per_page: number;
    last_page: number;
  };
}

export const CustomerService = {
  getCustomers: (params?: { page?: number; per_page?: number; search?: string }) =>
    apiRequest<CustomerListResponse>(customerApiClient, {
      method: 'GET',
      url: '/customers',
      params,
      cache: true,
    }),
};
```

### Error Handling

- Use try-catch blocks to handle API errors
- Set the error state in the store
- Display error messages to the user
- Provide a way to retry failed requests

## Testing

### Unit Tests

- Write unit tests for all components and functions
- Use Jest and React Testing Library
- Mock external dependencies
- Test component rendering, user interactions, and state changes
- Aim for high test coverage

Example:

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { CustomerList } from '../customer-list';
import { useCustomerStore } from '@/lib/store/customer-store';

// Mock the store
jest.mock('@/lib/store/customer-store');

describe('CustomerList', () => {
  beforeEach(() => {
    (useCustomerStore as jest.Mock).mockReturnValue({
      customers: [],
      isLoading: false,
      error: null,
      fetchCustomers: jest.fn(),
    });
  });

  it('renders the customer list', () => {
    render(<CustomerList />);
    expect(screen.getByText('Customers')).toBeInTheDocument();
  });

  // ... other tests
});
```

### Integration Tests

- Write integration tests for complex workflows
- Test the interaction between components
- Test the integration with the API
- Use mock API responses

### End-to-End Tests

- Write end-to-end tests for critical user flows
- Use Cypress or Playwright
- Test the application as a whole
- Test the integration with the backend

## Documentation

### Code Documentation

- Use JSDoc comments for functions and components
- Document props, return values, and side effects
- Document complex logic and algorithms
- Document API endpoints and responses

Example:

```tsx
/**
 * CustomerList component displays a list of customers with search, filtering, and pagination.
 *
 * @param initialPage - The initial page to display (default: 1)
 * @param perPage - The number of items per page (default: 10)
 */
export function CustomerList({ initialPage = 1, perPage = 10 }: CustomerListProps) {
  // Component logic
}
```

### README

- Update the README with new features and changes
- Document the purpose and usage of the application
- Document the development workflow
- Document the deployment process

### Documentation Files

- Update the documentation files with new features and changes
- Document the architecture and design decisions
- Document the API endpoints and responses
- Document the component structure and usage

## Git Workflow

### Branching

- Use the GitFlow branching model
- Create feature branches from `develop`
- Create release branches from `develop`
- Create hotfix branches from `main`
- Merge feature branches into `develop`
- Merge release branches into `main` and `develop`
- Merge hotfix branches into `main` and `develop`

### Commits

- Use descriptive commit messages
- Use the imperative mood in commit messages
- Reference issue numbers in commit messages
- Keep commits small and focused

### Merge Requests

- Create merge requests for all changes
- Assign merge requests to a reviewer
- Include a description of the changes
- Include screenshots or videos for UI changes
- Include test results
- Address all review comments

## Conclusion

Following these guidelines will help ensure that the QuickServe frontend application is maintainable, scalable, and of high quality. If you have any questions or suggestions, please reach out to the frontend team.

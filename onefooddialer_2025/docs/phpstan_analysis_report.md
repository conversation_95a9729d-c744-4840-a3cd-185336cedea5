# PHPStan Static Analysis Report

## Overview

This document contains the results of a static analysis of the codebase using PHPStan. The analysis was performed to identify potential issues and improve code quality.

## Analysis Configuration

- **Tool**: PHPStan
- **Level**: 0 (Basic level)
- **Target**: Multiple modules analyzed sequentially
- **Memory Limit**: 512MB

## Summary of Findings

The static analysis identified several categories of issues in the codebase:

1. **Unknown Classes**: References to classes that PHPStan cannot find
2. **Undefined Properties**: Access to properties that are not defined in the class
3. **Static Method Calls on Unknown Classes**: Calls to static methods on classes that PHPStan cannot find
4. **Access to Constants on Unknown Classes**: Access to constants on classes that PHPStan cannot find
5. **Undefined Methods**: Calls to methods that are not defined in the class
6. **Missing Return Statements**: Methods that should return a value but don't have a return statement
7. **Undefined Variables**: References to variables that are not defined in the scope
8. **Duplicate Array Keys**: Arrays with duplicate keys

## Detailed Findings

### 1. Unknown Classes

PHPStan could not find several classes that are instantiated or referenced in the codebase. This could be due to:

- Classes not being properly autoloaded
- Classes being in non-standard locations
- Missing dependencies

#### Examples:

```php
// Instantiated class not found
Instantiated class Lib\CronHelper\CronLock not found.
Instantiated class Lib\Email\Email not found.
Instantiated class Lib\QuickServe\Db\Sql\QSelect not found.
Instantiated class DOMPDFModule\View\Model\PdfModel not found.
Instantiated class Lib\Email\Storage\MailDatabaseStorage not found.
Instantiated class Lib\Email\Queue not found.
```

### 2. Undefined Properties

PHPStan detected access to properties that are not defined in the class. This could lead to runtime errors or unexpected behavior.

#### Examples:

```php
// Access to undefined properties
Access to an undefined property Misscall\Controller\PreorderController::$emailTable.
Access to an undefined property Misscall\Controller\PreorderController::$smsTable.
Access to an undefined property Misscall\Controller\PreorderController::$_tblCustomer.
```

### 3. Static Method Calls on Unknown Classes

PHPStan detected calls to static methods on classes that it cannot find. This could lead to runtime errors.

#### Examples:

```php
// Static method calls on unknown classes
Call to static method getInstance() on an unknown class Lib\QuickServe\CommonConfig.
Call to static method getInstance() on an unknown class Lib\QuickServe\Order.
Call to static method getInstance() on an unknown class Lib\QuickServe\Wallet.
Call to static method getInstance() on an unknown class Lib\Utility.
Call to static method getInstance() on an unknown class Lib\QuickServe\Payment.
Call to static method getInstance() on an unknown class Lib\QuickServe\Customer.
Call to static method getInstance() on an unknown class Lib\TPDelivery\YourGuy.
```

### 4. Access to Constants on Unknown Classes

PHPStan detected access to constants on classes that it cannot find. This could lead to runtime errors.

#### Examples:

```php
// Access to constants on unknown classes
Access to constant PRIORITY_SEND_IMMEDIATELY on an unknown class Lib\Email\Email.
```

### 5. Undefined Methods

PHPStan detected calls to methods that are not defined in the class. This could lead to runtime errors.

#### Examples:

```php
// Calls to undefined methods
Call to an undefined method Misscall\Model\MisscallTable::selectWith().
Call to an undefined method Misscall\Model\MisscallTable::insert().
Call to an undefined method Misscall\Model\MisscallTable::update().
Call to an undefined method SanAuth\Model\ForgotPasswordTable::selectWith().
```

### 6. Missing Return Statements

PHPStan detected methods that should return a value but don't have a return statement. This could lead to unexpected behavior.

#### Examples:

```php
// Methods missing return statements
Method SanAuth\Module::onBootstrap() should return SanAuth\voidsmstemplate but return statement is missing.
Method Misscall\Model\MisscallTable::totalOrderAmount() should return int but return statement is missing.
Method Misscall\Model\PreorderTable::getTodayOrder() should return string but return statement is missing.
Method Misscall\Model\PreorderTable::createInvoices() should return array but return statement is missing.
```

### 7. Undefined Variables

PHPStan detected references to variables that are not defined in the scope. This could lead to runtime errors.

#### Examples:

```php
// References to undefined variables
Undefined variable: $sm
Undefined variable: $promo_code_message
Undefined variable: $product
Undefined variable: $child_data
```

### 8. Duplicate Array Keys

PHPStan detected arrays with duplicate keys. This could lead to unexpected behavior.

#### Examples:

```php
// Arrays with duplicate keys
Array has 2 duplicate keys with value 'kitchen_master' ('kitchen_master', 'kitchen_master').
```

## Module Analysis Results

The following modules were analyzed with PHPStan:

| Module | Status | Issues |
|--------|--------|--------|
| EdpModuleLayouts | ✅ No errors | 0 |
| SanAuth | ❌ Errors found | 36 |
| Theme | ✅ No errors | 0 |
| Misscall | ❌ Errors found | 143 |
| Api | ❌ Analysis failed | N/A |
| Api-new | ❌ Analysis failed | N/A |
| QuickServe | ❌ Analysis failed | N/A |

Several modules could not be fully analyzed due to memory constraints or other issues.

## Recommendations

Based on the analysis, here are some recommendations to improve the codebase:

1. **Fix Autoloading**: Ensure that all classes are properly autoloaded. This may involve updating the autoloader configuration or moving classes to standard locations.

2. **Define Properties**: Ensure that all properties accessed in a class are properly defined in the class or its parent classes.

3. **Use Dependency Injection**: Instead of using static method calls like `getInstance()`, consider using dependency injection to make the code more testable and maintainable.

4. **Add PHPDoc Annotations**: Add proper PHPDoc annotations to classes, methods, and properties to help PHPStan understand the code better.

5. **Create a PHPStan Configuration**: Create a proper PHPStan configuration file that includes all necessary paths and bootstrap files.

6. **Fix Undefined Methods**: Ensure that all methods called on an object are properly defined in the class or its parent classes.

7. **Add Missing Return Statements**: Add return statements to methods that should return a value.

8. **Initialize Variables**: Ensure that all variables are properly initialized before they are used.

9. **Fix Duplicate Array Keys**: Ensure that arrays do not have duplicate keys.

## Next Steps

1. **Fix Critical Issues**: Address the most critical issues identified by PHPStan, such as undefined properties and unknown classes.

2. **Increase Analysis Level**: Once the basic issues are fixed, increase the PHPStan analysis level to identify more subtle issues.

3. **Integrate with CI/CD**: Consider integrating PHPStan into the CI/CD pipeline to catch issues early.

4. **Regular Analysis**: Perform regular static analysis to maintain code quality.

5. **Optimize Memory Usage**: Consider optimizing the codebase to reduce memory usage, which will make it easier to analyze with tools like PHPStan.

6. **Incremental Analysis**: Implement an incremental analysis approach, analyzing only changed files to reduce the resource requirements.

## Conclusion

The PHPStan analysis has identified several issues in the codebase that should be addressed to improve code quality and reduce the risk of runtime errors. By following the recommendations and next steps outlined in this report, the codebase can be made more robust and maintainable.

# Zend to Laravel Migration Documentation

This directory contains documentation related to the migration of the CubeOneBiz Food Delivery Platform from Zend Framework 2 to Laravel 12 microservices.

## Audit Reports

- [Migration Audit Report (2025-05-17)](../migration_audit_report_2025-05-17.md) - Comprehensive audit of migration progress
- [Migration Audit JSON Data (2025-05-17)](./migration_audit_report.json) - Detailed JSON data of migration status

## Migration Plan

The migration follows this high-level approach:

1. **Authentication Service** (Completed)
   - Migrate SanAuth module to Laravel 12 Auth Service
   - Implement Sanctum for token-based authentication
   - Support both legacy and Keycloak authentication flows

2. **Core Business Modules** (In Progress)
   - Customer Service (Completed)
   - QuickServe Service (Partial)
   - Payment Service (Partial)
   - Meal Service (Completed)
   - Subscription Service (Partial)

3. **Operational Modules** (Not Started)
   - Kitchen Service
   - Delivery Service
   - Stdcatalogue Service

4. **Administrative Modules** (Not Started)
   - Admin Service
   - Analytics Service

5. **Auxiliary Modules** (Not Started)
   - Misscall Service
   - Theme Service

## Architecture

The new architecture follows a microservices approach with:

- Kong API Gateway for routing
- Laravel 12 microservices
- RabbitMQ for asynchronous communication
- JWT for authentication
- MySQL/SQLite for data storage

## Progress Tracking

| Module | Status | Completion Date |
|--------|--------|-----------------|
| Auth Service | ✅ Complete | 2025-04-15 |
| Customer Service | ✅ Complete | 2025-04-30 |
| Meal Service | ✅ Complete | 2025-05-10 |
| QuickServe Service | ⚠️ Partial | In Progress |
| Payment Service | ⚠️ Partial | In Progress |
| Subscription Service | ⚠️ Partial | In Progress |
| Gateway Service | ⚠️ Partial | In Progress |
| Admin Service | ❌ Not Started | - |
| Analytics Service | ❌ Not Started | - |
| Kitchen Service | ❌ Not Started | - |
| Delivery Service | ❌ Not Started | - |
| Misscall Service | ❌ Not Started | - |
| Stdcatalogue Service | ❌ Not Started | - |
| Theme Service | ❌ Not Started | - |

## Additional Resources

- [Laravel 12 Documentation](https://laravel.com/docs/12.x)
- [Microservices Best Practices](https://microservices.io/patterns/index.html)
- [Kong API Gateway Documentation](https://docs.konghq.com/)

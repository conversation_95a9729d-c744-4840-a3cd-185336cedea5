namespace QuickServe\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use QuickServe\Service\ConfigService;

class ConfigServiceFactory implements FactoryInterface
{
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $dbAdapter = $serviceLocator->get('Zend\Db\Adapter\Adapter');
        $config = $serviceLocator->get('config');
        
        return new ConfigService($dbAdapter, $config);
    }
}
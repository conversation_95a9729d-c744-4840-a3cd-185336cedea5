# API Module Migration Plan

## Overview

The API module is a critical component for our microservices migration strategy. It currently provides RESTful endpoints for various functionalities including product, customer, order, and configuration management. This document outlines the plan to migrate the Zend Framework API module to a Laravel 12 microservice.

## Current Structure

The current API module has the following components:

- **Controllers**:
  - IndexController
  - AlbumController
  - ProductController
  - ConfigController
  - CustomerController
  - OrderController
  - PlanController

- **Authentication Mechanisms**:
  - OAuth2 Authentication
  - SSO Authentication
  - Keycloak Authentication

- **Dependencies**:
  - QuickServe module for business logic
  - SanAuth module for authentication
  - Various model tables from other modules

## Target Architecture

The API module will be migrated to a Laravel 12 API Gateway service with the following components:

- **Controllers**: RESTful API controllers following Laravel conventions
- **Authentication**: Laravel Sanctum for token-based authentication
- **Middleware**: For request validation, authentication, and routing
- **Services**: Business logic extracted from Zend controllers
- **Models**: Data models with Eloquent ORM
- **Routes**: API routes with versioning (v2)
- **Documentation**: OpenAPI 3.1 specifications

## Migration Steps

### 1. Setup Laravel 12 API Gateway Service

```bash
# Create new Laravel 12 project
composer create-project laravel/laravel api-gateway-service "12.*"

# Navigate to the project directory
cd api-gateway-service

# Install required packages
composer require laravel/sanctum
composer require darkaonline/l5-swagger
```

### 2. Configure Authentication

1. Set up Laravel Sanctum for API authentication
2. Implement token-based authentication
3. Create authentication controllers and middleware
4. Migrate existing authentication logic from Zend

### 3. Create API Controllers

For each Zend controller, create a corresponding Laravel controller:

1. **ProductController**: Endpoints for product management
2. **CustomerController**: Endpoints for customer management
3. **OrderController**: Endpoints for order management
4. **ConfigController**: Endpoints for configuration management
5. **PlanController**: Endpoints for subscription plan management

### 4. Extract Business Logic

1. Identify business logic in Zend controllers
2. Extract logic into Laravel service classes
3. Implement SOLID principles
4. Create unit tests for business logic

### 5. Create Models and Migrations

1. Analyze existing database schema
2. Create Laravel Eloquent models
3. Create database migrations
4. Implement relationships between models

### 6. Configure API Routes

```php
// routes/api.php

// API version prefix
Route::prefix('v2')->group(function () {
    // Product routes
    Route::prefix('products')->group(function () {
        Route::get('/', [ProductController::class, 'index']);
        Route::get('/{id}', [ProductController::class, 'show']);
        Route::post('/', [ProductController::class, 'store'])->middleware('auth:sanctum');
        Route::put('/{id}', [ProductController::class, 'update'])->middleware('auth:sanctum');
        Route::delete('/{id}', [ProductController::class, 'destroy'])->middleware('auth:sanctum');
    });

    // Customer routes
    Route::prefix('customers')->group(function () {
        Route::get('/', [CustomerController::class, 'index'])->middleware('auth:sanctum');
        Route::get('/{id}', [CustomerController::class, 'show'])->middleware('auth:sanctum');
        Route::post('/', [CustomerController::class, 'store']);
        Route::put('/{id}', [CustomerController::class, 'update'])->middleware('auth:sanctum');
        Route::delete('/{id}', [CustomerController::class, 'destroy'])->middleware('auth:sanctum');
    });

    // Order routes
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index'])->middleware('auth:sanctum');
        Route::get('/{id}', [OrderController::class, 'show'])->middleware('auth:sanctum');
        Route::post('/', [OrderController::class, 'store'])->middleware('auth:sanctum');
        Route::put('/{id}', [OrderController::class, 'update'])->middleware('auth:sanctum');
        Route::delete('/{id}', [OrderController::class, 'destroy'])->middleware('auth:sanctum');
    });

    // Config routes
    Route::prefix('config')->group(function () {
        Route::get('/', [ConfigController::class, 'index']);
        Route::get('/{key}', [ConfigController::class, 'show']);
        Route::post('/', [ConfigController::class, 'store'])->middleware('auth:sanctum');
        Route::put('/{key}', [ConfigController::class, 'update'])->middleware('auth:sanctum');
        Route::delete('/{key}', [ConfigController::class, 'destroy'])->middleware('auth:sanctum');
    });

    // Plan routes
    Route::prefix('plans')->group(function () {
        Route::get('/', [PlanController::class, 'index']);
        Route::get('/{id}', [PlanController::class, 'show']);
        Route::post('/', [PlanController::class, 'store'])->middleware('auth:sanctum');
        Route::put('/{id}', [PlanController::class, 'update'])->middleware('auth:sanctum');
        Route::delete('/{id}', [PlanController::class, 'destroy'])->middleware('auth:sanctum');
    });
});
```

### 7. Generate OpenAPI Specifications

1. Install and configure L5-Swagger
2. Add OpenAPI annotations to controllers
3. Generate OpenAPI documentation

### 8. Integrate with Kong API Gateway

1. Configure Kong API Gateway
2. Set up routes in Kong for the API Gateway service
3. Configure authentication and rate limiting

### 9. Testing

1. Create unit tests for controllers and services
2. Create integration tests for API endpoints
3. Create end-to-end tests for complete flows

### 10. Deployment

1. Set up CI/CD pipeline for the API Gateway service
2. Deploy to staging environment
3. Perform testing and validation
4. Deploy to production environment

## Timeline

- **Week 1**: Setup and configuration
- **Week 2**: Authentication implementation
- **Week 3-4**: Controller and service implementation
- **Week 5**: Testing and documentation
- **Week 6**: Integration with Kong and deployment

## Success Criteria

- All existing API functionality is migrated to Laravel 12
- API endpoints are accessible through Kong API Gateway
- Authentication works correctly with Laravel Sanctum
- OpenAPI documentation is generated and accessible
- Test coverage is at least 90%
- Performance is equal to or better than the existing API

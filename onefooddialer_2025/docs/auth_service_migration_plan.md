# Auth Service Migration Plan

## Overview

This document outlines the plan to migrate the SanAuth module from Zend Framework to a Laravel 12 Auth Service. The Auth Service will be a standalone microservice responsible for user authentication, authorization, and token management.

## Current SanAuth Module

The current SanAuth module provides:

1. Multiple authentication methods (legacy, Keycloak)
2. User management
3. Role-based access control
4. Token-based authentication
5. Session management

## Migration Goals

1. Create a standalone Laravel 12 Auth Service
2. Implement all existing authentication methods
3. Provide a RESTful API for authentication
4. Use Laravel Sanctum for token-based authentication
5. Ensure backward compatibility with existing systems
6. Improve security and performance

## Migration Steps

### 1. Setup Laravel 12 Project

```bash
# Create new Laravel 12 project
composer create-project laravel/laravel auth-service-v12 "12.*"

# Navigate to the project directory
cd auth-service-v12

# Install required packages
composer require laravel/sanctum
composer require firebase/php-jwt
composer require darkaonline/l5-swagger
```

### 2. Configure Database

1. Create database migrations for:
   - Users table
   - Password reset tokens table
   - Personal access tokens table (Sanctum)

2. Update the User model to include all required fields:
   - username
   - email
   - password
   - first_name
   - last_name
   - role_id
   - status
   - company_id
   - unit_id
   - auth_type
   - auth_token

### 3. Implement Authentication Services

#### 3.1 Create Authentication Service Interface

```php
<?php

namespace App\Services\Auth;

interface AuthenticationServiceInterface
{
    /**
     * Authenticate a user
     *
     * @param string $username
     * @param string $password
     * @return array
     */
    public function authenticate(string $username, string $password): array;
    
    /**
     * Refresh the authentication token
     *
     * @param string $refreshToken
     * @return array
     */
    public function refreshToken(string $refreshToken): array;
    
    /**
     * Validate the authentication token
     *
     * @param string $token
     * @return bool
     */
    public function validateToken(string $token): bool;
    
    /**
     * Revoke the authentication token
     *
     * @param string $token
     * @return bool
     */
    public function revokeToken(string $token): bool;
}
```

#### 3.2 Implement Legacy Authentication Service

```php
<?php

namespace App\Services\Auth;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\NewAccessToken;

class LegacyAuthenticationService implements AuthenticationServiceInterface
{
    /**
     * Authenticate a user
     *
     * @param string $username
     * @param string $password
     * @return array
     */
    public function authenticate(string $username, string $password): array
    {
        // Find user by username or email
        $user = User::where('username', $username)
            ->orWhere('email', $username)
            ->first();
            
        if (!$user || !Hash::check($password, $user->password)) {
            return [
                'success' => false,
                'message' => 'Invalid credentials'
            ];
        }
        
        // Create token
        $token = $user->createToken('auth_token');
        
        return [
            'success' => true,
            'user' => $user,
            'token' => $token->plainTextToken,
            'token_type' => 'Bearer'
        ];
    }
    
    // Implement other methods...
}
```

#### 3.3 Implement Keycloak Authentication Service

```php
<?php

namespace App\Services\Auth;

use App\Models\User;
use Illuminate\Support\Facades\Http;

class KeycloakAuthenticationService implements AuthenticationServiceInterface
{
    protected $config;
    
    public function __construct(array $config)
    {
        $this->config = $config;
    }
    
    /**
     * Authenticate a user
     *
     * @param string $username
     * @param string $password
     * @return array
     */
    public function authenticate(string $username, string $password): array
    {
        // Get tokens from Keycloak
        $response = Http::asForm()->post($this->getTokenUrl(), [
            'grant_type' => 'password',
            'client_id' => $this->config['client_id'],
            'client_secret' => $this->config['client_secret'],
            'username' => $username,
            'password' => $password,
            'scope' => 'openid profile email'
        ]);
        
        if ($response->failed()) {
            return [
                'success' => false,
                'message' => 'Keycloak authentication failed'
            ];
        }
        
        $tokens = $response->json();
        
        // Get user info
        $userInfo = $this->getUserInfo($tokens['access_token']);
        
        // Find or create user
        $user = $this->findOrCreateUser($userInfo);
        
        // Create Sanctum token
        $token = $user->createToken('auth_token');
        
        return [
            'success' => true,
            'user' => $user,
            'token' => $token->plainTextToken,
            'token_type' => 'Bearer',
            'keycloak_tokens' => $tokens
        ];
    }
    
    // Implement other methods...
}
```

#### 3.4 Implement Unified Authentication Service

```php
<?php

namespace App\Services\Auth;

class UnifiedAuthenticationService implements AuthenticationServiceInterface
{
    protected $legacyService;
    protected $keycloakService;
    protected $authMode;
    
    public function __construct(
        LegacyAuthenticationService $legacyService,
        KeycloakAuthenticationService $keycloakService,
        string $authMode = 'legacy'
    ) {
        $this->legacyService = $legacyService;
        $this->keycloakService = $keycloakService;
        $this->authMode = $authMode;
    }
    
    /**
     * Authenticate a user
     *
     * @param string $username
     * @param string $password
     * @return array
     */
    public function authenticate(string $username, string $password): array
    {
        // Try Keycloak authentication if enabled
        if ($this->authMode === 'keycloak' || $this->authMode === 'both') {
            $result = $this->keycloakService->authenticate($username, $password);
            if ($result['success']) {
                return $result;
            }
        }
        
        // Fall back to legacy authentication if Keycloak failed or is not enabled
        if ($this->authMode === 'legacy' || $this->authMode === 'both') {
            return $this->legacyService->authenticate($username, $password);
        }
        
        return [
            'success' => false,
            'message' => 'Authentication failed'
        ];
    }
    
    // Implement other methods...
}
```

### 4. Implement API Controllers

#### 4.1 Create Auth Controller

```php
<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RefreshTokenRequest;
use App\Services\Auth\AuthenticationServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class AuthController extends Controller
{
    protected $authService;
    
    public function __construct(AuthenticationServiceInterface $authService)
    {
        $this->authService = $authService;
    }
    
    /**
     * Login
     *
     * @param LoginRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(LoginRequest $request)
    {
        $result = $this->authService->authenticate(
            $request->input('username'),
            $request->input('password')
        );
        
        if (!$result['success']) {
            return response()->json([
                'status' => 'error',
                'message' => $result['message']
            ], Response::HTTP_UNAUTHORIZED);
        }
        
        return response()->json([
            'status' => 'success',
            'data' => [
                'user' => $result['user'],
                'token' => $result['token'],
                'token_type' => $result['token_type']
            ]
        ]);
    }
    
    /**
     * Logout
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Logged out successfully'
        ]);
    }
    
    // Implement other methods...
}
```

### 5. Configure Routes

```php
// routes/api.php

// API version prefix
Route::prefix('v2')->group(function () {
    // Auth routes
    Route::prefix('auth')->group(function () {
        // Public routes
        Route::post('login', [AuthController::class, 'login']);
        Route::post('refresh-token', [AuthController::class, 'refreshToken']);
        Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('reset-password', [AuthController::class, 'resetPassword']);
        
        // Keycloak routes
        Route::get('keycloak/login', [AuthController::class, 'keycloakLogin']);
        Route::get('keycloak/callback', [AuthController::class, 'keycloakCallback']);
        
        // Protected routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('logout', [AuthController::class, 'logout']);
            Route::get('user', [AuthController::class, 'getUser']);
            Route::post('validate-token', [AuthController::class, 'validateToken']);
        });
    });
});
```

### 6. Implement Form Requests for Validation

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => ['required', 'string'],
            'password' => ['required', 'string'],
        ];
    }
}
```

### 7. Implement API Resources for Response Formatting

```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'username' => $this->username,
            'email' => $this->email,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'full_name' => $this->full_name,
            'role_id' => $this->role_id,
            'status' => $this->status,
            'company_id' => $this->company_id,
            'unit_id' => $this->unit_id,
            'auth_type' => $this->auth_type,
            'created_at' => $this->created_at->toIso8601String(),
            'updated_at' => $this->updated_at->toIso8601String(),
        ];
    }
}
```

### 8. Testing

1. Create unit tests for authentication services
2. Create feature tests for API endpoints
3. Create integration tests for complete authentication flows

### 9. Documentation

1. Generate OpenAPI documentation for API endpoints
2. Create technical documentation for the Auth Service
3. Document migration process and changes

### 10. Kong API Gateway Integration

1. Configure Kong API Gateway routes for the Auth Service
2. Set up authentication and authorization in Kong
3. Configure rate limiting and caching in Kong

## Timeline

- **Week 1**: Setup and database configuration
- **Week 2**: Authentication services implementation
- **Week 3**: API controllers and routes implementation
- **Week 4**: Testing and documentation
- **Week 5**: Kong API Gateway integration and deployment

## Success Criteria

1. All existing authentication methods are migrated to Laravel 12
2. API endpoints are accessible through Kong API Gateway
3. Authentication works correctly with Laravel Sanctum
4. OpenAPI documentation is generated and accessible
5. Test coverage is at least 90%
6. Performance is equal to or better than the existing SanAuth module

# Keycloak Integration Plan

This document outlines the plan for integrating Keycloak (OneSso) authentication with the tenant application while maintaining backward compatibility with the legacy authentication system.

## Current Authentication System

The current authentication system uses:

1. **Database Authentication:**
   - Users are stored in the `users` table
   - Passwords are hashed using MD5
   - Authentication is handled by `<PERSON><PERSON><PERSON>\Controller\AuthController`

2. **Session Management:**
   - User sessions are managed using PHP sessions
   - Session data is stored in `$_SESSION['tenant']`

## Keycloak Integration Goals

1. **Maintain Backward Compatibility:**
   - Allow existing users to continue using the legacy authentication system
   - Gradually migrate users to Keycloak

2. **Implement Single Sign-On:**
   - Allow users to sign in once and access all applications
   - Implement single sign-out

3. **Improve Security:**
   - Replace MD5 hashing with more secure methods
   - Implement token-based authentication

4. **Centralize User Management:**
   - Manage users in Keycloak
   - Sync user data between the application and Keycloak

## Integration Steps

### 1. Set Up Keycloak Server

1. **Install Keycloak:**
   - Set up a Keycloak server
   - Configure the realm, client, and roles

2. **Configure Client:**
   - Create a client for the tenant application
   - Set up client credentials
   - Configure redirect URIs

3. **Define Roles:**
   - Create roles that match the application's roles
   - Set up role mappings

### 2. Implement Keycloak Authentication in the Application

1. **Add Keycloak Client Library:**
   - Install the Keycloak PHP adapter
   - Configure the adapter with client credentials

2. **Update AuthController:**
   - Add methods to handle Keycloak authentication
   - Implement OAuth 2.0 authorization code flow
   - Handle token validation and refresh

3. **Update Login Form:**
   - Add Keycloak login button
   - Maintain legacy login form for backward compatibility

4. **Implement Token Storage:**
   - Store Keycloak tokens securely
   - Implement token refresh mechanism

### 3. Implement User Synchronization

1. **Create User Sync Service:**
   - Implement a service to sync users between the application and Keycloak
   - Handle user creation, update, and deletion

2. **Map User Attributes:**
   - Map application user attributes to Keycloak user attributes
   - Ensure all required attributes are synchronized

3. **Handle Role Mapping:**
   - Map application roles to Keycloak roles
   - Ensure role changes are synchronized

### 4. Update Authorization Logic

1. **Implement Role-Based Access Control:**
   - Use Keycloak roles for authorization
   - Maintain backward compatibility with legacy roles

2. **Update ACL:**
   - Update access control lists to work with Keycloak roles
   - Ensure all permissions are correctly mapped

3. **Implement Resource-Based Authorization:**
   - Use Keycloak policies for resource-based authorization
   - Implement fine-grained access control

### 5. Implement Single Sign-Out

1. **Update Logout Functionality:**
   - Implement Keycloak logout
   - Ensure all sessions are terminated

2. **Handle Session Expiration:**
   - Implement session timeout handling
   - Redirect to login page when session expires

### 6. Test and Deploy

1. **Unit Testing:**
   - Test authentication flow
   - Test authorization logic
   - Test user synchronization

2. **Integration Testing:**
   - Test with Keycloak server
   - Test with multiple applications

3. **User Acceptance Testing:**
   - Test with real users
   - Gather feedback and make improvements

4. **Deployment:**
   - Deploy to staging environment
   - Monitor for issues
   - Deploy to production environment

## Code Changes

### 1. Update AuthController

```php
// Add Keycloak authentication method
public function keycloakLoginAction()
{
    // Get Keycloak client
    $keycloak = $this->getKeycloakClient();
    
    // Handle authorization code
    $code = $this->params()->fromQuery('code');
    if ($code) {
        // Exchange code for tokens
        $tokens = $keycloak->getTokens($code);
        
        // Store tokens
        $this->storeTokens($tokens);
        
        // Get user info
        $userInfo = $keycloak->getUserInfo($tokens['access_token']);
        
        // Create or update user
        $user = $this->syncUser($userInfo);
        
        // Set user session
        $this->setUserSession($user);
        
        // Redirect to dashboard
        return $this->redirect()->toRoute('admin');
    }
    
    // Redirect to Keycloak login
    $authUrl = $keycloak->getAuthUrl();
    return $this->redirect()->toUrl($authUrl);
}
```

### 2. Update Login Form

```html
<div class="row">
    <div class="large-12 columns">
        <h4>Sign In</h4>
        <p>Choose your authentication method:</p>
        
        <!-- Legacy Login Form -->
        <form method="post">
            <!-- Legacy login form fields -->
        </form>
        
        <!-- Keycloak Login Button -->
        <div class="keycloak-login">
            <a href="<?php echo $this->url('keycloak-login'); ?>" class="button">
                <i class="fa fa-key"></i> Sign In with OneSso
            </a>
        </div>
    </div>
</div>
```

### 3. Implement Token Storage

```php
// Store Keycloak tokens
private function storeTokens($tokens)
{
    // Store in database
    $userId = $this->getUserId();
    $this->getOnessoUserTable()->saveTokens($userId, $tokens);
    
    // Store in session
    $session = new Container('keycloak');
    $session->tokens = $tokens;
}

// Get stored tokens
private function getTokens()
{
    $session = new Container('keycloak');
    return $session->tokens;
}

// Refresh tokens if expired
private function refreshTokensIfNeeded()
{
    $tokens = $this->getTokens();
    
    // Check if access token is expired
    if ($this->isTokenExpired($tokens['access_token'])) {
        // Refresh tokens
        $keycloak = $this->getKeycloakClient();
        $newTokens = $keycloak->refreshTokens($tokens['refresh_token']);
        
        // Store new tokens
        $this->storeTokens($newTokens);
        
        return $newTokens;
    }
    
    return $tokens;
}
```

### 4. Implement User Synchronization

```php
// Sync user from Keycloak
private function syncUser($userInfo)
{
    // Check if user exists
    $email = $userInfo['email'];
    $user = $this->getUserTable()->getUserByEmail($email);
    
    if ($user) {
        // Update user
        $user = $this->updateUser($user, $userInfo);
    } else {
        // Create user
        $user = $this->createUser($userInfo);
    }
    
    // Sync roles
    $this->syncRoles($user, $userInfo);
    
    return $user;
}

// Create user from Keycloak
private function createUser($userInfo)
{
    $user = [
        'email_id' => $userInfo['email'],
        'first_name' => $userInfo['given_name'],
        'last_name' => $userInfo['family_name'],
        'status' => 1,
        'company_id' => $GLOBALS['company_id'],
        'unit_id' => $GLOBALS['unit_id']
    ];
    
    $userId = $this->getUserTable()->saveUser($user);
    
    // Create OneSso user record
    $onessoUser = [
        'user_id' => $userId,
        'keycloak_id' => $userInfo['sub'],
        'status' => 1,
        'company_id' => $GLOBALS['company_id'],
        'unit_id' => $GLOBALS['unit_id']
    ];
    
    $this->getOnessoUserTable()->saveOnessoUser($onessoUser);
    
    return $this->getUserTable()->getUser($userId);
}
```

## Migration Strategy

1. **Phase 1: Parallel Authentication**
   - Implement Keycloak authentication alongside legacy authentication
   - Allow users to choose their authentication method
   - Test with a small group of users

2. **Phase 2: Gradual Migration**
   - Migrate users to Keycloak in batches
   - Provide documentation and support for migrated users
   - Monitor for issues and address them promptly

3. **Phase 3: Legacy Authentication Deprecation**
   - Encourage all users to use Keycloak authentication
   - Set a deadline for legacy authentication deprecation
   - Provide tools for self-service migration

4. **Phase 4: Legacy Authentication Removal**
   - Remove legacy authentication code
   - Clean up database tables
   - Complete the migration

## Monitoring and Maintenance

1. **Monitor Authentication Metrics:**
   - Track successful and failed authentication attempts
   - Monitor token refresh rate
   - Track user migration progress

2. **Maintain Keycloak Server:**
   - Keep Keycloak server up to date
   - Monitor server performance
   - Implement high availability

3. **Handle Issues:**
   - Set up alerting for authentication issues
   - Implement fallback mechanisms
   - Provide support for users experiencing issues

## Conclusion

Integrating Keycloak with the tenant application will improve security, provide single sign-on capabilities, and centralize user management. By following this plan and implementing the changes gradually, we can ensure a smooth transition from the legacy authentication system to Keycloak while maintaining backward compatibility and minimizing disruption to users.

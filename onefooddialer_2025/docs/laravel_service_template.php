<?php

namespace App\Services;

use App\Models\Product;
use App\Repositories\Interfaces\ProductRepositoryInterface;
use App\Exceptions\ProductNotFoundException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Class ProductService
 * 
 * This service handles the business logic for products.
 */
class ProductService
{
    /**
     * The product repository instance.
     *
     * @var \App\Repositories\Interfaces\ProductRepositoryInterface
     */
    protected $productRepository;

    /**
     * Create a new service instance.
     *
     * @param  \App\Repositories\Interfaces\ProductRepositoryInterface  $productRepository
     * @return void
     */
    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    /**
     * Get all products with optional filtering.
     *
     * @param  int  $perPage
     * @param  int|null  $categoryId
     * @param  string|null  $search
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAllProducts(int $perPage = 15, ?int $categoryId = null, ?string $search = null): LengthAwarePaginator
    {
        return $this->productRepository->getAllProducts($perPage, $categoryId, $search);
    }

    /**
     * Get a product by ID.
     *
     * @param  int  $id
     * @return \App\Models\Product
     *
     * @throws \App\Exceptions\ProductNotFoundException
     */
    public function getProductById(int $id): Product
    {
        $product = $this->productRepository->findById($id);

        if (!$product) {
            throw new ProductNotFoundException("Product with ID {$id} not found");
        }

        return $product;
    }

    /**
     * Create a new product.
     *
     * @param  array  $data
     * @return \App\Models\Product
     */
    public function createProduct(array $data): Product
    {
        try {
            DB::beginTransaction();

            // Process and validate data if needed
            $product = $this->productRepository->create($data);

            // Handle any related data (e.g., categories, images)
            if (isset($data['categories'])) {
                $this->syncProductCategories($product, $data['categories']);
            }

            if (isset($data['image']) && $data['image']) {
                $this->handleProductImage($product, $data['image']);
            }

            DB::commit();
            
            return $product;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create product: ' . $e->getMessage(), [
                'data' => $data,
                'exception' => $e
            ]);
            throw $e;
        }
    }

    /**
     * Update an existing product.
     *
     * @param  int  $id
     * @param  array  $data
     * @return \App\Models\Product
     *
     * @throws \App\Exceptions\ProductNotFoundException
     */
    public function updateProduct(int $id, array $data): Product
    {
        $product = $this->getProductById($id);

        try {
            DB::beginTransaction();

            // Update the product
            $product = $this->productRepository->update($product, $data);

            // Handle any related data (e.g., categories, images)
            if (isset($data['categories'])) {
                $this->syncProductCategories($product, $data['categories']);
            }

            if (isset($data['image']) && $data['image']) {
                $this->handleProductImage($product, $data['image']);
            }

            DB::commit();
            
            return $product;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update product: ' . $e->getMessage(), [
                'id' => $id,
                'data' => $data,
                'exception' => $e
            ]);
            throw $e;
        }
    }

    /**
     * Delete a product.
     *
     * @param  int  $id
     * @return bool
     *
     * @throws \App\Exceptions\ProductNotFoundException
     */
    public function deleteProduct(int $id): bool
    {
        $product = $this->getProductById($id);

        try {
            DB::beginTransaction();

            // Delete related data if needed
            // $this->deleteProductRelatedData($product);

            // Delete the product
            $result = $this->productRepository->delete($product);

            DB::commit();
            
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete product: ' . $e->getMessage(), [
                'id' => $id,
                'exception' => $e
            ]);
            throw $e;
        }
    }

    /**
     * Sync product categories.
     *
     * @param  \App\Models\Product  $product
     * @param  array  $categoryIds
     * @return void
     */
    protected function syncProductCategories(Product $product, array $categoryIds): void
    {
        $product->categories()->sync($categoryIds);
    }

    /**
     * Handle product image upload.
     *
     * @param  \App\Models\Product  $product
     * @param  mixed  $image
     * @return void
     */
    protected function handleProductImage(Product $product, $image): void
    {
        // Implementation for handling image upload
        // This could use a dedicated ImageService for better separation of concerns
    }

    /**
     * Get products by category.
     *
     * @param  int  $categoryId
     * @param  int  $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getProductsByCategory(int $categoryId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->productRepository->getProductsByCategory($categoryId, $perPage);
    }

    /**
     * Search products by name or description.
     *
     * @param  string  $query
     * @param  int  $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function searchProducts(string $query, int $perPage = 15): LengthAwarePaginator
    {
        return $this->productRepository->searchProducts($query, $perPage);
    }

    /**
     * Get featured products.
     *
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFeaturedProducts(int $limit = 10): Collection
    {
        return $this->productRepository->getFeaturedProducts($limit);
    }

    /**
     * Get related products.
     *
     * @param  \App\Models\Product  $product
     * @param  int  $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRelatedProducts(Product $product, int $limit = 4): Collection
    {
        return $this->productRepository->getRelatedProducts($product, $limit);
    }
}

# API Documentation

This section provides detailed information about the API endpoints, request/response formats, and integration points used in the QuickServe frontend application.

## Table of Contents

- [API Integration](api-integration.md)
- [Authentication](authentication.md)
- [Customer API](customer-api.md)
- [Payment API](payment-api.md)
- [Order API](order-api.md)
- [Kitchen API](kitchen-api.md)
- [Delivery API](delivery-api.md)

## Overview

The QuickServe frontend communicates with a set of microservices through a Kong API Gateway. Each microservice provides a RESTful API for a specific business domain.

## API Gateway

All API requests are routed through a Kong API Gateway at the following base URL:

```
https://api.quickserve.example.com/v2/
```

The API Gateway provides the following features:

- **Authentication**: JWT-based authentication
- **Rate Limiting**: 100 requests per minute per user
- **Logging**: Request and response logging
- **Monitoring**: Prometheus metrics
- **Caching**: Response caching for GET requests
- **Circuit Breaking**: Automatic circuit breaking for failed requests

## API Client

The frontend uses a custom API client to communicate with the API Gateway. The API client is implemented in `src/lib/api/api-client.ts` and provides the following features:

- **Authentication**: Automatically adds authentication headers to requests
- **Error Handling**: Standardized error handling
- **Caching**: Request caching and deduplication
- **Retry Logic**: Automatic retry for failed requests
- **Cancellation**: Request cancellation
- **TypeScript Types**: Type-safe API requests and responses

## API Services

Each microfrontend has its own service layer that uses the API client to communicate with the corresponding microservice:

- **Customer Service**: `src/services/customer-service.ts`
- **Payment Service**: `src/services/payment-service.ts`
- **Order Service**: `src/services/order-service.ts`
- **Kitchen Service**: `src/services/kitchen-service.ts`
- **Delivery Service**: `src/services/delivery-service.ts`

## Authentication

The API uses JWT-based authentication. The authentication flow is as follows:

1. User logs in with username and password
2. Server validates credentials and returns a JWT token
3. Frontend stores the token in local storage
4. API client includes the token in the `Authorization` header of all requests
5. Server validates the token for each request
6. If the token is invalid or expired, the server returns a 401 error
7. Frontend redirects the user to the login page

## Error Handling

The API client handles errors in a standardized way:

1. If the server returns a 4xx or 5xx error, the API client throws an error
2. The error includes the status code, error message, and any additional data from the server
3. The error is caught by the service layer and handled appropriately
4. If the error is a 401 (Unauthorized), the user is redirected to the login page
5. If the error is a 403 (Forbidden), the user is shown an access denied message
6. For other errors, a toast notification is shown to the user

## Request/Response Format

All API requests and responses use JSON format. The general structure of a response is:

```json
{
  "data": {
    // Response data
  },
  "meta": {
    "total": 100,
    "current_page": 1,
    "per_page": 10,
    "last_page": 10
  },
  "links": {
    "first": "https://api.quickserve.example.com/v2/resource?page=1",
    "last": "https://api.quickserve.example.com/v2/resource?page=10",
    "prev": null,
    "next": "https://api.quickserve.example.com/v2/resource?page=2"
  }
}
```

## Pagination

List endpoints support pagination with the following query parameters:

- `page`: The page number (default: 1)
- `per_page`: The number of items per page (default: 10, max: 100)

The response includes pagination metadata in the `meta` object and pagination links in the `links` object.

## Filtering and Sorting

List endpoints support filtering and sorting with the following query parameters:

- `filter[field]`: Filter by field value (e.g., `filter[status]=active`)
- `sort`: Sort by field (e.g., `sort=created_at` or `sort=-created_at` for descending)

## Search

List endpoints support searching with the following query parameter:

- `search`: Search term (e.g., `search=john`)

## API Versioning

The API is versioned through the URL path. The current version is `v2`.

## Rate Limiting

The API Gateway enforces rate limiting of 100 requests per minute per user. If the rate limit is exceeded, the server returns a 429 (Too Many Requests) error.

## Caching

GET requests are cached by the API Gateway for 5 minutes. The cache can be bypassed by adding a `no-cache=true` query parameter.

## CORS

The API Gateway allows cross-origin requests from the following domains:

- `https://quickserve.example.com`
- `https://dev.quickserve.example.com`
- `http://localhost:3000`

## API Documentation

Each API endpoint is documented in the corresponding API documentation file. The documentation includes:

- **Endpoint**: The URL path and HTTP method
- **Description**: A description of the endpoint
- **Request Parameters**: Query parameters, path parameters, and request body
- **Response**: The response format and status codes
- **Example**: Example request and response

## Further Reading

For more detailed information about specific APIs, please refer to the individual documentation files linked in the Table of Contents.

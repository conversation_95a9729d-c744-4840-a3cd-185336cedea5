# API Integration

This document provides detailed information about how the QuickServe frontend integrates with the backend APIs.

## Overview

The QuickServe frontend communicates with a set of microservices through a Kong API Gateway. Each microservice provides a RESTful API for a specific business domain.

## API Client

The frontend uses a custom API client to communicate with the API Gateway. The API client is implemented in `src/lib/api/api-client.ts` and provides a standardized way to make API requests.

### API Client Implementation

```typescript
// src/lib/api/api-client.ts

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Create API clients for each microservice
export const authApiClient = createApiClient('/v2/auth');
export const customerApiClient = createApiClient('/v2/customer');
export const paymentApiClient = createApiClient('/v2/payment');
export const orderApiClient = createApiClient('/v2/order');
export const kitchenApiClient = createApiClient('/v2/kitchen');
export const deliveryApiClient = createApiClient('/v2/delivery');

// Create a base API client
function createApiClient(baseURL: string): AxiosInstance {
  const client = axios.create({
    baseURL: `${process.env.NEXT_PUBLIC_API_URL}${baseURL}`,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Add request interceptor for authentication
  client.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Add response interceptor for error handling
  client.interceptors.response.use(
    (response) => response,
    (error) => {
      // Handle 401 Unauthorized errors
      if (error.response && error.response.status === 401) {
        // Redirect to login page
        window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );

  return client;
}

// Generic API request function with TypeScript types
export async function apiRequest<T>(
  client: AxiosInstance,
  config: {
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    url: string;
    params?: Record<string, any>;
    data?: any;
    cache?: boolean;
  }
): Promise<T> {
  try {
    const { method, url, params, data, cache } = config;
    
    const axiosConfig: AxiosRequestConfig = {
      method,
      url,
      params,
      data,
      headers: {
        'Cache-Control': cache ? 'max-age=300' : 'no-cache',
      },
    };
    
    const response: AxiosResponse<T> = await client(axiosConfig);
    return response.data;
  } catch (error) {
    // Handle and transform error
    if (axios.isAxiosError(error) && error.response) {
      const { status, data } = error.response;
      throw new Error(
        `API Error (${status}): ${
          data.message || 'An unknown error occurred'
        }`
      );
    }
    throw error;
  }
}
```

### Using the API Client

The API client is used by the service layer to communicate with the backend APIs. Each microfrontend has its own service layer that uses the API client to communicate with the corresponding microservice.

Example of a service using the API client:

```typescript
// src/services/customer-service.ts

import { customerApiClient, apiRequest } from '@/lib/api/api-client';

export interface Customer {
  id: number;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  created_at: string;
  updated_at: string;
}

export interface CustomerListResponse {
  data: Customer[];
  meta: {
    total: number;
    current_page: number;
    per_page: number;
    last_page: number;
  };
}

export interface CustomerResponse {
  data: Customer;
}

export const CustomerService = {
  // Get all customers with pagination and filtering
  getCustomers: (params?: { page?: number; per_page?: number; search?: string }) =>
    apiRequest<CustomerListResponse>(customerApiClient, {
      method: 'GET',
      url: '/customers',
      params,
      cache: true,
    }),

  // Get a single customer by ID
  getCustomer: (id: number) =>
    apiRequest<CustomerResponse>(customerApiClient, {
      method: 'GET',
      url: `/customers/${id}`,
      cache: true,
    }),

  // Create a new customer
  createCustomer: (data: { name: string; email: string; phone?: string; address?: string }) =>
    apiRequest<CustomerResponse>(customerApiClient, {
      method: 'POST',
      url: '/customers',
      data,
    }),

  // Update a customer
  updateCustomer: (id: number, data: { name?: string; email?: string; phone?: string; address?: string }) =>
    apiRequest<CustomerResponse>(customerApiClient, {
      method: 'PATCH',
      url: `/customers/${id}`,
      data,
    }),

  // Delete a customer
  deleteCustomer: (id: number) =>
    apiRequest<void>(customerApiClient, {
      method: 'DELETE',
      url: `/customers/${id}`,
    }),
};
```

## Service Layer

The service layer provides a clean API for the components to interact with the backend APIs. Each microfrontend has its own service layer that uses the API client to communicate with the corresponding microservice.

### Service Layer Structure

Each service follows a similar structure:

1. **Type Definitions**: TypeScript interfaces for request and response types
2. **Service Object**: An object with methods for each API endpoint
3. **Method Implementation**: Each method uses the `apiRequest` function to make API requests

### Using the Service Layer

The service layer is used by the Zustand stores to fetch and update data. The stores then provide the data to the components.

Example of a store using the service layer:

```typescript
// src/lib/store/customer-store.ts

import { create } from 'zustand';
import { Customer, CustomerService } from '@/services/customer-service';

interface CustomerState {
  customers: Customer[];
  selectedCustomer: Customer | null;
  isLoading: boolean;
  error: string | null;
  totalCustomers: number;
  currentPage: number;
  perPage: number;
  lastPage: number;
  
  fetchCustomers: (params?: { page?: number; per_page?: number; search?: string }) => Promise<void>;
  fetchCustomer: (id: number) => Promise<void>;
  createCustomer: (data: { name: string; email: string; phone?: string; address?: string }) => Promise<Customer>;
  updateCustomer: (id: number, data: { name?: string; email?: string; phone?: string; address?: string }) => Promise<Customer>;
  deleteCustomer: (id: number) => Promise<void>;
  setSelectedCustomer: (customer: Customer | null) => void;
  clearError: () => void;
}

export const useCustomerStore = create<CustomerState>((set, get) => ({
  customers: [],
  selectedCustomer: null,
  isLoading: false,
  error: null,
  totalCustomers: 0,
  currentPage: 1,
  perPage: 10,
  lastPage: 1,
  
  fetchCustomers: async (params) => {
    set({ isLoading: true, error: null });
    try {
      const response = await CustomerService.getCustomers(params);
      set({
        customers: response.data,
        totalCustomers: response.meta.total,
        currentPage: response.meta.current_page,
        perPage: response.meta.per_page,
        lastPage: response.meta.last_page,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch customers',
        isLoading: false,
      });
    }
  },
  
  // ... other methods
}));
```

## Error Handling

The API client handles errors in a standardized way:

1. If the server returns a 4xx or 5xx error, the API client throws an error
2. The error includes the status code, error message, and any additional data from the server
3. The error is caught by the service layer and handled appropriately
4. If the error is a 401 (Unauthorized), the user is redirected to the login page
5. If the error is a 403 (Forbidden), the user is shown an access denied message
6. For other errors, a toast notification is shown to the user

Example of error handling in a component:

```tsx
import { useCustomerStore } from '@/lib/store/customer-store';
import { toast } from 'sonner';

function CustomerList() {
  const { customers, isLoading, error, fetchCustomers } = useCustomerStore();
  
  const handleCreateCustomer = async (data) => {
    try {
      await createCustomer(data);
      toast.success('Customer created successfully');
    } catch (error) {
      toast.error(error.message || 'Failed to create customer');
    }
  };
  
  // ... rest of the component
}
```

## Authentication

The API client automatically adds the authentication token to all requests. The token is stored in local storage and is added to the `Authorization` header of all requests.

If the server returns a 401 (Unauthorized) error, the user is redirected to the login page.

## Caching

The API client supports caching for GET requests. The cache is controlled by the `cache` parameter in the `apiRequest` function.

```typescript
// Cache enabled (default: 5 minutes)
const response = await CustomerService.getCustomers({ cache: true });

// Cache disabled
const response = await CustomerService.getCustomers({ cache: false });
```

## Conclusion

The API integration layer provides a clean and standardized way for the frontend to communicate with the backend APIs. By using a custom API client and a service layer, we can ensure consistent error handling, authentication, and caching across the application.

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Documentation - CubeOneBiz Microservices</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui.css" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@5.10.5/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="https://unpkg.com/swagger-ui-dist@5.10.5/favicon-16x16.png" sizes="16x16" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }

        *, *:before, *:after {
            box-sizing: inherit;
        }

        body {
            margin: 0;
            background: #fafafa;
        }

        .swagger-ui .topbar {
            background-color: #2c3e50;
        }

        .swagger-ui .topbar .download-url-wrapper {
            display: none;
        }

        .service-selector {
            background: #34495e;
            padding: 20px;
            text-align: center;
            color: white;
        }

        .service-selector h1 {
            margin: 0 0 20px 0;
            color: #ecf0f1;
        }

        .service-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .service-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .service-btn:hover {
            background: #2980b9;
        }

        .service-btn.active {
            background: #e74c3c;
        }

        .service-info {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }

        #swagger-ui {
            max-width: 1200px;
            margin: 0 auto;
        }
    </style>
</head>

<body>
    <div class="service-selector">
        <h1>🚀 CubeOneBiz Microservices API Documentation</h1>
        <p>Select a microservice to view its API documentation:</p>
        
        <div class="service-buttons">
            <button class="service-btn active" onclick="loadService('auth-service-v12')">
                🔐 Authentication Service
            </button>
            <button class="service-btn" onclick="loadService('customer-service-v12')">
                👥 Customer Service
            </button>
            <button class="service-btn" onclick="loadService('payment-service-v12')">
                💳 Payment Service
            </button>
            <button class="service-btn" onclick="loadService('quickserve-service-v12')">
                📦 QuickServe Service
            </button>
            <button class="service-btn" onclick="loadService('kitchen-service-v12')">
                🍳 Kitchen Service
            </button>
            <button class="service-btn" onclick="loadService('delivery-service-v12')">
                🚚 Delivery Service
            </button>
            <button class="service-btn" onclick="loadService('analytics-service-v12')">
                📊 Analytics Service
            </button>
        </div>

        <div class="service-info">
            <p><strong>Current Service:</strong> <span id="current-service">Authentication Service</span></p>
            <p><strong>Base URL:</strong> <span id="current-base-url">http://localhost:8000/v2/auth</span></p>
            <p><strong>Version:</strong> v2.0.0</p>
        </div>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-standalone-preset.js"></script>
    <script>
        // Service configurations
        const services = {
            'auth-service-v12': {
                name: 'Authentication Service',
                baseUrl: 'http://localhost:8000/v2/auth',
                spec: '../openapi/auth-service-v12.yaml'
            },
            'customer-service-v12': {
                name: 'Customer Service',
                baseUrl: 'http://localhost:8000/v2/customers',
                spec: '../openapi/customer-service-v12.yaml'
            },
            'payment-service-v12': {
                name: 'Payment Service',
                baseUrl: 'http://localhost:8000/v2/payments',
                spec: '../openapi/payment-service-v12.yaml'
            },
            'quickserve-service-v12': {
                name: 'QuickServe Service',
                baseUrl: 'http://localhost:8000/v2/orders',
                spec: '../openapi/quickserve-service-v12.yaml'
            },
            'kitchen-service-v12': {
                name: 'Kitchen Service',
                baseUrl: 'http://localhost:8000/v2/kitchens',
                spec: '../openapi/kitchen-service-v12.yaml'
            },
            'delivery-service-v12': {
                name: 'Delivery Service',
                baseUrl: 'http://localhost:8000/v2/delivery',
                spec: '../openapi/delivery-service-v12.yaml'
            },
            'analytics-service-v12': {
                name: 'Analytics Service',
                baseUrl: 'http://localhost:8000/v2/analytics',
                spec: '../openapi/analytics-service-v12.yaml'
            }
        };

        let currentUi = null;

        function loadService(serviceKey) {
            const service = services[serviceKey];
            if (!service) return;

            // Update active button
            document.querySelectorAll('.service-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update service info
            document.getElementById('current-service').textContent = service.name;
            document.getElementById('current-base-url').textContent = service.baseUrl;

            // Initialize Swagger UI
            if (currentUi) {
                // Clear existing UI
                document.getElementById('swagger-ui').innerHTML = '';
            }

            currentUi = SwaggerUIBundle({
                url: service.spec,
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                tryItOutEnabled: true,
                requestInterceptor: (request) => {
                    // Add authorization header if available
                    const token = localStorage.getItem('api_token');
                    if (token) {
                        request.headers['Authorization'] = `Bearer ${token}`;
                    }
                    return request;
                },
                responseInterceptor: (response) => {
                    // Handle token refresh if needed
                    if (response.status === 401) {
                        console.log('Unauthorized response received');
                        // Could implement automatic token refresh here
                    }
                    return response;
                },
                onComplete: () => {
                    console.log(`Loaded ${service.name} API documentation`);
                },
                validatorUrl: null, // Disable validator
                docExpansion: 'list',
                operationsSorter: 'alpha',
                tagsSorter: 'alpha',
                filter: true,
                showExtensions: true,
                showCommonExtensions: true
            });
        }

        // Authentication helper functions
        function setApiToken(token) {
            localStorage.setItem('api_token', token);
            console.log('API token set for requests');
        }

        function clearApiToken() {
            localStorage.removeItem('api_token');
            console.log('API token cleared');
        }

        function getApiToken() {
            return localStorage.getItem('api_token');
        }

        // Make functions available globally for console use
        window.setApiToken = setApiToken;
        window.clearApiToken = clearApiToken;
        window.getApiToken = getApiToken;

        // Load default service on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadService('auth-service-v12');
            
            // Add helpful console message
            console.log(`
🚀 CubeOneBiz API Documentation Loaded!

Available functions:
- setApiToken('your-token-here') - Set authentication token for API requests
- clearApiToken() - Clear the authentication token
- getApiToken() - Get the current authentication token

Services available:
${Object.keys(services).map(key => `- ${services[key].name}`).join('\n')}

Happy testing! 🎉
            `);
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                const serviceKeys = Object.keys(services);
                const currentIndex = serviceKeys.findIndex(key => 
                    document.querySelector(`[onclick="loadService('${key}')"]`).classList.contains('active')
                );
                
                if (e.key === 'ArrowLeft' && currentIndex > 0) {
                    e.preventDefault();
                    loadService(serviceKeys[currentIndex - 1]);
                } else if (e.key === 'ArrowRight' && currentIndex < serviceKeys.length - 1) {
                    e.preventDefault();
                    loadService(serviceKeys[currentIndex + 1]);
                }
            }
        });
    </script>
</body>
</html>

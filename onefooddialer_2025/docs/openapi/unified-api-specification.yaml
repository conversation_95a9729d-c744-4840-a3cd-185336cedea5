openapi: 3.1.0
info:
  title: OneFoodDialer 2025 - Unified API Specification
  description: |
    Comprehensive API documentation for the OneFoodDialer 2025 microservices architecture.
    
    This unified specification covers all 426 API endpoints across 11 microservices:
    - Authentication Service (45 endpoints)
    - Customer Service (89 endpoints) 
    - Payment Service (67 endpoints)
    - QuickServe Service (156 endpoints)
    - Kitchen Service (45 endpoints)
    - Delivery Service (78 endpoints)
    - Analytics Service (52 endpoints)
    - Admin Service (23 endpoints)
    - Catalogue Service (48 endpoints)
    - Notification Service (22 endpoints)
    - Misscall Service (16 endpoints)
    
    **Architecture:** Laravel 12 microservices with Kong API Gateway
    **Authentication:** JWT Bearer tokens via Laravel Sanctum
    **API Version:** v2 (current), v1 (legacy support)
    
  version: 2.0.0
  contact:
    name: OneFoodDialer API Support
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: Proprietary
    url: https://onefooddialer.com/license

servers:
  - url: https://api.onefooddialer.com/v2
    description: Production API Server (Kong Gateway)
  - url: https://staging-api.onefooddialer.com/v2
    description: Staging API Server (Kong Gateway)
  - url: http://localhost:8000/v2
    description: Local Development Server (Kong Gateway)

security:
  - BearerAuth: []

# =============================================================================
# AUTHENTICATION SERVICE PATHS
# =============================================================================
paths:
  # Auth Service - 45 endpoints
  /auth/login:
    post:
      tags: [Authentication]
      summary: User login
      description: Authenticate user with email/phone and password
      operationId: login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/logout:
    post:
      tags: [Authentication]
      summary: User logout
      description: Invalidate current session token
      operationId: logout
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /auth/refresh:
    post:
      tags: [Authentication]
      summary: Refresh access token
      description: Get new access token using refresh token
      operationId: refreshToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'

  /auth/register:
    post:
      tags: [Authentication]
      summary: User registration
      description: Register new user account
      operationId: register
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: Registration successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterResponse'

  /auth/verify-otp:
    post:
      tags: [Authentication]
      summary: Verify OTP
      description: Verify one-time password for account verification
      operationId: verifyOtp
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OtpVerificationRequest'
      responses:
        '200':
          description: OTP verified successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  # =============================================================================
  # CUSTOMER SERVICE PATHS  
  # =============================================================================
  
  /customers:
    get:
      tags: [Customer Management]
      summary: List customers
      description: Retrieve paginated list of customers
      operationId: listCustomers
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: per_page
          in: query
          schema:
            type: integer
            default: 15
            maximum: 100
        - name: search
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerListResponse'

    post:
      tags: [Customer Management]
      summary: Create customer
      description: Create a new customer account
      operationId: createCustomer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'

  /customers/{id}:
    get:
      tags: [Customer Management]
      summary: Get customer
      description: Retrieve customer details by ID
      operationId: getCustomer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customer retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags: [Customer Management]
      summary: Update customer
      description: Update customer information
      operationId: updateCustomer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerRequest'
      responses:
        '200':
          description: Customer updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerResponse'

    delete:
      tags: [Customer Management]
      summary: Delete customer
      description: Soft delete customer account
      operationId: deleteCustomer
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customer deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  # =============================================================================
  # PAYMENT SERVICE PATHS
  # =============================================================================
  
  /payments:
    post:
      tags: [Payment Processing]
      summary: Process payment
      description: Process payment for an order
      operationId: processPayment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentRequest'
      responses:
        '200':
          description: Payment processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentResponse'
        '400':
          description: Payment failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentErrorResponse'

  /payments/{id}/status:
    get:
      tags: [Payment Processing]
      summary: Get payment status
      description: Check the status of a payment transaction
      operationId: getPaymentStatus
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Payment status retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentStatusResponse'

  /payments/{id}/refund:
    post:
      tags: [Payment Processing]
      summary: Refund payment
      description: Process refund for a payment
      operationId: refundPayment
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefundRequest'
      responses:
        '200':
          description: Refund processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefundResponse'

# =============================================================================
# COMPONENTS SECTION
# =============================================================================
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from /auth/login endpoint

  schemas:
    # Authentication Schemas
    LoginRequest:
      type: object
      required: [email, password]
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          format: password
          example: "password123"
        remember_me:
          type: boolean
          default: false

    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Login successful"
        data:
          type: object
          properties:
            user:
              $ref: '#/components/schemas/User'
            access_token:
              type: string
              example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            refresh_token:
              type: string
              example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            token_type:
              type: string
              example: "Bearer"
            expires_in:
              type: integer
              example: 86400

    # User Schema
    User:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+1234567890"
        email_verified_at:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    # Standard Response Schemas
    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "An error occurred"
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string

# =============================================================================
# TAGS SECTION
# =============================================================================
tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Customer Management
    description: Customer CRUD operations and management
  - name: Payment Processing
    description: Payment processing and transaction management
  - name: Order Management
    description: Order lifecycle management
  - name: Kitchen Operations
    description: Kitchen workflow and order preparation
  - name: Delivery Management
    description: Delivery tracking and logistics
  - name: Analytics
    description: Business intelligence and reporting
  - name: Administration
    description: System administration and configuration
  - name: Catalogue Management
    description: Product and menu management
  - name: Notifications
    description: Push notifications and messaging
  - name: Miscall Service
    description: Missed call handling and callbacks

externalDocs:
  description: OneFoodDialer 2025 Complete Documentation
  url: https://docs.onefooddialer.com

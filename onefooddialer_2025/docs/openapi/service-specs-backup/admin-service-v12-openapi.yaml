openapi: 3.1.0
info:
  title: Admin Service API
  description: |
    API for business configuration management.

    This API provides endpoints for managing business configuration settings, roles, permissions, and setup wizard.
  version: 2.0.0
  contact:
    name: Admin Service Team
    email: <EMAIL>
servers:
  - url: http://localhost:8000/api
    description: Local development server
  - url: https://api.example.com/api
    description: Production server
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Error:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: Error message
    Success:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: Success message
    Setting:
      type: object
      properties:
        setting_key:
          type: string
          example: MERCHANT_COMPANY_NAME
        setting_value:
          type: string
          example: Demo Company
        setting_group:
          type: string
          example: company
        setting_type:
          type: string
          example: string
        is_system:
          type: boolean
          example: false
        is_public:
          type: boolean
          example: true
        description:
          type: string
          example: Company name
    Role:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: admin
        display_name:
          type: string
          example: Administrator
        description:
          type: string
          example: System administrator with full access
        is_system:
          type: boolean
          example: true
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/Permission'
    Permission:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: view_settings
        display_name:
          type: string
          example: View Settings
        description:
          type: string
          example: Can view system settings
        module:
          type: string
          example: config
        is_system:
          type: boolean
          example: true
    SetupWizardStatus:
      type: object
      properties:
        completed:
          type: boolean
          example: false
        current_step:
          type: integer
          example: 1
security:
  - bearerAuth: []
paths:
  /v2/admin/user:
    get:
      summary: Get current user information
      description: Returns information about the authenticated user
      responses:
        '200':
          description: User information
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      name:
                        type: string
                        example: John Doe
                      email:
                        type: string
                        example: <EMAIL>
                      roles:
                        type: array
                        items:
                          type: string
                        example: [admin, manager]
                      permissions:
                        type: array
                        items:
                          type: string
                        example: [view_settings, edit_settings]
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/health:
    get:
      summary: Health check endpoint
      description: Returns the health status of the service
      security: []
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Service is healthy
                  timestamp:
                    type: string
                    format: date-time
                    example: 2023-05-20T12:34:56Z
  /v2/admin/config:
    get:
      summary: Get all configuration values
      description: Returns all configuration values
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Configuration values
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    additionalProperties:
                      type: string
                    example:
                      MERCHANT_COMPANY_NAME: Demo Company
                      GLOBAL_CURRENCY: USD
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/config/{key}:
    get:
      summary: Get a specific configuration value
      description: Returns a specific configuration value
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
          description: Configuration key
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Configuration value
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      key:
                        type: string
                        example: MERCHANT_COMPANY_NAME
                      value:
                        type: string
                        example: Demo Company
        '404':
          description: Configuration key not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update a configuration value
      description: Updates a specific configuration value
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
          description: Configuration key
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                value:
                  type: string
                  example: New Company Name
                type:
                  type: string
                  enum: [string, boolean, integer, float, json, array]
                  example: string
                group:
                  type: string
                  example: company
                is_system:
                  type: boolean
                  example: false
                is_public:
                  type: boolean
                  example: true
                description:
                  type: string
                  example: Company name
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
              required:
                - value
      responses:
        '200':
          description: Configuration value updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Configuration key 'MERCHANT_COMPANY_NAME' updated successfully.
                  data:
                    type: object
                    properties:
                      key:
                        type: string
                        example: MERCHANT_COMPANY_NAME
                      value:
                        type: string
                        example: New Company Name
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      value: [The value field is required.]
    delete:
      summary: Delete a configuration value
      description: Deletes a specific configuration value
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
          description: Configuration key
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Configuration value deleted
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Configuration key 'MERCHANT_COMPANY_NAME' deleted successfully.
        '404':
          description: Configuration key not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/config/group/{group}:
    get:
      summary: Get settings by group
      description: Returns configuration values for a specific group
      parameters:
        - name: group
          in: path
          required: true
          schema:
            type: string
          description: Setting group
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Configuration values for the group
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    additionalProperties:
                      type: string
                    example:
                      MERCHANT_COMPANY_NAME: Demo Company
                      MERCHANT_POSTAL_ADDRESS: 123 Main St
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/roles:
    get:
      summary: Get all roles
      description: Returns all roles
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Roles
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Role'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Create a new role
      description: Creates a new role
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: editor
                display_name:
                  type: string
                  example: Editor
                description:
                  type: string
                  example: Can edit content
                permissions:
                  type: array
                  items:
                    type: integer
                  example: [1, 2, 3]
                is_system:
                  type: boolean
                  example: false
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
              required:
                - name
      responses:
        '201':
          description: Role created
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Role created successfully
                  data:
                    $ref: '#/components/schemas/Role'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      name: [The name field is required.]
  /v2/admin/roles/{id}:
    get:
      summary: Get a specific role
      description: Returns a specific role
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Role ID
      responses:
        '200':
          description: Role
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Role'
        '404':
          description: Role not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update a role
      description: Updates a specific role
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Role ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: editor
                display_name:
                  type: string
                  example: Content Editor
                description:
                  type: string
                  example: Can edit content
                permissions:
                  type: array
                  items:
                    type: integer
                  example: [1, 2, 3, 4]
                is_system:
                  type: boolean
                  example: false
      responses:
        '200':
          description: Role updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Role updated successfully
                  data:
                    $ref: '#/components/schemas/Role'
        '404':
          description: Role not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      name: [The name has already been taken.]
    delete:
      summary: Delete a role
      description: Deletes a specific role
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Role ID
      responses:
        '200':
          description: Role deleted
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Role deleted successfully
        '403':
          description: Cannot delete system role
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Cannot delete system role.
        '404':
          description: Role not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/permissions:
    get:
      summary: Get all permissions
      description: Returns all permissions
      responses:
        '200':
          description: Permissions
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Permission'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/permissions/module/{module}:
    get:
      summary: Get permissions by module
      description: Returns permissions for a specific module
      parameters:
        - name: module
          in: path
          required: true
          schema:
            type: string
          description: Module name
      responses:
        '200':
          description: Permissions for the module
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Permission'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/setup-wizard/status:
    get:
      summary: Get setup wizard status
      description: Returns the current status of the setup wizard
      responses:
        '200':
          description: Setup wizard status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/SetupWizardStatus'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update setup wizard status
      description: Updates the status of the setup wizard
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                completed:
                  type: boolean
                  example: true
                current_step:
                  type: integer
                  example: 3
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Setup wizard status updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Setup wizard status updated successfully
                  data:
                    $ref: '#/components/schemas/SetupWizardStatus'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      current_step: [The current step must be between 1 and 5.]
  /v2/admin/setup-wizard/company-profile:
    post:
      summary: Setup company profile
      description: Sets up the company profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                company_name:
                  type: string
                  example: Demo Company
                postal_address:
                  type: string
                  example: 123 Main St, Anytown, CA 12345
                support_email:
                  type: string
                  example: <EMAIL>
                phone:
                  type: string
                  example: 555-1234
                sender_id:
                  type: string
                  example: DEMO
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
              required:
                - company_name
                - postal_address
                - support_email
                - phone
                - sender_id
      responses:
        '200':
          description: Company profile setup completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Company profile setup completed successfully
                  data:
                    type: object
                    properties:
                      current_step:
                        type: integer
                        example: 2
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      company_name: [The company name field is required.]
  /v2/admin/setup-wizard/system-settings:
    post:
      summary: Setup system settings
      description: Sets up the system settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                locale:
                  type: string
                  example: en_US
                currency:
                  type: string
                  example: USD
                currency_symbol:
                  type: string
                  example: $
                time_zone:
                  type: string
                  example: UTC
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
              required:
                - locale
                - currency
                - currency_symbol
                - time_zone
      responses:
        '200':
          description: System settings setup completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: System settings setup completed successfully
                  data:
                    type: object
                    properties:
                      current_step:
                        type: integer
                        example: 3
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      locale: [The locale field is required.]
  /v2/admin/setup-wizard/complete:
    post:
      summary: Complete setup wizard
      description: Completes the setup wizard
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Setup wizard completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Setup wizard completed successfully
                  data:
                    type: object
                    properties:
                      completed:
                        type: boolean
                        example: true
                      current_step:
                        type: integer
                        example: 5
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
# Assets Guide for Tenant Application

This document provides information about the assets (logos, UI components, CSS, JavaScript) used in the tenant application.

## Logo Files

The main logo files are located in several directories:

1. **Main Admin Logo:**
   - `/public/admin/images/logo.png` - This is the main logo used in the login page

2. **Other Logo Variations:**
   - `/public/images/logo.png`, `/public/images/logo1.png`, `/public/images/logo2.png`, `/public/images/logo3.png` - Various logo versions
   - `/public/front/images/logo.png` - Logo for the front-end
   - `/public/kitchen_assets/images/logo.png` - Logo for kitchen interface
   - `/public/delivery_assets/images/logo.png` - Logo for delivery interface

## CSS Files

The main CSS files are located in the following directories:

1. **Admin CSS:**
   - `/public/admin/css/default.css` - Main admin styles
   - `/public/admin/css/foundation.css` - Foundation framework styles
   - `/public/admin/css/font-awesome.css` - Font Awesome icons
   - `/public/admin/css/uniform.aristo.css` - Form styling

2. **Theme CSS:**
   - `/public/css/bootstrap-theme.min.css` - Bootstrap theme
   - `/public/stdcatalogue/css/jquery-ui.theme.css` - jQuery UI theme
   - `/public/front/css/bootstrap-theme.min.css` - Front-end Bootstrap theme

## JavaScript Files

The main JavaScript files are located in the following directories:

1. **Admin JavaScript:**
   - `/public/admin/js/jquery-1.10.2.min.js` - jQuery library
   - `/public/admin/js/foundation.min.js` - Foundation framework
   - `/public/admin/js/customforms.js` - Custom form handling
   - `/public/admin/js/script.js` - Main application script

## Font Files

The main font files are located in the following directories:

1. **Admin Fonts:**
   - `/public/admin/fonts/Maven.woff` - Maven Pro font
   - `/public/admin/fonts/fontawesome-webfont.woff2` - Font Awesome icons

## Layout Templates

The main layout templates are located in the following directories:

1. **Authentication Layout:**
   - `/module/SanAuth/view/layout/layout_new.phtml` - Login page layout

2. **Admin Layout:**
   - `/module/Admin/view/admin/layout/layout_new.phtml` - Admin dashboard layout

3. **Theme Layouts:**
   - `/module/Theme/view/theme/default/layout.phtml` - Default theme layout
   - `/module/Theme/view/theme/mumbai/layout.phtml` - Mumbai theme layout
   - `/module/Theme/view/theme/sandwich/layout.phtml` - Sandwich theme layout
   - `/module/Theme/view/theme/monsoon/layout.phtml` - Monsoon theme layout
   - `/module/Theme/view/theme/burger/layout.phtml` - Burger theme layout
   - `/module/Theme/view/theme/pizza/layout.phtml` - Pizza theme layout

## UI Components

The main UI components are defined in the CSS files and used in the layout templates. Here are some key components:

1. **Login Form:**
   - Defined in `/public/admin/css/default.css`
   - Used in `/module/SanAuth/view/san-auth/auth/login.phtml`

2. **Navigation Menu:**
   - Defined in `/public/admin/css/default.css`
   - Used in various layout templates

3. **Buttons:**
   - Defined in `/public/admin/css/default.css` and `/public/admin/css/foundation.css`
   - Used throughout the application

4. **Forms:**
   - Defined in `/public/admin/css/default.css` and `/public/admin/css/uniform.aristo.css`
   - Used throughout the application

## Updating Assets

When updating assets, consider the following:

1. **Logo:**
   - Update all logo files in the various directories to maintain consistency
   - Ensure the logo has the correct dimensions and format (PNG recommended)

2. **CSS:**
   - Make changes to the appropriate CSS files based on the component you want to modify
   - Test changes in different browsers to ensure compatibility

3. **JavaScript:**
   - Make changes to the appropriate JavaScript files based on the functionality you want to modify
   - Test changes to ensure they don't break existing functionality

4. **Layout Templates:**
   - Make changes to the appropriate layout templates based on the page you want to modify
   - Test changes to ensure they don't break the layout in different screen sizes

## Keycloak Integration

When integrating Keycloak, consider the following:

1. **Login Form:**
   - Modify `/module/SanAuth/view/san-auth/auth/login.phtml` to add Keycloak login button
   - Update `/module/SanAuth/src/SanAuth/Controller/AuthController.php` to handle Keycloak authentication

2. **User Management:**
   - Update user management pages to handle Keycloak users
   - Add functionality to sync users between the application and Keycloak

3. **Session Management:**
   - Update session management to handle Keycloak tokens
   - Add functionality to refresh tokens when they expire

4. **Logout:**
   - Update logout functionality to also log out from Keycloak
   - Ensure single sign-out works correctly

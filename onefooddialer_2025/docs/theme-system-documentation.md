# OneFoodDialer 2025 - Theme System Documentation

## **🎨 Overview**

The OneFoodDialer 2025 theme system provides a comprehensive, type-safe solution for managing UI themes across the application. It includes 8 pre-built themes, a specialized "School Tiffin" theme, and seamless integration with the tenant setup wizard.

## **📋 Table of Contents**

1. [Architecture Overview](#architecture-overview)
2. [Theme Categories](#theme-categories)
3. [School Tiffin Theme](#school-tiffin-theme)
4. [Setup Wizard Integration](#setup-wizard-integration)
5. [Developer Guide](#developer-guide)
6. [API Reference](#api-reference)
7. [Customization Guide](#customization-guide)
8. [Testing](#testing)

## **🏗️ Architecture Overview**

### **Core Components**

```
src/
├── themes/
│   └── index.ts                    # Central theme registry
├── app/
│   └── theme.css                   # CSS theme definitions
├── components/
│   ├── active-theme.tsx            # Theme context provider
│   ├── theme-selector.tsx          # Global theme selector
│   └── setup-wizard/
│       ├── theme-selector.tsx      # Setup wizard theme selector
│       └── theme-selection-form.tsx # Setup wizard form
└── lib/
    └── setup-wizard-constants.ts   # Setup wizard configuration
```

### **Data Flow**

```mermaid
graph TD
    A[Theme Registry] --> B[Theme Provider]
    B --> C[Theme Selector]
    C --> D[CSS Variables]
    D --> E[UI Components]
    
    F[Setup Wizard] --> G[Theme Selection Form]
    G --> H[API Endpoint]
    H --> I[Database Storage]
    I --> J[Theme Application]
```

## **🎯 Theme Categories**

### **1. Default Themes (4 themes)**
- **Default**: Clean and professional (`var(--color-neutral-600)`)
- **Blue**: Corporate blue (`var(--color-blue-600)`)
- **Green**: Eco-friendly green (`var(--color-lime-600)`)
- **Amber**: Warm amber (`var(--color-amber-600)`)

### **2. Scaled Themes (2 themes)**
- **Default Scaled**: Optimized for larger screens
- **Blue Scaled**: Blue theme with enhanced scaling

### **3. Monospace Themes (1 theme)**
- **Mono**: Developer-focused with monospace fonts

### **4. Specialized Themes (1 theme)**
- **School Tiffin**: Designed for school meal services ✅ **NEW**

## **🏫 School Tiffin Theme**

### **Design Philosophy**
The School Tiffin theme is specifically designed for school meal services, featuring:
- **Vibrant Colors**: Appealing to children and parents
- **Friendly Typography**: Poppins font for readability
- **Warm Atmosphere**: Creating a welcoming environment

### **Color Palette**
```css
--primary: #4CAF50;           /* Healthy Green */
--secondary: #FFC107;         /* Energetic Yellow */
--accent: #FF5722;            /* Warm Orange */
--background: #FFF8E1;        /* Light Cream */
--text: #212121;              /* Dark Text */
```

### **Typography**
```css
--font-sans: 'Poppins', 'Helvetica', 'Arial', sans-serif;
--font-serif: 'Merriweather', serif;
```

### **Special Components**
- **`.school-tiffin-card`**: Gradient background cards
- **`.school-tiffin-button`**: Interactive buttons with hover effects
- **`.school-tiffin-accent`**: Accent color utility
- **`.school-tiffin-secondary`**: Secondary color utility

## **🧙‍♂️ Setup Wizard Integration**

### **Theme Selection Step (Step 3 of 8)**

The theme selection is integrated as Step 3 in the setup wizard flow:

1. Company Profile
2. System Settings
3. **Theme Selection** ✅
4. Payment Gateways
5. Menu Setup
6. Subscription Plan
7. Team Invitations
8. Complete Setup

### **Features**
- **Real-time Preview**: Themes apply instantly on hover
- **Categorized Display**: Themes organized by type
- **Color Palette Preview**: Visual color indicators
- **Responsive Design**: Works on all devices
- **Form Validation**: Zod schema validation

### **User Experience Flow**
```
1. User enters theme selection step
2. Themes displayed in categories
3. User hovers over theme → Real-time preview
4. User selects theme → Form validation
5. User clicks continue → API call to save
6. Theme applied and user proceeds to next step
```

## **👨‍💻 Developer Guide**

### **Adding a New Theme**

1. **Define Theme Configuration**
```typescript
// src/themes/index.ts
export const MyCustomTheme: ThemeConfig = {
  name: 'my-custom',
  displayName: 'My Custom Theme',
  colors: {
    primary: '#YOUR_COLOR',
    primaryForeground: '#FFFFFF',
    // ... other colors
  },
  cssClass: 'theme-my-custom',
  category: 'specialized',
  description: 'Your theme description',
};
```

2. **Add CSS Variables**
```css
/* src/app/theme.css */
.theme-my-custom {
  --primary: #YOUR_COLOR;
  --primary-foreground: #FFFFFF;
  /* ... other CSS variables */
}
```

3. **Register Theme**
```typescript
// src/themes/index.ts
export const AvailableThemes: Record<string, ThemeConfig> = {
  // ... existing themes
  'my-custom': MyCustomTheme,
};
```

### **Using Themes in Components**

```tsx
import { useThemeConfig } from '@/components/active-theme';

function MyComponent() {
  const { activeTheme, setActiveTheme } = useThemeConfig();
  
  return (
    <div className={`theme-${activeTheme}`}>
      <button onClick={() => setActiveTheme('school-tiffin')}>
        Switch to School Tiffin
      </button>
    </div>
  );
}
```

### **Theme-Aware Styling**

```css
/* Use CSS variables for theme-aware styling */
.my-component {
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: 1px solid var(--border);
}

/* School Tiffin specific styling */
.theme-school-tiffin .my-component {
  border-radius: var(--radius);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}
```

## **📡 API Reference**

### **Theme Context**

```typescript
interface ThemeContextType {
  activeTheme: string;
  setActiveTheme: (theme: string) => void;
}

// Usage
const { activeTheme, setActiveTheme } = useThemeConfig();
```

### **Theme Configuration Interface**

```typescript
interface ThemeConfig {
  name: string;
  displayName: string;
  colors: ThemeColors;
  fonts?: ThemeFonts;
  borderRadius?: BorderRadius;
  spacing?: Spacing;
  cssClass: string;
  category: 'default' | 'scaled' | 'mono' | 'specialized';
  description?: string;
  previewImage?: string;
}
```

### **Helper Functions**

```typescript
// Get theme by name
const theme = getThemeByName('school-tiffin');

// Get all themes
const allThemes = getAllThemes();

// Get themes by category
const specializedThemes = getThemesByCategory('specialized');

// Get theme options for selectors
const options = getThemeOptions();
```

### **Backend API Endpoints**

```typescript
// Save theme selection
POST /api/admin-service-v12/setup/theme-selection
{
  "ui_theme": "school-tiffin",
  "apply_immediately": true,
  "step_completed": true
}

// Get current theme
GET /api/tenants/current/settings

// Update theme
PUT /api/tenants/{tenant}/settings/theme
{
  "ui_theme": "school-tiffin"
}
```

## **🎨 Customization Guide**

### **Creating Custom Color Schemes**

```typescript
// Define custom colors
const customColors: ThemeColors = {
  primary: '#YOUR_BRAND_COLOR',
  primaryForeground: '#FFFFFF',
  secondary: '#YOUR_SECONDARY_COLOR',
  accent: '#YOUR_ACCENT_COLOR',
  background: '#YOUR_BACKGROUND_COLOR',
  text: '#YOUR_TEXT_COLOR',
};
```

### **Custom Typography**

```typescript
// Define custom fonts
const customFonts: ThemeFonts = {
  sans: ['Your Custom Font', 'Helvetica', 'Arial'],
  serif: ['Your Serif Font', 'serif'],
  mono: ['Your Mono Font', 'monospace'],
};
```

### **Dark Mode Support**

```css
.theme-my-custom {
  /* Light mode colors */
  --primary: #4CAF50;
  
  @variant dark {
    /* Dark mode colors */
    --primary: #66BB6A;
  }
}
```

## **🧪 Testing**

### **Unit Tests**

```typescript
// Test theme switching
import { render, screen } from '@testing-library/react';
import { ThemeSelector } from '@/components/setup-wizard/theme-selector';

test('renders School Tiffin theme', () => {
  render(<ThemeSelector onSelect={jest.fn()} />);
  expect(screen.getByText('School Tiffin')).toBeInTheDocument();
});
```

### **Integration Tests**

```typescript
// Test theme application
test('applies School Tiffin theme correctly', () => {
  const { setActiveTheme } = useThemeConfig();
  setActiveTheme('school-tiffin');
  
  expect(document.body.classList.contains('theme-school-tiffin')).toBe(true);
});
```

### **Visual Regression Tests**

```typescript
// Test theme appearance
test('School Tiffin theme visual appearance', async () => {
  await page.goto('/setup/theme-selection');
  await page.click('[data-theme="school-tiffin"]');
  
  const screenshot = await page.screenshot();
  expect(screenshot).toMatchImageSnapshot();
});
```

## **🚀 Deployment**

### **Build Process**

```bash
# Type check
npm run type-check

# Lint
npm run lint

# Build
npm run build

# Test
npm run test
```

### **Environment Variables**

```env
# Theme configuration
NEXT_PUBLIC_DEFAULT_THEME=default
NEXT_PUBLIC_ENABLE_THEME_PREVIEW=true
```

### **Production Considerations**

1. **Performance**: CSS variables provide optimal performance
2. **Caching**: Theme assets are cached for fast loading
3. **Accessibility**: All themes meet WCAG standards
4. **Browser Support**: Compatible with modern browsers

## **📈 Analytics & Monitoring**

### **Theme Usage Tracking**

```typescript
// Track theme selection
analytics.track('Theme Selected', {
  theme: 'school-tiffin',
  category: 'specialized',
  step: 'setup-wizard',
});
```

### **Performance Monitoring**

```typescript
// Monitor theme switching performance
performance.mark('theme-switch-start');
setActiveTheme('school-tiffin');
performance.mark('theme-switch-end');
performance.measure('theme-switch', 'theme-switch-start', 'theme-switch-end');
```

## **🔮 Future Enhancements**

### **Planned Features**
- **Custom Theme Builder**: Visual theme creation tool
- **Theme Marketplace**: Community-contributed themes
- **Advanced Animations**: Smooth theme transitions
- **Brand Asset Integration**: Logo and asset management
- **Theme Scheduling**: Time-based theme switching

### **Roadmap**
- **Q1 2025**: Custom theme builder
- **Q2 2025**: Theme marketplace
- **Q3 2025**: Advanced animations
- **Q4 2025**: AI-powered theme suggestions

---

**Documentation Version**: 1.0  
**Last Updated**: December 2024  
**Maintained By**: OneFoodDialer Development Team

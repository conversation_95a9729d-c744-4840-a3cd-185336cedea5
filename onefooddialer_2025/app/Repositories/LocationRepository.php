<?php

namespace App\Repositories;

use App\Models\DeliveryLocation;
use App\Models\UserLocation;
use App\Repositories\Contracts\LocationRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class LocationRepository implements LocationRepositoryInterface
{
    /**
     * Get locations for user.
     *
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLocationsForUser(int $userId): Collection
    {
        $locationIds = UserLocation::where('user_id', $userId)
            ->where('status', true)
            ->pluck('location_id')
            ->toArray();
        
        return DeliveryLocation::whereIn('pk_location_code', $locationIds)
            ->where('status', true)
            ->orderBy('location')
            ->get();
    }
    
    /**
     * Get location by ID.
     *
     * @param int $locationId
     * @return \App\Models\DeliveryLocation|null
     */
    public function getLocationById(int $locationId): ?DeliveryLocation
    {
        return DeliveryLocation::find($locationId);
    }
    
    /**
     * Create a new location.
     *
     * @param array $data
     * @return \App\Models\DeliveryLocation
     */
    public function createLocation(array $data): DeliveryLocation
    {
        return DeliveryLocation::create($data);
    }
    
    /**
     * Update a location.
     *
     * @param int $locationId
     * @param array $data
     * @return bool
     */
    public function updateLocation(int $locationId, array $data): bool
    {
        $location = $this->getLocationById($locationId);
        
        if (!$location) {
            return false;
        }
        
        return $location->update($data);
    }
    
    /**
     * Delete a location.
     *
     * @param int $locationId
     * @return bool
     */
    public function deleteLocation(int $locationId): bool
    {
        $location = $this->getLocationById($locationId);
        
        if (!$location) {
            return false;
        }
        
        return $location->delete();
    }
}

<?php

namespace App\Repositories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection as SupportCollection;

class OrderRepository
{
    /**
     * The order model instance.
     *
     * @var Order
     */
    protected $model;

    /**
     * Create a new repository instance.
     *
     * @param Order $model
     * @return void
     */
    public function __construct(Order $model)
    {
        $this->model = $model;
    }

    /**
     * Get all orders with optional filtering.
     *
     * @param array $params
     * @return Collection|SupportCollection|LengthAwarePaginator
     */
    public function getAllOrders(array $params = []): Collection|SupportCollection|LengthAwarePaginator
    {
        $query = $this->model->newQuery();
        
        // Apply filters
        if (isset($params['order_status'])) {
            $query->where('order_status', $params['order_status']);
        }
        
        if (isset($params['delivery_status'])) {
            $query->where('delivery_status', $params['delivery_status']);
        }
        
        if (isset($params['order_menu'])) {
            $query->where('order_menu', $params['order_menu']);
        }
        
        if (isset($params['from_date']) && isset($params['to_date'])) {
            $query->whereBetween('order_date', [$params['from_date'], $params['to_date']]);
        } elseif (isset($params['from_date'])) {
            $query->where('order_date', '>=', $params['from_date']);
        } elseif (isset($params['to_date'])) {
            $query->where('order_date', '<=', $params['to_date']);
        }
        
        if (isset($params['location_code'])) {
            $query->where('location_code', $params['location_code']);
        }
        
        if (isset($params['product_type'])) {
            $query->where('product_type', $params['product_type']);
        }
        
        if (isset($params['food_type'])) {
            $query->where('food_type', $params['food_type']);
        }
        
        // Apply sorting
        $sortField = $params['sort_field'] ?? 'pk_order_no';
        $sortDirection = $params['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($params['per_page'])) {
            return $query->paginate($params['per_page']);
        }
        
        return $query->get();
    }

    /**
     * Find order by ID.
     *
     * @param int $id
     * @return Order|null
     */
    public function findById(int $id): ?Order
    {
        return $this->model->find($id);
    }

    /**
     * Create a new order.
     *
     * @param array $data
     * @return Order
     */
    public function create(array $data): Order
    {
        return $this->model->create($data);
    }

    /**
     * Update an existing order.
     *
     * @param Order $order
     * @param array $data
     * @return Order
     */
    public function update(Order $order, array $data): Order
    {
        $order->update($data);
        return $order->fresh();
    }

    /**
     * Delete an order.
     *
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $order = $this->findById($id);
        
        if (!$order) {
            return false;
        }
        
        return $order->delete();
    }

    /**
     * Get orders by customer ID.
     *
     * @param int $customerId
     * @param array $params
     * @return Collection|SupportCollection|LengthAwarePaginator
     */
    public function getOrdersByCustomer(int $customerId, array $params = []): Collection|SupportCollection|LengthAwarePaginator
    {
        $query = $this->model->where('customer_code', $customerId);
        
        // Apply filters
        if (isset($params['order_status'])) {
            $query->where('order_status', $params['order_status']);
        }
        
        if (isset($params['delivery_status'])) {
            $query->where('delivery_status', $params['delivery_status']);
        }
        
        if (isset($params['order_menu'])) {
            $query->where('order_menu', $params['order_menu']);
        }
        
        if (isset($params['from_date']) && isset($params['to_date'])) {
            $query->whereBetween('order_date', [$params['from_date'], $params['to_date']]);
        } elseif (isset($params['from_date'])) {
            $query->where('order_date', '>=', $params['from_date']);
        } elseif (isset($params['to_date'])) {
            $query->where('order_date', '<=', $params['to_date']);
        }
        
        // Apply sorting
        $sortField = $params['sort_field'] ?? 'order_date';
        $sortDirection = $params['sort_direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);
        
        // Apply pagination if requested
        if (isset($params['per_page'])) {
            return $query->paginate($params['per_page']);
        }
        
        return $query->get();
    }
}

<?php

namespace App\Repositories\Contracts;

use App\Models\DeliveryLocation;
use Illuminate\Database\Eloquent\Collection;

interface LocationRepositoryInterface
{
    /**
     * Get locations for user.
     *
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLocationsForUser(int $userId): Collection;
    
    /**
     * Get location by ID.
     *
     * @param int $locationId
     * @return \App\Models\DeliveryLocation|null
     */
    public function getLocationById(int $locationId): ?DeliveryLocation;
    
    /**
     * Create a new location.
     *
     * @param array $data
     * @return \App\Models\DeliveryLocation
     */
    public function createLocation(array $data): DeliveryLocation;
    
    /**
     * Update a location.
     *
     * @param int $locationId
     * @param array $data
     * @return bool
     */
    public function updateLocation(int $locationId, array $data): bool;
    
    /**
     * Delete a location.
     *
     * @param int $locationId
     * @return bool
     */
    public function deleteLocation(int $locationId): bool;
}

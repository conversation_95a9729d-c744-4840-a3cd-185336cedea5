<?php

namespace App\Http\Requests\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Update Customer Request
 * 
 * This class handles validation for updating a customer.
 */
class UpdateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $customerId = $this->route('id');
        
        return [
            'name' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:20', Rule::unique('customers', 'phone')->ignore($customerId, 'pk_customer_code')],
            'email' => ['nullable', 'email', 'max:255', Rule::unique('customers', 'email_address')->ignore($customerId, 'pk_customer_code')],
            'food_preference' => ['nullable', 'string', 'max:50'],
            'city' => ['nullable', 'string', 'max:50'],
            'city_name' => ['nullable', 'string', 'max:100'],
            'company_name' => ['nullable', 'string', 'max:255'],
            'group_code' => ['nullable', 'string', 'max:50'],
            'group_name' => ['nullable', 'string', 'max:100'],
            'status' => ['nullable', 'boolean'],
            'password' => ['nullable', 'string', 'min:8'],
            'phone_verified' => ['nullable', 'boolean'],
            'email_verified' => ['nullable', 'boolean'],
            'subscription_notification' => ['nullable', 'boolean'],
            'alt_phone' => ['nullable', 'string', 'max:20'],
            'company_id' => ['nullable', 'integer'],
            'unit_id' => ['nullable', 'integer'],
            'isguest' => ['nullable', 'boolean'],
            'delivery_note' => ['nullable', 'string', 'max:255'],
        ];
    }
    
    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Customer name is required',
            'phone.required' => 'Phone number is required',
            'phone.unique' => 'This phone number is already registered',
            'email.email' => 'Please enter a valid email address',
            'email.unique' => 'This email address is already registered',
            'password.min' => 'Password must be at least 8 characters long',
        ];
    }
}

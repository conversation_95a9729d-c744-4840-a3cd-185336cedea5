<?php

namespace App\Http\Requests\Customer;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Create Customer Request
 * 
 * This class handles validation for creating a customer.
 */
class CreateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:20', 'unique:customers,phone'],
            'email' => ['nullable', 'email', 'max:255', 'unique:customers,email_address'],
            'food_preference' => ['nullable', 'string', 'max:50'],
            'city' => ['nullable', 'string', 'max:50'],
            'city_name' => ['nullable', 'string', 'max:100'],
            'company_name' => ['nullable', 'string', 'max:255'],
            'group_code' => ['nullable', 'string', 'max:50'],
            'group_name' => ['nullable', 'string', 'max:100'],
            'registered_from' => ['nullable', 'string', 'max:50'],
            'status' => ['nullable', 'boolean'],
            'password' => ['nullable', 'string', 'min:8'],
            'phone_verified' => ['nullable', 'boolean'],
            'email_verified' => ['nullable', 'boolean'],
            'subscription_notification' => ['nullable', 'boolean'],
            'source' => ['nullable', 'string', 'max:50'],
            'referer' => ['nullable', 'string', 'max:255'],
            'alt_phone' => ['nullable', 'string', 'max:20'],
            'company_id' => ['nullable', 'integer'],
            'unit_id' => ['nullable', 'integer'],
            'isguest' => ['nullable', 'boolean'],
            'delivery_note' => ['nullable', 'string', 'max:255'],
            'addresses' => ['nullable', 'array'],
            'addresses.*.type' => ['required_with:addresses', 'string', 'max:50'],
            'addresses.*.address' => ['required_with:addresses', 'string', 'max:255'],
            'addresses.*.landmark' => ['nullable', 'string', 'max:255'],
            'addresses.*.location_code' => ['nullable', 'string', 'max:50'],
            'addresses.*.location_name' => ['nullable', 'string', 'max:100'],
            'addresses.*.city' => ['nullable', 'string', 'max:50'],
            'addresses.*.city_name' => ['nullable', 'string', 'max:100'],
            'addresses.*.state' => ['nullable', 'string', 'max:50'],
            'addresses.*.country' => ['nullable', 'string', 'max:50'],
            'addresses.*.pincode' => ['nullable', 'string', 'max:20'],
            'addresses.*.latitude' => ['nullable', 'numeric'],
            'addresses.*.longitude' => ['nullable', 'numeric'],
            'addresses.*.is_default' => ['nullable', 'boolean'],
        ];
    }
    
    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Customer name is required',
            'phone.required' => 'Phone number is required',
            'phone.unique' => 'This phone number is already registered',
            'email.email' => 'Please enter a valid email address',
            'email.unique' => 'This email address is already registered',
            'password.min' => 'Password must be at least 8 characters long',
            'addresses.*.type.required_with' => 'Address type is required',
            'addresses.*.address.required_with' => 'Address is required',
        ];
    }
}

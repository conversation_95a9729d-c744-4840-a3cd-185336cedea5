<?php

declare(strict_types=1);

namespace App\Events;

use App\Models\SchoolMealSubscription;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Event fired when subscription is suspended
 */
class SubscriptionSuspended
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly SchoolMealSubscription $subscription,
        public readonly string $reason
    ) {}

    /**
     * Get event data for logging/monitoring
     */
    public function toArray(): array
    {
        return [
            'event' => 'subscription_suspended',
            'subscription_id' => $this->subscription->id,
            'customer_id' => $this->subscription->customer_id,
            'school_id' => $this->subscription->school_id,
            'reason' => $this->reason,
            'billing_failures' => $this->subscription->billing_failures,
            'last_billing_failure_date' => $this->subscription->last_billing_failure_date?->toISOString(),
            'suspended_at' => now()->toISOString(),
        ];
    }
}

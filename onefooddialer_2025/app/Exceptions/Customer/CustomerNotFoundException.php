<?php

namespace App\Exceptions\Customer;

use Exception;

/**
 * Customer Not Found Exception
 * 
 * This exception is thrown when a customer is not found.
 */
class CustomerNotFoundException extends Exception
{
    /**
     * Constructor
     * 
     * @param string $message
     * @param int $code
     * @param Exception|null $previous
     */
    public function __construct(string $message = "Customer not found", int $code = 404, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

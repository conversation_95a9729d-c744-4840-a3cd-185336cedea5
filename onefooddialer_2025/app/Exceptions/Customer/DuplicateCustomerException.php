<?php

namespace App\Exceptions\Customer;

use Exception;

/**
 * Duplicate Customer Exception
 * 
 * This exception is thrown when a duplicate customer is found.
 */
class DuplicateCustomerException extends Exception
{
    /**
     * Constructor
     * 
     * @param string $message
     * @param int $code
     * @param Exception|null $previous
     */
    public function __construct(string $message = "Duplicate customer", int $code = 409, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

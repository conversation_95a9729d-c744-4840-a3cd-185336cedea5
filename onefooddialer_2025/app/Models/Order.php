<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Order extends Model
{
    use HasFactory;
    
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_order_no';
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_no',
        'customer_code',
        'customer_name',
        'customer_phone',
        'ship_address',
        'order_date',
        'delivery_status',
        'order_status',
        'delivery_person',
        'location_code',
        'amount',
        'tax',
        'delivery_charges',
        'applied_discount',
        'payment_mode',
        'amount_paid',
        'fk_kitchen_code',
        'order_menu',
        'delivery_type',
        'delivery_time',
        'delivery_end_time',
        'tp_delivery_order_id',
        'company_id',
        'unit_id'
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'order_date' => 'date',
        'amount' => 'decimal:2',
        'tax' => 'decimal:2',
        'delivery_charges' => 'decimal:2',
        'applied_discount' => 'decimal:2',
        'amount_paid' => 'boolean',
        'delivery_time' => 'datetime:H:i:s',
        'delivery_end_time' => 'datetime:H:i:s'
    ];
    
    /**
     * Get the location associated with this order.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(DeliveryLocation::class, 'location_code', 'pk_location_code');
    }
    
    /**
     * Get the delivery person associated with this order.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function deliveryPerson(): BelongsTo
    {
        return $this->belongsTo(User::class, 'delivery_person', 'id');
    }
    
    /**
     * Calculate the total amount of the order.
     *
     * @return float
     */
    public function getTotalAttribute(): float
    {
        return $this->amount + $this->tax + $this->delivery_charges - $this->applied_discount;
    }
}

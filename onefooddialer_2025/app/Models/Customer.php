<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

/**
 * Customer Model
 * 
 * This model represents a customer in the system.
 */
class Customer extends Model
{
    use HasFactory, Notifiable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customers';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'pk_customer_code';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_name',
        'phone',
        'email_address',
        'customer_Address',
        'location_code',
        'location_name',
        'lunch_loc_code',
        'lunch_loc_name',
        'lunch_add',
        'dinner_loc_code',
        'dinner_loc_name',
        'dinner_add',
        'food_preference',
        'city',
        'city_name',
        'company_name',
        'group_code',
        'group_name',
        'registered_on',
        'registered_from',
        'food_referance',
        'status',
        'otp',
        'password',
        'thirdparty',
        'phone_verified',
        'subscription_notification',
        'email_verified',
        'source',
        'referer',
        'gcm_id',
        'alt_phone',
        'company_id',
        'unit_id',
        'dabbawala_code_type',
        'dabbawala_code',
        'dabbawala_image',
        'isguest',
        'delivery_note'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
        'phone_verified' => 'boolean',
        'email_verified' => 'boolean',
        'registered_on' => 'datetime',
    ];

    /**
     * Get the customer's full name.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return $this->customer_name;
    }

    /**
     * Check if the customer is active.
     *
     * @return bool
     */
    public function isActive()
    {
        return $this->status == 1;
    }

    /**
     * Get the customer's addresses.
     */
    public function addresses()
    {
        return $this->hasMany(CustomerAddress::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the customer's orders.
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Get the customer's wallet.
     */
    public function wallet()
    {
        return $this->hasOne(CustomerWallet::class, 'customer_code', 'pk_customer_code');
    }

    /**
     * Scope a query to only include active customers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope a query to only include customers with verified phone.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePhoneVerified($query)
    {
        return $query->where('phone_verified', 1);
    }

    /**
     * Scope a query to only include customers with verified email.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEmailVerified($query)
    {
        return $query->where('email_verified', 1);
    }

    /**
     * Scope a query to only include customers from a specific company.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $companyId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope a query to only include customers from a specific unit.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $unitId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }
}

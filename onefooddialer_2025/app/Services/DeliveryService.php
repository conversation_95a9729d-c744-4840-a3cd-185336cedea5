<?php

namespace App\Services;

use App\DTOs\DeliveryStatusUpdateDTO;
use App\DTOs\OrderDeliveryDTO;
use App\Events\OrderDeliveredEvent;
use App\Repositories\Contracts\DeliveryRepositoryInterface;
use App\Repositories\Contracts\LocationRepositoryInterface;
use App\Services\Contracts\DeliveryServiceInterface;
use App\Services\ThirdPartyDelivery\Contracts\TPDeliveryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

class DeliveryService implements DeliveryServiceInterface
{
    /**
     * @var \App\Repositories\Contracts\DeliveryRepositoryInterface
     */
    private DeliveryRepositoryInterface $deliveryRepository;
    
    /**
     * @var \App\Repositories\Contracts\LocationRepositoryInterface
     */
    private LocationRepositoryInterface $locationRepository;
    
    /**
     * @var \App\Services\ThirdPartyDelivery\Contracts\TPDeliveryInterface
     */
    private TPDeliveryInterface $thirdPartyDelivery;
    
    /**
     * Create a new service instance.
     *
     * @param \App\Repositories\Contracts\DeliveryRepositoryInterface $deliveryRepository
     * @param \App\Repositories\Contracts\LocationRepositoryInterface $locationRepository
     * @param \App\Services\ThirdPartyDelivery\Contracts\TPDeliveryInterface $thirdPartyDelivery
     */
    public function __construct(
        DeliveryRepositoryInterface $deliveryRepository,
        LocationRepositoryInterface $locationRepository,
        TPDeliveryInterface $thirdPartyDelivery
    ) {
        $this->deliveryRepository = $deliveryRepository;
        $this->locationRepository = $locationRepository;
        $this->thirdPartyDelivery = $thirdPartyDelivery;
    }
    
    /**
     * Get orders for delivery.
     *
     * @param int $userId
     * @param int|null $locationId
     * @param string|null $date
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getDeliveryOrders(int $userId, ?int $locationId = null, ?string $date = null): LengthAwarePaginator
    {
        $date = $date ?? date('Y-m-d');
        return $this->deliveryRepository->getOrdersForDelivery($userId, $locationId, $date);
    }
    
    /**
     * Search orders.
     *
     * @param int $userId
     * @param string $searchTerm
     * @param int|null $locationId
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function searchOrders(int $userId, string $searchTerm, ?int $locationId = null): LengthAwarePaginator
    {
        return $this->deliveryRepository->searchOrders($userId, $searchTerm, $locationId);
    }
    
    /**
     * Update delivery status.
     *
     * @param \App\DTOs\DeliveryStatusUpdateDTO $dto
     * @return bool
     */
    public function updateDeliveryStatus(DeliveryStatusUpdateDTO $dto): bool
    {
        try {
            $result = $this->deliveryRepository->updateDeliveryStatus(
                $dto->orderId,
                $dto->userId,
                $dto->orderCompleted
            );
            
            if ($result) {
                // Dispatch event for order delivered
                event(new OrderDeliveredEvent($dto->orderId, $dto->userId));
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('Error updating delivery status: ' . $e->getMessage(), [
                'order_id' => $dto->orderId,
                'user_id' => $dto->userId
            ]);
            return false;
        }
    }
    
    /**
     * Get delivery locations for user.
     *
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDeliveryLocations(int $userId): Collection
    {
        return $this->locationRepository->getLocationsForUser($userId);
    }
    
    /**
     * Assign delivery person to order.
     *
     * @param int $orderId
     * @param int $deliveryPersonId
     * @return bool
     */
    public function assignDeliveryPerson(int $orderId, int $deliveryPersonId): bool
    {
        return $this->deliveryRepository->assignDeliveryPerson($orderId, $deliveryPersonId);
    }
    
    /**
     * Book third-party delivery.
     *
     * @param \App\DTOs\OrderDeliveryDTO $dto
     * @return array
     */
    public function bookThirdPartyDelivery(OrderDeliveryDTO $dto): array
    {
        $order = $this->deliveryRepository->getOrderById($dto->orderId);
        
        if (!$order) {
            return ['success' => false, 'message' => 'Order not found'];
        }
        
        $data = [
            'pk_order_no' => $order->pk_order_no,
            'order_date' => $order->order_date,
            'fk_kitchen_code' => $order->fk_kitchen_code,
            'order_menu' => $order->order_menu,
            'customer_name' => $order->customer_name,
            'customer_phone' => $order->customer_phone,
            'ship_address' => $order->ship_address,
            'amount' => $order->amount
        ];
        
        $result = $this->thirdPartyDelivery->book($data);
        
        return $result;
    }
    
    /**
     * Cancel third-party delivery.
     *
     * @param int $orderId
     * @return bool
     */
    public function cancelThirdPartyDelivery(int $orderId): bool
    {
        $result = $this->thirdPartyDelivery->cancel($orderId);
        return isset($result['code']) && $result['code'] == 200;
    }
    
    /**
     * Get third-party delivery status.
     *
     * @param int $orderId
     * @return array
     */
    public function getThirdPartyDeliveryStatus(int $orderId): array
    {
        $order = $this->deliveryRepository->getOrderById($orderId);
        
        if (!$order) {
            return ['success' => false, 'message' => 'Order not found'];
        }
        
        $data = [
            'pk_order_no' => $order->pk_order_no
        ];
        
        return $this->thirdPartyDelivery->getStatus($orderId, $data);
    }
}

<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\Subscription;
use App\Models\SubscriptionItem;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SubscriptionService
{
    /**
     * Get all subscriptions.
     *
     * @param array $filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllSubscriptions(array $filters = []): Collection
    {
        $query = Subscription::with(['customer', 'plan', 'items']);

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['plan_id'])) {
            $query->where('plan_id', $filters['plan_id']);
        }

        if (isset($filters['start_date'])) {
            $query->where('start_date', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('end_date', '<=', $filters['end_date']);
        }

        return $query->get();
    }

    /**
     * Get a subscription by ID.
     *
     * @param int $id
     * @return \App\Models\Subscription|null
     */
    public function getSubscriptionById(int $id): ?Subscription
    {
        return Subscription::with(['customer', 'plan', 'items'])->find($id);
    }

    /**
     * Get subscriptions for a customer.
     *
     * @param int $customerId
     * @param string|null $status
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getSubscriptionsForCustomer(int $customerId, ?string $status = null): Collection
    {
        $query = Subscription::with(['plan', 'items'])->where('customer_id', $customerId);

        if ($status) {
            $query->where('status', $status);
        }

        return $query->get();
    }

    /**
     * Create a new subscription.
     *
     * @param array $data
     * @param array $items
     * @return \App\Models\Subscription
     */
    public function createSubscription(array $data, array $items = []): Subscription
    {
        DB::beginTransaction();

        try {
            // Generate a unique subscription number
            $data['subscription_no'] = $this->generateSubscriptionNumber();

            // Create the subscription
            $subscription = Subscription::create($data);

            // Add items to the subscription
            if (!empty($items)) {
                foreach ($items as $item) {
                    $item['subscription_id'] = $subscription->id;
                    $item['company_id'] = $subscription->company_id;
                    $item['unit_id'] = $subscription->unit_id;
                    
                    // Calculate the total for the item
                    $item['total'] = $item['price'] * $item['quantity'];
                    
                    SubscriptionItem::create($item);
                }
            }

            DB::commit();
            return $subscription->fresh(['items']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update a subscription.
     *
     * @param int $id
     * @param array $data
     * @return \App\Models\Subscription|null
     */
    public function updateSubscription(int $id, array $data): ?Subscription
    {
        $subscription = Subscription::find($id);

        if (!$subscription) {
            return null;
        }

        $subscription->update($data);
        return $subscription->fresh();
    }

    /**
     * Delete a subscription.
     *
     * @param int $id
     * @return bool
     */
    public function deleteSubscription(int $id): bool
    {
        $subscription = Subscription::find($id);

        if (!$subscription) {
            return false;
        }

        return $subscription->delete();
    }

    /**
     * Pause a subscription.
     *
     * @param int $id
     * @param string|null $reason
     * @param string|null $resumeDate
     * @return \App\Models\Subscription|null
     */
    public function pauseSubscription(int $id, ?string $reason = null, ?string $resumeDate = null): ?Subscription
    {
        $subscription = Subscription::find($id);

        if (!$subscription) {
            return null;
        }

        $subscription->pause($reason, $resumeDate);
        return $subscription->fresh();
    }

    /**
     * Resume a subscription.
     *
     * @param int $id
     * @return \App\Models\Subscription|null
     */
    public function resumeSubscription(int $id): ?Subscription
    {
        $subscription = Subscription::find($id);

        if (!$subscription) {
            return null;
        }

        $subscription->resume();
        return $subscription->fresh();
    }

    /**
     * Cancel a subscription.
     *
     * @param int $id
     * @param string|null $reason
     * @return \App\Models\Subscription|null
     */
    public function cancelSubscription(int $id, ?string $reason = null): ?Subscription
    {
        $subscription = Subscription::find($id);

        if (!$subscription) {
            return null;
        }

        $subscription->cancel($reason);
        return $subscription->fresh();
    }

    /**
     * Renew a subscription.
     *
     * @param int $id
     * @param int|null $days
     * @return \App\Models\Subscription|null
     */
    public function renewSubscription(int $id, ?int $days = null): ?Subscription
    {
        $subscription = Subscription::find($id);

        if (!$subscription) {
            return null;
        }

        $subscription->renew($days);
        return $subscription->fresh();
    }

    /**
     * Generate a unique subscription number.
     *
     * @return string
     */
    protected function generateSubscriptionNumber(): string
    {
        $prefix = 'SUB-';
        $timestamp = Carbon::now()->format('YmdHis');
        $random = Str::random(4);
        
        return $prefix . $timestamp . '-' . $random;
    }
}

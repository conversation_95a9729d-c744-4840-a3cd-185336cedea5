<?php

namespace App\Services;

use App\Models\Order;
use App\Repositories\OrderRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection as SupportCollection;

class OrderService
{
    /**
     * The order repository instance.
     *
     * @var OrderRepository
     */
    protected $orderRepository;

    /**
     * Create a new service instance.
     *
     * @param OrderRepository $orderRepository
     * @return void
     */
    public function __construct(OrderRepository $orderRepository)
    {
        $this->orderRepository = $orderRepository;
    }

    /**
     * Get all orders with optional filtering.
     *
     * @param array $params
     * @return Collection|SupportCollection|LengthAwarePaginator
     */
    public function getAllOrders(array $params = []): Collection|SupportCollection|LengthAwarePaginator
    {
        return $this->orderRepository->getAllOrders($params);
    }

    /**
     * Get order by ID.
     *
     * @param int $id
     * @return Order|null
     */
    public function getOrderById(int $id): ?Order
    {
        return $this->orderRepository->findById($id);
    }

    /**
     * Create a new order.
     *
     * @param array $data
     * @return Order
     */
    public function createOrder(array $data): Order
    {
        // Calculate tax, delivery charges, etc. if needed
        if (!isset($data['order_no'])) {
            $data['order_no'] = $this->generateOrderNumber();
        }

        if (!isset($data['order_status'])) {
            $data['order_status'] = 'New';
        }

        if (!isset($data['delivery_status'])) {
            $data['delivery_status'] = 'Pending';
        }

        if (!isset($data['invoice_status'])) {
            $data['invoice_status'] = 'Unbill';
        }

        if (!isset($data['created_date'])) {
            $data['created_date'] = now();
        }

        return $this->orderRepository->create($data);
    }

    /**
     * Update an existing order.
     *
     * @param int $id
     * @param array $data
     * @return Order|null
     */
    public function updateOrder(int $id, array $data): ?Order
    {
        $order = $this->orderRepository->findById($id);

        if (!$order) {
            return null;
        }

        return $this->orderRepository->update($order, $data);
    }

    /**
     * Delete an order.
     *
     * @param int $id
     * @return bool
     */
    public function deleteOrder(int $id): bool
    {
        return $this->orderRepository->delete($id);
    }

    /**
     * Get orders by customer ID.
     *
     * @param int $customerId
     * @param array $params
     * @return Collection|SupportCollection|LengthAwarePaginator
     */
    public function getOrdersByCustomer(int $customerId, array $params = []): Collection|SupportCollection|LengthAwarePaginator
    {
        return $this->orderRepository->getOrdersByCustomer($customerId, $params);
    }

    /**
     * Update order status.
     *
     * @param int $id
     * @param string $status
     * @return Order|null
     */
    public function updateOrderStatus(int $id, string $status): ?Order
    {
        $order = $this->orderRepository->findById($id);

        if (!$order) {
            return null;
        }

        return $this->orderRepository->update($order, ['order_status' => $status]);
    }

    /**
     * Update delivery status.
     *
     * @param int $id
     * @param string $status
     * @return Order|null
     */
    public function updateDeliveryStatus(int $id, string $status): ?Order
    {
        $order = $this->orderRepository->findById($id);

        if (!$order) {
            return null;
        }

        return $this->orderRepository->update($order, ['delivery_status' => $status]);
    }

    /**
     * Generate a unique order number.
     *
     * @return string
     */
    protected function generateOrderNumber(): string
    {
        $prefix = 'ORD';
        $timestamp = now()->format('YmdHis');
        $random = rand(1000, 9999);

        return $prefix . $timestamp . $random;
    }
}

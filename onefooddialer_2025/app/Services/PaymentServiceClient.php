<?php

declare(strict_types=1);

namespace App\Services;

use App\DTOs\Payment\PaymentRequestDTO;
use App\DTOs\Payment\PaymentResponseDTO;
use App\Exceptions\Payment\PaymentException;
use App\Services\Resilience\CircuitBreaker;
use App\Services\Resilience\Retry;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Payment Service Client for Subscription Service V12
 * 
 * Handles communication with payment-service-v12 microservice
 * Implements circuit breaker pattern and retry logic for resilience
 */
class PaymentServiceClient
{
    private string $baseUrl;
    private string $apiKey;
    private CircuitBreaker $circuitBreaker;
    private Retry $retry;
    private int $timeout;

    public function __construct()
    {
        $this->baseUrl = config('services.payment.base_url', 'http://payment-service-v12:8000');
        $this->apiKey = config('services.payment.api_key');
        $this->timeout = config('services.payment.timeout', 30);
        
        $this->circuitBreaker = new CircuitBreaker('payment-service', [
            'failure_threshold' => 5,
            'recovery_timeout' => 60,
            'expected_exceptions' => [PaymentException::class],
        ]);
        
        $this->retry = new Retry([
            'max_attempts' => 3,
            'delay' => 1000, // 1 second
            'backoff_multiplier' => 2,
        ]);
    }

    /**
     * Process subscription payment
     */
    public function processSubscriptionPayment(
        int $subscriptionId,
        float $amount,
        string $currency,
        string $paymentMethod,
        array $customerData,
        array $metadata = []
    ): PaymentResponseDTO {
        $paymentData = [
            'order_id' => $subscriptionId,
            'customer_id' => $customerData['customer_id'],
            'amount' => $amount,
            'currency' => $currency,
            'gateway' => $paymentMethod,
            'customer_name' => $customerData['name'],
            'customer_email' => $customerData['email'],
            'customer_phone' => $customerData['phone'] ?? null,
            'product_info' => 'Subscription Payment',
            'metadata' => array_merge($metadata, [
                'subscription_id' => $subscriptionId,
                'service' => 'subscription-service-v12',
            ]),
        ];

        return $this->circuitBreaker->call(function () use ($paymentData) {
            return $this->retry->execute(function () use ($paymentData) {
                $response = $this->makeRequest('POST', '/api/v2/payments', $paymentData);
                
                if (!$response['success']) {
                    throw new PaymentException('Payment initiation failed: ' . ($response['message'] ?? 'Unknown error'));
                }
                
                return new PaymentResponseDTO($response['data']);
            });
        });
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(string $paymentId): PaymentResponseDTO
    {
        return $this->circuitBreaker->call(function () use ($paymentId) {
            $response = $this->makeRequest('GET', "/api/v2/payments/{$paymentId}");
            
            if (!$response['success']) {
                throw new PaymentException('Failed to get payment status: ' . ($response['message'] ?? 'Unknown error'));
            }
            
            return new PaymentResponseDTO($response['data']);
        });
    }

    /**
     * Refund payment
     */
    public function refundPayment(string $paymentId, ?float $amount = null, string $reason = ''): PaymentResponseDTO
    {
        $refundData = [
            'amount' => $amount,
            'reason' => $reason,
        ];

        return $this->circuitBreaker->call(function () use ($paymentId, $refundData) {
            return $this->retry->execute(function () use ($paymentId, $refundData) {
                $response = $this->makeRequest('POST', "/api/v2/payments/{$paymentId}/refund", $refundData);
                
                if (!$response['success']) {
                    throw new PaymentException('Payment refund failed: ' . ($response['message'] ?? 'Unknown error'));
                }
                
                return new PaymentResponseDTO($response['data']);
            });
        });
    }

    /**
     * Cancel payment
     */
    public function cancelPayment(string $paymentId, string $reason = ''): PaymentResponseDTO
    {
        $cancelData = [
            'reason' => $reason,
        ];

        return $this->circuitBreaker->call(function () use ($paymentId, $cancelData) {
            $response = $this->makeRequest('POST', "/api/v2/payments/{$paymentId}/cancel", $cancelData);
            
            if (!$response['success']) {
                throw new PaymentException('Payment cancellation failed: ' . ($response['message'] ?? 'Unknown error'));
            }
            
            return new PaymentResponseDTO($response['data']);
        });
    }

    /**
     * Get customer payment methods
     */
    public function getCustomerPaymentMethods(int $customerId): array
    {
        $cacheKey = "customer_payment_methods_{$customerId}";
        
        return Cache::remember($cacheKey, 300, function () use ($customerId) {
            return $this->circuitBreaker->call(function () use ($customerId) {
                $response = $this->makeRequest('GET', "/api/v2/payment-methods/customer/{$customerId}");
                
                if (!$response['success']) {
                    Log::warning('Failed to get customer payment methods', [
                        'customer_id' => $customerId,
                        'error' => $response['message'] ?? 'Unknown error',
                    ]);
                    return [];
                }
                
                return $response['data'] ?? [];
            });
        });
    }

    /**
     * Get payment statistics for subscription
     */
    public function getSubscriptionPaymentStats(int $subscriptionId, array $filters = []): array
    {
        $params = array_merge($filters, [
            'order_id' => $subscriptionId,
        ]);

        return $this->circuitBreaker->call(function () use ($params) {
            $response = $this->makeRequest('GET', '/api/v2/payments/statistics', $params);
            
            if (!$response['success']) {
                Log::warning('Failed to get subscription payment statistics', [
                    'params' => $params,
                    'error' => $response['message'] ?? 'Unknown error',
                ]);
                return [];
            }
            
            return $response['data'] ?? [];
        });
    }

    /**
     * Verify payment webhook
     */
    public function verifyPaymentWebhook(string $paymentId, array $webhookData): bool
    {
        try {
            $response = $this->makeRequest('POST', "/api/v2/payments/{$paymentId}/verify", $webhookData);
            return $response['success'] ?? false;
        } catch (\Exception $e) {
            Log::error('Payment webhook verification failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Make HTTP request to payment service
     */
    private function makeRequest(string $method, string $endpoint, array $data = []): array
    {
        $correlationId = $this->generateCorrelationId();
        
        try {
            $client = $this->createHttpClient($correlationId);
            
            $response = match (strtoupper($method)) {
                'GET' => $client->get($endpoint, $data),
                'POST' => $client->post($endpoint, $data),
                'PUT' => $client->put($endpoint, $data),
                'DELETE' => $client->delete($endpoint, $data),
                default => throw new \InvalidArgumentException("Unsupported HTTP method: {$method}"),
            };

            $responseData = $response->json();
            
            // Log request/response for debugging
            Log::info('Payment service request', [
                'correlation_id' => $correlationId,
                'method' => $method,
                'endpoint' => $endpoint,
                'status_code' => $response->status(),
                'success' => $responseData['success'] ?? false,
            ]);

            if (!$response->successful()) {
                throw new PaymentException(
                    "Payment service request failed with status {$response->status()}: " . 
                    ($responseData['message'] ?? 'Unknown error')
                );
            }

            return $responseData;
            
        } catch (\Exception $e) {
            Log::error('Payment service request failed', [
                'correlation_id' => $correlationId,
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
            ]);
            
            throw new PaymentException("Payment service communication failed: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * Create HTTP client with authentication and headers
     */
    private function createHttpClient(string $correlationId): PendingRequest
    {
        return Http::withHeaders([
            'Authorization' => "Bearer {$this->apiKey}",
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'X-Correlation-ID' => $correlationId,
            'X-Service' => 'subscription-service-v12',
        ])
        ->timeout($this->timeout)
        ->baseUrl($this->baseUrl);
    }

    /**
     * Generate correlation ID for request tracing
     */
    private function generateCorrelationId(): string
    {
        return 'sub-' . uniqid() . '-' . time();
    }

    /**
     * Check if payment service is healthy
     */
    public function healthCheck(): bool
    {
        try {
            $response = $this->makeRequest('GET', '/api/v2/payments/health');
            return $response['success'] ?? false;
        } catch (\Exception $e) {
            Log::warning('Payment service health check failed', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}

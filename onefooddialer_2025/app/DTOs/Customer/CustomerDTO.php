<?php

namespace App\DTOs\Customer;

/**
 * Customer Data Transfer Object
 * 
 * This DTO encapsulates customer data.
 */
class CustomerDTO
{
    /**
     * @var string
     */
    public $name;
    
    /**
     * @var string
     */
    public $phone;
    
    /**
     * @var string|null
     */
    public $email;
    
    /**
     * @var string|null
     */
    public $foodPreference;
    
    /**
     * @var string|null
     */
    public $city;
    
    /**
     * @var string|null
     */
    public $cityName;
    
    /**
     * @var string|null
     */
    public $companyName;
    
    /**
     * @var string|null
     */
    public $groupCode;
    
    /**
     * @var string|null
     */
    public $groupName;
    
    /**
     * @var string|null
     */
    public $registeredFrom;
    
    /**
     * @var bool
     */
    public $status;
    
    /**
     * @var string|null
     */
    public $otp;
    
    /**
     * @var string|null
     */
    public $password;
    
    /**
     * @var bool
     */
    public $phoneVerified;
    
    /**
     * @var bool
     */
    public $emailVerified;
    
    /**
     * @var bool
     */
    public $subscriptionNotification;
    
    /**
     * @var string|null
     */
    public $source;
    
    /**
     * @var string|null
     */
    public $referer;
    
    /**
     * @var string|null
     */
    public $altPhone;
    
    /**
     * @var int
     */
    public $companyId;
    
    /**
     * @var int
     */
    public $unitId;
    
    /**
     * @var bool
     */
    public $isGuest;
    
    /**
     * @var string|null
     */
    public $deliveryNote;
    
    /**
     * @var array
     */
    public $addresses = [];
    
    /**
     * Constructor
     * 
     * @param string $name
     * @param string $phone
     * @param string|null $email
     * @param string|null $foodPreference
     * @param string|null $city
     * @param string|null $cityName
     * @param string|null $companyName
     * @param string|null $groupCode
     * @param string|null $groupName
     * @param string|null $registeredFrom
     * @param bool $status
     * @param string|null $otp
     * @param string|null $password
     * @param bool $phoneVerified
     * @param bool $emailVerified
     * @param bool $subscriptionNotification
     * @param string|null $source
     * @param string|null $referer
     * @param string|null $altPhone
     * @param int $companyId
     * @param int $unitId
     * @param bool $isGuest
     * @param string|null $deliveryNote
     * @param array $addresses
     */
    public function __construct(
        string $name,
        string $phone,
        ?string $email = null,
        ?string $foodPreference = null,
        ?string $city = null,
        ?string $cityName = null,
        ?string $companyName = null,
        ?string $groupCode = null,
        ?string $groupName = null,
        ?string $registeredFrom = null,
        bool $status = true,
        ?string $otp = null,
        ?string $password = null,
        bool $phoneVerified = false,
        bool $emailVerified = false,
        bool $subscriptionNotification = false,
        ?string $source = null,
        ?string $referer = null,
        ?string $altPhone = null,
        int $companyId = 1,
        int $unitId = 1,
        bool $isGuest = false,
        ?string $deliveryNote = null,
        array $addresses = []
    ) {
        $this->name = $name;
        $this->phone = $phone;
        $this->email = $email;
        $this->foodPreference = $foodPreference;
        $this->city = $city;
        $this->cityName = $cityName;
        $this->companyName = $companyName;
        $this->groupCode = $groupCode;
        $this->groupName = $groupName;
        $this->registeredFrom = $registeredFrom;
        $this->status = $status;
        $this->otp = $otp;
        $this->password = $password;
        $this->phoneVerified = $phoneVerified;
        $this->emailVerified = $emailVerified;
        $this->subscriptionNotification = $subscriptionNotification;
        $this->source = $source;
        $this->referer = $referer;
        $this->altPhone = $altPhone;
        $this->companyId = $companyId;
        $this->unitId = $unitId;
        $this->isGuest = $isGuest;
        $this->deliveryNote = $deliveryNote;
        $this->addresses = $addresses;
    }
    
    /**
     * Create from array
     * 
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        $addresses = [];
        if (isset($data['addresses']) && is_array($data['addresses'])) {
            foreach ($data['addresses'] as $address) {
                $addresses[] = AddressDTO::fromArray($address);
            }
        }
        
        return new self(
            $data['name'],
            $data['phone'],
            $data['email'] ?? null,
            $data['food_preference'] ?? null,
            $data['city'] ?? null,
            $data['city_name'] ?? null,
            $data['company_name'] ?? null,
            $data['group_code'] ?? null,
            $data['group_name'] ?? null,
            $data['registered_from'] ?? null,
            $data['status'] ?? true,
            $data['otp'] ?? null,
            $data['password'] ?? null,
            $data['phone_verified'] ?? false,
            $data['email_verified'] ?? false,
            $data['subscription_notification'] ?? false,
            $data['source'] ?? null,
            $data['referer'] ?? null,
            $data['alt_phone'] ?? null,
            $data['company_id'] ?? 1,
            $data['unit_id'] ?? 1,
            $data['isguest'] ?? false,
            $data['delivery_note'] ?? null,
            $addresses
        );
    }
    
    /**
     * Convert to array
     * 
     * @return array
     */
    public function toArray(): array
    {
        $addresses = [];
        foreach ($this->addresses as $address) {
            $addresses[] = $address->toArray();
        }
        
        return [
            'name' => $this->name,
            'phone' => $this->phone,
            'email' => $this->email,
            'food_preference' => $this->foodPreference,
            'city' => $this->city,
            'city_name' => $this->cityName,
            'company_name' => $this->companyName,
            'group_code' => $this->groupCode,
            'group_name' => $this->groupName,
            'registered_from' => $this->registeredFrom,
            'status' => $this->status,
            'otp' => $this->otp,
            'phone_verified' => $this->phoneVerified,
            'email_verified' => $this->emailVerified,
            'subscription_notification' => $this->subscriptionNotification,
            'source' => $this->source,
            'referer' => $this->referer,
            'alt_phone' => $this->altPhone,
            'company_id' => $this->companyId,
            'unit_id' => $this->unitId,
            'isguest' => $this->isGuest,
            'delivery_note' => $this->deliveryNote,
            'addresses' => $addresses
        ];
    }
}

<?php

namespace App\DTOs\Customer;

/**
 * Address Data Transfer Object
 * 
 * This DTO encapsulates customer address data.
 */
class AddressDTO
{
    /**
     * @var string
     */
    public $type;
    
    /**
     * @var string
     */
    public $address;
    
    /**
     * @var string|null
     */
    public $landmark;
    
    /**
     * @var string|null
     */
    public $locationCode;
    
    /**
     * @var string|null
     */
    public $locationName;
    
    /**
     * @var string|null
     */
    public $city;
    
    /**
     * @var string|null
     */
    public $cityName;
    
    /**
     * @var string|null
     */
    public $state;
    
    /**
     * @var string|null
     */
    public $country;
    
    /**
     * @var string|null
     */
    public $pincode;
    
    /**
     * @var float|null
     */
    public $latitude;
    
    /**
     * @var float|null
     */
    public $longitude;
    
    /**
     * @var bool
     */
    public $isDefault;
    
    /**
     * Constructor
     * 
     * @param string $type
     * @param string $address
     * @param string|null $landmark
     * @param string|null $locationCode
     * @param string|null $locationName
     * @param string|null $city
     * @param string|null $cityName
     * @param string|null $state
     * @param string|null $country
     * @param string|null $pincode
     * @param float|null $latitude
     * @param float|null $longitude
     * @param bool $isDefault
     */
    public function __construct(
        string $type,
        string $address,
        ?string $landmark = null,
        ?string $locationCode = null,
        ?string $locationName = null,
        ?string $city = null,
        ?string $cityName = null,
        ?string $state = null,
        ?string $country = null,
        ?string $pincode = null,
        ?float $latitude = null,
        ?float $longitude = null,
        bool $isDefault = false
    ) {
        $this->type = $type;
        $this->address = $address;
        $this->landmark = $landmark;
        $this->locationCode = $locationCode;
        $this->locationName = $locationName;
        $this->city = $city;
        $this->cityName = $cityName;
        $this->state = $state;
        $this->country = $country;
        $this->pincode = $pincode;
        $this->latitude = $latitude;
        $this->longitude = $longitude;
        $this->isDefault = $isDefault;
    }
    
    /**
     * Create from array
     * 
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            $data['type'],
            $data['address'],
            $data['landmark'] ?? null,
            $data['location_code'] ?? null,
            $data['location_name'] ?? null,
            $data['city'] ?? null,
            $data['city_name'] ?? null,
            $data['state'] ?? null,
            $data['country'] ?? null,
            $data['pincode'] ?? null,
            $data['latitude'] ?? null,
            $data['longitude'] ?? null,
            $data['is_default'] ?? false
        );
    }
    
    /**
     * Convert to array
     * 
     * @return array
     */
    public function toArray(): array
    {
        return [
            'type' => $this->type,
            'address' => $this->address,
            'landmark' => $this->landmark,
            'location_code' => $this->locationCode,
            'location_name' => $this->locationName,
            'city' => $this->city,
            'city_name' => $this->cityName,
            'state' => $this->state,
            'country' => $this->country,
            'pincode' => $this->pincode,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'is_default' => $this->isDefault
        ];
    }
}

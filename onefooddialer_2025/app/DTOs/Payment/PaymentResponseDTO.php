<?php

namespace App\DTOs\Payment;

class PaymentResponseDTO
{
    /**
     * The transaction ID.
     *
     * @var string
     */
    public string $transactionId;

    /**
     * The amount.
     *
     * @var float
     */
    public float $amount;

    /**
     * The status.
     *
     * @var string
     */
    public string $status;

    /**
     * The payment URL.
     *
     * @var string|null
     */
    public ?string $paymentUrl;

    /**
     * The error message.
     *
     * @var string|null
     */
    public ?string $errorMessage;

    /**
     * Create a new DTO instance.
     *
     * @param string $transactionId
     * @param float $amount
     * @param string $status
     * @param string|null $paymentUrl
     * @param string|null $errorMessage
     */
    public function __construct(
        string $transactionId,
        float $amount,
        string $status,
        ?string $paymentUrl = null,
        ?string $errorMessage = null
    ) {
        $this->transactionId = $transactionId;
        $this->amount = $amount;
        $this->status = $status;
        $this->paymentUrl = $paymentUrl;
        $this->errorMessage = $errorMessage;
    }

    /**
     * Convert the DTO to an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'transaction_id' => $this->transactionId,
            'amount' => $this->amount,
            'status' => $this->status,
            'payment_url' => $this->paymentUrl,
            'error_message' => $this->errorMessage,
        ];
    }
}

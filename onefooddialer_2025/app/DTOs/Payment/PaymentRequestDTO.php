<?php

namespace App\DTOs\Payment;

class PaymentRequestDTO
{
    /**
     * The order number.
     *
     * @var string
     */
    public string $orderNo;

    /**
     * The amount.
     *
     * @var float
     */
    public float $amount;

    /**
     * The currency.
     *
     * @var string
     */
    public string $currency;

    /**
     * The payment gateway.
     *
     * @var string
     */
    public string $gateway;

    /**
     * The customer code.
     *
     * @var string
     */
    public string $customerCode;

    /**
     * The customer name.
     *
     * @var string
     */
    public string $customerName;

    /**
     * The customer email.
     *
     * @var string|null
     */
    public ?string $customerEmail;

    /**
     * The customer phone.
     *
     * @var string|null
     */
    public ?string $customerPhone;

    /**
     * The return URL.
     *
     * @var string|null
     */
    public ?string $returnUrl;

    /**
     * The callback URL.
     *
     * @var string|null
     */
    public ?string $callbackUrl;

    /**
     * Create a new DTO instance.
     *
     * @param string $orderNo
     * @param float $amount
     * @param string $gateway
     * @param string $customerCode
     * @param string $customerName
     * @param string|null $customerEmail
     * @param string|null $customerPhone
     * @param string $currency
     * @param string|null $returnUrl
     * @param string|null $callbackUrl
     */
    public function __construct(
        string $orderNo,
        float $amount,
        string $gateway,
        string $customerCode,
        string $customerName,
        ?string $customerEmail = null,
        ?string $customerPhone = null,
        string $currency = 'INR',
        ?string $returnUrl = null,
        ?string $callbackUrl = null
    ) {
        $this->orderNo = $orderNo;
        $this->amount = $amount;
        $this->gateway = $gateway;
        $this->customerCode = $customerCode;
        $this->customerName = $customerName;
        $this->customerEmail = $customerEmail;
        $this->customerPhone = $customerPhone;
        $this->currency = $currency;
        $this->returnUrl = $returnUrl;
        $this->callbackUrl = $callbackUrl;
    }

    /**
     * Convert the DTO to an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'order_no' => $this->orderNo,
            'amount' => $this->amount,
            'gateway' => $this->gateway,
            'customer_code' => $this->customerCode,
            'customer_name' => $this->customerName,
            'customer_email' => $this->customerEmail,
            'customer_phone' => $this->customerPhone,
            'currency' => $this->currency,
            'return_url' => $this->returnUrl,
            'callback_url' => $this->callbackUrl,
        ];
    }
}

<?php

namespace App\DTOs;

class DeliveryStatusUpdateDTO
{
    /**
     * @var int
     */
    public int $orderId;
    
    /**
     * @var int
     */
    public int $userId;
    
    /**
     * @var bool
     */
    public bool $orderCompleted;
    
    /**
     * Create a new DTO instance.
     *
     * @param int $orderId
     * @param int $userId
     * @param bool $orderCompleted
     */
    public function __construct(int $orderId, int $userId, bool $orderCompleted = false)
    {
        $this->orderId = $orderId;
        $this->userId = $userId;
        $this->orderCompleted = $orderCompleted;
    }
}
